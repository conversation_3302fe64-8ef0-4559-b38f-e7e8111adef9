import { useState, useEffect } from "react"
import { VSCodeButton } from "@vscode/webview-ui-toolkit/react"
import { User, Shield, AlertTriangle, ExternalLink } from "lucide-react"

import { useAppTranslation } from "@src/i18n/TranslationContext"
import { vscode } from "@src/utils/vscode"
import { useAuthStore, useIsAuthenticated, useIsAuthenticating, useAuthError } from "@src/stores/authStore"
import { Button } from "@src/components/ui"

const AuthRequiredView = () => {
	const { t } = useAppTranslation()
	const isAuthenticated = useIsAuthenticated()
	const isAuthenticating = useIsAuthenticating()
	const authError = useAuthError()
	const [showRetry, setShowRetry] = useState(false)
	const [imagesBaseUri] = useState(() => {
		const w = window as any
		return w.IMAGES_BASE_URI || ""
	})

	useEffect(() => {
		if (authError) {
			setShowRetry(true)
		}
	}, [authError])

	const handleSignIn = () => {
		setShowRetry(false)
		vscode.postMessage({ type: "deviceOAuthSignIn" })
	}

	const handleRetry = () => {
		setShowRetry(false)
		vscode.postMessage({ type: "deviceOAuthSignIn" })
	}

	if (isAuthenticated) {
		return null // This component should not render when authenticated
	}

	return (
		<div className="flex flex-col min-h-screen p-6 text-center">
			{/* Cubent Logo and Title - Small and at Top */}
			<div className="flex items-center justify-center space-x-2 pt-8 pb-16">
				{/* Cubent Logo */}
				<div className="w-4 h-4 flex items-center justify-center">
					<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" className="text-foreground">
						<g transform="translate(12.5, 12) scale(0.0296, 0.0296) translate(-315, -330)">
							<path d="M 304.59375 3.09375 L 31.457031 149.265625 L 314.679688 301.847656 L 595.179688 147.667969 L 325.085938 3.574219 C 323.539062 2.667969 321.902344 1.96875 320.175781 1.480469 C 318.453125 0.992188 316.691406 0.722656 314.902344 0.683594 C 313.109375 0.640625 311.339844 0.824219 309.59375 1.230469 C 307.847656 1.640625 306.179688 2.261719 304.59375 3.09375 Z" fill="currentColor"/>
							<path d="M 611.667969 512.703125 C 612.527344 512.246094 613.351562 511.730469 614.140625 511.160156 C 614.929688 510.589844 615.675781 509.972656 616.382812 509.300781 C 617.085938 508.628906 617.742188 507.914062 618.347656 507.152344 C 618.957031 506.390625 619.507812 505.59375 620.007812 504.757812 C 620.503906 503.921875 620.945312 503.058594 621.328125 502.164062 C 621.707031 501.265625 622.027344 500.351562 622.28125 499.410156 C 622.539062 498.472656 622.730469 497.519531 622.855469 496.554688 C 622.980469 495.589844 623.042969 494.621094 623.035156 493.648438 L 623.035156 181.769531 L 336.613281 339.308594 L 336.613281 659.515625 Z" fill="currentColor" opacity="0.7"/>
							<path d="M 6.480469 493.007812 C 6.476562 493.988281 6.539062 494.960938 6.664062 495.929688 C 6.792969 496.902344 6.984375 497.855469 7.242188 498.800781 C 7.496094 499.746094 7.816406 500.667969 8.195312 501.570312 C 8.578125 502.46875 9.015625 503.339844 9.515625 504.183594 C 10.011719 505.023438 10.5625 505.828125 11.171875 506.597656 C 11.777344 507.363281 12.433594 508.085938 13.136719 508.765625 C 13.839844 509.445312 14.585938 510.074219 15.375 510.652344 C 16.164062 511.230469 16.988281 511.753906 17.847656 512.222656 L 293.222656 659.515625 L 293.222656 339.308594 L 6.480469 184.808594 Z" fill="currentColor" opacity="0.6"/>
						</g>
					</svg>
				</div>

				{/* Cubent Title */}
				<h1 className="text-sm text-foreground">
					Cubent
				</h1>
			</div>

			{/* Main Content - Centered */}
			<div className="flex-1 flex flex-col items-center justify-center max-w-md mx-auto space-y-8">
				{/* Authentication Required Title */}
				<h2 className="text-xl font-semibold text-foreground">
					Authentication Required
				</h2>

				{/* Description */}
				<div className="space-y-4 text-muted-foreground text-center">
					<p className="text-base leading-relaxed">
						To use Cubent Coder, you need to sign in with your account. This ensures secure access to AI models and your personalized settings.
					</p>
					<p className="text-sm leading-relaxed">
						Your authentication will be saved securely and persist across VS Code sessions until you manually sign out.
					</p>
				</div>

				{/* Error Message */}
				{authError && (
					<div className="p-4 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg">
						<div className="flex items-center space-x-2 text-red-700 dark:text-red-400">
							<AlertTriangle className="w-5 h-5" />
							<span className="font-medium">Authentication Failed</span>
						</div>
						<p className="mt-2 text-sm text-red-600 dark:text-red-300">
							{authError}
						</p>
					</div>
				)}

				{/* Sign In Button */}
				<div className="space-y-3">
					{!isAuthenticating && !showRetry && (
						<Button
							onClick={handleSignIn}
							className="w-full py-3 px-6 bg-blue-600 hover:bg-blue-700 text-white font-medium rounded-lg transition-colors"
						>
							<User className="w-5 h-5 mr-2" />
							Sign In to Continue
						</Button>
					)}

					{isAuthenticating && (
						<div className="space-y-3">
							<Button
								disabled
								className="w-full py-3 px-6 bg-gray-400 text-white font-medium rounded-lg cursor-not-allowed"
							>
								<div className="flex items-center justify-center space-x-2">
									<div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
									<span>Authenticating...</span>
								</div>
							</Button>
							<p className="text-sm text-muted-foreground">
								Please complete the authentication in your browser
							</p>
						</div>
					)}

					{showRetry && (
						<Button
							onClick={handleRetry}
							className="w-full py-3 px-6 bg-blue-600 hover:bg-blue-700 text-white font-medium rounded-lg transition-colors"
						>
							Try Again
						</Button>
					)}
				</div>

				{/* Help Text */}
				<div className="pt-4 border-t border-border">
					<p className="text-sm text-muted-foreground">
						Need help? Visit our{" "}
						<button
							onClick={() => vscode.postMessage({ type: "openExternal", url: "https://docs.cubent.com" })}
							className="text-blue-600 dark:text-blue-400 hover:underline inline-flex items-center"
						>
							documentation
							<ExternalLink className="w-3 h-3 ml-1" />
						</button>
					</p>
				</div>
			</div>
		</div>
	)
}

export default AuthRequiredView
