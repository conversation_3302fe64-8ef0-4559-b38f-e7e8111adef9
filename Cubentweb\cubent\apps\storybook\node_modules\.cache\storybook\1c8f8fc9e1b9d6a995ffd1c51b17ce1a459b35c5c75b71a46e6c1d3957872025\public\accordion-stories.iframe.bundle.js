"use strict";
(self["webpackChunkstorybook"] = self["webpackChunkstorybook"] || []).push([["accordion-stories"],{

/***/ "../../packages/design-system/components/ui/accordion.tsx":
/*!****************************************************************!*\
  !*** ../../packages/design-system/components/ui/accordion.tsx ***!
  \****************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   Accordion: () => (/* binding */ Accordion),
/* harmony export */   AccordionContent: () => (/* binding */ AccordionContent),
/* harmony export */   AccordionItem: () => (/* binding */ AccordionItem),
/* harmony export */   AccordionTrigger: () => (/* binding */ AccordionTrigger)
/* harmony export */ });
/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ "../../node_modules/.pnpm/next@15.3.2_@babel+core@7.2_09efc9edc30971f6ad8d5c21a0ab8f1f/node_modules/next/dist/compiled/react/jsx-dev-runtime.js");
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ "../../node_modules/.pnpm/next@15.3.2_@babel+core@7.2_09efc9edc30971f6ad8d5c21a0ab8f1f/node_modules/next/dist/compiled/react/index.js");
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);
/* harmony import */ var _radix_ui_react_accordion__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @radix-ui/react-accordion */ "../../node_modules/.pnpm/@radix-ui+react-accordion@1_51304fa0823e9b2a065feab26851e0ad/node_modules/@radix-ui/react-accordion/dist/index.mjs");
/* harmony import */ var _barrel_optimize_names_ChevronDownIcon_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDownIcon!=!lucide-react */ "../../node_modules/.pnpm/lucide-react@0.511.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/chevron-down.js");
/* harmony import */ var _repo_design_system_lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @repo/design-system/lib/utils */ "../../packages/design-system/lib/utils.ts");
/* provided dependency */ var __react_refresh_utils__ = __webpack_require__(/*! ../../node_modules/.pnpm/@pmmmwh+react-refresh-webpa_ef086d3919e106717f56f099979b20b9/node_modules/@pmmmwh/react-refresh-webpack-plugin/lib/runtime/RefreshUtils.js */ "../../node_modules/.pnpm/@pmmmwh+react-refresh-webpa_ef086d3919e106717f56f099979b20b9/node_modules/@pmmmwh/react-refresh-webpack-plugin/lib/runtime/RefreshUtils.js");
__webpack_require__.$Refresh$.runtime = __webpack_require__(/*! ../../node_modules/.pnpm/react-refresh@0.14.2/node_modules/react-refresh/runtime.js */ "../../node_modules/.pnpm/react-refresh@0.14.2/node_modules/react-refresh/runtime.js");

"use client";





function Accordion({ ...props }) {
    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_accordion__WEBPACK_IMPORTED_MODULE_3__.Root, {
        "data-slot": "accordion",
        ...props
    }, void 0, false, {
        fileName: "C:\\Users\\<USER>\\Documents\\Cubentweb\\cubent\\packages\\design-system\\components\\ui\\accordion.tsx",
        lineNumber: 12,
        columnNumber: 10
    }, this);
}
_c = Accordion;
function AccordionItem({ className, ...props }) {
    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_accordion__WEBPACK_IMPORTED_MODULE_3__.Item, {
        "data-slot": "accordion-item",
        className: (0,_repo_design_system_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)("border-b last:border-b-0", className),
        ...props
    }, void 0, false, {
        fileName: "C:\\Users\\<USER>\\Documents\\Cubentweb\\cubent\\packages\\design-system\\components\\ui\\accordion.tsx",
        lineNumber: 20,
        columnNumber: 5
    }, this);
}
_c1 = AccordionItem;
function AccordionTrigger({ className, children, ...props }) {
    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_accordion__WEBPACK_IMPORTED_MODULE_3__.Header, {
        className: "flex",
        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_accordion__WEBPACK_IMPORTED_MODULE_3__.Trigger, {
            "data-slot": "accordion-trigger",
            className: (0,_repo_design_system_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)("focus-visible:border-ring focus-visible:ring-ring/50 flex flex-1 items-start justify-between gap-4 rounded-md py-4 text-left text-sm font-medium transition-all outline-none hover:underline focus-visible:ring-[3px] disabled:pointer-events-none disabled:opacity-50 [&[data-state=open]>svg]:rotate-180", className),
            ...props,
            children: [
                children,
                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDownIcon_lucide_react__WEBPACK_IMPORTED_MODULE_4__["default"], {
                    className: "text-muted-foreground pointer-events-none size-4 shrink-0 translate-y-0.5 transition-transform duration-200"
                }, void 0, false, {
                    fileName: "C:\\Users\\<USER>\\Documents\\Cubentweb\\cubent\\packages\\design-system\\components\\ui\\accordion.tsx",
                    lineNumber: 44,
                    columnNumber: 9
                }, this)
            ]
        }, void 0, true, {
            fileName: "C:\\Users\\<USER>\\Documents\\Cubentweb\\cubent\\packages\\design-system\\components\\ui\\accordion.tsx",
            lineNumber: 35,
            columnNumber: 7
        }, this)
    }, void 0, false, {
        fileName: "C:\\Users\\<USER>\\Documents\\Cubentweb\\cubent\\packages\\design-system\\components\\ui\\accordion.tsx",
        lineNumber: 34,
        columnNumber: 5
    }, this);
}
_c2 = AccordionTrigger;
function AccordionContent({ className, children, ...props }) {
    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_accordion__WEBPACK_IMPORTED_MODULE_3__.Content, {
        "data-slot": "accordion-content",
        className: "data-[state=closed]:animate-accordion-up data-[state=open]:animate-accordion-down overflow-hidden text-sm",
        ...props,
        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)("div", {
            className: (0,_repo_design_system_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)("pt-0 pb-4", className),
            children: children
        }, void 0, false, {
            fileName: "C:\\Users\\<USER>\\Documents\\Cubentweb\\cubent\\packages\\design-system\\components\\ui\\accordion.tsx",
            lineNumber: 61,
            columnNumber: 7
        }, this)
    }, void 0, false, {
        fileName: "C:\\Users\\<USER>\\Documents\\Cubentweb\\cubent\\packages\\design-system\\components\\ui\\accordion.tsx",
        lineNumber: 56,
        columnNumber: 5
    }, this);
}
_c3 = AccordionContent;

Accordion.__docgenInfo = {
    "description": "",
    "methods": [],
    "displayName": "Accordion"
};
AccordionItem.__docgenInfo = {
    "description": "",
    "methods": [],
    "displayName": "AccordionItem"
};
AccordionTrigger.__docgenInfo = {
    "description": "",
    "methods": [],
    "displayName": "AccordionTrigger"
};
AccordionContent.__docgenInfo = {
    "description": "",
    "methods": [],
    "displayName": "AccordionContent"
};
var _c, _c1, _c2, _c3;
__webpack_require__.$Refresh$.register(_c, "Accordion");
__webpack_require__.$Refresh$.register(_c1, "AccordionItem");
__webpack_require__.$Refresh$.register(_c2, "AccordionTrigger");
__webpack_require__.$Refresh$.register(_c3, "AccordionContent");


const $ReactRefreshModuleId$ = __webpack_require__.$Refresh$.moduleId;
const $ReactRefreshCurrentExports$ = __react_refresh_utils__.getModuleExports(
	$ReactRefreshModuleId$
);

function $ReactRefreshModuleRuntime$(exports) {
	if (true) {
		let errorOverlay;
		if (true) {
			errorOverlay = false;
		}
		let testMode;
		if (typeof __react_refresh_test__ !== 'undefined') {
			testMode = __react_refresh_test__;
		}
		return __react_refresh_utils__.executeRuntime(
			exports,
			$ReactRefreshModuleId$,
			module.hot,
			errorOverlay,
			testMode
		);
	}
}

if (typeof Promise !== 'undefined' && $ReactRefreshCurrentExports$ instanceof Promise) {
	$ReactRefreshCurrentExports$.then($ReactRefreshModuleRuntime$);
} else {
	$ReactRefreshModuleRuntime$($ReactRefreshCurrentExports$);
}

/***/ }),

/***/ "./stories/accordion.stories.tsx":
/*!***************************************!*\
  !*** ./stories/accordion.stories.tsx ***!
  \***************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   Default: () => (/* binding */ Default),
/* harmony export */   __namedExportsOrder: () => (/* binding */ __namedExportsOrder),
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ "../../node_modules/.pnpm/next@15.3.2_@babel+core@7.2_09efc9edc30971f6ad8d5c21a0ab8f1f/node_modules/next/dist/compiled/react/jsx-dev-runtime.js");
/* harmony import */ var _repo_design_system_components_ui_accordion__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @repo/design-system/components/ui/accordion */ "../../packages/design-system/components/ui/accordion.tsx");
/* provided dependency */ var __react_refresh_utils__ = __webpack_require__(/*! ../../node_modules/.pnpm/@pmmmwh+react-refresh-webpa_ef086d3919e106717f56f099979b20b9/node_modules/@pmmmwh/react-refresh-webpack-plugin/lib/runtime/RefreshUtils.js */ "../../node_modules/.pnpm/@pmmmwh+react-refresh-webpa_ef086d3919e106717f56f099979b20b9/node_modules/@pmmmwh/react-refresh-webpack-plugin/lib/runtime/RefreshUtils.js");
__webpack_require__.$Refresh$.runtime = __webpack_require__(/*! ../../node_modules/.pnpm/react-refresh@0.14.2/node_modules/react-refresh/runtime.js */ "../../node_modules/.pnpm/react-refresh@0.14.2/node_modules/react-refresh/runtime.js");



/**
 * A vertically stacked set of interactive headings that each reveal a section
 * of content.
 */
const meta = {
  title: 'ui/Accordion',
  component: _repo_design_system_components_ui_accordion__WEBPACK_IMPORTED_MODULE_1__.Accordion,
  tags: ['autodocs'],
  argTypes: {
    type: {
      options: ['single', 'multiple'],
      control: {
        type: 'radio'
      }
    }
  },
  args: {
    type: 'single',
    collapsible: true
  },
  render: args => /*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_repo_design_system_components_ui_accordion__WEBPACK_IMPORTED_MODULE_1__.Accordion, {
    ...args,
    children: [/*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_repo_design_system_components_ui_accordion__WEBPACK_IMPORTED_MODULE_1__.AccordionItem, {
      value: "item-1",
      children: [/*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_repo_design_system_components_ui_accordion__WEBPACK_IMPORTED_MODULE_1__.AccordionTrigger, {
        children: "Is it accessible?"
      }, void 0, false, {
        fileName: "C:\\Users\\<USER>\\Documents\\Cubentweb\\cubent\\apps\\storybook\\stories\\accordion.stories.tsx",
        lineNumber: 31,
        columnNumber: 9
      }, undefined), /*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_repo_design_system_components_ui_accordion__WEBPACK_IMPORTED_MODULE_1__.AccordionContent, {
        children: "Yes. It adheres to the WAI-ARIA design pattern."
      }, void 0, false, {
        fileName: "C:\\Users\\<USER>\\Documents\\Cubentweb\\cubent\\apps\\storybook\\stories\\accordion.stories.tsx",
        lineNumber: 32,
        columnNumber: 9
      }, undefined)]
    }, void 0, true, {
      fileName: "C:\\Users\\<USER>\\Documents\\Cubentweb\\cubent\\apps\\storybook\\stories\\accordion.stories.tsx",
      lineNumber: 30,
      columnNumber: 7
    }, undefined), /*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_repo_design_system_components_ui_accordion__WEBPACK_IMPORTED_MODULE_1__.AccordionItem, {
      value: "item-2",
      children: [/*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_repo_design_system_components_ui_accordion__WEBPACK_IMPORTED_MODULE_1__.AccordionTrigger, {
        children: "Is it styled?"
      }, void 0, false, {
        fileName: "C:\\Users\\<USER>\\Documents\\Cubentweb\\cubent\\apps\\storybook\\stories\\accordion.stories.tsx",
        lineNumber: 37,
        columnNumber: 9
      }, undefined), /*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_repo_design_system_components_ui_accordion__WEBPACK_IMPORTED_MODULE_1__.AccordionContent, {
        children: "Yes. It comes with default styles that matches the other components' aesthetic."
      }, void 0, false, {
        fileName: "C:\\Users\\<USER>\\Documents\\Cubentweb\\cubent\\apps\\storybook\\stories\\accordion.stories.tsx",
        lineNumber: 38,
        columnNumber: 9
      }, undefined)]
    }, void 0, true, {
      fileName: "C:\\Users\\<USER>\\Documents\\Cubentweb\\cubent\\apps\\storybook\\stories\\accordion.stories.tsx",
      lineNumber: 36,
      columnNumber: 7
    }, undefined), /*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_repo_design_system_components_ui_accordion__WEBPACK_IMPORTED_MODULE_1__.AccordionItem, {
      value: "item-3",
      children: [/*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_repo_design_system_components_ui_accordion__WEBPACK_IMPORTED_MODULE_1__.AccordionTrigger, {
        children: "Is it animated?"
      }, void 0, false, {
        fileName: "C:\\Users\\<USER>\\Documents\\Cubentweb\\cubent\\apps\\storybook\\stories\\accordion.stories.tsx",
        lineNumber: 44,
        columnNumber: 9
      }, undefined), /*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_repo_design_system_components_ui_accordion__WEBPACK_IMPORTED_MODULE_1__.AccordionContent, {
        children: "Yes. It's animated by default, but you can disable it if you prefer."
      }, void 0, false, {
        fileName: "C:\\Users\\<USER>\\Documents\\Cubentweb\\cubent\\apps\\storybook\\stories\\accordion.stories.tsx",
        lineNumber: 45,
        columnNumber: 9
      }, undefined)]
    }, void 0, true, {
      fileName: "C:\\Users\\<USER>\\Documents\\Cubentweb\\cubent\\apps\\storybook\\stories\\accordion.stories.tsx",
      lineNumber: 43,
      columnNumber: 7
    }, undefined)]
  }, void 0, true, {
    fileName: "C:\\Users\\<USER>\\Documents\\Cubentweb\\cubent\\apps\\storybook\\stories\\accordion.stories.tsx",
    lineNumber: 29,
    columnNumber: 5
  }, undefined),
  parameters: {
    docs: {
      description: {
        component: "A vertically stacked set of interactive headings that each reveal a section\r\nof content."
      }
    }
  }
};
/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (meta);
/**
 * The default behavior of the accordion allows only one item to be open.
 */
const Default = {};
;
const __namedExportsOrder = ["Default"];
Default.parameters = {
  ...Default.parameters,
  docs: {
    ...Default.parameters?.docs,
    source: {
      originalSource: "{}",
      ...Default.parameters?.docs?.source
    },
    description: {
      story: "The default behavior of the accordion allows only one item to be open.",
      ...Default.parameters?.docs?.description
    }
  }
};

const $ReactRefreshModuleId$ = __webpack_require__.$Refresh$.moduleId;
const $ReactRefreshCurrentExports$ = __react_refresh_utils__.getModuleExports(
	$ReactRefreshModuleId$
);

function $ReactRefreshModuleRuntime$(exports) {
	if (true) {
		let errorOverlay;
		if (true) {
			errorOverlay = false;
		}
		let testMode;
		if (typeof __react_refresh_test__ !== 'undefined') {
			testMode = __react_refresh_test__;
		}
		return __react_refresh_utils__.executeRuntime(
			exports,
			$ReactRefreshModuleId$,
			module.hot,
			errorOverlay,
			testMode
		);
	}
}

if (typeof Promise !== 'undefined' && $ReactRefreshCurrentExports$ instanceof Promise) {
	$ReactRefreshCurrentExports$.then($ReactRefreshModuleRuntime$);
} else {
	$ReactRefreshModuleRuntime$($ReactRefreshCurrentExports$);
}

/***/ })

}]);
//# sourceMappingURL=accordion-stories.iframe.bundle.js.map