{"version": 3, "file": "aspect-ratio-stories.iframe.bundle.js", "mappings": ";;;;;;;;;;;;;;;;;;AAAA;;AAEA;AAEA;AAGA;AAAA;AAAA;;;;;;AACA;AAJA;AAMA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACTA;AAEA;AAEA;;;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;AAIA;AAEA;AACA;;;;;;;;;AAGA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AAEA;AAIA;;;AAGA;AACA;AACA;AACA;AACA;AAEA;;;AAGA;AACA;AACA;AACA;AACA;AAEA;;;AAGA;AACA;AACA;AACA;AACA;AAEA;;;AAGA;AACA;AACA;AACA;AACA", "sources": ["webpack://storybook/../../packages/design-system/components/ui/aspect-ratio.tsx", "webpack://storybook/./stories/aspect-ratio.stories.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as AspectRatioPrimitive from \"@radix-ui/react-aspect-ratio\"\n\nfunction AspectRatio({\n  ...props\n}: React.ComponentProps<typeof AspectRatioPrimitive.Root>) {\n  return <AspectRatioPrimitive.Root data-slot=\"aspect-ratio\" {...props} />\n}\n\nexport { AspectRatio }\n", "import type { Meta, StoryObj } from '@storybook/react';\nimport Image from 'next/image';\n\nimport { AspectRatio } from '@repo/design-system/components/ui/aspect-ratio';\n\n/**\n * Displays content within a desired ratio.\n */\nconst meta: Meta<typeof AspectRatio> = {\n  title: 'ui/AspectRatio',\n  component: AspectRatio,\n  tags: ['autodocs'],\n  argTypes: {},\n  render: (args) => (\n    <AspectRatio {...args} className=\"bg-slate-50 dark:bg-slate-800\">\n      <Image\n        src=\"https://images.unsplash.com/photo-1576075796033-848c2a5f3696?w=800&dpr=2&q=80\"\n        alt=\"Photo by <PERSON><PERSON>\"\n        fill\n        className=\"rounded-md object-cover\"\n      />\n    </AspectRatio>\n  ),\n  decorators: [\n    (Story) => (\n      <div className=\"w-1/2\">\n        <Story />\n      </div>\n    ),\n  ],\n} satisfies Meta<typeof AspectRatio>;\n\nexport default meta;\n\ntype Story = StoryObj<typeof meta>;\n\n/**\n * The default form of the aspect ratio.\n */\nexport const Default: Story = {\n  args: {\n    ratio: 16 / 9,\n  },\n};\n\n/**\n * Use the `1:1` aspect ratio to display a square image.\n */\nexport const Square: Story = {\n  args: {\n    ratio: 1,\n  },\n};\n\n/**\n * Use the `4:3` aspect ratio to display a landscape image.\n */\nexport const Landscape: Story = {\n  args: {\n    ratio: 4 / 3,\n  },\n};\n\n/**\n * Use the `2.35:1` aspect ratio to display a cinemascope image.\n */\nexport const Cinemascope: Story = {\n  args: {\n    ratio: 2.35 / 1,\n  },\n};\n"], "names": [], "sourceRoot": ""}