{"version": 3, "file": "avatar-stories.iframe.bundle.js", "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AASA;AACA;;;;;;;;;;;;;;;;AC/HA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAGA;AACA;;;;;;;;;;;;AChBA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;AC9FA;AACA;AACA;AAGA;AACA;;;;;;;;;;;;;;;;;;;;;;;;;ACNA;;AAEA;AACA;AAEA;AAEA;AAIA;AAEA;AACA;AAIA;;;;;;AAGA;;AAEA;AAIA;AAEA;AACA;AACA;;;;;;AAGA;AAXA;AAaA;AAIA;AAEA;AACA;AAIA;;;;;;AAGA;AAdA;AAgBA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AClDA;AAMA;;;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;;;;;AACA;AAAA;;;;;;;;;;;AAGA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AAEA;AAIA;;;AAGA", "sources": ["webpack://storybook/../../node_modules/.pnpm/@radix-ui+react-avatar@1.1._2102b0ade537f1e2a172941acff2e7b1/node_modules/@radix-ui/react-avatar/dist/index.mjs", "webpack://storybook/../../node_modules/.pnpm/@radix-ui+react-use-is-hydr_82baae08d9497ff2d0670f703ce5ede6/node_modules/@radix-ui/react-use-is-hydrated/dist/index.mjs", "webpack://storybook/../../node_modules/.pnpm/use-sync-external-store@1.5.0_react@19.1.0/node_modules/use-sync-external-store/cjs/use-sync-external-store-shim.development.js", "webpack://storybook/../../node_modules/.pnpm/use-sync-external-store@1.5.0_react@19.1.0/node_modules/use-sync-external-store/shim/index.js", "webpack://storybook/../../packages/design-system/components/ui/avatar.tsx", "webpack://storybook/./stories/avatar.stories.tsx"], "sourcesContent": ["\"use client\";\n\n// src/avatar.tsx\nimport * as React from \"react\";\nimport { createContextScope } from \"@radix-ui/react-context\";\nimport { useCallbackRef } from \"@radix-ui/react-use-callback-ref\";\nimport { useLayoutEffect } from \"@radix-ui/react-use-layout-effect\";\nimport { Primitive } from \"@radix-ui/react-primitive\";\nimport { useIsHydrated } from \"@radix-ui/react-use-is-hydrated\";\nimport { jsx } from \"react/jsx-runtime\";\nvar AVATAR_NAME = \"Avatar\";\nvar [createAvatarContext, createAvatarScope] = createContextScope(AVATAR_NAME);\nvar [AvatarProvider, useAvatarContext] = createAvatarContext(AVATAR_NAME);\nvar Avatar = React.forwardRef(\n  (props, forwardedRef) => {\n    const { __scopeAvatar, ...avatarProps } = props;\n    const [imageLoadingStatus, setImageLoadingStatus] = React.useState(\"idle\");\n    return /* @__PURE__ */ jsx(\n      AvatarProvider,\n      {\n        scope: __scopeAvatar,\n        imageLoadingStatus,\n        onImageLoadingStatusChange: setImageLoadingStatus,\n        children: /* @__PURE__ */ jsx(Primitive.span, { ...avatarProps, ref: forwardedRef })\n      }\n    );\n  }\n);\nAvatar.displayName = AVATAR_NAME;\nvar IMAGE_NAME = \"AvatarImage\";\nvar AvatarImage = React.forwardRef(\n  (props, forwardedRef) => {\n    const { __scopeAvatar, src, onLoadingStatusChange = () => {\n    }, ...imageProps } = props;\n    const context = useAvatarContext(IMAGE_NAME, __scopeAvatar);\n    const imageLoadingStatus = useImageLoadingStatus(src, imageProps);\n    const handleLoadingStatusChange = useCallbackRef((status) => {\n      onLoadingStatusChange(status);\n      context.onImageLoadingStatusChange(status);\n    });\n    useLayoutEffect(() => {\n      if (imageLoadingStatus !== \"idle\") {\n        handleLoadingStatusChange(imageLoadingStatus);\n      }\n    }, [imageLoadingStatus, handleLoadingStatusChange]);\n    return imageLoadingStatus === \"loaded\" ? /* @__PURE__ */ jsx(Primitive.img, { ...imageProps, ref: forwardedRef, src }) : null;\n  }\n);\nAvatarImage.displayName = IMAGE_NAME;\nvar FALLBACK_NAME = \"AvatarFallback\";\nvar AvatarFallback = React.forwardRef(\n  (props, forwardedRef) => {\n    const { __scopeAvatar, delayMs, ...fallbackProps } = props;\n    const context = useAvatarContext(FALLBACK_NAME, __scopeAvatar);\n    const [canRender, setCanRender] = React.useState(delayMs === void 0);\n    React.useEffect(() => {\n      if (delayMs !== void 0) {\n        const timerId = window.setTimeout(() => setCanRender(true), delayMs);\n        return () => window.clearTimeout(timerId);\n      }\n    }, [delayMs]);\n    return canRender && context.imageLoadingStatus !== \"loaded\" ? /* @__PURE__ */ jsx(Primitive.span, { ...fallbackProps, ref: forwardedRef }) : null;\n  }\n);\nAvatarFallback.displayName = FALLBACK_NAME;\nfunction resolveLoadingStatus(image, src) {\n  if (!image) {\n    return \"idle\";\n  }\n  if (!src) {\n    return \"error\";\n  }\n  if (image.src !== src) {\n    image.src = src;\n  }\n  return image.complete && image.naturalWidth > 0 ? \"loaded\" : \"loading\";\n}\nfunction useImageLoadingStatus(src, { referrerPolicy, crossOrigin }) {\n  const isHydrated = useIsHydrated();\n  const imageRef = React.useRef(null);\n  const image = (() => {\n    if (!isHydrated) return null;\n    if (!imageRef.current) {\n      imageRef.current = new window.Image();\n    }\n    return imageRef.current;\n  })();\n  const [loadingStatus, setLoadingStatus] = React.useState(\n    () => resolveLoadingStatus(image, src)\n  );\n  useLayoutEffect(() => {\n    setLoadingStatus(resolveLoadingStatus(image, src));\n  }, [image, src]);\n  useLayoutEffect(() => {\n    const updateStatus = (status) => () => {\n      setLoadingStatus(status);\n    };\n    if (!image) return;\n    const handleLoad = updateStatus(\"loaded\");\n    const handleError = updateStatus(\"error\");\n    image.addEventListener(\"load\", handleLoad);\n    image.addEventListener(\"error\", handleError);\n    if (referrerPolicy) {\n      image.referrerPolicy = referrerPolicy;\n    }\n    if (typeof crossOrigin === \"string\") {\n      image.crossOrigin = crossOrigin;\n    }\n    return () => {\n      image.removeEventListener(\"load\", handleLoad);\n      image.removeEventListener(\"error\", handleError);\n    };\n  }, [image, crossOrigin, referrerPolicy]);\n  return loadingStatus;\n}\nvar Root = Avatar;\nvar Image = AvatarImage;\nvar Fallback = AvatarFallback;\nexport {\n  Avatar,\n  AvatarFallback,\n  AvatarImage,\n  Fallback,\n  Image,\n  Root,\n  createAvatarScope\n};\n//# sourceMappingURL=index.mjs.map\n", "// src/use-is-hydrated.tsx\nimport { useSyncExternalStore } from \"use-sync-external-store/shim\";\nfunction useIsHydrated() {\n  return useSyncExternalStore(\n    subscribe,\n    () => true,\n    () => false\n  );\n}\nfunction subscribe() {\n  return () => {\n  };\n}\nexport {\n  useIsHydrated\n};\n//# sourceMappingURL=index.mjs.map\n", "/**\n * @license React\n * use-sync-external-store-shim.development.js\n *\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\n\"use strict\";\n\"production\" !== process.env.NODE_ENV &&\n  (function () {\n    function is(x, y) {\n      return (x === y && (0 !== x || 1 / x === 1 / y)) || (x !== x && y !== y);\n    }\n    function useSyncExternalStore$2(subscribe, getSnapshot) {\n      didWarnOld18Alpha ||\n        void 0 === React.startTransition ||\n        ((didWarnOld18Alpha = !0),\n        console.error(\n          \"You are using an outdated, pre-release alpha of React 18 that does not support useSyncExternalStore. The use-sync-external-store shim will not work correctly. Upgrade to a newer pre-release.\"\n        ));\n      var value = getSnapshot();\n      if (!didWarnUncachedGetSnapshot) {\n        var cachedValue = getSnapshot();\n        objectIs(value, cachedValue) ||\n          (console.error(\n            \"The result of getSnapshot should be cached to avoid an infinite loop\"\n          ),\n          (didWarnUncachedGetSnapshot = !0));\n      }\n      cachedValue = useState({\n        inst: { value: value, getSnapshot: getSnapshot }\n      });\n      var inst = cachedValue[0].inst,\n        forceUpdate = cachedValue[1];\n      useLayoutEffect(\n        function () {\n          inst.value = value;\n          inst.getSnapshot = getSnapshot;\n          checkIfSnapshotChanged(inst) && forceUpdate({ inst: inst });\n        },\n        [subscribe, value, getSnapshot]\n      );\n      useEffect(\n        function () {\n          checkIfSnapshotChanged(inst) && forceUpdate({ inst: inst });\n          return subscribe(function () {\n            checkIfSnapshotChanged(inst) && forceUpdate({ inst: inst });\n          });\n        },\n        [subscribe]\n      );\n      useDebugValue(value);\n      return value;\n    }\n    function checkIfSnapshotChanged(inst) {\n      var latestGetSnapshot = inst.getSnapshot;\n      inst = inst.value;\n      try {\n        var nextValue = latestGetSnapshot();\n        return !objectIs(inst, nextValue);\n      } catch (error) {\n        return !0;\n      }\n    }\n    function useSyncExternalStore$1(subscribe, getSnapshot) {\n      return getSnapshot();\n    }\n    \"undefined\" !== typeof __REACT_DEVTOOLS_GLOBAL_HOOK__ &&\n      \"function\" ===\n        typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStart &&\n      __REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStart(Error());\n    var React = require(\"react\"),\n      objectIs = \"function\" === typeof Object.is ? Object.is : is,\n      useState = React.useState,\n      useEffect = React.useEffect,\n      useLayoutEffect = React.useLayoutEffect,\n      useDebugValue = React.useDebugValue,\n      didWarnOld18Alpha = !1,\n      didWarnUncachedGetSnapshot = !1,\n      shim =\n        \"undefined\" === typeof window ||\n        \"undefined\" === typeof window.document ||\n        \"undefined\" === typeof window.document.createElement\n          ? useSyncExternalStore$1\n          : useSyncExternalStore$2;\n    exports.useSyncExternalStore =\n      void 0 !== React.useSyncExternalStore ? React.useSyncExternalStore : shim;\n    \"undefined\" !== typeof __REACT_DEVTOOLS_GLOBAL_HOOK__ &&\n      \"function\" ===\n        typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStop &&\n      __REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStop(Error());\n  })();\n", "'use strict';\n\nif (process.env.NODE_ENV === 'production') {\n  module.exports = require('../cjs/use-sync-external-store-shim.production.js');\n} else {\n  module.exports = require('../cjs/use-sync-external-store-shim.development.js');\n}\n", "\"use client\"\n\nimport * as React from \"react\"\nimport * as AvatarPrimitive from \"@radix-ui/react-avatar\"\n\nimport { cn } from \"@repo/design-system/lib/utils\"\n\nfunction Avatar({\n  className,\n  ...props\n}: React.ComponentProps<typeof AvatarPrimitive.Root>) {\n  return (\n    <AvatarPrimitive.Root\n      data-slot=\"avatar\"\n      className={cn(\n        \"relative flex size-8 shrink-0 overflow-hidden rounded-full\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction AvatarImage({\n  className,\n  ...props\n}: React.ComponentProps<typeof AvatarPrimitive.Image>) {\n  return (\n    <AvatarPrimitive.Image\n      data-slot=\"avatar-image\"\n      className={cn(\"aspect-square size-full\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction AvatarFallback({\n  className,\n  ...props\n}: React.ComponentProps<typeof AvatarPrimitive.Fallback>) {\n  return (\n    <AvatarPrimitive.Fallback\n      data-slot=\"avatar-fallback\"\n      className={cn(\n        \"bg-muted flex size-full items-center justify-center rounded-full\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Avatar, AvatarImage, AvatarFallback }\n", "import type { <PERSON><PERSON>, StoryObj } from '@storybook/react';\n\nimport {\n  Avatar,\n  AvatarFallback,\n  AvatarImage,\n} from '@repo/design-system/components/ui/avatar';\n\n/**\n * An image element with a fallback for representing the user.\n */\nconst meta = {\n  title: 'ui/Avatar',\n  component: Avatar,\n  tags: ['autodocs'],\n  argTypes: {},\n  render: (args) => (\n    <Avatar {...args}>\n      <AvatarImage src=\"https://github.com/shadcn.png\" />\n      <AvatarFallback>CN</AvatarFallback>\n    </Avatar>\n  ),\n  parameters: {\n    layout: 'centered',\n  },\n} satisfies Meta<typeof Avatar>;\n\nexport default meta;\n\ntype Story = StoryObj<typeof meta>;\n\n/**\n * The default form of the avatar.\n */\nexport const Default: Story = {};\n"], "names": [], "sourceRoot": ""}