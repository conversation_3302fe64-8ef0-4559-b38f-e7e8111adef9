"use strict";
(self["webpackChunkstorybook"] = self["webpackChunkstorybook"] || []).push([["chart-stories"],{

/***/ "../../packages/design-system/components/ui/chart.tsx":
/*!************************************************************!*\
  !*** ../../packages/design-system/components/ui/chart.tsx ***!
  \************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   ChartContainer: () => (/* binding */ ChartContainer),
/* harmony export */   ChartLegend: () => (/* binding */ ChartLegend),
/* harmony export */   ChartLegendContent: () => (/* binding */ ChartLegendContent),
/* harmony export */   ChartStyle: () => (/* binding */ ChartStyle),
/* harmony export */   ChartTooltip: () => (/* binding */ ChartTooltip),
/* harmony export */   ChartTooltipContent: () => (/* binding */ ChartTooltipContent)
/* harmony export */ });
/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ "../../node_modules/.pnpm/next@15.3.2_@babel+core@7.2_09efc9edc30971f6ad8d5c21a0ab8f1f/node_modules/next/dist/compiled/react/jsx-dev-runtime.js");
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ "../../node_modules/.pnpm/next@15.3.2_@babel+core@7.2_09efc9edc30971f6ad8d5c21a0ab8f1f/node_modules/next/dist/compiled/react/index.js");
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);
/* harmony import */ var recharts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! recharts */ "../../node_modules/.pnpm/recharts@2.15.3_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/recharts/es6/component/ResponsiveContainer.js");
/* harmony import */ var recharts__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! recharts */ "../../node_modules/.pnpm/recharts@2.15.3_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/recharts/es6/component/Tooltip.js");
/* harmony import */ var recharts__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! recharts */ "../../node_modules/.pnpm/recharts@2.15.3_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/recharts/es6/component/Legend.js");
/* harmony import */ var _repo_design_system_lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @repo/design-system/lib/utils */ "../../packages/design-system/lib/utils.ts");
/* provided dependency */ var __react_refresh_utils__ = __webpack_require__(/*! ../../node_modules/.pnpm/@pmmmwh+react-refresh-webpa_ef086d3919e106717f56f099979b20b9/node_modules/@pmmmwh/react-refresh-webpack-plugin/lib/runtime/RefreshUtils.js */ "../../node_modules/.pnpm/@pmmmwh+react-refresh-webpa_ef086d3919e106717f56f099979b20b9/node_modules/@pmmmwh/react-refresh-webpack-plugin/lib/runtime/RefreshUtils.js");
__webpack_require__.$Refresh$.runtime = __webpack_require__(/*! ../../node_modules/.pnpm/react-refresh@0.14.2/node_modules/react-refresh/runtime.js */ "../../node_modules/.pnpm/react-refresh@0.14.2/node_modules/react-refresh/runtime.js");


var _s = __webpack_require__.$Refresh$.signature(), _s1 = __webpack_require__.$Refresh$.signature(), _s2 = __webpack_require__.$Refresh$.signature(), _s3 = __webpack_require__.$Refresh$.signature();
"use client";



// Format: { THEME_NAME: CSS_SELECTOR }
const THEMES = {
    light: "",
    dark: ".dark"
};
const ChartContext = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.createContext(null);
function useChart() {
    _s();
    const context = react__WEBPACK_IMPORTED_MODULE_1__.useContext(ChartContext);
    if (!context) {
        throw new Error("useChart must be used within a <ChartContainer />");
    }
    return context;
}
_s(useChart, "b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=");
function ChartContainer({ id, className, children, config, ...props }) {
    _s1();
    const uniqueId = react__WEBPACK_IMPORTED_MODULE_1__.useId();
    const chartId = `chart-${id || uniqueId.replace(/:/g, "")}`;
    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ChartContext.Provider, {
        value: {
            config
        },
        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)("div", {
            "data-slot": "chart",
            "data-chart": chartId,
            className: (0,_repo_design_system_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)("[&_.recharts-cartesian-axis-tick_text]:fill-muted-foreground [&_.recharts-cartesian-grid_line[stroke='#ccc']]:stroke-border/50 [&_.recharts-curve.recharts-tooltip-cursor]:stroke-border [&_.recharts-polar-grid_[stroke='#ccc']]:stroke-border [&_.recharts-radial-bar-background-sector]:fill-muted [&_.recharts-rectangle.recharts-tooltip-cursor]:fill-muted [&_.recharts-reference-line_[stroke='#ccc']]:stroke-border flex aspect-video justify-center text-xs [&_.recharts-dot[stroke='#fff']]:stroke-transparent [&_.recharts-layer]:outline-hidden [&_.recharts-sector]:outline-hidden [&_.recharts-sector[stroke='#fff']]:stroke-transparent [&_.recharts-surface]:outline-hidden", className),
            ...props,
            children: [
                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ChartStyle, {
                    id: chartId,
                    config: config
                }, void 0, false, {
                    fileName: "C:\\Users\\<USER>\\Documents\\Cubentweb\\cubent\\packages\\design-system\\components\\ui\\chart.tsx",
                    lineNumber: 63,
                    columnNumber: 9
                }, this),
                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(recharts__WEBPACK_IMPORTED_MODULE_3__.ResponsiveContainer, {
                    children: children
                }, void 0, false, {
                    fileName: "C:\\Users\\<USER>\\Documents\\Cubentweb\\cubent\\packages\\design-system\\components\\ui\\chart.tsx",
                    lineNumber: 64,
                    columnNumber: 9
                }, this)
            ]
        }, void 0, true, {
            fileName: "C:\\Users\\<USER>\\Documents\\Cubentweb\\cubent\\packages\\design-system\\components\\ui\\chart.tsx",
            lineNumber: 54,
            columnNumber: 7
        }, this)
    }, void 0, false, {
        fileName: "C:\\Users\\<USER>\\Documents\\Cubentweb\\cubent\\packages\\design-system\\components\\ui\\chart.tsx",
        lineNumber: 53,
        columnNumber: 5
    }, this);
}
_s1(ChartContainer, "j7NPILheLIfrWAvm8S/GM4Sml/8=");
_c = ChartContainer;
const ChartStyle = ({ id, config })=>{
    const colorConfig = Object.entries(config).filter(([, config])=>config.theme || config.color);
    if (!colorConfig.length) {
        return null;
    }
    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)("style", {
        dangerouslySetInnerHTML: {
            __html: Object.entries(THEMES).map(([theme, prefix])=>`
${prefix} [data-chart=${id}] {
${colorConfig.map(([key, itemConfig])=>{
                    var _itemConfig_theme;
                    const color = ((_itemConfig_theme = itemConfig.theme) === null || _itemConfig_theme === void 0 ? void 0 : _itemConfig_theme[theme]) || itemConfig.color;
                    return color ? `  --color-${key}: ${color};` : null;
                }).join("\n")}
}
`).join("\n")
        }
    }, void 0, false, {
        fileName: "C:\\Users\\<USER>\\Documents\\Cubentweb\\cubent\\packages\\design-system\\components\\ui\\chart.tsx",
        lineNumber: 82,
        columnNumber: 5
    }, undefined);
};
_c1 = ChartStyle;
const ChartTooltip = recharts__WEBPACK_IMPORTED_MODULE_4__.Tooltip;
function ChartTooltipContent({ active, payload, className, indicator = "dot", hideLabel = false, hideIndicator = false, label, labelFormatter, labelClassName, formatter, color, nameKey, labelKey }) {
    _s2();
    const { config } = useChart();
    const tooltipLabel = react__WEBPACK_IMPORTED_MODULE_1__.useMemo({
        "ChartTooltipContent.useMemo[tooltipLabel]": ()=>{
            var _config_label;
            if (hideLabel || !(payload === null || payload === void 0 ? void 0 : payload.length)) {
                return null;
            }
            const [item] = payload;
            const key = `${labelKey || (item === null || item === void 0 ? void 0 : item.dataKey) || (item === null || item === void 0 ? void 0 : item.name) || "value"}`;
            const itemConfig = getPayloadConfigFromPayload(config, item, key);
            const value = !labelKey && typeof label === "string" ? ((_config_label = config[label]) === null || _config_label === void 0 ? void 0 : _config_label.label) || label : itemConfig === null || itemConfig === void 0 ? void 0 : itemConfig.label;
            if (labelFormatter) {
                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)("div", {
                    className: (0,_repo_design_system_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)("font-medium", labelClassName),
                    children: labelFormatter(value, payload)
                }, void 0, false, {
                    fileName: "C:\\Users\\<USER>\\Documents\\Cubentweb\\cubent\\packages\\design-system\\components\\ui\\chart.tsx",
                    lineNumber: 146,
                    columnNumber: 9
                }, this);
            }
            if (!value) {
                return null;
            }
            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)("div", {
                className: (0,_repo_design_system_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)("font-medium", labelClassName),
                children: value
            }, void 0, false, {
                fileName: "C:\\Users\\<USER>\\Documents\\Cubentweb\\cubent\\packages\\design-system\\components\\ui\\chart.tsx",
                lineNumber: 156,
                columnNumber: 12
            }, this);
        }
    }["ChartTooltipContent.useMemo[tooltipLabel]"], [
        label,
        labelFormatter,
        payload,
        hideLabel,
        labelClassName,
        config,
        labelKey
    ]);
    if (!active || !(payload === null || payload === void 0 ? void 0 : payload.length)) {
        return null;
    }
    const nestLabel = payload.length === 1 && indicator !== "dot";
    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)("div", {
        className: (0,_repo_design_system_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)("border-border/50 bg-background grid min-w-[8rem] items-start gap-1.5 rounded-lg border px-2.5 py-1.5 text-xs shadow-xl", className),
        children: [
            !nestLabel ? tooltipLabel : null,
            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)("div", {
                className: "grid gap-1.5",
                children: payload.map((item, index)=>{
                    const key = `${nameKey || item.name || item.dataKey || "value"}`;
                    const itemConfig = getPayloadConfigFromPayload(config, item, key);
                    const indicatorColor = color || item.payload.fill || item.color;
                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)("div", {
                        className: (0,_repo_design_system_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)("[&>svg]:text-muted-foreground flex w-full flex-wrap items-stretch gap-2 [&>svg]:h-2.5 [&>svg]:w-2.5", indicator === "dot" && "items-center"),
                        children: formatter && (item === null || item === void 0 ? void 0 : item.value) !== undefined && item.name ? formatter(item.value, item.name, item, index, item.payload) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {
                            children: [
                                (itemConfig === null || itemConfig === void 0 ? void 0 : itemConfig.icon) ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(itemConfig.icon, {}, void 0, false, {
                                    fileName: "C:\\Users\\<USER>\\Documents\\Cubentweb\\cubent\\packages\\design-system\\components\\ui\\chart.tsx",
                                    lineNumber: 200,
                                    columnNumber: 21
                                }, this) : !hideIndicator && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)("div", {
                                    className: (0,_repo_design_system_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)("shrink-0 rounded-[2px] border-(--color-border) bg-(--color-bg)", {
                                        "h-2.5 w-2.5": indicator === "dot",
                                        "w-1": indicator === "line",
                                        "w-0 border-[1.5px] border-dashed bg-transparent": indicator === "dashed",
                                        "my-0.5": nestLabel && indicator === "dashed"
                                    }),
                                    style: {
                                        "--color-bg": indicatorColor,
                                        "--color-border": indicatorColor
                                    }
                                }, void 0, false, {
                                    fileName: "C:\\Users\\<USER>\\Documents\\Cubentweb\\cubent\\packages\\design-system\\components\\ui\\chart.tsx",
                                    lineNumber: 203,
                                    columnNumber: 23
                                }, this),
                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)("div", {
                                    className: (0,_repo_design_system_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)("flex flex-1 justify-between leading-none", nestLabel ? "items-end" : "items-center"),
                                    children: [
                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)("div", {
                                            className: "grid gap-1.5",
                                            children: [
                                                nestLabel ? tooltipLabel : null,
                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)("span", {
                                                    className: "text-muted-foreground",
                                                    children: (itemConfig === null || itemConfig === void 0 ? void 0 : itemConfig.label) || item.name
                                                }, void 0, false, {
                                                    fileName: "C:\\Users\\<USER>\\Documents\\Cubentweb\\cubent\\packages\\design-system\\components\\ui\\chart.tsx",
                                                    lineNumber: 231,
                                                    columnNumber: 23
                                                }, this)
                                            ]
                                        }, void 0, true, {
                                            fileName: "C:\\Users\\<USER>\\Documents\\Cubentweb\\cubent\\packages\\design-system\\components\\ui\\chart.tsx",
                                            lineNumber: 229,
                                            columnNumber: 21
                                        }, this),
                                        item.value && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)("span", {
                                            className: "text-foreground font-mono font-medium tabular-nums",
                                            children: item.value.toLocaleString()
                                        }, void 0, false, {
                                            fileName: "C:\\Users\\<USER>\\Documents\\Cubentweb\\cubent\\packages\\design-system\\components\\ui\\chart.tsx",
                                            lineNumber: 236,
                                            columnNumber: 23
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "C:\\Users\\<USER>\\Documents\\Cubentweb\\cubent\\packages\\design-system\\components\\ui\\chart.tsx",
                                    lineNumber: 223,
                                    columnNumber: 19
                                }, this)
                            ]
                        }, void 0, true)
                    }, item.dataKey, false, {
                        fileName: "C:\\Users\\<USER>\\Documents\\Cubentweb\\cubent\\packages\\design-system\\components\\ui\\chart.tsx",
                        lineNumber: 188,
                        columnNumber: 13
                    }, this);
                })
            }, void 0, false, {
                fileName: "C:\\Users\\<USER>\\Documents\\Cubentweb\\cubent\\packages\\design-system\\components\\ui\\chart.tsx",
                lineNumber: 181,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true, {
        fileName: "C:\\Users\\<USER>\\Documents\\Cubentweb\\cubent\\packages\\design-system\\components\\ui\\chart.tsx",
        lineNumber: 174,
        columnNumber: 5
    }, this);
}
_s2(ChartTooltipContent, "nRMgiGinpZEd+NE7/dAtqF0Z2iA=", false, function() {
    return [
        useChart
    ];
});
_c2 = ChartTooltipContent;
const ChartLegend = recharts__WEBPACK_IMPORTED_MODULE_5__.Legend;
function ChartLegendContent({ className, hideIcon = false, payload, verticalAlign = "bottom", nameKey }) {
    _s3();
    const { config } = useChart();
    if (!(payload === null || payload === void 0 ? void 0 : payload.length)) {
        return null;
    }
    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)("div", {
        className: (0,_repo_design_system_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)("flex items-center justify-center gap-4", verticalAlign === "top" ? "pb-3" : "pt-3", className),
        children: payload.map((item)=>{
            const key = `${nameKey || item.dataKey || "value"}`;
            const itemConfig = getPayloadConfigFromPayload(config, item, key);
            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)("div", {
                className: (0,_repo_design_system_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)("[&>svg]:text-muted-foreground flex items-center gap-1.5 [&>svg]:h-3 [&>svg]:w-3"),
                children: [
                    (itemConfig === null || itemConfig === void 0 ? void 0 : itemConfig.icon) && !hideIcon ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(itemConfig.icon, {}, void 0, false, {
                        fileName: "C:\\Users\\<USER>\\Documents\\Cubentweb\\cubent\\packages\\design-system\\components\\ui\\chart.tsx",
                        lineNumber: 290,
                        columnNumber: 15
                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)("div", {
                        className: "h-2 w-2 shrink-0 rounded-[2px]",
                        style: {
                            backgroundColor: item.color
                        }
                    }, void 0, false, {
                        fileName: "C:\\Users\\<USER>\\Documents\\Cubentweb\\cubent\\packages\\design-system\\components\\ui\\chart.tsx",
                        lineNumber: 292,
                        columnNumber: 15
                    }, this),
                    itemConfig === null || itemConfig === void 0 ? void 0 : itemConfig.label
                ]
            }, item.value, true, {
                fileName: "C:\\Users\\<USER>\\Documents\\Cubentweb\\cubent\\packages\\design-system\\components\\ui\\chart.tsx",
                lineNumber: 283,
                columnNumber: 11
            }, this);
        })
    }, void 0, false, {
        fileName: "C:\\Users\\<USER>\\Documents\\Cubentweb\\cubent\\packages\\design-system\\components\\ui\\chart.tsx",
        lineNumber: 271,
        columnNumber: 5
    }, this);
}
_s3(ChartLegendContent, "qnidj+dVqj1Euuv2nRBc6D+LeAA=", false, function() {
    return [
        useChart
    ];
});
_c3 = ChartLegendContent;
// Helper to extract item config from a payload.
function getPayloadConfigFromPayload(config, payload, key) {
    if (typeof payload !== "object" || payload === null) {
        return undefined;
    }
    const payloadPayload = "payload" in payload && typeof payload.payload === "object" && payload.payload !== null ? payload.payload : undefined;
    let configLabelKey = key;
    if (key in payload && typeof payload[key] === "string") {
        configLabelKey = payload[key];
    } else if (payloadPayload && key in payloadPayload && typeof payloadPayload[key] === "string") {
        configLabelKey = payloadPayload[key];
    }
    return configLabelKey in config ? config[configLabelKey] : config[key];
}

ChartContainer.__docgenInfo = {
    "description": "",
    "methods": [],
    "displayName": "ChartContainer",
    "props": {
        "config": {
            "required": true,
            "tsType": {
                "name": "signature",
                "type": "object",
                "raw": "{\n  [k in string]: {\n    label?: React.ReactNode\n    icon?: React.ComponentType\n  } & (\n    | { color?: string; theme?: never }\n    | { color?: never; theme: Record<keyof typeof THEMES, string> }\n  )\n}",
                "signature": {
                    "properties": [
                        {
                            "key": {
                                "name": "string",
                                "required": true
                            },
                            "value": {
                                "name": "intersection",
                                "raw": "{\n  label?: React.ReactNode\n  icon?: React.ComponentType\n} & (\n  | { color?: string; theme?: never }\n  | { color?: never; theme: Record<keyof typeof THEMES, string> }\n)",
                                "elements": [
                                    {
                                        "name": "signature",
                                        "type": "object",
                                        "raw": "{\n  label?: React.ReactNode\n  icon?: React.ComponentType\n}",
                                        "signature": {
                                            "properties": [
                                                {
                                                    "key": "label",
                                                    "value": {
                                                        "name": "ReactReactNode",
                                                        "raw": "React.ReactNode",
                                                        "required": false
                                                    }
                                                },
                                                {
                                                    "key": "icon",
                                                    "value": {
                                                        "name": "ReactComponentType",
                                                        "raw": "React.ComponentType",
                                                        "required": false
                                                    }
                                                }
                                            ]
                                        }
                                    },
                                    {
                                        "name": "unknown"
                                    }
                                ]
                            }
                        }
                    ]
                }
            },
            "description": ""
        },
        "children": {
            "required": true,
            "tsType": {
                "name": "ReactComponentProps[\"children\"]",
                "raw": "React.ComponentProps<\n  typeof RechartsPrimitive.ResponsiveContainer\n>[\"children\"]"
            },
            "description": ""
        }
    }
};
ChartTooltipContent.__docgenInfo = {
    "description": "",
    "methods": [],
    "displayName": "ChartTooltipContent",
    "props": {
        "hideLabel": {
            "required": false,
            "tsType": {
                "name": "boolean"
            },
            "description": "",
            "defaultValue": {
                "value": "false",
                "computed": false
            }
        },
        "hideIndicator": {
            "required": false,
            "tsType": {
                "name": "boolean"
            },
            "description": "",
            "defaultValue": {
                "value": "false",
                "computed": false
            }
        },
        "indicator": {
            "required": false,
            "tsType": {
                "name": "union",
                "raw": "\"line\" | \"dot\" | \"dashed\"",
                "elements": [
                    {
                        "name": "literal",
                        "value": "\"line\""
                    },
                    {
                        "name": "literal",
                        "value": "\"dot\""
                    },
                    {
                        "name": "literal",
                        "value": "\"dashed\""
                    }
                ]
            },
            "description": "",
            "defaultValue": {
                "value": "\"dot\"",
                "computed": false
            }
        },
        "nameKey": {
            "required": false,
            "tsType": {
                "name": "string"
            },
            "description": ""
        },
        "labelKey": {
            "required": false,
            "tsType": {
                "name": "string"
            },
            "description": ""
        }
    }
};
ChartLegendContent.__docgenInfo = {
    "description": "",
    "methods": [],
    "displayName": "ChartLegendContent",
    "props": {
        "hideIcon": {
            "required": false,
            "tsType": {
                "name": "boolean"
            },
            "description": "",
            "defaultValue": {
                "value": "false",
                "computed": false
            }
        },
        "nameKey": {
            "required": false,
            "tsType": {
                "name": "string"
            },
            "description": ""
        },
        "verticalAlign": {
            "defaultValue": {
                "value": "\"bottom\"",
                "computed": false
            },
            "required": false
        }
    }
};
ChartStyle.__docgenInfo = {
    "description": "",
    "methods": [],
    "displayName": "ChartStyle",
    "props": {
        "id": {
            "required": true,
            "tsType": {
                "name": "string"
            },
            "description": ""
        },
        "config": {
            "required": true,
            "tsType": {
                "name": "signature",
                "type": "object",
                "raw": "{\n  [k in string]: {\n    label?: React.ReactNode\n    icon?: React.ComponentType\n  } & (\n    | { color?: string; theme?: never }\n    | { color?: never; theme: Record<keyof typeof THEMES, string> }\n  )\n}",
                "signature": {
                    "properties": [
                        {
                            "key": {
                                "name": "string",
                                "required": true
                            },
                            "value": {
                                "name": "intersection",
                                "raw": "{\n  label?: React.ReactNode\n  icon?: React.ComponentType\n} & (\n  | { color?: string; theme?: never }\n  | { color?: never; theme: Record<keyof typeof THEMES, string> }\n)",
                                "elements": [
                                    {
                                        "name": "signature",
                                        "type": "object",
                                        "raw": "{\n  label?: React.ReactNode\n  icon?: React.ComponentType\n}",
                                        "signature": {
                                            "properties": [
                                                {
                                                    "key": "label",
                                                    "value": {
                                                        "name": "ReactReactNode",
                                                        "raw": "React.ReactNode",
                                                        "required": false
                                                    }
                                                },
                                                {
                                                    "key": "icon",
                                                    "value": {
                                                        "name": "ReactComponentType",
                                                        "raw": "React.ComponentType",
                                                        "required": false
                                                    }
                                                }
                                            ]
                                        }
                                    },
                                    {
                                        "name": "unknown"
                                    }
                                ]
                            }
                        }
                    ]
                }
            },
            "description": ""
        }
    }
};
var _c, _c1, _c2, _c3;
__webpack_require__.$Refresh$.register(_c, "ChartContainer");
__webpack_require__.$Refresh$.register(_c1, "ChartStyle");
__webpack_require__.$Refresh$.register(_c2, "ChartTooltipContent");
__webpack_require__.$Refresh$.register(_c3, "ChartLegendContent");


const $ReactRefreshModuleId$ = __webpack_require__.$Refresh$.moduleId;
const $ReactRefreshCurrentExports$ = __react_refresh_utils__.getModuleExports(
	$ReactRefreshModuleId$
);

function $ReactRefreshModuleRuntime$(exports) {
	if (true) {
		let errorOverlay;
		if (true) {
			errorOverlay = false;
		}
		let testMode;
		if (typeof __react_refresh_test__ !== 'undefined') {
			testMode = __react_refresh_test__;
		}
		return __react_refresh_utils__.executeRuntime(
			exports,
			$ReactRefreshModuleId$,
			module.hot,
			errorOverlay,
			testMode
		);
	}
}

if (typeof Promise !== 'undefined' && $ReactRefreshCurrentExports$ instanceof Promise) {
	$ReactRefreshCurrentExports$.then($ReactRefreshModuleRuntime$);
} else {
	$ReactRefreshModuleRuntime$($ReactRefreshCurrentExports$);
}

/***/ }),

/***/ "./stories/chart.stories.tsx":
/*!***********************************!*\
  !*** ./stories/chart.stories.tsx ***!
  \***********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   DoughnutChart: () => (/* binding */ DoughnutChart),
/* harmony export */   MultiLineChart: () => (/* binding */ MultiLineChart),
/* harmony export */   StackedAreaChart: () => (/* binding */ StackedAreaChart),
/* harmony export */   StackedBarChart: () => (/* binding */ StackedBarChart),
/* harmony export */   __namedExportsOrder: () => (/* binding */ __namedExportsOrder),
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ "../../node_modules/.pnpm/next@15.3.2_@babel+core@7.2_09efc9edc30971f6ad8d5c21a0ab8f1f/node_modules/next/dist/compiled/react/jsx-dev-runtime.js");
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ "../../node_modules/.pnpm/next@15.3.2_@babel+core@7.2_09efc9edc30971f6ad8d5c21a0ab8f1f/node_modules/next/dist/compiled/react/index.js");
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);
/* harmony import */ var _barrel_optimize_names_Area_AreaChart_Bar_BarChart_CartesianGrid_Label_Line_LineChart_Pie_PieChart_XAxis_recharts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Area,AreaChart,Bar,BarChart,CartesianGrid,Label,Line,LineChart,Pie,PieChart,XAxis!=!recharts */ "../../node_modules/.pnpm/recharts@2.15.3_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/recharts/es6/chart/AreaChart.js");
/* harmony import */ var _barrel_optimize_names_Area_AreaChart_Bar_BarChart_CartesianGrid_Label_Line_LineChart_Pie_PieChart_XAxis_recharts__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Area,AreaChart,Bar,BarChart,CartesianGrid,Label,Line,LineChart,Pie,PieChart,XAxis!=!recharts */ "../../node_modules/.pnpm/recharts@2.15.3_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/recharts/es6/cartesian/CartesianGrid.js");
/* harmony import */ var _barrel_optimize_names_Area_AreaChart_Bar_BarChart_CartesianGrid_Label_Line_LineChart_Pie_PieChart_XAxis_recharts__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Area,AreaChart,Bar,BarChart,CartesianGrid,Label,Line,LineChart,Pie,PieChart,XAxis!=!recharts */ "../../node_modules/.pnpm/recharts@2.15.3_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/recharts/es6/cartesian/XAxis.js");
/* harmony import */ var _barrel_optimize_names_Area_AreaChart_Bar_BarChart_CartesianGrid_Label_Line_LineChart_Pie_PieChart_XAxis_recharts__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Area,AreaChart,Bar,BarChart,CartesianGrid,Label,Line,LineChart,Pie,PieChart,XAxis!=!recharts */ "../../node_modules/.pnpm/recharts@2.15.3_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/recharts/es6/cartesian/Area.js");
/* harmony import */ var _barrel_optimize_names_Area_AreaChart_Bar_BarChart_CartesianGrid_Label_Line_LineChart_Pie_PieChart_XAxis_recharts__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Area,AreaChart,Bar,BarChart,CartesianGrid,Label,Line,LineChart,Pie,PieChart,XAxis!=!recharts */ "../../node_modules/.pnpm/recharts@2.15.3_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/recharts/es6/chart/BarChart.js");
/* harmony import */ var _barrel_optimize_names_Area_AreaChart_Bar_BarChart_CartesianGrid_Label_Line_LineChart_Pie_PieChart_XAxis_recharts__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Area,AreaChart,Bar,BarChart,CartesianGrid,Label,Line,LineChart,Pie,PieChart,XAxis!=!recharts */ "../../node_modules/.pnpm/recharts@2.15.3_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/recharts/es6/cartesian/Bar.js");
/* harmony import */ var _barrel_optimize_names_Area_AreaChart_Bar_BarChart_CartesianGrid_Label_Line_LineChart_Pie_PieChart_XAxis_recharts__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Area,AreaChart,Bar,BarChart,CartesianGrid,Label,Line,LineChart,Pie,PieChart,XAxis!=!recharts */ "../../node_modules/.pnpm/recharts@2.15.3_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/recharts/es6/chart/LineChart.js");
/* harmony import */ var _barrel_optimize_names_Area_AreaChart_Bar_BarChart_CartesianGrid_Label_Line_LineChart_Pie_PieChart_XAxis_recharts__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Area,AreaChart,Bar,BarChart,CartesianGrid,Label,Line,LineChart,Pie,PieChart,XAxis!=!recharts */ "../../node_modules/.pnpm/recharts@2.15.3_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/recharts/es6/cartesian/Line.js");
/* harmony import */ var _barrel_optimize_names_Area_AreaChart_Bar_BarChart_CartesianGrid_Label_Line_LineChart_Pie_PieChart_XAxis_recharts__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Area,AreaChart,Bar,BarChart,CartesianGrid,Label,Line,LineChart,Pie,PieChart,XAxis!=!recharts */ "../../node_modules/.pnpm/recharts@2.15.3_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/recharts/es6/chart/PieChart.js");
/* harmony import */ var _barrel_optimize_names_Area_AreaChart_Bar_BarChart_CartesianGrid_Label_Line_LineChart_Pie_PieChart_XAxis_recharts__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Area,AreaChart,Bar,BarChart,CartesianGrid,Label,Line,LineChart,Pie,PieChart,XAxis!=!recharts */ "../../node_modules/.pnpm/recharts@2.15.3_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/recharts/es6/polar/Pie.js");
/* harmony import */ var _barrel_optimize_names_Area_AreaChart_Bar_BarChart_CartesianGrid_Label_Line_LineChart_Pie_PieChart_XAxis_recharts__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Area,AreaChart,Bar,BarChart,CartesianGrid,Label,Line,LineChart,Pie,PieChart,XAxis!=!recharts */ "../../node_modules/.pnpm/recharts@2.15.3_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/recharts/es6/component/Label.js");
/* harmony import */ var _repo_design_system_components_ui_chart__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @repo/design-system/components/ui/chart */ "../../packages/design-system/components/ui/chart.tsx");
/* provided dependency */ var __react_refresh_utils__ = __webpack_require__(/*! ../../node_modules/.pnpm/@pmmmwh+react-refresh-webpa_ef086d3919e106717f56f099979b20b9/node_modules/@pmmmwh/react-refresh-webpack-plugin/lib/runtime/RefreshUtils.js */ "../../node_modules/.pnpm/@pmmmwh+react-refresh-webpa_ef086d3919e106717f56f099979b20b9/node_modules/@pmmmwh/react-refresh-webpack-plugin/lib/runtime/RefreshUtils.js");
__webpack_require__.$Refresh$.runtime = __webpack_require__(/*! ../../node_modules/.pnpm/react-refresh@0.14.2/node_modules/react-refresh/runtime.js */ "../../node_modules/.pnpm/react-refresh@0.14.2/node_modules/react-refresh/runtime.js");


var _s = __webpack_require__.$Refresh$.signature();



const multiSeriesData = [{
  month: 'January',
  desktop: 186,
  mobile: 80
}, {
  month: 'February',
  desktop: 305,
  mobile: 200
}, {
  month: 'March',
  desktop: 237,
  mobile: 120
}, {
  month: 'April',
  desktop: 73,
  mobile: 190
}, {
  month: 'May',
  desktop: 209,
  mobile: 130
}, {
  month: 'June',
  desktop: 214,
  mobile: 140
}];
const multiSeriesConfig = {
  desktop: {
    label: 'Desktop',
    color: 'hsl(var(--chart-1))'
  },
  mobile: {
    label: 'Mobile',
    color: 'hsl(var(--chart-2))'
  }
};
const singleSeriesData = [{
  browser: 'chrome',
  visitors: 275,
  fill: 'var(--color-chrome)'
}, {
  browser: 'safari',
  visitors: 200,
  fill: 'var(--color-safari)'
}, {
  browser: 'other',
  visitors: 190,
  fill: 'var(--color-other)'
}];
const singleSeriesConfig = {
  visitors: {
    label: 'Visitors'
  },
  chrome: {
    label: 'Chrome',
    color: 'hsl(var(--chart-1))'
  },
  safari: {
    label: 'Safari',
    color: 'hsl(var(--chart-2))'
  },
  other: {
    label: 'Other',
    color: 'hsl(var(--chart-5))'
  }
};
/**
 * Beautiful charts. Built using Recharts. Copy and paste into your apps.
 */
const meta = {
  title: 'ui/Chart',
  component: _repo_design_system_components_ui_chart__WEBPACK_IMPORTED_MODULE_2__.ChartContainer,
  tags: ['autodocs'],
  argTypes: {},
  args: {
    children: /*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)("div", {}, void 0, false, {
      fileName: "C:\\Users\\<USER>\\Documents\\Cubentweb\\cubent\\apps\\storybook\\stories\\chart.stories.tsx",
      lineNumber: 77,
      columnNumber: 15
    }, undefined)
  },
  parameters: {
    docs: {
      description: {
        component: "Beautiful charts. Built using Recharts. Copy and paste into your apps."
      }
    }
  }
};
/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (meta);
/**
 * Combine multiple Area components to create a stacked area chart.
 */
const StackedAreaChart = {
  args: {
    config: multiSeriesConfig
  },
  render: args => /*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_repo_design_system_components_ui_chart__WEBPACK_IMPORTED_MODULE_2__.ChartContainer, {
    ...args,
    children: /*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Area_AreaChart_Bar_BarChart_CartesianGrid_Label_Line_LineChart_Pie_PieChart_XAxis_recharts__WEBPACK_IMPORTED_MODULE_3__.AreaChart, {
      accessibilityLayer: true,
      data: multiSeriesData,
      margin: {
        left: 12,
        right: 12
      },
      children: [/*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Area_AreaChart_Bar_BarChart_CartesianGrid_Label_Line_LineChart_Pie_PieChart_XAxis_recharts__WEBPACK_IMPORTED_MODULE_4__.CartesianGrid, {
        vertical: false
      }, void 0, false, {
        fileName: "C:\\Users\\<USER>\\Documents\\Cubentweb\\cubent\\apps\\storybook\\stories\\chart.stories.tsx",
        lineNumber: 102,
        columnNumber: 9
      }, undefined), /*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Area_AreaChart_Bar_BarChart_CartesianGrid_Label_Line_LineChart_Pie_PieChart_XAxis_recharts__WEBPACK_IMPORTED_MODULE_5__.XAxis, {
        dataKey: "month",
        tickLine: false,
        axisLine: false,
        tickMargin: 8,
        tickFormatter: value => value.slice(0, 3)
      }, void 0, false, {
        fileName: "C:\\Users\\<USER>\\Documents\\Cubentweb\\cubent\\apps\\storybook\\stories\\chart.stories.tsx",
        lineNumber: 103,
        columnNumber: 9
      }, undefined), /*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_repo_design_system_components_ui_chart__WEBPACK_IMPORTED_MODULE_2__.ChartTooltip, {
        cursor: false,
        content: /*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_repo_design_system_components_ui_chart__WEBPACK_IMPORTED_MODULE_2__.ChartTooltipContent, {
          indicator: "dot"
        }, void 0, false, {
          fileName: "C:\\Users\\<USER>\\Documents\\Cubentweb\\cubent\\apps\\storybook\\stories\\chart.stories.tsx",
          lineNumber: 112,
          columnNumber: 20
        }, void 0)
      }, void 0, false, {
        fileName: "C:\\Users\\<USER>\\Documents\\Cubentweb\\cubent\\apps\\storybook\\stories\\chart.stories.tsx",
        lineNumber: 110,
        columnNumber: 9
      }, undefined), /*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Area_AreaChart_Bar_BarChart_CartesianGrid_Label_Line_LineChart_Pie_PieChart_XAxis_recharts__WEBPACK_IMPORTED_MODULE_6__.Area, {
        dataKey: "mobile",
        type: "natural",
        fill: "var(--color-mobile)",
        fillOpacity: 0.4,
        stroke: "var(--color-mobile)",
        stackId: "a"
      }, void 0, false, {
        fileName: "C:\\Users\\<USER>\\Documents\\Cubentweb\\cubent\\apps\\storybook\\stories\\chart.stories.tsx",
        lineNumber: 114,
        columnNumber: 9
      }, undefined), /*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Area_AreaChart_Bar_BarChart_CartesianGrid_Label_Line_LineChart_Pie_PieChart_XAxis_recharts__WEBPACK_IMPORTED_MODULE_6__.Area, {
        dataKey: "desktop",
        type: "natural",
        fill: "var(--color-desktop)",
        fillOpacity: 0.4,
        stroke: "var(--color-desktop)",
        stackId: "a"
      }, void 0, false, {
        fileName: "C:\\Users\\<USER>\\Documents\\Cubentweb\\cubent\\apps\\storybook\\stories\\chart.stories.tsx",
        lineNumber: 122,
        columnNumber: 9
      }, undefined)]
    }, void 0, true, {
      fileName: "C:\\Users\\<USER>\\Documents\\Cubentweb\\cubent\\apps\\storybook\\stories\\chart.stories.tsx",
      lineNumber: 94,
      columnNumber: 7
    }, undefined)
  }, void 0, false, {
    fileName: "C:\\Users\\<USER>\\Documents\\Cubentweb\\cubent\\apps\\storybook\\stories\\chart.stories.tsx",
    lineNumber: 93,
    columnNumber: 5
  }, undefined)
};
/**
 * Combine multiple Bar components to create a stacked bar chart.
 */
const StackedBarChart = {
  args: {
    config: multiSeriesConfig
  },
  render: args => /*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_repo_design_system_components_ui_chart__WEBPACK_IMPORTED_MODULE_2__.ChartContainer, {
    ...args,
    children: /*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Area_AreaChart_Bar_BarChart_CartesianGrid_Label_Line_LineChart_Pie_PieChart_XAxis_recharts__WEBPACK_IMPORTED_MODULE_7__.BarChart, {
      accessibilityLayer: true,
      data: multiSeriesData,
      children: [/*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Area_AreaChart_Bar_BarChart_CartesianGrid_Label_Line_LineChart_Pie_PieChart_XAxis_recharts__WEBPACK_IMPORTED_MODULE_4__.CartesianGrid, {
        vertical: false
      }, void 0, false, {
        fileName: "C:\\Users\\<USER>\\Documents\\Cubentweb\\cubent\\apps\\storybook\\stories\\chart.stories.tsx",
        lineNumber: 145,
        columnNumber: 9
      }, undefined), /*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Area_AreaChart_Bar_BarChart_CartesianGrid_Label_Line_LineChart_Pie_PieChart_XAxis_recharts__WEBPACK_IMPORTED_MODULE_5__.XAxis, {
        dataKey: "month",
        tickLine: false,
        tickMargin: 10,
        axisLine: false,
        tickFormatter: value => value.slice(0, 3)
      }, void 0, false, {
        fileName: "C:\\Users\\<USER>\\Documents\\Cubentweb\\cubent\\apps\\storybook\\stories\\chart.stories.tsx",
        lineNumber: 146,
        columnNumber: 9
      }, undefined), /*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_repo_design_system_components_ui_chart__WEBPACK_IMPORTED_MODULE_2__.ChartTooltip, {
        cursor: false,
        content: /*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_repo_design_system_components_ui_chart__WEBPACK_IMPORTED_MODULE_2__.ChartTooltipContent, {
          indicator: "dashed"
        }, void 0, false, {
          fileName: "C:\\Users\\<USER>\\Documents\\Cubentweb\\cubent\\apps\\storybook\\stories\\chart.stories.tsx",
          lineNumber: 155,
          columnNumber: 20
        }, void 0)
      }, void 0, false, {
        fileName: "C:\\Users\\<USER>\\Documents\\Cubentweb\\cubent\\apps\\storybook\\stories\\chart.stories.tsx",
        lineNumber: 153,
        columnNumber: 9
      }, undefined), /*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Area_AreaChart_Bar_BarChart_CartesianGrid_Label_Line_LineChart_Pie_PieChart_XAxis_recharts__WEBPACK_IMPORTED_MODULE_8__.Bar, {
        dataKey: "desktop",
        fill: "var(--color-desktop)",
        radius: 4
      }, void 0, false, {
        fileName: "C:\\Users\\<USER>\\Documents\\Cubentweb\\cubent\\apps\\storybook\\stories\\chart.stories.tsx",
        lineNumber: 157,
        columnNumber: 9
      }, undefined), /*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Area_AreaChart_Bar_BarChart_CartesianGrid_Label_Line_LineChart_Pie_PieChart_XAxis_recharts__WEBPACK_IMPORTED_MODULE_8__.Bar, {
        dataKey: "mobile",
        fill: "var(--color-mobile)",
        radius: 4
      }, void 0, false, {
        fileName: "C:\\Users\\<USER>\\Documents\\Cubentweb\\cubent\\apps\\storybook\\stories\\chart.stories.tsx",
        lineNumber: 158,
        columnNumber: 9
      }, undefined)]
    }, void 0, true, {
      fileName: "C:\\Users\\<USER>\\Documents\\Cubentweb\\cubent\\apps\\storybook\\stories\\chart.stories.tsx",
      lineNumber: 144,
      columnNumber: 7
    }, undefined)
  }, void 0, false, {
    fileName: "C:\\Users\\<USER>\\Documents\\Cubentweb\\cubent\\apps\\storybook\\stories\\chart.stories.tsx",
    lineNumber: 143,
    columnNumber: 5
  }, undefined)
};
/**
 * Combine multiple Line components to create a single line chart.
 */
const MultiLineChart = {
  args: {
    config: multiSeriesConfig
  },
  render: args => /*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_repo_design_system_components_ui_chart__WEBPACK_IMPORTED_MODULE_2__.ChartContainer, {
    ...args,
    children: /*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Area_AreaChart_Bar_BarChart_CartesianGrid_Label_Line_LineChart_Pie_PieChart_XAxis_recharts__WEBPACK_IMPORTED_MODULE_9__.LineChart, {
      accessibilityLayer: true,
      data: multiSeriesData,
      margin: {
        left: 12,
        right: 12
      },
      children: [/*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Area_AreaChart_Bar_BarChart_CartesianGrid_Label_Line_LineChart_Pie_PieChart_XAxis_recharts__WEBPACK_IMPORTED_MODULE_4__.CartesianGrid, {
        vertical: false
      }, void 0, false, {
        fileName: "C:\\Users\\<USER>\\Documents\\Cubentweb\\cubent\\apps\\storybook\\stories\\chart.stories.tsx",
        lineNumber: 181,
        columnNumber: 9
      }, undefined), /*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Area_AreaChart_Bar_BarChart_CartesianGrid_Label_Line_LineChart_Pie_PieChart_XAxis_recharts__WEBPACK_IMPORTED_MODULE_5__.XAxis, {
        dataKey: "month",
        tickLine: false,
        axisLine: false,
        tickMargin: 8,
        tickFormatter: value => value.slice(0, 3)
      }, void 0, false, {
        fileName: "C:\\Users\\<USER>\\Documents\\Cubentweb\\cubent\\apps\\storybook\\stories\\chart.stories.tsx",
        lineNumber: 182,
        columnNumber: 9
      }, undefined), /*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_repo_design_system_components_ui_chart__WEBPACK_IMPORTED_MODULE_2__.ChartTooltip, {
        cursor: false,
        content: /*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_repo_design_system_components_ui_chart__WEBPACK_IMPORTED_MODULE_2__.ChartTooltipContent, {
          hideLabel: true
        }, void 0, false, {
          fileName: "C:\\Users\\<USER>\\Documents\\Cubentweb\\cubent\\apps\\storybook\\stories\\chart.stories.tsx",
          lineNumber: 191,
          columnNumber: 20
        }, void 0)
      }, void 0, false, {
        fileName: "C:\\Users\\<USER>\\Documents\\Cubentweb\\cubent\\apps\\storybook\\stories\\chart.stories.tsx",
        lineNumber: 189,
        columnNumber: 9
      }, undefined), /*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Area_AreaChart_Bar_BarChart_CartesianGrid_Label_Line_LineChart_Pie_PieChart_XAxis_recharts__WEBPACK_IMPORTED_MODULE_10__.Line, {
        dataKey: "desktop",
        type: "natural",
        stroke: "var(--color-desktop)",
        strokeWidth: 2,
        dot: false
      }, void 0, false, {
        fileName: "C:\\Users\\<USER>\\Documents\\Cubentweb\\cubent\\apps\\storybook\\stories\\chart.stories.tsx",
        lineNumber: 193,
        columnNumber: 9
      }, undefined), /*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Area_AreaChart_Bar_BarChart_CartesianGrid_Label_Line_LineChart_Pie_PieChart_XAxis_recharts__WEBPACK_IMPORTED_MODULE_10__.Line, {
        dataKey: "mobile",
        type: "natural",
        stroke: "var(--color-mobile)",
        strokeWidth: 2,
        dot: false
      }, void 0, false, {
        fileName: "C:\\Users\\<USER>\\Documents\\Cubentweb\\cubent\\apps\\storybook\\stories\\chart.stories.tsx",
        lineNumber: 200,
        columnNumber: 9
      }, undefined)]
    }, void 0, true, {
      fileName: "C:\\Users\\<USER>\\Documents\\Cubentweb\\cubent\\apps\\storybook\\stories\\chart.stories.tsx",
      lineNumber: 173,
      columnNumber: 7
    }, undefined)
  }, void 0, false, {
    fileName: "C:\\Users\\<USER>\\Documents\\Cubentweb\\cubent\\apps\\storybook\\stories\\chart.stories.tsx",
    lineNumber: 172,
    columnNumber: 5
  }, undefined)
};
/**
 * Combine Pie and Label components to create a doughnut chart.
 */
const DoughnutChart = {
  args: {
    config: singleSeriesConfig
  },
  render: _s(args => {
    _s();
    const totalVisitors = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)({
      "useMemo[totalVisitors]": () => {
        return singleSeriesData.reduce({
          "useMemo[totalVisitors]": (acc, curr) => acc + curr.visitors
        }["useMemo[totalVisitors]"], 0);
      }
    }["useMemo[totalVisitors]"], []);
    return /*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_repo_design_system_components_ui_chart__WEBPACK_IMPORTED_MODULE_2__.ChartContainer, {
      ...args,
      children: /*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Area_AreaChart_Bar_BarChart_CartesianGrid_Label_Line_LineChart_Pie_PieChart_XAxis_recharts__WEBPACK_IMPORTED_MODULE_11__.PieChart, {
        children: [/*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_repo_design_system_components_ui_chart__WEBPACK_IMPORTED_MODULE_2__.ChartTooltip, {
          cursor: false,
          content: /*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_repo_design_system_components_ui_chart__WEBPACK_IMPORTED_MODULE_2__.ChartTooltipContent, {
            hideLabel: true
          }, void 0, false, {
            fileName: "C:\\Users\\<USER>\\Documents\\Cubentweb\\cubent\\apps\\storybook\\stories\\chart.stories.tsx",
            lineNumber: 228,
            columnNumber: 22
          }, void 0)
        }, void 0, false, {
          fileName: "C:\\Users\\<USER>\\Documents\\Cubentweb\\cubent\\apps\\storybook\\stories\\chart.stories.tsx",
          lineNumber: 226,
          columnNumber: 11
        }, undefined), /*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Area_AreaChart_Bar_BarChart_CartesianGrid_Label_Line_LineChart_Pie_PieChart_XAxis_recharts__WEBPACK_IMPORTED_MODULE_12__.Pie, {
          data: singleSeriesData,
          dataKey: "visitors",
          nameKey: "browser",
          innerRadius: 48,
          strokeWidth: 5,
          children: /*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Area_AreaChart_Bar_BarChart_CartesianGrid_Label_Line_LineChart_Pie_PieChart_XAxis_recharts__WEBPACK_IMPORTED_MODULE_13__.Label, {
            content: ({
              viewBox
            }) => {
              if (viewBox && 'cx' in viewBox && 'cy' in viewBox) {
                return /*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)("text", {
                  x: viewBox.cx,
                  y: viewBox.cy,
                  textAnchor: "middle",
                  dominantBaseline: "middle",
                  children: [/*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)("tspan", {
                    x: viewBox.cx,
                    y: viewBox.cy,
                    className: "fill-foreground font-bold text-3xl",
                    children: totalVisitors.toLocaleString()
                  }, void 0, false, {
                    fileName: "C:\\Users\\<USER>\\Documents\\Cubentweb\\cubent\\apps\\storybook\\stories\\chart.stories.tsx",
                    lineNumber: 247,
                    columnNumber: 23
                  }, void 0), /*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)("tspan", {
                    x: viewBox.cx,
                    y: (viewBox.cy || 0) + 24,
                    className: "fill-muted-foreground",
                    children: "Visitors"
                  }, void 0, false, {
                    fileName: "C:\\Users\\<USER>\\Documents\\Cubentweb\\cubent\\apps\\storybook\\stories\\chart.stories.tsx",
                    lineNumber: 254,
                    columnNumber: 23
                  }, void 0)]
                }, void 0, true, {
                  fileName: "C:\\Users\\<USER>\\Documents\\Cubentweb\\cubent\\apps\\storybook\\stories\\chart.stories.tsx",
                  lineNumber: 241,
                  columnNumber: 21
                }, void 0);
              }
            }
          }, void 0, false, {
            fileName: "C:\\Users\\<USER>\\Documents\\Cubentweb\\cubent\\apps\\storybook\\stories\\chart.stories.tsx",
            lineNumber: 237,
            columnNumber: 13
          }, undefined)
        }, void 0, false, {
          fileName: "C:\\Users\\<USER>\\Documents\\Cubentweb\\cubent\\apps\\storybook\\stories\\chart.stories.tsx",
          lineNumber: 230,
          columnNumber: 11
        }, undefined)]
      }, void 0, true, {
        fileName: "C:\\Users\\<USER>\\Documents\\Cubentweb\\cubent\\apps\\storybook\\stories\\chart.stories.tsx",
        lineNumber: 225,
        columnNumber: 9
      }, undefined)
    }, void 0, false, {
      fileName: "C:\\Users\\<USER>\\Documents\\Cubentweb\\cubent\\apps\\storybook\\stories\\chart.stories.tsx",
      lineNumber: 224,
      columnNumber: 7
    }, undefined);
  }, "+8HHGFnS1nhBMhCx7G+5SOliEx4=")
};
;
const __namedExportsOrder = ["StackedAreaChart", "StackedBarChart", "MultiLineChart", "DoughnutChart"];
StackedAreaChart.parameters = {
  ...StackedAreaChart.parameters,
  docs: {
    ...StackedAreaChart.parameters?.docs,
    source: {
      originalSource: "{\n  args: {\n    config: multiSeriesConfig\n  },\n  render: args => <ChartContainer {...args}>\r\n      <AreaChart accessibilityLayer data={multiSeriesData} margin={{\n      left: 12,\n      right: 12\n    }}>\r\n        <CartesianGrid vertical={false} />\r\n        <XAxis dataKey=\"month\" tickLine={false} axisLine={false} tickMargin={8} tickFormatter={value => value.slice(0, 3)} />\r\n        <ChartTooltip cursor={false} content={<ChartTooltipContent indicator=\"dot\" />} />\r\n        <Area dataKey=\"mobile\" type=\"natural\" fill=\"var(--color-mobile)\" fillOpacity={0.4} stroke=\"var(--color-mobile)\" stackId=\"a\" />\r\n        <Area dataKey=\"desktop\" type=\"natural\" fill=\"var(--color-desktop)\" fillOpacity={0.4} stroke=\"var(--color-desktop)\" stackId=\"a\" />\r\n      </AreaChart>\r\n    </ChartContainer>\n}",
      ...StackedAreaChart.parameters?.docs?.source
    },
    description: {
      story: "Combine multiple Area components to create a stacked area chart.",
      ...StackedAreaChart.parameters?.docs?.description
    }
  }
};
StackedBarChart.parameters = {
  ...StackedBarChart.parameters,
  docs: {
    ...StackedBarChart.parameters?.docs,
    source: {
      originalSource: "{\n  args: {\n    config: multiSeriesConfig\n  },\n  render: args => <ChartContainer {...args}>\r\n      <BarChart accessibilityLayer data={multiSeriesData}>\r\n        <CartesianGrid vertical={false} />\r\n        <XAxis dataKey=\"month\" tickLine={false} tickMargin={10} axisLine={false} tickFormatter={value => value.slice(0, 3)} />\r\n        <ChartTooltip cursor={false} content={<ChartTooltipContent indicator=\"dashed\" />} />\r\n        <Bar dataKey=\"desktop\" fill=\"var(--color-desktop)\" radius={4} />\r\n        <Bar dataKey=\"mobile\" fill=\"var(--color-mobile)\" radius={4} />\r\n      </BarChart>\r\n    </ChartContainer>\n}",
      ...StackedBarChart.parameters?.docs?.source
    },
    description: {
      story: "Combine multiple Bar components to create a stacked bar chart.",
      ...StackedBarChart.parameters?.docs?.description
    }
  }
};
MultiLineChart.parameters = {
  ...MultiLineChart.parameters,
  docs: {
    ...MultiLineChart.parameters?.docs,
    source: {
      originalSource: "{\n  args: {\n    config: multiSeriesConfig\n  },\n  render: args => <ChartContainer {...args}>\r\n      <LineChart accessibilityLayer data={multiSeriesData} margin={{\n      left: 12,\n      right: 12\n    }}>\r\n        <CartesianGrid vertical={false} />\r\n        <XAxis dataKey=\"month\" tickLine={false} axisLine={false} tickMargin={8} tickFormatter={value => value.slice(0, 3)} />\r\n        <ChartTooltip cursor={false} content={<ChartTooltipContent hideLabel />} />\r\n        <Line dataKey=\"desktop\" type=\"natural\" stroke=\"var(--color-desktop)\" strokeWidth={2} dot={false} />\r\n        <Line dataKey=\"mobile\" type=\"natural\" stroke=\"var(--color-mobile)\" strokeWidth={2} dot={false} />\r\n      </LineChart>\r\n    </ChartContainer>\n}",
      ...MultiLineChart.parameters?.docs?.source
    },
    description: {
      story: "Combine multiple Line components to create a single line chart.",
      ...MultiLineChart.parameters?.docs?.description
    }
  }
};
DoughnutChart.parameters = {
  ...DoughnutChart.parameters,
  docs: {
    ...DoughnutChart.parameters?.docs,
    source: {
      originalSource: "{\n  args: {\n    config: singleSeriesConfig\n  },\n  render: args => {\n    const totalVisitors = useMemo(() => {\n      return singleSeriesData.reduce((acc, curr) => acc + curr.visitors, 0);\n    }, []);\n    return <ChartContainer {...args}>\r\n        <PieChart>\r\n          <ChartTooltip cursor={false} content={<ChartTooltipContent hideLabel />} />\r\n          <Pie data={singleSeriesData} dataKey=\"visitors\" nameKey=\"browser\" innerRadius={48} strokeWidth={5}>\r\n            <Label content={({\n            viewBox\n          }) => {\n            if (viewBox && 'cx' in viewBox && 'cy' in viewBox) {\n              return <text x={viewBox.cx} y={viewBox.cy} textAnchor=\"middle\" dominantBaseline=\"middle\">\r\n                      <tspan x={viewBox.cx} y={viewBox.cy} className=\"fill-foreground font-bold text-3xl\">\r\n                        {totalVisitors.toLocaleString()}\r\n                      </tspan>\r\n                      <tspan x={viewBox.cx} y={(viewBox.cy || 0) + 24} className=\"fill-muted-foreground\">\r\n                        Visitors\r\n                      </tspan>\r\n                    </text>;\n            }\n          }} />\r\n          </Pie>\r\n        </PieChart>\r\n      </ChartContainer>;\n  }\n}",
      ...DoughnutChart.parameters?.docs?.source
    },
    description: {
      story: "Combine Pie and Label components to create a doughnut chart.",
      ...DoughnutChart.parameters?.docs?.description
    }
  }
};

const $ReactRefreshModuleId$ = __webpack_require__.$Refresh$.moduleId;
const $ReactRefreshCurrentExports$ = __react_refresh_utils__.getModuleExports(
	$ReactRefreshModuleId$
);

function $ReactRefreshModuleRuntime$(exports) {
	if (true) {
		let errorOverlay;
		if (true) {
			errorOverlay = false;
		}
		let testMode;
		if (typeof __react_refresh_test__ !== 'undefined') {
			testMode = __react_refresh_test__;
		}
		return __react_refresh_utils__.executeRuntime(
			exports,
			$ReactRefreshModuleId$,
			module.hot,
			errorOverlay,
			testMode
		);
	}
}

if (typeof Promise !== 'undefined' && $ReactRefreshCurrentExports$ instanceof Promise) {
	$ReactRefreshCurrentExports$.then($ReactRefreshModuleRuntime$);
} else {
	$ReactRefreshModuleRuntime$($ReactRefreshCurrentExports$);
}

/***/ })

}]);
//# sourceMappingURL=chart-stories.iframe.bundle.js.map