{"version": 3, "file": "checkbox-stories.iframe.bundle.js", "mappings": ";;;;;;;;;;;;;;;;;;;;;;AAAA;;AAEA;AACA;AACA;AAEA;AAEA;AAIA;AAEA;AACA;AAIA;AAEA;AACA;AACA;AAEA;AAAA;;;;;;;;;;;;;;;;AAIA;AArBA;AAuBA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC7BA;AAEA;;;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;;;;;AACA;AACA;AACA;AACA;;;;;;;;;;;AAKA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AAEA;AAIA;;;AAGA;AAEA;;;AAGA;AACA;AACA;AACA;AACA;AACA", "sources": ["webpack://storybook/../../packages/design-system/components/ui/checkbox.tsx", "webpack://storybook/./stories/checkbox.stories.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as CheckboxPrimitive from \"@radix-ui/react-checkbox\"\nimport { CheckIcon } from \"lucide-react\"\n\nimport { cn } from \"@repo/design-system/lib/utils\"\n\nfunction Checkbox({\n  className,\n  ...props\n}: React.ComponentProps<typeof CheckboxPrimitive.Root>) {\n  return (\n    <CheckboxPrimitive.Root\n      data-slot=\"checkbox\"\n      className={cn(\n        \"peer border-input dark:bg-input/30 data-[state=checked]:bg-primary data-[state=checked]:text-primary-foreground dark:data-[state=checked]:bg-primary data-[state=checked]:border-primary focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive size-4 shrink-0 rounded-[4px] border shadow-xs transition-shadow outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50\",\n        className\n      )}\n      {...props}\n    >\n      <CheckboxPrimitive.Indicator\n        data-slot=\"checkbox-indicator\"\n        className=\"flex items-center justify-center text-current transition-none\"\n      >\n        <CheckIcon className=\"size-3.5\" />\n      </CheckboxPrimitive.Indicator>\n    </CheckboxPrimitive.Root>\n  )\n}\n\nexport { Checkbox }\n", "import type { <PERSON>a, StoryObj } from '@storybook/react';\n\nimport { Checkbox } from '@repo/design-system/components/ui/checkbox';\n\n/**\n * A control that allows the user to toggle between checked and not checked.\n */\nconst meta: Meta<typeof Checkbox> = {\n  title: 'ui/Checkbox',\n  component: Checkbox,\n  tags: ['autodocs'],\n  argTypes: {},\n  args: {\n    id: 'terms',\n    disabled: false,\n  },\n  render: (args) => (\n    <div className=\"flex space-x-2\">\n      <Checkbox {...args} />\n      <label\n        htmlFor={args.id}\n        className=\"font-medium text-sm leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-50\"\n      >\n        Accept terms and conditions\n      </label>\n    </div>\n  ),\n  parameters: {\n    layout: 'centered',\n  },\n} satisfies Meta<typeof Checkbox>;\n\nexport default meta;\n\ntype Story = StoryObj<typeof meta>;\n\n/**\n * The default form of the checkbox.\n */\nexport const Default: Story = {};\n\n/**\n * Use the `disabled` prop to disable the checkbox.\n */\nexport const Disabled: Story = {\n  args: {\n    id: 'disabled-terms',\n    disabled: true,\n  },\n};\n"], "names": [], "sourceRoot": ""}