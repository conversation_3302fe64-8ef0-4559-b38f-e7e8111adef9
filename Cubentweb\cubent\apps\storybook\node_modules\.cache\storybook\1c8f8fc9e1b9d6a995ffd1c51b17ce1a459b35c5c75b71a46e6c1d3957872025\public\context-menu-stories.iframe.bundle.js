"use strict";
(self["webpackChunkstorybook"] = self["webpackChunkstorybook"] || []).push([["context-menu-stories"],{

/***/ "../../packages/design-system/components/ui/context-menu.tsx":
/*!*******************************************************************!*\
  !*** ../../packages/design-system/components/ui/context-menu.tsx ***!
  \*******************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   ContextMenu: () => (/* binding */ ContextMenu),
/* harmony export */   ContextMenuCheckboxItem: () => (/* binding */ ContextMenuCheckboxItem),
/* harmony export */   ContextMenuContent: () => (/* binding */ ContextMenuContent),
/* harmony export */   ContextMenuGroup: () => (/* binding */ ContextMenuGroup),
/* harmony export */   ContextMenuItem: () => (/* binding */ ContextMenuItem),
/* harmony export */   ContextMenuLabel: () => (/* binding */ ContextMenuLabel),
/* harmony export */   ContextMenuPortal: () => (/* binding */ ContextMenuPortal),
/* harmony export */   ContextMenuRadioGroup: () => (/* binding */ ContextMenuRadioGroup),
/* harmony export */   ContextMenuRadioItem: () => (/* binding */ ContextMenuRadioItem),
/* harmony export */   ContextMenuSeparator: () => (/* binding */ ContextMenuSeparator),
/* harmony export */   ContextMenuShortcut: () => (/* binding */ ContextMenuShortcut),
/* harmony export */   ContextMenuSub: () => (/* binding */ ContextMenuSub),
/* harmony export */   ContextMenuSubContent: () => (/* binding */ ContextMenuSubContent),
/* harmony export */   ContextMenuSubTrigger: () => (/* binding */ ContextMenuSubTrigger),
/* harmony export */   ContextMenuTrigger: () => (/* binding */ ContextMenuTrigger)
/* harmony export */ });
/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ "../../node_modules/.pnpm/next@15.3.2_@babel+core@7.2_09efc9edc30971f6ad8d5c21a0ab8f1f/node_modules/next/dist/compiled/react/jsx-dev-runtime.js");
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ "../../node_modules/.pnpm/next@15.3.2_@babel+core@7.2_09efc9edc30971f6ad8d5c21a0ab8f1f/node_modules/next/dist/compiled/react/index.js");
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);
/* harmony import */ var _radix_ui_react_context_menu__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @radix-ui/react-context-menu */ "../../node_modules/.pnpm/@radix-ui+react-context-men_72c6958fcc1befd6afed1fc8a44148fd/node_modules/@radix-ui/react-context-menu/dist/index.mjs");
/* harmony import */ var _barrel_optimize_names_CheckIcon_ChevronRightIcon_CircleIcon_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=CheckIcon,ChevronRightIcon,CircleIcon!=!lucide-react */ "../../node_modules/.pnpm/lucide-react@0.511.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/chevron-right.js");
/* harmony import */ var _barrel_optimize_names_CheckIcon_ChevronRightIcon_CircleIcon_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=CheckIcon,ChevronRightIcon,CircleIcon!=!lucide-react */ "../../node_modules/.pnpm/lucide-react@0.511.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/check.js");
/* harmony import */ var _barrel_optimize_names_CheckIcon_ChevronRightIcon_CircleIcon_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=CheckIcon,ChevronRightIcon,CircleIcon!=!lucide-react */ "../../node_modules/.pnpm/lucide-react@0.511.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/circle.js");
/* harmony import */ var _repo_design_system_lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @repo/design-system/lib/utils */ "../../packages/design-system/lib/utils.ts");
/* provided dependency */ var __react_refresh_utils__ = __webpack_require__(/*! ../../node_modules/.pnpm/@pmmmwh+react-refresh-webpa_ef086d3919e106717f56f099979b20b9/node_modules/@pmmmwh/react-refresh-webpack-plugin/lib/runtime/RefreshUtils.js */ "../../node_modules/.pnpm/@pmmmwh+react-refresh-webpa_ef086d3919e106717f56f099979b20b9/node_modules/@pmmmwh/react-refresh-webpack-plugin/lib/runtime/RefreshUtils.js");
__webpack_require__.$Refresh$.runtime = __webpack_require__(/*! ../../node_modules/.pnpm/react-refresh@0.14.2/node_modules/react-refresh/runtime.js */ "../../node_modules/.pnpm/react-refresh@0.14.2/node_modules/react-refresh/runtime.js");

"use client";





function ContextMenu({ ...props }) {
    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_context_menu__WEBPACK_IMPORTED_MODULE_3__.Root, {
        "data-slot": "context-menu",
        ...props
    }, void 0, false, {
        fileName: "C:\\Users\\<USER>\\Documents\\Cubentweb\\cubent\\packages\\design-system\\components\\ui\\context-menu.tsx",
        lineNumber: 12,
        columnNumber: 10
    }, this);
}
_c = ContextMenu;
function ContextMenuTrigger({ ...props }) {
    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_context_menu__WEBPACK_IMPORTED_MODULE_3__.Trigger, {
        "data-slot": "context-menu-trigger",
        ...props
    }, void 0, false, {
        fileName: "C:\\Users\\<USER>\\Documents\\Cubentweb\\cubent\\packages\\design-system\\components\\ui\\context-menu.tsx",
        lineNumber: 19,
        columnNumber: 5
    }, this);
}
_c1 = ContextMenuTrigger;
function ContextMenuGroup({ ...props }) {
    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_context_menu__WEBPACK_IMPORTED_MODULE_3__.Group, {
        "data-slot": "context-menu-group",
        ...props
    }, void 0, false, {
        fileName: "C:\\Users\\<USER>\\Documents\\Cubentweb\\cubent\\packages\\design-system\\components\\ui\\context-menu.tsx",
        lineNumber: 27,
        columnNumber: 5
    }, this);
}
_c2 = ContextMenuGroup;
function ContextMenuPortal({ ...props }) {
    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_context_menu__WEBPACK_IMPORTED_MODULE_3__.Portal, {
        "data-slot": "context-menu-portal",
        ...props
    }, void 0, false, {
        fileName: "C:\\Users\\<USER>\\Documents\\Cubentweb\\cubent\\packages\\design-system\\components\\ui\\context-menu.tsx",
        lineNumber: 35,
        columnNumber: 5
    }, this);
}
_c3 = ContextMenuPortal;
function ContextMenuSub({ ...props }) {
    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_context_menu__WEBPACK_IMPORTED_MODULE_3__.Sub, {
        "data-slot": "context-menu-sub",
        ...props
    }, void 0, false, {
        fileName: "C:\\Users\\<USER>\\Documents\\Cubentweb\\cubent\\packages\\design-system\\components\\ui\\context-menu.tsx",
        lineNumber: 42,
        columnNumber: 10
    }, this);
}
_c4 = ContextMenuSub;
function ContextMenuRadioGroup({ ...props }) {
    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_context_menu__WEBPACK_IMPORTED_MODULE_3__.RadioGroup, {
        "data-slot": "context-menu-radio-group",
        ...props
    }, void 0, false, {
        fileName: "C:\\Users\\<USER>\\Documents\\Cubentweb\\cubent\\packages\\design-system\\components\\ui\\context-menu.tsx",
        lineNumber: 49,
        columnNumber: 5
    }, this);
}
_c5 = ContextMenuRadioGroup;
function ContextMenuSubTrigger({ className, inset, children, ...props }) {
    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_context_menu__WEBPACK_IMPORTED_MODULE_3__.SubTrigger, {
        "data-slot": "context-menu-sub-trigger",
        "data-inset": inset,
        className: (0,_repo_design_system_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)("focus:bg-accent focus:text-accent-foreground data-[state=open]:bg-accent data-[state=open]:text-accent-foreground flex cursor-default items-center rounded-sm px-2 py-1.5 text-sm outline-hidden select-none data-[inset]:pl-8 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4", className),
        ...props,
        children: [
            children,
            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckIcon_ChevronRightIcon_CircleIcon_lucide_react__WEBPACK_IMPORTED_MODULE_4__["default"], {
                className: "ml-auto"
            }, void 0, false, {
                fileName: "C:\\Users\\<USER>\\Documents\\Cubentweb\\cubent\\packages\\design-system\\components\\ui\\context-menu.tsx",
                lineNumber: 75,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true, {
        fileName: "C:\\Users\\<USER>\\Documents\\Cubentweb\\cubent\\packages\\design-system\\components\\ui\\context-menu.tsx",
        lineNumber: 65,
        columnNumber: 5
    }, this);
}
_c6 = ContextMenuSubTrigger;
function ContextMenuSubContent({ className, ...props }) {
    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_context_menu__WEBPACK_IMPORTED_MODULE_3__.SubContent, {
        "data-slot": "context-menu-sub-content",
        className: (0,_repo_design_system_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)("bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 min-w-[8rem] origin-(--radix-context-menu-content-transform-origin) overflow-hidden rounded-md border p-1 shadow-lg", className),
        ...props
    }, void 0, false, {
        fileName: "C:\\Users\\<USER>\\Documents\\Cubentweb\\cubent\\packages\\design-system\\components\\ui\\context-menu.tsx",
        lineNumber: 85,
        columnNumber: 5
    }, this);
}
_c7 = ContextMenuSubContent;
function ContextMenuContent({ className, ...props }) {
    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_context_menu__WEBPACK_IMPORTED_MODULE_3__.Portal, {
        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_context_menu__WEBPACK_IMPORTED_MODULE_3__.Content, {
            "data-slot": "context-menu-content",
            className: (0,_repo_design_system_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)("bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 max-h-(--radix-context-menu-content-available-height) min-w-[8rem] origin-(--radix-context-menu-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border p-1 shadow-md", className),
            ...props
        }, void 0, false, {
            fileName: "C:\\Users\\<USER>\\Documents\\Cubentweb\\cubent\\packages\\design-system\\components\\ui\\context-menu.tsx",
            lineNumber: 102,
            columnNumber: 7
        }, this)
    }, void 0, false, {
        fileName: "C:\\Users\\<USER>\\Documents\\Cubentweb\\cubent\\packages\\design-system\\components\\ui\\context-menu.tsx",
        lineNumber: 101,
        columnNumber: 5
    }, this);
}
_c8 = ContextMenuContent;
function ContextMenuItem({ className, inset, variant = "default", ...props }) {
    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_context_menu__WEBPACK_IMPORTED_MODULE_3__.Item, {
        "data-slot": "context-menu-item",
        "data-inset": inset,
        "data-variant": variant,
        className: (0,_repo_design_system_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)("focus:bg-accent focus:text-accent-foreground data-[variant=destructive]:text-destructive data-[variant=destructive]:focus:bg-destructive/10 dark:data-[variant=destructive]:focus:bg-destructive/20 data-[variant=destructive]:focus:text-destructive data-[variant=destructive]:*:[svg]:!text-destructive [&_svg:not([class*='text-'])]:text-muted-foreground relative flex cursor-default items-center gap-2 rounded-sm px-2 py-1.5 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 data-[inset]:pl-8 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4", className),
        ...props
    }, void 0, false, {
        fileName: "C:\\Users\\<USER>\\Documents\\Cubentweb\\cubent\\packages\\design-system\\components\\ui\\context-menu.tsx",
        lineNumber: 124,
        columnNumber: 5
    }, this);
}
_c9 = ContextMenuItem;
function ContextMenuCheckboxItem({ className, children, checked, ...props }) {
    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_context_menu__WEBPACK_IMPORTED_MODULE_3__.CheckboxItem, {
        "data-slot": "context-menu-checkbox-item",
        className: (0,_repo_design_system_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)("focus:bg-accent focus:text-accent-foreground relative flex cursor-default items-center gap-2 rounded-sm py-1.5 pr-2 pl-8 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4", className),
        checked: checked,
        ...props,
        children: [
            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)("span", {
                className: "pointer-events-none absolute left-2 flex size-3.5 items-center justify-center",
                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_context_menu__WEBPACK_IMPORTED_MODULE_3__.ItemIndicator, {
                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckIcon_ChevronRightIcon_CircleIcon_lucide_react__WEBPACK_IMPORTED_MODULE_5__["default"], {
                        className: "size-4"
                    }, void 0, false, {
                        fileName: "C:\\Users\\<USER>\\Documents\\Cubentweb\\cubent\\packages\\design-system\\components\\ui\\context-menu.tsx",
                        lineNumber: 155,
                        columnNumber: 11
                    }, this)
                }, void 0, false, {
                    fileName: "C:\\Users\\<USER>\\Documents\\Cubentweb\\cubent\\packages\\design-system\\components\\ui\\context-menu.tsx",
                    lineNumber: 154,
                    columnNumber: 9
                }, this)
            }, void 0, false, {
                fileName: "C:\\Users\\<USER>\\Documents\\Cubentweb\\cubent\\packages\\design-system\\components\\ui\\context-menu.tsx",
                lineNumber: 153,
                columnNumber: 7
            }, this),
            children
        ]
    }, void 0, true, {
        fileName: "C:\\Users\\<USER>\\Documents\\Cubentweb\\cubent\\packages\\design-system\\components\\ui\\context-menu.tsx",
        lineNumber: 144,
        columnNumber: 5
    }, this);
}
_c10 = ContextMenuCheckboxItem;
function ContextMenuRadioItem({ className, children, ...props }) {
    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_context_menu__WEBPACK_IMPORTED_MODULE_3__.RadioItem, {
        "data-slot": "context-menu-radio-item",
        className: (0,_repo_design_system_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)("focus:bg-accent focus:text-accent-foreground relative flex cursor-default items-center gap-2 rounded-sm py-1.5 pr-2 pl-8 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4", className),
        ...props,
        children: [
            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)("span", {
                className: "pointer-events-none absolute left-2 flex size-3.5 items-center justify-center",
                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_context_menu__WEBPACK_IMPORTED_MODULE_3__.ItemIndicator, {
                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckIcon_ChevronRightIcon_CircleIcon_lucide_react__WEBPACK_IMPORTED_MODULE_6__["default"], {
                        className: "size-2 fill-current"
                    }, void 0, false, {
                        fileName: "C:\\Users\\<USER>\\Documents\\Cubentweb\\cubent\\packages\\design-system\\components\\ui\\context-menu.tsx",
                        lineNumber: 179,
                        columnNumber: 11
                    }, this)
                }, void 0, false, {
                    fileName: "C:\\Users\\<USER>\\Documents\\Cubentweb\\cubent\\packages\\design-system\\components\\ui\\context-menu.tsx",
                    lineNumber: 178,
                    columnNumber: 9
                }, this)
            }, void 0, false, {
                fileName: "C:\\Users\\<USER>\\Documents\\Cubentweb\\cubent\\packages\\design-system\\components\\ui\\context-menu.tsx",
                lineNumber: 177,
                columnNumber: 7
            }, this),
            children
        ]
    }, void 0, true, {
        fileName: "C:\\Users\\<USER>\\Documents\\Cubentweb\\cubent\\packages\\design-system\\components\\ui\\context-menu.tsx",
        lineNumber: 169,
        columnNumber: 5
    }, this);
}
_c11 = ContextMenuRadioItem;
function ContextMenuLabel({ className, inset, ...props }) {
    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_context_menu__WEBPACK_IMPORTED_MODULE_3__.Label, {
        "data-slot": "context-menu-label",
        "data-inset": inset,
        className: (0,_repo_design_system_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)("text-foreground px-2 py-1.5 text-sm font-medium data-[inset]:pl-8", className),
        ...props
    }, void 0, false, {
        fileName: "C:\\Users\\<USER>\\Documents\\Cubentweb\\cubent\\packages\\design-system\\components\\ui\\context-menu.tsx",
        lineNumber: 195,
        columnNumber: 5
    }, this);
}
_c12 = ContextMenuLabel;
function ContextMenuSeparator({ className, ...props }) {
    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_context_menu__WEBPACK_IMPORTED_MODULE_3__.Separator, {
        "data-slot": "context-menu-separator",
        className: (0,_repo_design_system_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)("bg-border -mx-1 my-1 h-px", className),
        ...props
    }, void 0, false, {
        fileName: "C:\\Users\\<USER>\\Documents\\Cubentweb\\cubent\\packages\\design-system\\components\\ui\\context-menu.tsx",
        lineNumber: 212,
        columnNumber: 5
    }, this);
}
_c13 = ContextMenuSeparator;
function ContextMenuShortcut({ className, ...props }) {
    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)("span", {
        "data-slot": "context-menu-shortcut",
        className: (0,_repo_design_system_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)("text-muted-foreground ml-auto text-xs tracking-widest", className),
        ...props
    }, void 0, false, {
        fileName: "C:\\Users\\<USER>\\Documents\\Cubentweb\\cubent\\packages\\design-system\\components\\ui\\context-menu.tsx",
        lineNumber: 225,
        columnNumber: 5
    }, this);
}
_c14 = ContextMenuShortcut;

ContextMenu.__docgenInfo = {
    "description": "",
    "methods": [],
    "displayName": "ContextMenu"
};
ContextMenuTrigger.__docgenInfo = {
    "description": "",
    "methods": [],
    "displayName": "ContextMenuTrigger"
};
ContextMenuContent.__docgenInfo = {
    "description": "",
    "methods": [],
    "displayName": "ContextMenuContent"
};
ContextMenuItem.__docgenInfo = {
    "description": "",
    "methods": [],
    "displayName": "ContextMenuItem",
    "props": {
        "inset": {
            "required": false,
            "tsType": {
                "name": "boolean"
            },
            "description": ""
        },
        "variant": {
            "required": false,
            "tsType": {
                "name": "union",
                "raw": "\"default\" | \"destructive\"",
                "elements": [
                    {
                        "name": "literal",
                        "value": "\"default\""
                    },
                    {
                        "name": "literal",
                        "value": "\"destructive\""
                    }
                ]
            },
            "description": "",
            "defaultValue": {
                "value": "\"default\"",
                "computed": false
            }
        }
    }
};
ContextMenuCheckboxItem.__docgenInfo = {
    "description": "",
    "methods": [],
    "displayName": "ContextMenuCheckboxItem"
};
ContextMenuRadioItem.__docgenInfo = {
    "description": "",
    "methods": [],
    "displayName": "ContextMenuRadioItem"
};
ContextMenuLabel.__docgenInfo = {
    "description": "",
    "methods": [],
    "displayName": "ContextMenuLabel",
    "props": {
        "inset": {
            "required": false,
            "tsType": {
                "name": "boolean"
            },
            "description": ""
        }
    }
};
ContextMenuSeparator.__docgenInfo = {
    "description": "",
    "methods": [],
    "displayName": "ContextMenuSeparator"
};
ContextMenuShortcut.__docgenInfo = {
    "description": "",
    "methods": [],
    "displayName": "ContextMenuShortcut"
};
ContextMenuGroup.__docgenInfo = {
    "description": "",
    "methods": [],
    "displayName": "ContextMenuGroup"
};
ContextMenuPortal.__docgenInfo = {
    "description": "",
    "methods": [],
    "displayName": "ContextMenuPortal"
};
ContextMenuSub.__docgenInfo = {
    "description": "",
    "methods": [],
    "displayName": "ContextMenuSub"
};
ContextMenuSubContent.__docgenInfo = {
    "description": "",
    "methods": [],
    "displayName": "ContextMenuSubContent"
};
ContextMenuSubTrigger.__docgenInfo = {
    "description": "",
    "methods": [],
    "displayName": "ContextMenuSubTrigger",
    "props": {
        "inset": {
            "required": false,
            "tsType": {
                "name": "boolean"
            },
            "description": ""
        }
    }
};
ContextMenuRadioGroup.__docgenInfo = {
    "description": "",
    "methods": [],
    "displayName": "ContextMenuRadioGroup"
};
var _c, _c1, _c2, _c3, _c4, _c5, _c6, _c7, _c8, _c9, _c10, _c11, _c12, _c13, _c14;
__webpack_require__.$Refresh$.register(_c, "ContextMenu");
__webpack_require__.$Refresh$.register(_c1, "ContextMenuTrigger");
__webpack_require__.$Refresh$.register(_c2, "ContextMenuGroup");
__webpack_require__.$Refresh$.register(_c3, "ContextMenuPortal");
__webpack_require__.$Refresh$.register(_c4, "ContextMenuSub");
__webpack_require__.$Refresh$.register(_c5, "ContextMenuRadioGroup");
__webpack_require__.$Refresh$.register(_c6, "ContextMenuSubTrigger");
__webpack_require__.$Refresh$.register(_c7, "ContextMenuSubContent");
__webpack_require__.$Refresh$.register(_c8, "ContextMenuContent");
__webpack_require__.$Refresh$.register(_c9, "ContextMenuItem");
__webpack_require__.$Refresh$.register(_c10, "ContextMenuCheckboxItem");
__webpack_require__.$Refresh$.register(_c11, "ContextMenuRadioItem");
__webpack_require__.$Refresh$.register(_c12, "ContextMenuLabel");
__webpack_require__.$Refresh$.register(_c13, "ContextMenuSeparator");
__webpack_require__.$Refresh$.register(_c14, "ContextMenuShortcut");


const $ReactRefreshModuleId$ = __webpack_require__.$Refresh$.moduleId;
const $ReactRefreshCurrentExports$ = __react_refresh_utils__.getModuleExports(
	$ReactRefreshModuleId$
);

function $ReactRefreshModuleRuntime$(exports) {
	if (true) {
		let errorOverlay;
		if (true) {
			errorOverlay = false;
		}
		let testMode;
		if (typeof __react_refresh_test__ !== 'undefined') {
			testMode = __react_refresh_test__;
		}
		return __react_refresh_utils__.executeRuntime(
			exports,
			$ReactRefreshModuleId$,
			module.hot,
			errorOverlay,
			testMode
		);
	}
}

if (typeof Promise !== 'undefined' && $ReactRefreshCurrentExports$ instanceof Promise) {
	$ReactRefreshCurrentExports$.then($ReactRefreshModuleRuntime$);
} else {
	$ReactRefreshModuleRuntime$($ReactRefreshCurrentExports$);
}

/***/ }),

/***/ "./stories/context-menu.stories.tsx":
/*!******************************************!*\
  !*** ./stories/context-menu.stories.tsx ***!
  \******************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   Default: () => (/* binding */ Default),
/* harmony export */   WithCheckboxes: () => (/* binding */ WithCheckboxes),
/* harmony export */   WithRadioGroup: () => (/* binding */ WithRadioGroup),
/* harmony export */   WithShortcuts: () => (/* binding */ WithShortcuts),
/* harmony export */   WithSubmenu: () => (/* binding */ WithSubmenu),
/* harmony export */   __namedExportsOrder: () => (/* binding */ __namedExportsOrder),
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ "../../node_modules/.pnpm/next@15.3.2_@babel+core@7.2_09efc9edc30971f6ad8d5c21a0ab8f1f/node_modules/next/dist/compiled/react/jsx-dev-runtime.js");
/* harmony import */ var _repo_design_system_components_ui_context_menu__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @repo/design-system/components/ui/context-menu */ "../../packages/design-system/components/ui/context-menu.tsx");
/* provided dependency */ var __react_refresh_utils__ = __webpack_require__(/*! ../../node_modules/.pnpm/@pmmmwh+react-refresh-webpa_ef086d3919e106717f56f099979b20b9/node_modules/@pmmmwh/react-refresh-webpack-plugin/lib/runtime/RefreshUtils.js */ "../../node_modules/.pnpm/@pmmmwh+react-refresh-webpa_ef086d3919e106717f56f099979b20b9/node_modules/@pmmmwh/react-refresh-webpack-plugin/lib/runtime/RefreshUtils.js");
__webpack_require__.$Refresh$.runtime = __webpack_require__(/*! ../../node_modules/.pnpm/react-refresh@0.14.2/node_modules/react-refresh/runtime.js */ "../../node_modules/.pnpm/react-refresh@0.14.2/node_modules/react-refresh/runtime.js");



/**
 * Displays a menu to the user — such as a set of actions or functions —
 * triggered by a button.
 */
const meta = {
  title: 'ui/ContextMenu',
  component: _repo_design_system_components_ui_context_menu__WEBPACK_IMPORTED_MODULE_1__.ContextMenu,
  tags: ['autodocs'],
  argTypes: {},
  args: {},
  render: args => /*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_repo_design_system_components_ui_context_menu__WEBPACK_IMPORTED_MODULE_1__.ContextMenu, {
    ...args,
    children: [/*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_repo_design_system_components_ui_context_menu__WEBPACK_IMPORTED_MODULE_1__.ContextMenuTrigger, {
      className: "flex h-48 w-96 items-center justify-center rounded-md border border-dashed bg-accent text-sm",
      children: "Right click here"
    }, void 0, false, {
      fileName: "C:\\Users\\<USER>\\Documents\\Cubentweb\\cubent\\apps\\storybook\\stories\\context-menu.stories.tsx",
      lineNumber: 31,
      columnNumber: 7
    }, undefined), /*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_repo_design_system_components_ui_context_menu__WEBPACK_IMPORTED_MODULE_1__.ContextMenuContent, {
      className: "w-32",
      children: [/*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_repo_design_system_components_ui_context_menu__WEBPACK_IMPORTED_MODULE_1__.ContextMenuItem, {
        children: "Profile"
      }, void 0, false, {
        fileName: "C:\\Users\\<USER>\\Documents\\Cubentweb\\cubent\\apps\\storybook\\stories\\context-menu.stories.tsx",
        lineNumber: 35,
        columnNumber: 9
      }, undefined), /*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_repo_design_system_components_ui_context_menu__WEBPACK_IMPORTED_MODULE_1__.ContextMenuItem, {
        children: "Billing"
      }, void 0, false, {
        fileName: "C:\\Users\\<USER>\\Documents\\Cubentweb\\cubent\\apps\\storybook\\stories\\context-menu.stories.tsx",
        lineNumber: 36,
        columnNumber: 9
      }, undefined), /*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_repo_design_system_components_ui_context_menu__WEBPACK_IMPORTED_MODULE_1__.ContextMenuItem, {
        children: "Team"
      }, void 0, false, {
        fileName: "C:\\Users\\<USER>\\Documents\\Cubentweb\\cubent\\apps\\storybook\\stories\\context-menu.stories.tsx",
        lineNumber: 37,
        columnNumber: 9
      }, undefined), /*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_repo_design_system_components_ui_context_menu__WEBPACK_IMPORTED_MODULE_1__.ContextMenuItem, {
        children: "Subscription"
      }, void 0, false, {
        fileName: "C:\\Users\\<USER>\\Documents\\Cubentweb\\cubent\\apps\\storybook\\stories\\context-menu.stories.tsx",
        lineNumber: 38,
        columnNumber: 9
      }, undefined)]
    }, void 0, true, {
      fileName: "C:\\Users\\<USER>\\Documents\\Cubentweb\\cubent\\apps\\storybook\\stories\\context-menu.stories.tsx",
      lineNumber: 34,
      columnNumber: 7
    }, undefined)]
  }, void 0, true, {
    fileName: "C:\\Users\\<USER>\\Documents\\Cubentweb\\cubent\\apps\\storybook\\stories\\context-menu.stories.tsx",
    lineNumber: 30,
    columnNumber: 5
  }, undefined),
  parameters: {
    layout: 'centered',
    docs: {
      description: {
        component: "Displays a menu to the user \u2014 such as a set of actions or functions \u2014\r\ntriggered by a button."
      }
    }
  }
};
/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (meta);
/**
 * The default form of the context menu.
 */
const Default = {};
/**
 * A context menu with shortcuts.
 */
const WithShortcuts = {
  render: args => /*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_repo_design_system_components_ui_context_menu__WEBPACK_IMPORTED_MODULE_1__.ContextMenu, {
    ...args,
    children: [/*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_repo_design_system_components_ui_context_menu__WEBPACK_IMPORTED_MODULE_1__.ContextMenuTrigger, {
      className: "flex h-48 w-96 items-center justify-center rounded-md border border-dashed bg-accent text-sm",
      children: "Right click here"
    }, void 0, false, {
      fileName: "C:\\Users\\<USER>\\Documents\\Cubentweb\\cubent\\apps\\storybook\\stories\\context-menu.stories.tsx",
      lineNumber: 62,
      columnNumber: 7
    }, undefined), /*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_repo_design_system_components_ui_context_menu__WEBPACK_IMPORTED_MODULE_1__.ContextMenuContent, {
      className: "w-32",
      children: [/*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_repo_design_system_components_ui_context_menu__WEBPACK_IMPORTED_MODULE_1__.ContextMenuItem, {
        children: ["Back", /*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_repo_design_system_components_ui_context_menu__WEBPACK_IMPORTED_MODULE_1__.ContextMenuShortcut, {
          children: "⌘["
        }, void 0, false, {
          fileName: "C:\\Users\\<USER>\\Documents\\Cubentweb\\cubent\\apps\\storybook\\stories\\context-menu.stories.tsx",
          lineNumber: 68,
          columnNumber: 11
        }, undefined)]
      }, void 0, true, {
        fileName: "C:\\Users\\<USER>\\Documents\\Cubentweb\\cubent\\apps\\storybook\\stories\\context-menu.stories.tsx",
        lineNumber: 66,
        columnNumber: 9
      }, undefined), /*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_repo_design_system_components_ui_context_menu__WEBPACK_IMPORTED_MODULE_1__.ContextMenuItem, {
        disabled: true,
        children: ["Forward", /*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_repo_design_system_components_ui_context_menu__WEBPACK_IMPORTED_MODULE_1__.ContextMenuShortcut, {
          children: "⌘]"
        }, void 0, false, {
          fileName: "C:\\Users\\<USER>\\Documents\\Cubentweb\\cubent\\apps\\storybook\\stories\\context-menu.stories.tsx",
          lineNumber: 72,
          columnNumber: 11
        }, undefined)]
      }, void 0, true, {
        fileName: "C:\\Users\\<USER>\\Documents\\Cubentweb\\cubent\\apps\\storybook\\stories\\context-menu.stories.tsx",
        lineNumber: 70,
        columnNumber: 9
      }, undefined), /*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_repo_design_system_components_ui_context_menu__WEBPACK_IMPORTED_MODULE_1__.ContextMenuItem, {
        children: ["Reload", /*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_repo_design_system_components_ui_context_menu__WEBPACK_IMPORTED_MODULE_1__.ContextMenuShortcut, {
          children: "⌘R"
        }, void 0, false, {
          fileName: "C:\\Users\\<USER>\\Documents\\Cubentweb\\cubent\\apps\\storybook\\stories\\context-menu.stories.tsx",
          lineNumber: 76,
          columnNumber: 11
        }, undefined)]
      }, void 0, true, {
        fileName: "C:\\Users\\<USER>\\Documents\\Cubentweb\\cubent\\apps\\storybook\\stories\\context-menu.stories.tsx",
        lineNumber: 74,
        columnNumber: 9
      }, undefined)]
    }, void 0, true, {
      fileName: "C:\\Users\\<USER>\\Documents\\Cubentweb\\cubent\\apps\\storybook\\stories\\context-menu.stories.tsx",
      lineNumber: 65,
      columnNumber: 7
    }, undefined)]
  }, void 0, true, {
    fileName: "C:\\Users\\<USER>\\Documents\\Cubentweb\\cubent\\apps\\storybook\\stories\\context-menu.stories.tsx",
    lineNumber: 61,
    columnNumber: 5
  }, undefined)
};
/**
 * A context menu with a submenu.
 */
const WithSubmenu = {
  render: args => /*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_repo_design_system_components_ui_context_menu__WEBPACK_IMPORTED_MODULE_1__.ContextMenu, {
    ...args,
    children: [/*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_repo_design_system_components_ui_context_menu__WEBPACK_IMPORTED_MODULE_1__.ContextMenuTrigger, {
      className: "flex h-48 w-96 items-center justify-center rounded-md border border-dashed bg-accent text-sm",
      children: "Right click here"
    }, void 0, false, {
      fileName: "C:\\Users\\<USER>\\Documents\\Cubentweb\\cubent\\apps\\storybook\\stories\\context-menu.stories.tsx",
      lineNumber: 89,
      columnNumber: 7
    }, undefined), /*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_repo_design_system_components_ui_context_menu__WEBPACK_IMPORTED_MODULE_1__.ContextMenuContent, {
      className: "w-32",
      children: [/*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_repo_design_system_components_ui_context_menu__WEBPACK_IMPORTED_MODULE_1__.ContextMenuItem, {
        children: ["New Tab", /*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_repo_design_system_components_ui_context_menu__WEBPACK_IMPORTED_MODULE_1__.ContextMenuShortcut, {
          children: "⌘N"
        }, void 0, false, {
          fileName: "C:\\Users\\<USER>\\Documents\\Cubentweb\\cubent\\apps\\storybook\\stories\\context-menu.stories.tsx",
          lineNumber: 95,
          columnNumber: 11
        }, undefined)]
      }, void 0, true, {
        fileName: "C:\\Users\\<USER>\\Documents\\Cubentweb\\cubent\\apps\\storybook\\stories\\context-menu.stories.tsx",
        lineNumber: 93,
        columnNumber: 9
      }, undefined), /*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_repo_design_system_components_ui_context_menu__WEBPACK_IMPORTED_MODULE_1__.ContextMenuSub, {
        children: [/*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_repo_design_system_components_ui_context_menu__WEBPACK_IMPORTED_MODULE_1__.ContextMenuSubTrigger, {
          children: "More Tools"
        }, void 0, false, {
          fileName: "C:\\Users\\<USER>\\Documents\\Cubentweb\\cubent\\apps\\storybook\\stories\\context-menu.stories.tsx",
          lineNumber: 98,
          columnNumber: 11
        }, undefined), /*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_repo_design_system_components_ui_context_menu__WEBPACK_IMPORTED_MODULE_1__.ContextMenuSubContent, {
          children: [/*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_repo_design_system_components_ui_context_menu__WEBPACK_IMPORTED_MODULE_1__.ContextMenuItem, {
            children: ["Save Page As...", /*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_repo_design_system_components_ui_context_menu__WEBPACK_IMPORTED_MODULE_1__.ContextMenuShortcut, {
              children: "⇧⌘S"
            }, void 0, false, {
              fileName: "C:\\Users\\<USER>\\Documents\\Cubentweb\\cubent\\apps\\storybook\\stories\\context-menu.stories.tsx",
              lineNumber: 102,
              columnNumber: 15
            }, undefined)]
          }, void 0, true, {
            fileName: "C:\\Users\\<USER>\\Documents\\Cubentweb\\cubent\\apps\\storybook\\stories\\context-menu.stories.tsx",
            lineNumber: 100,
            columnNumber: 13
          }, undefined), /*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_repo_design_system_components_ui_context_menu__WEBPACK_IMPORTED_MODULE_1__.ContextMenuItem, {
            children: "Create Shortcut..."
          }, void 0, false, {
            fileName: "C:\\Users\\<USER>\\Documents\\Cubentweb\\cubent\\apps\\storybook\\stories\\context-menu.stories.tsx",
            lineNumber: 104,
            columnNumber: 13
          }, undefined), /*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_repo_design_system_components_ui_context_menu__WEBPACK_IMPORTED_MODULE_1__.ContextMenuItem, {
            children: "Name Window..."
          }, void 0, false, {
            fileName: "C:\\Users\\<USER>\\Documents\\Cubentweb\\cubent\\apps\\storybook\\stories\\context-menu.stories.tsx",
            lineNumber: 105,
            columnNumber: 13
          }, undefined), /*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_repo_design_system_components_ui_context_menu__WEBPACK_IMPORTED_MODULE_1__.ContextMenuSeparator, {}, void 0, false, {
            fileName: "C:\\Users\\<USER>\\Documents\\Cubentweb\\cubent\\apps\\storybook\\stories\\context-menu.stories.tsx",
            lineNumber: 106,
            columnNumber: 13
          }, undefined), /*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_repo_design_system_components_ui_context_menu__WEBPACK_IMPORTED_MODULE_1__.ContextMenuItem, {
            children: "Developer Tools"
          }, void 0, false, {
            fileName: "C:\\Users\\<USER>\\Documents\\Cubentweb\\cubent\\apps\\storybook\\stories\\context-menu.stories.tsx",
            lineNumber: 107,
            columnNumber: 13
          }, undefined)]
        }, void 0, true, {
          fileName: "C:\\Users\\<USER>\\Documents\\Cubentweb\\cubent\\apps\\storybook\\stories\\context-menu.stories.tsx",
          lineNumber: 99,
          columnNumber: 11
        }, undefined)]
      }, void 0, true, {
        fileName: "C:\\Users\\<USER>\\Documents\\Cubentweb\\cubent\\apps\\storybook\\stories\\context-menu.stories.tsx",
        lineNumber: 97,
        columnNumber: 9
      }, undefined)]
    }, void 0, true, {
      fileName: "C:\\Users\\<USER>\\Documents\\Cubentweb\\cubent\\apps\\storybook\\stories\\context-menu.stories.tsx",
      lineNumber: 92,
      columnNumber: 7
    }, undefined)]
  }, void 0, true, {
    fileName: "C:\\Users\\<USER>\\Documents\\Cubentweb\\cubent\\apps\\storybook\\stories\\context-menu.stories.tsx",
    lineNumber: 88,
    columnNumber: 5
  }, undefined)
};
/**
 * A context menu with checkboxes.
 */
const WithCheckboxes = {
  render: args => /*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_repo_design_system_components_ui_context_menu__WEBPACK_IMPORTED_MODULE_1__.ContextMenu, {
    ...args,
    children: [/*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_repo_design_system_components_ui_context_menu__WEBPACK_IMPORTED_MODULE_1__.ContextMenuTrigger, {
      className: "flex h-48 w-96 items-center justify-center rounded-md border border-dashed bg-accent text-sm",
      children: "Right click here"
    }, void 0, false, {
      fileName: "C:\\Users\\<USER>\\Documents\\Cubentweb\\cubent\\apps\\storybook\\stories\\context-menu.stories.tsx",
      lineNumber: 121,
      columnNumber: 7
    }, undefined), /*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_repo_design_system_components_ui_context_menu__WEBPACK_IMPORTED_MODULE_1__.ContextMenuContent, {
      className: "w-64",
      children: [/*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_repo_design_system_components_ui_context_menu__WEBPACK_IMPORTED_MODULE_1__.ContextMenuCheckboxItem, {
        checked: true,
        children: ["Show Comments", /*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_repo_design_system_components_ui_context_menu__WEBPACK_IMPORTED_MODULE_1__.ContextMenuShortcut, {
          children: "⌘⇧C"
        }, void 0, false, {
          fileName: "C:\\Users\\<USER>\\Documents\\Cubentweb\\cubent\\apps\\storybook\\stories\\context-menu.stories.tsx",
          lineNumber: 127,
          columnNumber: 11
        }, undefined)]
      }, void 0, true, {
        fileName: "C:\\Users\\<USER>\\Documents\\Cubentweb\\cubent\\apps\\storybook\\stories\\context-menu.stories.tsx",
        lineNumber: 125,
        columnNumber: 9
      }, undefined), /*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_repo_design_system_components_ui_context_menu__WEBPACK_IMPORTED_MODULE_1__.ContextMenuCheckboxItem, {
        children: "Show Preview"
      }, void 0, false, {
        fileName: "C:\\Users\\<USER>\\Documents\\Cubentweb\\cubent\\apps\\storybook\\stories\\context-menu.stories.tsx",
        lineNumber: 129,
        columnNumber: 9
      }, undefined)]
    }, void 0, true, {
      fileName: "C:\\Users\\<USER>\\Documents\\Cubentweb\\cubent\\apps\\storybook\\stories\\context-menu.stories.tsx",
      lineNumber: 124,
      columnNumber: 7
    }, undefined)]
  }, void 0, true, {
    fileName: "C:\\Users\\<USER>\\Documents\\Cubentweb\\cubent\\apps\\storybook\\stories\\context-menu.stories.tsx",
    lineNumber: 120,
    columnNumber: 5
  }, undefined)
};
/**
 * A context menu with a radio group.
 */
const WithRadioGroup = {
  render: args => /*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_repo_design_system_components_ui_context_menu__WEBPACK_IMPORTED_MODULE_1__.ContextMenu, {
    ...args,
    children: [/*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_repo_design_system_components_ui_context_menu__WEBPACK_IMPORTED_MODULE_1__.ContextMenuTrigger, {
      className: "flex h-48 w-96 items-center justify-center rounded-md border border-dashed bg-accent text-sm",
      children: "Right click here"
    }, void 0, false, {
      fileName: "C:\\Users\\<USER>\\Documents\\Cubentweb\\cubent\\apps\\storybook\\stories\\context-menu.stories.tsx",
      lineNumber: 141,
      columnNumber: 7
    }, undefined), /*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_repo_design_system_components_ui_context_menu__WEBPACK_IMPORTED_MODULE_1__.ContextMenuContent, {
      className: "w-64",
      children: /*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_repo_design_system_components_ui_context_menu__WEBPACK_IMPORTED_MODULE_1__.ContextMenuRadioGroup, {
        value: "light",
        children: [/*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_repo_design_system_components_ui_context_menu__WEBPACK_IMPORTED_MODULE_1__.ContextMenuLabel, {
          inset: true,
          children: "Theme"
        }, void 0, false, {
          fileName: "C:\\Users\\<USER>\\Documents\\Cubentweb\\cubent\\apps\\storybook\\stories\\context-menu.stories.tsx",
          lineNumber: 146,
          columnNumber: 11
        }, undefined), /*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_repo_design_system_components_ui_context_menu__WEBPACK_IMPORTED_MODULE_1__.ContextMenuRadioItem, {
          value: "light",
          children: "Light"
        }, void 0, false, {
          fileName: "C:\\Users\\<USER>\\Documents\\Cubentweb\\cubent\\apps\\storybook\\stories\\context-menu.stories.tsx",
          lineNumber: 147,
          columnNumber: 11
        }, undefined), /*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_repo_design_system_components_ui_context_menu__WEBPACK_IMPORTED_MODULE_1__.ContextMenuRadioItem, {
          value: "dark",
          children: "Dark"
        }, void 0, false, {
          fileName: "C:\\Users\\<USER>\\Documents\\Cubentweb\\cubent\\apps\\storybook\\stories\\context-menu.stories.tsx",
          lineNumber: 148,
          columnNumber: 11
        }, undefined)]
      }, void 0, true, {
        fileName: "C:\\Users\\<USER>\\Documents\\Cubentweb\\cubent\\apps\\storybook\\stories\\context-menu.stories.tsx",
        lineNumber: 145,
        columnNumber: 9
      }, undefined)
    }, void 0, false, {
      fileName: "C:\\Users\\<USER>\\Documents\\Cubentweb\\cubent\\apps\\storybook\\stories\\context-menu.stories.tsx",
      lineNumber: 144,
      columnNumber: 7
    }, undefined)]
  }, void 0, true, {
    fileName: "C:\\Users\\<USER>\\Documents\\Cubentweb\\cubent\\apps\\storybook\\stories\\context-menu.stories.tsx",
    lineNumber: 140,
    columnNumber: 5
  }, undefined)
};
;
const __namedExportsOrder = ["Default", "WithShortcuts", "WithSubmenu", "WithCheckboxes", "WithRadioGroup"];
Default.parameters = {
  ...Default.parameters,
  docs: {
    ...Default.parameters?.docs,
    source: {
      originalSource: "{}",
      ...Default.parameters?.docs?.source
    },
    description: {
      story: "The default form of the context menu.",
      ...Default.parameters?.docs?.description
    }
  }
};
WithShortcuts.parameters = {
  ...WithShortcuts.parameters,
  docs: {
    ...WithShortcuts.parameters?.docs,
    source: {
      originalSource: "{\n  render: args => <ContextMenu {...args}>\r\n      <ContextMenuTrigger className=\"flex h-48 w-96 items-center justify-center rounded-md border border-dashed bg-accent text-sm\">\r\n        Right click here\r\n      </ContextMenuTrigger>\r\n      <ContextMenuContent className=\"w-32\">\r\n        <ContextMenuItem>\r\n          Back\r\n          <ContextMenuShortcut>\u2318[</ContextMenuShortcut>\r\n        </ContextMenuItem>\r\n        <ContextMenuItem disabled>\r\n          Forward\r\n          <ContextMenuShortcut>\u2318]</ContextMenuShortcut>\r\n        </ContextMenuItem>\r\n        <ContextMenuItem>\r\n          Reload\r\n          <ContextMenuShortcut>\u2318R</ContextMenuShortcut>\r\n        </ContextMenuItem>\r\n      </ContextMenuContent>\r\n    </ContextMenu>\n}",
      ...WithShortcuts.parameters?.docs?.source
    },
    description: {
      story: "A context menu with shortcuts.",
      ...WithShortcuts.parameters?.docs?.description
    }
  }
};
WithSubmenu.parameters = {
  ...WithSubmenu.parameters,
  docs: {
    ...WithSubmenu.parameters?.docs,
    source: {
      originalSource: "{\n  render: args => <ContextMenu {...args}>\r\n      <ContextMenuTrigger className=\"flex h-48 w-96 items-center justify-center rounded-md border border-dashed bg-accent text-sm\">\r\n        Right click here\r\n      </ContextMenuTrigger>\r\n      <ContextMenuContent className=\"w-32\">\r\n        <ContextMenuItem>\r\n          New Tab\r\n          <ContextMenuShortcut>\u2318N</ContextMenuShortcut>\r\n        </ContextMenuItem>\r\n        <ContextMenuSub>\r\n          <ContextMenuSubTrigger>More Tools</ContextMenuSubTrigger>\r\n          <ContextMenuSubContent>\r\n            <ContextMenuItem>\r\n              Save Page As...\r\n              <ContextMenuShortcut>\u21E7\u2318S</ContextMenuShortcut>\r\n            </ContextMenuItem>\r\n            <ContextMenuItem>Create Shortcut...</ContextMenuItem>\r\n            <ContextMenuItem>Name Window...</ContextMenuItem>\r\n            <ContextMenuSeparator />\r\n            <ContextMenuItem>Developer Tools</ContextMenuItem>\r\n          </ContextMenuSubContent>\r\n        </ContextMenuSub>\r\n      </ContextMenuContent>\r\n    </ContextMenu>\n}",
      ...WithSubmenu.parameters?.docs?.source
    },
    description: {
      story: "A context menu with a submenu.",
      ...WithSubmenu.parameters?.docs?.description
    }
  }
};
WithCheckboxes.parameters = {
  ...WithCheckboxes.parameters,
  docs: {
    ...WithCheckboxes.parameters?.docs,
    source: {
      originalSource: "{\n  render: args => <ContextMenu {...args}>\r\n      <ContextMenuTrigger className=\"flex h-48 w-96 items-center justify-center rounded-md border border-dashed bg-accent text-sm\">\r\n        Right click here\r\n      </ContextMenuTrigger>\r\n      <ContextMenuContent className=\"w-64\">\r\n        <ContextMenuCheckboxItem checked>\r\n          Show Comments\r\n          <ContextMenuShortcut>\u2318\u21E7C</ContextMenuShortcut>\r\n        </ContextMenuCheckboxItem>\r\n        <ContextMenuCheckboxItem>Show Preview</ContextMenuCheckboxItem>\r\n      </ContextMenuContent>\r\n    </ContextMenu>\n}",
      ...WithCheckboxes.parameters?.docs?.source
    },
    description: {
      story: "A context menu with checkboxes.",
      ...WithCheckboxes.parameters?.docs?.description
    }
  }
};
WithRadioGroup.parameters = {
  ...WithRadioGroup.parameters,
  docs: {
    ...WithRadioGroup.parameters?.docs,
    source: {
      originalSource: "{\n  render: args => <ContextMenu {...args}>\r\n      <ContextMenuTrigger className=\"flex h-48 w-96 items-center justify-center rounded-md border border-dashed bg-accent text-sm\">\r\n        Right click here\r\n      </ContextMenuTrigger>\r\n      <ContextMenuContent className=\"w-64\">\r\n        <ContextMenuRadioGroup value=\"light\">\r\n          <ContextMenuLabel inset>Theme</ContextMenuLabel>\r\n          <ContextMenuRadioItem value=\"light\">Light</ContextMenuRadioItem>\r\n          <ContextMenuRadioItem value=\"dark\">Dark</ContextMenuRadioItem>\r\n        </ContextMenuRadioGroup>\r\n      </ContextMenuContent>\r\n    </ContextMenu>\n}",
      ...WithRadioGroup.parameters?.docs?.source
    },
    description: {
      story: "A context menu with a radio group.",
      ...WithRadioGroup.parameters?.docs?.description
    }
  }
};

const $ReactRefreshModuleId$ = __webpack_require__.$Refresh$.moduleId;
const $ReactRefreshCurrentExports$ = __react_refresh_utils__.getModuleExports(
	$ReactRefreshModuleId$
);

function $ReactRefreshModuleRuntime$(exports) {
	if (true) {
		let errorOverlay;
		if (true) {
			errorOverlay = false;
		}
		let testMode;
		if (typeof __react_refresh_test__ !== 'undefined') {
			testMode = __react_refresh_test__;
		}
		return __react_refresh_utils__.executeRuntime(
			exports,
			$ReactRefreshModuleId$,
			module.hot,
			errorOverlay,
			testMode
		);
	}
}

if (typeof Promise !== 'undefined' && $ReactRefreshCurrentExports$ instanceof Promise) {
	$ReactRefreshCurrentExports$.then($ReactRefreshModuleRuntime$);
} else {
	$ReactRefreshModuleRuntime$($ReactRefreshCurrentExports$);
}

/***/ })

}]);
//# sourceMappingURL=context-menu-stories.iframe.bundle.js.map