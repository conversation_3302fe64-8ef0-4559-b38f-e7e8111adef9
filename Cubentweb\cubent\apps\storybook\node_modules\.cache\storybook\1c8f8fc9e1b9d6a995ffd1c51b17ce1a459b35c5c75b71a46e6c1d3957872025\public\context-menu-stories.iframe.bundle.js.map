{"version": 3, "file": "context-menu-stories.iframe.bundle.js", "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;;AAEA;AACA;AACA;AAEA;AAEA;AAGA;AAAA;AAAA;;;;;;AACA;;AAEA;AAGA;AACA;AAAA;;;;;;AAEA;;AAEA;AAGA;AACA;AAAA;;;;;;AAEA;AANA;AAQA;AAGA;AACA;AAAA;;;;;;AAEA;AANA;AAQA;AAGA;AAAA;AAAA;;;;;;AACA;AAJA;AAMA;AAGA;AAEA;AACA;;;;;;AAGA;AATA;AAWA;AAQA;AAEA;AACA;AACA;AAIA;;AAEA;AACA;AAAA;;;;;;;;;;;;AAGA;AAtBA;AAwBA;AAIA;AAEA;AACA;AAIA;;;;;;AAGA;AAdA;AAgBA;AAIA;AAEA;AACA;AACA;AAIA;;;;;;;;;;;AAIA;AAhBA;AAkBA;AASA;AAEA;AACA;AACA;AACA;AAIA;;;;;;AAGA;AArBA;AAuBA;AAMA;AAEA;AACA;AAIA;AACA;;AAEA;AAAA;AACA;AACA;AAAA;;;;;;;;;;;;;;;;AAGA;;;;;;;AAGA;AAxBA;AA0BA;AAKA;AAEA;AACA;AAIA;;AAEA;AAAA;AACA;AACA;AAAA;;;;;;;;;;;;;;;;AAGA;;;;;;;AAGA;AAtBA;AAwBA;AAOA;AAEA;AACA;AACA;AAIA;;;;;;AAGA;AAlBA;AAoBA;AAIA;AAEA;AACA;AACA;;;;;;AAGA;AAXA;AAaA;AAIA;AAEA;AACA;AAIA;;;;;;AAGA;AAdA;AAgBA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACzOA;AAgBA;;;;AAIA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;;;;;AAGA;AAAA;AACA;AAAA;;;;;AACA;AAAA;;;;;AACA;AAAA;;;;;AACA;AAAA;;;;;;;;;;;;;;;;AAIA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AAEA;AAIA;;;AAGA;AAEA;;;AAGA;AACA;AACA;AACA;AAAA;AAAA;;;;;AAGA;AAAA;AACA;AAAA;AAEA;;;;;;;;;;AAEA;AAAA;AAAA;AAEA;;;;;;;;;;AAEA;AAAA;AAEA;;;;;;;;;;;;;;;;;;;;;AAKA;AAEA;;;AAGA;AACA;AACA;AACA;AAAA;AAAA;;;;;AAGA;AAAA;AACA;AAAA;AAEA;;;;;;;;;;AAEA;AACA;AAAA;;;;;AACA;AACA;AAAA;AAEA;;;;;;;;;;AAEA;AAAA;;;;;AACA;AAAA;;;;;AACA;;;;AACA;AAAA;;;;;;;;;;;;;;;;;;;;;;;;;;AAMA;AAEA;;;AAGA;AACA;AACA;AACA;AAAA;AAAA;;;;;AAGA;AAAA;AACA;AAAA;AAAA;AAEA;;;;;;;;;;AAEA;AAAA;;;;;;;;;;;;;;;;AAIA;AAEA;;;AAGA;AACA;AACA;AACA;AAAA;AAAA;;;;;AAGA;AAAA;AACA;AAAA;AACA;AAAA;AAAA;;;;;AACA;AAAA;AAAA;;;;;AACA;AAAA;AAAA;;;;;;;;;;;;;;;;;;;;;AAKA", "sources": ["webpack://storybook/../../packages/design-system/components/ui/context-menu.tsx", "webpack://storybook/./stories/context-menu.stories.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as ContextMenuPrimitive from \"@radix-ui/react-context-menu\"\nimport { CheckIcon, ChevronRightIcon, CircleIcon } from \"lucide-react\"\n\nimport { cn } from \"@repo/design-system/lib/utils\"\n\nfunction ContextMenu({\n  ...props\n}: React.ComponentProps<typeof ContextMenuPrimitive.Root>) {\n  return <ContextMenuPrimitive.Root data-slot=\"context-menu\" {...props} />\n}\n\nfunction ContextMenuTrigger({\n  ...props\n}: React.ComponentProps<typeof ContextMenuPrimitive.Trigger>) {\n  return (\n    <ContextMenuPrimitive.Trigger data-slot=\"context-menu-trigger\" {...props} />\n  )\n}\n\nfunction ContextMenuGroup({\n  ...props\n}: React.ComponentProps<typeof ContextMenuPrimitive.Group>) {\n  return (\n    <ContextMenuPrimitive.Group data-slot=\"context-menu-group\" {...props} />\n  )\n}\n\nfunction ContextMenuPortal({\n  ...props\n}: React.ComponentProps<typeof ContextMenuPrimitive.Portal>) {\n  return (\n    <ContextMenuPrimitive.Portal data-slot=\"context-menu-portal\" {...props} />\n  )\n}\n\nfunction ContextMenuSub({\n  ...props\n}: React.ComponentProps<typeof ContextMenuPrimitive.Sub>) {\n  return <ContextMenuPrimitive.Sub data-slot=\"context-menu-sub\" {...props} />\n}\n\nfunction ContextMenuRadioGroup({\n  ...props\n}: React.ComponentProps<typeof ContextMenuPrimitive.RadioGroup>) {\n  return (\n    <ContextMenuPrimitive.RadioGroup\n      data-slot=\"context-menu-radio-group\"\n      {...props}\n    />\n  )\n}\n\nfunction ContextMenuSubTrigger({\n  className,\n  inset,\n  children,\n  ...props\n}: React.ComponentProps<typeof ContextMenuPrimitive.SubTrigger> & {\n  inset?: boolean\n}) {\n  return (\n    <ContextMenuPrimitive.SubTrigger\n      data-slot=\"context-menu-sub-trigger\"\n      data-inset={inset}\n      className={cn(\n        \"focus:bg-accent focus:text-accent-foreground data-[state=open]:bg-accent data-[state=open]:text-accent-foreground flex cursor-default items-center rounded-sm px-2 py-1.5 text-sm outline-hidden select-none data-[inset]:pl-8 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\n        className\n      )}\n      {...props}\n    >\n      {children}\n      <ChevronRightIcon className=\"ml-auto\" />\n    </ContextMenuPrimitive.SubTrigger>\n  )\n}\n\nfunction ContextMenuSubContent({\n  className,\n  ...props\n}: React.ComponentProps<typeof ContextMenuPrimitive.SubContent>) {\n  return (\n    <ContextMenuPrimitive.SubContent\n      data-slot=\"context-menu-sub-content\"\n      className={cn(\n        \"bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 min-w-[8rem] origin-(--radix-context-menu-content-transform-origin) overflow-hidden rounded-md border p-1 shadow-lg\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction ContextMenuContent({\n  className,\n  ...props\n}: React.ComponentProps<typeof ContextMenuPrimitive.Content>) {\n  return (\n    <ContextMenuPrimitive.Portal>\n      <ContextMenuPrimitive.Content\n        data-slot=\"context-menu-content\"\n        className={cn(\n          \"bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 max-h-(--radix-context-menu-content-available-height) min-w-[8rem] origin-(--radix-context-menu-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border p-1 shadow-md\",\n          className\n        )}\n        {...props}\n      />\n    </ContextMenuPrimitive.Portal>\n  )\n}\n\nfunction ContextMenuItem({\n  className,\n  inset,\n  variant = \"default\",\n  ...props\n}: React.ComponentProps<typeof ContextMenuPrimitive.Item> & {\n  inset?: boolean\n  variant?: \"default\" | \"destructive\"\n}) {\n  return (\n    <ContextMenuPrimitive.Item\n      data-slot=\"context-menu-item\"\n      data-inset={inset}\n      data-variant={variant}\n      className={cn(\n        \"focus:bg-accent focus:text-accent-foreground data-[variant=destructive]:text-destructive data-[variant=destructive]:focus:bg-destructive/10 dark:data-[variant=destructive]:focus:bg-destructive/20 data-[variant=destructive]:focus:text-destructive data-[variant=destructive]:*:[svg]:!text-destructive [&_svg:not([class*='text-'])]:text-muted-foreground relative flex cursor-default items-center gap-2 rounded-sm px-2 py-1.5 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 data-[inset]:pl-8 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction ContextMenuCheckboxItem({\n  className,\n  children,\n  checked,\n  ...props\n}: React.ComponentProps<typeof ContextMenuPrimitive.CheckboxItem>) {\n  return (\n    <ContextMenuPrimitive.CheckboxItem\n      data-slot=\"context-menu-checkbox-item\"\n      className={cn(\n        \"focus:bg-accent focus:text-accent-foreground relative flex cursor-default items-center gap-2 rounded-sm py-1.5 pr-2 pl-8 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\n        className\n      )}\n      checked={checked}\n      {...props}\n    >\n      <span className=\"pointer-events-none absolute left-2 flex size-3.5 items-center justify-center\">\n        <ContextMenuPrimitive.ItemIndicator>\n          <CheckIcon className=\"size-4\" />\n        </ContextMenuPrimitive.ItemIndicator>\n      </span>\n      {children}\n    </ContextMenuPrimitive.CheckboxItem>\n  )\n}\n\nfunction ContextMenuRadioItem({\n  className,\n  children,\n  ...props\n}: React.ComponentProps<typeof ContextMenuPrimitive.RadioItem>) {\n  return (\n    <ContextMenuPrimitive.RadioItem\n      data-slot=\"context-menu-radio-item\"\n      className={cn(\n        \"focus:bg-accent focus:text-accent-foreground relative flex cursor-default items-center gap-2 rounded-sm py-1.5 pr-2 pl-8 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\n        className\n      )}\n      {...props}\n    >\n      <span className=\"pointer-events-none absolute left-2 flex size-3.5 items-center justify-center\">\n        <ContextMenuPrimitive.ItemIndicator>\n          <CircleIcon className=\"size-2 fill-current\" />\n        </ContextMenuPrimitive.ItemIndicator>\n      </span>\n      {children}\n    </ContextMenuPrimitive.RadioItem>\n  )\n}\n\nfunction ContextMenuLabel({\n  className,\n  inset,\n  ...props\n}: React.ComponentProps<typeof ContextMenuPrimitive.Label> & {\n  inset?: boolean\n}) {\n  return (\n    <ContextMenuPrimitive.Label\n      data-slot=\"context-menu-label\"\n      data-inset={inset}\n      className={cn(\n        \"text-foreground px-2 py-1.5 text-sm font-medium data-[inset]:pl-8\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction ContextMenuSeparator({\n  className,\n  ...props\n}: React.ComponentProps<typeof ContextMenuPrimitive.Separator>) {\n  return (\n    <ContextMenuPrimitive.Separator\n      data-slot=\"context-menu-separator\"\n      className={cn(\"bg-border -mx-1 my-1 h-px\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction ContextMenuShortcut({\n  className,\n  ...props\n}: React.ComponentProps<\"span\">) {\n  return (\n    <span\n      data-slot=\"context-menu-shortcut\"\n      className={cn(\n        \"text-muted-foreground ml-auto text-xs tracking-widest\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport {\n  ContextMenu,\n  ContextMenuTrigger,\n  ContextMenuContent,\n  ContextMenuItem,\n  ContextMenuCheckboxItem,\n  ContextMenuRadioItem,\n  ContextMenuLabel,\n  ContextMenuSeparator,\n  ContextMenuShortcut,\n  ContextMenuGroup,\n  ContextMenuPortal,\n  ContextMenuSub,\n  ContextMenuSubContent,\n  ContextMenuSubTrigger,\n  ContextMenuRadioGroup,\n}\n", "import type { <PERSON><PERSON>, StoryObj } from '@storybook/react';\n\nimport {\n  ContextMenu,\n  ContextMenuCheckboxItem,\n  ContextMenuContent,\n  ContextMenuItem,\n  ContextMenuLabel,\n  ContextMenuRadioGroup,\n  ContextMenuRadioItem,\n  ContextMenuSeparator,\n  ContextMenuShortcut,\n  ContextMenuSub,\n  ContextMenuSubContent,\n  ContextMenuSubTrigger,\n  ContextMenuTrigger,\n} from '@repo/design-system/components/ui/context-menu';\n\n/**\n * Displays a menu to the user — such as a set of actions or functions —\n * triggered by a button.\n */\nconst meta = {\n  title: 'ui/ContextMenu',\n  component: ContextMenu,\n  tags: ['autodocs'],\n  argTypes: {},\n  args: {},\n  render: (args) => (\n    <ContextMenu {...args}>\n      <ContextMenuTrigger className=\"flex h-48 w-96 items-center justify-center rounded-md border border-dashed bg-accent text-sm\">\n        Right click here\n      </ContextMenuTrigger>\n      <ContextMenuContent className=\"w-32\">\n        <ContextMenuItem>Profile</ContextMenuItem>\n        <ContextMenuItem>Billing</ContextMenuItem>\n        <ContextMenuItem>Team</ContextMenuItem>\n        <ContextMenuItem>Subscription</ContextMenuItem>\n      </ContextMenuContent>\n    </ContextMenu>\n  ),\n  parameters: {\n    layout: 'centered',\n  },\n} satisfies Meta<typeof ContextMenu>;\n\nexport default meta;\n\ntype Story = StoryObj<typeof meta>;\n\n/**\n * The default form of the context menu.\n */\nexport const Default: Story = {};\n\n/**\n * A context menu with shortcuts.\n */\nexport const WithShortcuts: Story = {\n  render: (args) => (\n    <ContextMenu {...args}>\n      <ContextMenuTrigger className=\"flex h-48 w-96 items-center justify-center rounded-md border border-dashed bg-accent text-sm\">\n        Right click here\n      </ContextMenuTrigger>\n      <ContextMenuContent className=\"w-32\">\n        <ContextMenuItem>\n          Back\n          <ContextMenuShortcut>⌘[</ContextMenuShortcut>\n        </ContextMenuItem>\n        <ContextMenuItem disabled>\n          Forward\n          <ContextMenuShortcut>⌘]</ContextMenuShortcut>\n        </ContextMenuItem>\n        <ContextMenuItem>\n          Reload\n          <ContextMenuShortcut>⌘R</ContextMenuShortcut>\n        </ContextMenuItem>\n      </ContextMenuContent>\n    </ContextMenu>\n  ),\n};\n\n/**\n * A context menu with a submenu.\n */\nexport const WithSubmenu: Story = {\n  render: (args) => (\n    <ContextMenu {...args}>\n      <ContextMenuTrigger className=\"flex h-48 w-96 items-center justify-center rounded-md border border-dashed bg-accent text-sm\">\n        Right click here\n      </ContextMenuTrigger>\n      <ContextMenuContent className=\"w-32\">\n        <ContextMenuItem>\n          New Tab\n          <ContextMenuShortcut>⌘N</ContextMenuShortcut>\n        </ContextMenuItem>\n        <ContextMenuSub>\n          <ContextMenuSubTrigger>More Tools</ContextMenuSubTrigger>\n          <ContextMenuSubContent>\n            <ContextMenuItem>\n              Save Page As...\n              <ContextMenuShortcut>⇧⌘S</ContextMenuShortcut>\n            </ContextMenuItem>\n            <ContextMenuItem>Create Shortcut...</ContextMenuItem>\n            <ContextMenuItem>Name Window...</ContextMenuItem>\n            <ContextMenuSeparator />\n            <ContextMenuItem>Developer Tools</ContextMenuItem>\n          </ContextMenuSubContent>\n        </ContextMenuSub>\n      </ContextMenuContent>\n    </ContextMenu>\n  ),\n};\n\n/**\n * A context menu with checkboxes.\n */\nexport const WithCheckboxes: Story = {\n  render: (args) => (\n    <ContextMenu {...args}>\n      <ContextMenuTrigger className=\"flex h-48 w-96 items-center justify-center rounded-md border border-dashed bg-accent text-sm\">\n        Right click here\n      </ContextMenuTrigger>\n      <ContextMenuContent className=\"w-64\">\n        <ContextMenuCheckboxItem checked>\n          Show Comments\n          <ContextMenuShortcut>⌘⇧C</ContextMenuShortcut>\n        </ContextMenuCheckboxItem>\n        <ContextMenuCheckboxItem>Show Preview</ContextMenuCheckboxItem>\n      </ContextMenuContent>\n    </ContextMenu>\n  ),\n};\n\n/**\n * A context menu with a radio group.\n */\nexport const WithRadioGroup: Story = {\n  render: (args) => (\n    <ContextMenu {...args}>\n      <ContextMenuTrigger className=\"flex h-48 w-96 items-center justify-center rounded-md border border-dashed bg-accent text-sm\">\n        Right click here\n      </ContextMenuTrigger>\n      <ContextMenuContent className=\"w-64\">\n        <ContextMenuRadioGroup value=\"light\">\n          <ContextMenuLabel inset>Theme</ContextMenuLabel>\n          <ContextMenuRadioItem value=\"light\">Light</ContextMenuRadioItem>\n          <ContextMenuRadioItem value=\"dark\">Dark</ContextMenuRadioItem>\n        </ContextMenuRadioGroup>\n      </ContextMenuContent>\n    </ContextMenu>\n  ),\n};\n"], "names": [], "sourceRoot": ""}