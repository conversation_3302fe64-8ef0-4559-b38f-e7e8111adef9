{"version": 3, "file": "dropdown-menu-stories.iframe.bundle.js", "mappings": ";;;;;;;;;;;;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;;;;ACjBA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;;;;AChBA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;;;;AChBA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACjBA;AAEA;AAkBA;;;;AAIA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;;;;;AACA;AAAA;AACA;AAAA;;;;;AACA;;;;AACA;AAAA;;;;;AACA;AAAA;;;;;AACA;AAAA;;;;;AACA;AAAA;;;;;;;;;;;;;;;;AAIA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AAEA;AAIA;;;AAGA;AAEA;;;AAGA;AACA;AACA;AACA;AAAA;;;;;AACA;AAAA;AACA;AAAA;;;;;AACA;AAAA;AAEA;;;;;;;;;;AAEA;AAAA;AAAA;AAEA;;;;;;;;;;;;;;;;;;;;;AAKA;AAEA;;;AAGA;AACA;AACA;AACA;AAAA;;;;;AACA;AAAA;AACA;AACA;AAAA;;;;;AACA;AAAA;;;;;;;;;;AAEA;;;;AACA;AACA;AACA;AAAA;;;;;AACA;AAAA;;;;;AACA;AAAA;;;;;;;;;;AAEA;AACA;AACA;AAAA;;;;;AACA;AAAA;;;;;;;;;;AAEA;AACA;AACA;AACA;AAAA;;;;;AACA;AAAA;;;;;;;;;;AAEA;;;;AACA;AACA;AAAA;;;;;AACA;AAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASA;AAEA;;;AAGA;AACA;AACA;AACA;AAAA;;;;;AACA;AAAA;AACA;AAAA;AAAA;;;;;AACA;AAAA;AACA;AAAA;AAAA;;;;;AACA;AAAA;AAAA;;;;;AACA;AAAA;AAAA;;;;;;;;;;;;;;;;;;;;;AAKA;AAEA;;;AAGA;AACA;AACA;AACA;AAAA;;;;;AACA;AAAA;AACA;AAAA;AAAA;AAEA;;;;;;;;;;AAEA;AAAA;;;;;;;;;;;;;;;;AAIA", "sources": ["webpack://storybook/../../node_modules/.pnpm/lucide-react@0.511.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/circle-plus.js", "webpack://storybook/../../node_modules/.pnpm/lucide-react@0.511.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/mail.js", "webpack://storybook/../../node_modules/.pnpm/lucide-react@0.511.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/search.js", "webpack://storybook/../../node_modules/.pnpm/lucide-react@0.511.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/user-plus.js", "webpack://storybook/./stories/dropdown-menu.stories.tsx"], "sourcesContent": ["/**\n * @license lucide-react v0.511.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\n\nconst __iconNode = [\n  [\"circle\", { cx: \"12\", cy: \"12\", r: \"10\", key: \"1mglay\" }],\n  [\"path\", { d: \"M8 12h8\", key: \"1wcyev\" }],\n  [\"path\", { d: \"M12 8v8\", key: \"napkw2\" }]\n];\nconst CirclePlus = createLucideIcon(\"circle-plus\", __iconNode);\n\nexport { __iconNode, CirclePlus as default };\n//# sourceMappingURL=circle-plus.js.map\n", "/**\n * @license lucide-react v0.511.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\n\nconst __iconNode = [\n  [\"path\", { d: \"m22 7-8.991 5.727a2 2 0 0 1-2.009 0L2 7\", key: \"132q7q\" }],\n  [\"rect\", { x: \"2\", y: \"4\", width: \"20\", height: \"16\", rx: \"2\", key: \"izxlao\" }]\n];\nconst Mail = createLucideIcon(\"mail\", __iconNode);\n\nexport { __iconNode, Mail as default };\n//# sourceMappingURL=mail.js.map\n", "/**\n * @license lucide-react v0.511.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\n\nconst __iconNode = [\n  [\"path\", { d: \"m21 21-4.34-4.34\", key: \"14j7rj\" }],\n  [\"circle\", { cx: \"11\", cy: \"11\", r: \"8\", key: \"4ej97u\" }]\n];\nconst Search = createLucideIcon(\"search\", __iconNode);\n\nexport { __iconNode, Search as default };\n//# sourceMappingURL=search.js.map\n", "/**\n * @license lucide-react v0.511.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\n\nconst __iconNode = [\n  [\"path\", { d: \"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2\", key: \"1yyitq\" }],\n  [\"circle\", { cx: \"9\", cy: \"7\", r: \"4\", key: \"nufk8\" }],\n  [\"line\", { x1: \"19\", x2: \"19\", y1: \"8\", y2: \"14\", key: \"1bvyxn\" }],\n  [\"line\", { x1: \"22\", x2: \"16\", y1: \"11\", y2: \"11\", key: \"1shjgl\" }]\n];\nconst UserPlus = createLucideIcon(\"user-plus\", __iconNode);\n\nexport { __iconNode, UserPlus as default };\n//# sourceMappingURL=user-plus.js.map\n", "import type { <PERSON><PERSON>, <PERSON>Obj } from '@storybook/react';\nimport { Mail, Plus, PlusCircle, Search, UserPlus } from 'lucide-react';\n\nimport {\n  DropdownMenu,\n  DropdownMenuCheckboxItem,\n  DropdownMenuContent,\n  DropdownMenuGroup,\n  DropdownMenuItem,\n  DropdownMenuLabel,\n  DropdownMenuPortal,\n  DropdownMenuRadioGroup,\n  DropdownMenuRadioItem,\n  DropdownMenuSeparator,\n  DropdownMenuShortcut,\n  DropdownMenuSub,\n  DropdownMenuSubContent,\n  DropdownMenuSubTrigger,\n  DropdownMenuTrigger,\n} from '@repo/design-system/components/ui/dropdown-menu';\n\n/**\n * Displays a menu to the user — such as a set of actions or functions —\n * triggered by a button.\n */\nconst meta = {\n  title: 'ui/DropdownMenu',\n  component: DropdownMenu,\n  tags: ['autodocs'],\n  argTypes: {},\n  render: (args) => (\n    <DropdownMenu {...args}>\n      <DropdownMenuTrigger>Open</DropdownMenuTrigger>\n      <DropdownMenuContent className=\"w-44\">\n        <DropdownMenuLabel>My Account</DropdownMenuLabel>\n        <DropdownMenuSeparator />\n        <DropdownMenuItem>Profile</DropdownMenuItem>\n        <DropdownMenuItem>Billing</DropdownMenuItem>\n        <DropdownMenuItem>Team</DropdownMenuItem>\n        <DropdownMenuItem>Subscription</DropdownMenuItem>\n      </DropdownMenuContent>\n    </DropdownMenu>\n  ),\n  parameters: {\n    layout: 'centered',\n  },\n} satisfies Meta<typeof DropdownMenu>;\n\nexport default meta;\n\ntype Story = StoryObj<typeof meta>;\n\n/**\n * The default form of the dropdown menu.\n */\nexport const Default: Story = {};\n\n/**\n * A dropdown menu with shortcuts.\n */\nexport const WithShortcuts: Story = {\n  render: (args) => (\n    <DropdownMenu {...args}>\n      <DropdownMenuTrigger>Open</DropdownMenuTrigger>\n      <DropdownMenuContent className=\"w-44\">\n        <DropdownMenuLabel>Controls</DropdownMenuLabel>\n        <DropdownMenuItem>\n          Back\n          <DropdownMenuShortcut>⌘[</DropdownMenuShortcut>\n        </DropdownMenuItem>\n        <DropdownMenuItem disabled>\n          Forward\n          <DropdownMenuShortcut>⌘]</DropdownMenuShortcut>\n        </DropdownMenuItem>\n      </DropdownMenuContent>\n    </DropdownMenu>\n  ),\n};\n\n/**\n * A dropdown menu with submenus.\n */\nexport const WithSubmenus: Story = {\n  render: (args) => (\n    <DropdownMenu {...args}>\n      <DropdownMenuTrigger>Open</DropdownMenuTrigger>\n      <DropdownMenuContent className=\"w-44\">\n        <DropdownMenuItem>\n          <Search className=\"mr-2 size-4\" />\n          <span>Search</span>\n        </DropdownMenuItem>\n        <DropdownMenuSeparator />\n        <DropdownMenuGroup>\n          <DropdownMenuItem>\n            <Plus className=\"mr-2 size-4\" />\n            <span>New Team</span>\n            <DropdownMenuShortcut>⌘+T</DropdownMenuShortcut>\n          </DropdownMenuItem>\n          <DropdownMenuSub>\n            <DropdownMenuSubTrigger>\n              <UserPlus className=\"mr-2 size-4\" />\n              <span>Invite users</span>\n            </DropdownMenuSubTrigger>\n            <DropdownMenuPortal>\n              <DropdownMenuSubContent>\n                <DropdownMenuItem>\n                  <Mail className=\"mr-2 size-4\" />\n                  <span>Email</span>\n                </DropdownMenuItem>\n                <DropdownMenuSeparator />\n                <DropdownMenuItem>\n                  <PlusCircle className=\"mr-2 size-4\" />\n                  <span>More...</span>\n                </DropdownMenuItem>\n              </DropdownMenuSubContent>\n            </DropdownMenuPortal>\n          </DropdownMenuSub>\n        </DropdownMenuGroup>\n      </DropdownMenuContent>\n    </DropdownMenu>\n  ),\n};\n\n/**\n * A dropdown menu with radio items.\n */\nexport const WithRadioItems: Story = {\n  render: (args) => (\n    <DropdownMenu {...args}>\n      <DropdownMenuTrigger>Open</DropdownMenuTrigger>\n      <DropdownMenuContent className=\"w-44\">\n        <DropdownMenuLabel inset>Status</DropdownMenuLabel>\n        <DropdownMenuRadioGroup value=\"warning\">\n          <DropdownMenuRadioItem value=\"info\">Info</DropdownMenuRadioItem>\n          <DropdownMenuRadioItem value=\"warning\">Warning</DropdownMenuRadioItem>\n          <DropdownMenuRadioItem value=\"error\">Error</DropdownMenuRadioItem>\n        </DropdownMenuRadioGroup>\n      </DropdownMenuContent>\n    </DropdownMenu>\n  ),\n};\n\n/**\n * A dropdown menu with checkboxes.\n */\nexport const WithCheckboxes: Story = {\n  render: (args) => (\n    <DropdownMenu {...args}>\n      <DropdownMenuTrigger>Open</DropdownMenuTrigger>\n      <DropdownMenuContent className=\"w-44\">\n        <DropdownMenuCheckboxItem checked>\n          Autosave\n          <DropdownMenuShortcut>⌘S</DropdownMenuShortcut>\n        </DropdownMenuCheckboxItem>\n        <DropdownMenuCheckboxItem>Show Comments</DropdownMenuCheckboxItem>\n      </DropdownMenuContent>\n    </DropdownMenu>\n  ),\n};\n"], "names": [], "sourceRoot": ""}