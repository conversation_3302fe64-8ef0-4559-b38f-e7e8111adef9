"use strict";
(self["webpackChunkstorybook"] = self["webpackChunkstorybook"] || []).push([["input-otp-stories"],{

/***/ "../../packages/design-system/components/ui/input-otp.tsx":
/*!****************************************************************!*\
  !*** ../../packages/design-system/components/ui/input-otp.tsx ***!
  \****************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   InputOTP: () => (/* binding */ InputOTP),
/* harmony export */   InputOTPGroup: () => (/* binding */ InputOTPGroup),
/* harmony export */   InputOTPSeparator: () => (/* binding */ InputOTPSeparator),
/* harmony export */   InputOTPSlot: () => (/* binding */ InputOTPSlot)
/* harmony export */ });
/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ "../../node_modules/.pnpm/next@15.3.2_@babel+core@7.2_09efc9edc30971f6ad8d5c21a0ab8f1f/node_modules/next/dist/compiled/react/jsx-dev-runtime.js");
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ "../../node_modules/.pnpm/next@15.3.2_@babel+core@7.2_09efc9edc30971f6ad8d5c21a0ab8f1f/node_modules/next/dist/compiled/react/index.js");
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);
/* harmony import */ var input_otp__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! input-otp */ "../../node_modules/.pnpm/input-otp@1.4.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/input-otp/dist/index.mjs");
/* harmony import */ var _barrel_optimize_names_MinusIcon_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=MinusIcon!=!lucide-react */ "../../node_modules/.pnpm/lucide-react@0.511.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/minus.js");
/* harmony import */ var _repo_design_system_lib_utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @repo/design-system/lib/utils */ "../../packages/design-system/lib/utils.ts");
/* provided dependency */ var __react_refresh_utils__ = __webpack_require__(/*! ../../node_modules/.pnpm/@pmmmwh+react-refresh-webpa_ef086d3919e106717f56f099979b20b9/node_modules/@pmmmwh/react-refresh-webpack-plugin/lib/runtime/RefreshUtils.js */ "../../node_modules/.pnpm/@pmmmwh+react-refresh-webpa_ef086d3919e106717f56f099979b20b9/node_modules/@pmmmwh/react-refresh-webpack-plugin/lib/runtime/RefreshUtils.js");
__webpack_require__.$Refresh$.runtime = __webpack_require__(/*! ../../node_modules/.pnpm/react-refresh@0.14.2/node_modules/react-refresh/runtime.js */ "../../node_modules/.pnpm/react-refresh@0.14.2/node_modules/react-refresh/runtime.js");


var _s = __webpack_require__.$Refresh$.signature();
"use client";




function InputOTP({ className, containerClassName, ...props }) {
    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(input_otp__WEBPACK_IMPORTED_MODULE_2__.OTPInput, {
        "data-slot": "input-otp",
        containerClassName: (0,_repo_design_system_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)("flex items-center gap-2 has-disabled:opacity-50", containerClassName),
        className: (0,_repo_design_system_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)("disabled:cursor-not-allowed", className),
        ...props
    }, void 0, false, {
        fileName: "C:\\Users\\<USER>\\Documents\\Cubentweb\\cubent\\packages\\design-system\\components\\ui\\input-otp.tsx",
        lineNumber: 17,
        columnNumber: 5
    }, this);
}
_c = InputOTP;
function InputOTPGroup({ className, ...props }) {
    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)("div", {
        "data-slot": "input-otp-group",
        className: (0,_repo_design_system_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)("flex items-center", className),
        ...props
    }, void 0, false, {
        fileName: "C:\\Users\\<USER>\\Documents\\Cubentweb\\cubent\\packages\\design-system\\components\\ui\\input-otp.tsx",
        lineNumber: 31,
        columnNumber: 5
    }, this);
}
_c1 = InputOTPGroup;
function InputOTPSlot({ index, className, ...props }) {
    _s();
    const inputOTPContext = react__WEBPACK_IMPORTED_MODULE_1__.useContext(input_otp__WEBPACK_IMPORTED_MODULE_2__.OTPInputContext);
    var _inputOTPContext_slots_index;
    const { char, hasFakeCaret, isActive } = (_inputOTPContext_slots_index = inputOTPContext === null || inputOTPContext === void 0 ? void 0 : inputOTPContext.slots[index]) !== null && _inputOTPContext_slots_index !== void 0 ? _inputOTPContext_slots_index : {};
    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)("div", {
        "data-slot": "input-otp-slot",
        "data-active": isActive,
        className: (0,_repo_design_system_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)("data-[active=true]:border-ring data-[active=true]:ring-ring/50 data-[active=true]:aria-invalid:ring-destructive/20 dark:data-[active=true]:aria-invalid:ring-destructive/40 aria-invalid:border-destructive data-[active=true]:aria-invalid:border-destructive dark:bg-input/30 border-input relative flex h-9 w-9 items-center justify-center border-y border-r text-sm shadow-xs transition-all outline-none first:rounded-l-md first:border-l last:rounded-r-md data-[active=true]:z-10 data-[active=true]:ring-[3px]", className),
        ...props,
        children: [
            char,
            hasFakeCaret && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)("div", {
                className: "pointer-events-none absolute inset-0 flex items-center justify-center",
                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)("div", {
                    className: "animate-caret-blink bg-foreground h-4 w-px duration-1000"
                }, void 0, false, {
                    fileName: "C:\\Users\\<USER>\\Documents\\Cubentweb\\cubent\\packages\\design-system\\components\\ui\\input-otp.tsx",
                    lineNumber: 62,
                    columnNumber: 11
                }, this)
            }, void 0, false, {
                fileName: "C:\\Users\\<USER>\\Documents\\Cubentweb\\cubent\\packages\\design-system\\components\\ui\\input-otp.tsx",
                lineNumber: 61,
                columnNumber: 9
            }, this)
        ]
    }, void 0, true, {
        fileName: "C:\\Users\\<USER>\\Documents\\Cubentweb\\cubent\\packages\\design-system\\components\\ui\\input-otp.tsx",
        lineNumber: 50,
        columnNumber: 5
    }, this);
}
_s(InputOTPSlot, "JNxNoU/M6j9IpCTBZ1gkBzu503s=");
_c2 = InputOTPSlot;
function InputOTPSeparator({ ...props }) {
    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)("div", {
        "data-slot": "input-otp-separator",
        role: "separator",
        ...props,
        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_MinusIcon_lucide_react__WEBPACK_IMPORTED_MODULE_4__["default"], {}, void 0, false, {
            fileName: "C:\\Users\\<USER>\\Documents\\Cubentweb\\cubent\\packages\\design-system\\components\\ui\\input-otp.tsx",
            lineNumber: 72,
            columnNumber: 7
        }, this)
    }, void 0, false, {
        fileName: "C:\\Users\\<USER>\\Documents\\Cubentweb\\cubent\\packages\\design-system\\components\\ui\\input-otp.tsx",
        lineNumber: 71,
        columnNumber: 5
    }, this);
}
_c3 = InputOTPSeparator;

InputOTP.__docgenInfo = {
    "description": "",
    "methods": [],
    "displayName": "InputOTP",
    "props": {
        "containerClassName": {
            "required": false,
            "tsType": {
                "name": "string"
            },
            "description": ""
        }
    }
};
InputOTPGroup.__docgenInfo = {
    "description": "",
    "methods": [],
    "displayName": "InputOTPGroup"
};
InputOTPSlot.__docgenInfo = {
    "description": "",
    "methods": [],
    "displayName": "InputOTPSlot",
    "props": {
        "index": {
            "required": true,
            "tsType": {
                "name": "number"
            },
            "description": ""
        }
    }
};
InputOTPSeparator.__docgenInfo = {
    "description": "",
    "methods": [],
    "displayName": "InputOTPSeparator"
};
var _c, _c1, _c2, _c3;
__webpack_require__.$Refresh$.register(_c, "InputOTP");
__webpack_require__.$Refresh$.register(_c1, "InputOTPGroup");
__webpack_require__.$Refresh$.register(_c2, "InputOTPSlot");
__webpack_require__.$Refresh$.register(_c3, "InputOTPSeparator");


const $ReactRefreshModuleId$ = __webpack_require__.$Refresh$.moduleId;
const $ReactRefreshCurrentExports$ = __react_refresh_utils__.getModuleExports(
	$ReactRefreshModuleId$
);

function $ReactRefreshModuleRuntime$(exports) {
	if (true) {
		let errorOverlay;
		if (true) {
			errorOverlay = false;
		}
		let testMode;
		if (typeof __react_refresh_test__ !== 'undefined') {
			testMode = __react_refresh_test__;
		}
		return __react_refresh_utils__.executeRuntime(
			exports,
			$ReactRefreshModuleId$,
			module.hot,
			errorOverlay,
			testMode
		);
	}
}

if (typeof Promise !== 'undefined' && $ReactRefreshCurrentExports$ instanceof Promise) {
	$ReactRefreshCurrentExports$.then($ReactRefreshModuleRuntime$);
} else {
	$ReactRefreshModuleRuntime$($ReactRefreshCurrentExports$);
}

/***/ }),

/***/ "./stories/input-otp.stories.tsx":
/*!***************************************!*\
  !*** ./stories/input-otp.stories.tsx ***!
  \***************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   Default: () => (/* binding */ Default),
/* harmony export */   SeparatedGroup: () => (/* binding */ SeparatedGroup),
/* harmony export */   __namedExportsOrder: () => (/* binding */ __namedExportsOrder),
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ "../../node_modules/.pnpm/next@15.3.2_@babel+core@7.2_09efc9edc30971f6ad8d5c21a0ab8f1f/node_modules/next/dist/compiled/react/jsx-dev-runtime.js");
/* harmony import */ var input_otp__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! input-otp */ "../../node_modules/.pnpm/input-otp@1.4.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/input-otp/dist/index.mjs");
/* harmony import */ var _repo_design_system_components_ui_input_otp__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @repo/design-system/components/ui/input-otp */ "../../packages/design-system/components/ui/input-otp.tsx");
/* provided dependency */ var __react_refresh_utils__ = __webpack_require__(/*! ../../node_modules/.pnpm/@pmmmwh+react-refresh-webpa_ef086d3919e106717f56f099979b20b9/node_modules/@pmmmwh/react-refresh-webpack-plugin/lib/runtime/RefreshUtils.js */ "../../node_modules/.pnpm/@pmmmwh+react-refresh-webpa_ef086d3919e106717f56f099979b20b9/node_modules/@pmmmwh/react-refresh-webpack-plugin/lib/runtime/RefreshUtils.js");
__webpack_require__.$Refresh$.runtime = __webpack_require__(/*! ../../node_modules/.pnpm/react-refresh@0.14.2/node_modules/react-refresh/runtime.js */ "../../node_modules/.pnpm/react-refresh@0.14.2/node_modules/react-refresh/runtime.js");




/**
 * Accessible one-time password component with copy paste functionality.
 */
const meta = {
  title: 'ui/InputOTP',
  component: _repo_design_system_components_ui_input_otp__WEBPACK_IMPORTED_MODULE_2__.InputOTP,
  tags: ['autodocs'],
  argTypes: {},
  args: {
    maxLength: 6,
    pattern: input_otp__WEBPACK_IMPORTED_MODULE_1__.REGEXP_ONLY_DIGITS_AND_CHARS,
    children: null
  },
  render: args => /*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_repo_design_system_components_ui_input_otp__WEBPACK_IMPORTED_MODULE_2__.InputOTP, {
    ...args,
    render: undefined,
    children: /*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_repo_design_system_components_ui_input_otp__WEBPACK_IMPORTED_MODULE_2__.InputOTPGroup, {
      children: [/*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_repo_design_system_components_ui_input_otp__WEBPACK_IMPORTED_MODULE_2__.InputOTPSlot, {
        index: 0
      }, void 0, false, {
        fileName: "C:\\Users\\<USER>\\Documents\\Cubentweb\\cubent\\apps\\storybook\\stories\\input-otp.stories.tsx",
        lineNumber: 28,
        columnNumber: 9
      }, undefined), /*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_repo_design_system_components_ui_input_otp__WEBPACK_IMPORTED_MODULE_2__.InputOTPSlot, {
        index: 1
      }, void 0, false, {
        fileName: "C:\\Users\\<USER>\\Documents\\Cubentweb\\cubent\\apps\\storybook\\stories\\input-otp.stories.tsx",
        lineNumber: 29,
        columnNumber: 9
      }, undefined), /*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_repo_design_system_components_ui_input_otp__WEBPACK_IMPORTED_MODULE_2__.InputOTPSlot, {
        index: 2
      }, void 0, false, {
        fileName: "C:\\Users\\<USER>\\Documents\\Cubentweb\\cubent\\apps\\storybook\\stories\\input-otp.stories.tsx",
        lineNumber: 30,
        columnNumber: 9
      }, undefined), /*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_repo_design_system_components_ui_input_otp__WEBPACK_IMPORTED_MODULE_2__.InputOTPSlot, {
        index: 3
      }, void 0, false, {
        fileName: "C:\\Users\\<USER>\\Documents\\Cubentweb\\cubent\\apps\\storybook\\stories\\input-otp.stories.tsx",
        lineNumber: 31,
        columnNumber: 9
      }, undefined), /*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_repo_design_system_components_ui_input_otp__WEBPACK_IMPORTED_MODULE_2__.InputOTPSlot, {
        index: 4
      }, void 0, false, {
        fileName: "C:\\Users\\<USER>\\Documents\\Cubentweb\\cubent\\apps\\storybook\\stories\\input-otp.stories.tsx",
        lineNumber: 32,
        columnNumber: 9
      }, undefined), /*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_repo_design_system_components_ui_input_otp__WEBPACK_IMPORTED_MODULE_2__.InputOTPSlot, {
        index: 5
      }, void 0, false, {
        fileName: "C:\\Users\\<USER>\\Documents\\Cubentweb\\cubent\\apps\\storybook\\stories\\input-otp.stories.tsx",
        lineNumber: 33,
        columnNumber: 9
      }, undefined)]
    }, void 0, true, {
      fileName: "C:\\Users\\<USER>\\Documents\\Cubentweb\\cubent\\apps\\storybook\\stories\\input-otp.stories.tsx",
      lineNumber: 27,
      columnNumber: 7
    }, undefined)
  }, void 0, false, {
    fileName: "C:\\Users\\<USER>\\Documents\\Cubentweb\\cubent\\apps\\storybook\\stories\\input-otp.stories.tsx",
    lineNumber: 26,
    columnNumber: 5
  }, undefined),
  parameters: {
    layout: 'centered',
    docs: {
      description: {
        component: "Accessible one-time password component with copy paste functionality."
      }
    }
  }
};
/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (meta);
/**
 * The default form of the InputOTP field.
 */
const Default = {};
/**
 * Use multiple groups to separate the input slots.
 */
const SeparatedGroup = {
  render: args => /*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_repo_design_system_components_ui_input_otp__WEBPACK_IMPORTED_MODULE_2__.InputOTP, {
    ...args,
    render: undefined,
    children: [/*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_repo_design_system_components_ui_input_otp__WEBPACK_IMPORTED_MODULE_2__.InputOTPGroup, {
      children: [/*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_repo_design_system_components_ui_input_otp__WEBPACK_IMPORTED_MODULE_2__.InputOTPSlot, {
        index: 0
      }, void 0, false, {
        fileName: "C:\\Users\\<USER>\\Documents\\Cubentweb\\cubent\\apps\\storybook\\stories\\input-otp.stories.tsx",
        lineNumber: 58,
        columnNumber: 9
      }, undefined), /*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_repo_design_system_components_ui_input_otp__WEBPACK_IMPORTED_MODULE_2__.InputOTPSlot, {
        index: 1
      }, void 0, false, {
        fileName: "C:\\Users\\<USER>\\Documents\\Cubentweb\\cubent\\apps\\storybook\\stories\\input-otp.stories.tsx",
        lineNumber: 59,
        columnNumber: 9
      }, undefined), /*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_repo_design_system_components_ui_input_otp__WEBPACK_IMPORTED_MODULE_2__.InputOTPSlot, {
        index: 2
      }, void 0, false, {
        fileName: "C:\\Users\\<USER>\\Documents\\Cubentweb\\cubent\\apps\\storybook\\stories\\input-otp.stories.tsx",
        lineNumber: 60,
        columnNumber: 9
      }, undefined)]
    }, void 0, true, {
      fileName: "C:\\Users\\<USER>\\Documents\\Cubentweb\\cubent\\apps\\storybook\\stories\\input-otp.stories.tsx",
      lineNumber: 57,
      columnNumber: 7
    }, undefined), /*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_repo_design_system_components_ui_input_otp__WEBPACK_IMPORTED_MODULE_2__.InputOTPSeparator, {}, void 0, false, {
      fileName: "C:\\Users\\<USER>\\Documents\\Cubentweb\\cubent\\apps\\storybook\\stories\\input-otp.stories.tsx",
      lineNumber: 62,
      columnNumber: 7
    }, undefined), /*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_repo_design_system_components_ui_input_otp__WEBPACK_IMPORTED_MODULE_2__.InputOTPGroup, {
      children: [/*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_repo_design_system_components_ui_input_otp__WEBPACK_IMPORTED_MODULE_2__.InputOTPSlot, {
        index: 3
      }, void 0, false, {
        fileName: "C:\\Users\\<USER>\\Documents\\Cubentweb\\cubent\\apps\\storybook\\stories\\input-otp.stories.tsx",
        lineNumber: 64,
        columnNumber: 9
      }, undefined), /*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_repo_design_system_components_ui_input_otp__WEBPACK_IMPORTED_MODULE_2__.InputOTPSlot, {
        index: 4
      }, void 0, false, {
        fileName: "C:\\Users\\<USER>\\Documents\\Cubentweb\\cubent\\apps\\storybook\\stories\\input-otp.stories.tsx",
        lineNumber: 65,
        columnNumber: 9
      }, undefined), /*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_repo_design_system_components_ui_input_otp__WEBPACK_IMPORTED_MODULE_2__.InputOTPSlot, {
        index: 5
      }, void 0, false, {
        fileName: "C:\\Users\\<USER>\\Documents\\Cubentweb\\cubent\\apps\\storybook\\stories\\input-otp.stories.tsx",
        lineNumber: 66,
        columnNumber: 9
      }, undefined)]
    }, void 0, true, {
      fileName: "C:\\Users\\<USER>\\Documents\\Cubentweb\\cubent\\apps\\storybook\\stories\\input-otp.stories.tsx",
      lineNumber: 63,
      columnNumber: 7
    }, undefined)]
  }, void 0, true, {
    fileName: "C:\\Users\\<USER>\\Documents\\Cubentweb\\cubent\\apps\\storybook\\stories\\input-otp.stories.tsx",
    lineNumber: 56,
    columnNumber: 5
  }, undefined)
};
;
const __namedExportsOrder = ["Default", "SeparatedGroup"];
Default.parameters = {
  ...Default.parameters,
  docs: {
    ...Default.parameters?.docs,
    source: {
      originalSource: "{}",
      ...Default.parameters?.docs?.source
    },
    description: {
      story: "The default form of the InputOTP field.",
      ...Default.parameters?.docs?.description
    }
  }
};
SeparatedGroup.parameters = {
  ...SeparatedGroup.parameters,
  docs: {
    ...SeparatedGroup.parameters?.docs,
    source: {
      originalSource: "{\n  render: args => <InputOTP {...args} render={undefined}>\r\n      <InputOTPGroup>\r\n        <InputOTPSlot index={0} />\r\n        <InputOTPSlot index={1} />\r\n        <InputOTPSlot index={2} />\r\n      </InputOTPGroup>\r\n      <InputOTPSeparator />\r\n      <InputOTPGroup>\r\n        <InputOTPSlot index={3} />\r\n        <InputOTPSlot index={4} />\r\n        <InputOTPSlot index={5} />\r\n      </InputOTPGroup>\r\n    </InputOTP>\n}",
      ...SeparatedGroup.parameters?.docs?.source
    },
    description: {
      story: "Use multiple groups to separate the input slots.",
      ...SeparatedGroup.parameters?.docs?.description
    }
  }
};

const $ReactRefreshModuleId$ = __webpack_require__.$Refresh$.moduleId;
const $ReactRefreshCurrentExports$ = __react_refresh_utils__.getModuleExports(
	$ReactRefreshModuleId$
);

function $ReactRefreshModuleRuntime$(exports) {
	if (true) {
		let errorOverlay;
		if (true) {
			errorOverlay = false;
		}
		let testMode;
		if (typeof __react_refresh_test__ !== 'undefined') {
			testMode = __react_refresh_test__;
		}
		return __react_refresh_utils__.executeRuntime(
			exports,
			$ReactRefreshModuleId$,
			module.hot,
			errorOverlay,
			testMode
		);
	}
}

if (typeof Promise !== 'undefined' && $ReactRefreshCurrentExports$ instanceof Promise) {
	$ReactRefreshCurrentExports$.then($ReactRefreshModuleRuntime$);
} else {
	$ReactRefreshModuleRuntime$($ReactRefreshCurrentExports$);
}

/***/ })

}]);
//# sourceMappingURL=input-otp-stories.iframe.bundle.js.map