{"version": 3, "file": "label-stories.iframe.bundle.js", "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAIA;AACA;;;;;;;;;;;;;;;;;;;;;;;AC5BA;;AAEA;AACA;AAEA;AAEA;AAIA;AAEA;AACA;AAIA;;;;;;AAGA;AAdA;AAgBA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACrBA;AAEA;;;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AAEA;AAIA;;;AAGA", "sources": ["webpack://storybook/../../node_modules/.pnpm/@radix-ui+react-label@2.1.7_7793a902a18004088009a6b1964436da/node_modules/@radix-ui/react-label/dist/index.mjs", "webpack://storybook/../../packages/design-system/components/ui/label.tsx", "webpack://storybook/./stories/label.stories.tsx"], "sourcesContent": ["\"use client\";\n\n// src/label.tsx\nimport * as React from \"react\";\nimport { Primitive } from \"@radix-ui/react-primitive\";\nimport { jsx } from \"react/jsx-runtime\";\nvar NAME = \"Label\";\nvar Label = React.forwardRef((props, forwardedRef) => {\n  return /* @__PURE__ */ jsx(\n    Primitive.label,\n    {\n      ...props,\n      ref: forwardedRef,\n      onMouseDown: (event) => {\n        const target = event.target;\n        if (target.closest(\"button, input, select, textarea\")) return;\n        props.onMouseDown?.(event);\n        if (!event.defaultPrevented && event.detail > 1) event.preventDefault();\n      }\n    }\n  );\n});\nLabel.displayName = NAME;\nvar Root = Label;\nexport {\n  Label,\n  Root\n};\n//# sourceMappingURL=index.mjs.map\n", "\"use client\"\n\nimport * as React from \"react\"\nimport * as LabelPrimitive from \"@radix-ui/react-label\"\n\nimport { cn } from \"@repo/design-system/lib/utils\"\n\nfunction Label({\n  className,\n  ...props\n}: React.ComponentProps<typeof LabelPrimitive.Root>) {\n  return (\n    <LabelPrimitive.Root\n      data-slot=\"label\"\n      className={cn(\n        \"flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Label }\n", "import type { Meta, StoryObj } from '@storybook/react';\n\nimport { Label } from '@repo/design-system/components/ui/label';\n\n/**\n * Renders an accessible label associated with controls.\n */\nconst meta = {\n  title: 'ui/Label',\n  component: Label,\n  tags: ['autodocs'],\n  argTypes: {\n    children: {\n      control: { type: 'text' },\n    },\n  },\n  args: {\n    children: 'Your email address',\n    htmlFor: 'email',\n  },\n} satisfies Meta<typeof Label>;\n\nexport default meta;\n\ntype Story = StoryObj<typeof Label>;\n\n/**\n * The default form of the label.\n */\nexport const Default: Story = {};\n"], "names": [], "sourceRoot": ""}