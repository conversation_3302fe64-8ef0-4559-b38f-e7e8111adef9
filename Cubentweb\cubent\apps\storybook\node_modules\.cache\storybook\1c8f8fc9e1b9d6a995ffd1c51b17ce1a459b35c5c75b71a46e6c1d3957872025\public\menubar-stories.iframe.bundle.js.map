{"version": 3, "file": "menubar-stories.iframe.bundle.js", "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;;AAEA;AACA;AACA;AAEA;AAEA;AAIA;AAEA;AACA;AAIA;;;;;;AAGA;AAdA;AAgBA;AAGA;AAAA;AAAA;;;;;;AACA;AAJA;AAMA;AAGA;AAAA;AAAA;;;;;;AACA;AAJA;AAMA;AAGA;AAAA;AAAA;;;;;;AACA;;AAEA;AAGA;AACA;AAAA;;;;;;AAEA;AANA;AAQA;AAIA;AAEA;AACA;AAIA;;;;;;AAGA;AAdA;AAgBA;AAOA;AAEA;AACA;AACA;AACA;AACA;AACA;AAIA;;;;;;;;;;;AAIA;AAtBA;AAwBA;AASA;AAEA;AACA;AACA;AACA;AAIA;;;;;;AAGA;AArBA;AAuBA;AAMA;AAEA;AACA;AAIA;AACA;;AAEA;AAAA;AACA;AACA;AAAA;;;;;;;;;;;;;;;;AAGA;;;;;;;AAGA;AAxBA;AA0BA;AAKA;AAEA;AACA;AAIA;;AAEA;AAAA;AACA;AACA;AAAA;;;;;;;;;;;;;;;;AAGA;;;;;;;AAGA;AAtBA;AAwBA;AAOA;AAEA;AACA;AACA;AAIA;;;;;;AAGA;AAlBA;AAoBA;AAIA;AAEA;AACA;AACA;;;;;;AAGA;AAXA;AAaA;AAIA;AAEA;AACA;AAIA;;;;;;AAGA;AAdA;AAgBA;AAGA;AAAA;AAAA;;;;;;AACA;AAJA;AAMA;AAQA;AAEA;AACA;AACA;AAIA;;AAEA;AACA;AAAA;;;;;;;;;;;;AAGA;AAtBA;AAwBA;AAIA;AAEA;AACA;AAIA;;;;;;AAGA;AAdA;AAgBA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AChQA;AAkBA;;;;AAIA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AAAA;;;;;AACA;AACA;AAAA;AACA;;;;;;;;;;AAEA;AAAA;;;;;AACA;;;;AACA;AAAA;AAAA;;;;;AACA;;;;AACA;AAAA;;;;;;;;;;;;;;;;;;;;;AAKA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AAEA;AAIA;;;AAGA;AAEA;;;AAGA;AACA;AACA;AACA;AACA;AAAA;;;;;AACA;AACA;AAAA;;;;;AACA;AACA;AAAA;;;;;AACA;AACA;AAAA;;;;;AACA;AAAA;;;;;AACA;AAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOA;AAEA;;;AAGA;AACA;AACA;AACA;AACA;AAAA;;;;;AACA;AACA;AAAA;AAAA;;;;;AACA;AAAA;AACA;AAAA;AAAA;;;;;AACA;AAAA;AAAA;;;;;AACA;AAAA;AAAA;;;;;;;;;;;;;;;;;;;;;;;;;;AAMA;AAEA;;;AAGA;AACA;AACA;AACA;AACA;AAAA;;;;;AACA;AACA;AAAA;;;;;AACA;AACA;AAAA;AAAA;;;;;AACA;AAAA;AAAA;;;;;AACA;AAAA;;;;;;;;;;;;;;;;;;;;;;;;;;AAMA", "sources": ["webpack://storybook/../../packages/design-system/components/ui/menubar.tsx", "webpack://storybook/./stories/menubar.stories.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as MenubarPrimitive from \"@radix-ui/react-menubar\"\nimport { CheckIcon, ChevronRightIcon, CircleIcon } from \"lucide-react\"\n\nimport { cn } from \"@repo/design-system/lib/utils\"\n\nfunction Menubar({\n  className,\n  ...props\n}: React.ComponentProps<typeof MenubarPrimitive.Root>) {\n  return (\n    <MenubarPrimitive.Root\n      data-slot=\"menubar\"\n      className={cn(\n        \"bg-background flex h-9 items-center gap-1 rounded-md border p-1 shadow-xs\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction MenubarMenu({\n  ...props\n}: React.ComponentProps<typeof MenubarPrimitive.Menu>) {\n  return <MenubarPrimitive.Menu data-slot=\"menubar-menu\" {...props} />\n}\n\nfunction MenubarGroup({\n  ...props\n}: React.ComponentProps<typeof MenubarPrimitive.Group>) {\n  return <MenubarPrimitive.Group data-slot=\"menubar-group\" {...props} />\n}\n\nfunction MenubarPortal({\n  ...props\n}: React.ComponentProps<typeof MenubarPrimitive.Portal>) {\n  return <MenubarPrimitive.Portal data-slot=\"menubar-portal\" {...props} />\n}\n\nfunction MenubarRadioGroup({\n  ...props\n}: React.ComponentProps<typeof MenubarPrimitive.RadioGroup>) {\n  return (\n    <MenubarPrimitive.RadioGroup data-slot=\"menubar-radio-group\" {...props} />\n  )\n}\n\nfunction MenubarTrigger({\n  className,\n  ...props\n}: React.ComponentProps<typeof MenubarPrimitive.Trigger>) {\n  return (\n    <MenubarPrimitive.Trigger\n      data-slot=\"menubar-trigger\"\n      className={cn(\n        \"focus:bg-accent focus:text-accent-foreground data-[state=open]:bg-accent data-[state=open]:text-accent-foreground flex items-center rounded-sm px-2 py-1 text-sm font-medium outline-hidden select-none\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction MenubarContent({\n  className,\n  align = \"start\",\n  alignOffset = -4,\n  sideOffset = 8,\n  ...props\n}: React.ComponentProps<typeof MenubarPrimitive.Content>) {\n  return (\n    <MenubarPortal>\n      <MenubarPrimitive.Content\n        data-slot=\"menubar-content\"\n        align={align}\n        alignOffset={alignOffset}\n        sideOffset={sideOffset}\n        className={cn(\n          \"bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 min-w-[12rem] origin-(--radix-menubar-content-transform-origin) overflow-hidden rounded-md border p-1 shadow-md\",\n          className\n        )}\n        {...props}\n      />\n    </MenubarPortal>\n  )\n}\n\nfunction MenubarItem({\n  className,\n  inset,\n  variant = \"default\",\n  ...props\n}: React.ComponentProps<typeof MenubarPrimitive.Item> & {\n  inset?: boolean\n  variant?: \"default\" | \"destructive\"\n}) {\n  return (\n    <MenubarPrimitive.Item\n      data-slot=\"menubar-item\"\n      data-inset={inset}\n      data-variant={variant}\n      className={cn(\n        \"focus:bg-accent focus:text-accent-foreground data-[variant=destructive]:text-destructive data-[variant=destructive]:focus:bg-destructive/10 dark:data-[variant=destructive]:focus:bg-destructive/20 data-[variant=destructive]:focus:text-destructive data-[variant=destructive]:*:[svg]:!text-destructive [&_svg:not([class*='text-'])]:text-muted-foreground relative flex cursor-default items-center gap-2 rounded-sm px-2 py-1.5 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 data-[inset]:pl-8 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction MenubarCheckboxItem({\n  className,\n  children,\n  checked,\n  ...props\n}: React.ComponentProps<typeof MenubarPrimitive.CheckboxItem>) {\n  return (\n    <MenubarPrimitive.CheckboxItem\n      data-slot=\"menubar-checkbox-item\"\n      className={cn(\n        \"focus:bg-accent focus:text-accent-foreground relative flex cursor-default items-center gap-2 rounded-xs py-1.5 pr-2 pl-8 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\n        className\n      )}\n      checked={checked}\n      {...props}\n    >\n      <span className=\"pointer-events-none absolute left-2 flex size-3.5 items-center justify-center\">\n        <MenubarPrimitive.ItemIndicator>\n          <CheckIcon className=\"size-4\" />\n        </MenubarPrimitive.ItemIndicator>\n      </span>\n      {children}\n    </MenubarPrimitive.CheckboxItem>\n  )\n}\n\nfunction MenubarRadioItem({\n  className,\n  children,\n  ...props\n}: React.ComponentProps<typeof MenubarPrimitive.RadioItem>) {\n  return (\n    <MenubarPrimitive.RadioItem\n      data-slot=\"menubar-radio-item\"\n      className={cn(\n        \"focus:bg-accent focus:text-accent-foreground relative flex cursor-default items-center gap-2 rounded-xs py-1.5 pr-2 pl-8 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\n        className\n      )}\n      {...props}\n    >\n      <span className=\"pointer-events-none absolute left-2 flex size-3.5 items-center justify-center\">\n        <MenubarPrimitive.ItemIndicator>\n          <CircleIcon className=\"size-2 fill-current\" />\n        </MenubarPrimitive.ItemIndicator>\n      </span>\n      {children}\n    </MenubarPrimitive.RadioItem>\n  )\n}\n\nfunction MenubarLabel({\n  className,\n  inset,\n  ...props\n}: React.ComponentProps<typeof MenubarPrimitive.Label> & {\n  inset?: boolean\n}) {\n  return (\n    <MenubarPrimitive.Label\n      data-slot=\"menubar-label\"\n      data-inset={inset}\n      className={cn(\n        \"px-2 py-1.5 text-sm font-medium data-[inset]:pl-8\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction MenubarSeparator({\n  className,\n  ...props\n}: React.ComponentProps<typeof MenubarPrimitive.Separator>) {\n  return (\n    <MenubarPrimitive.Separator\n      data-slot=\"menubar-separator\"\n      className={cn(\"bg-border -mx-1 my-1 h-px\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction MenubarShortcut({\n  className,\n  ...props\n}: React.ComponentProps<\"span\">) {\n  return (\n    <span\n      data-slot=\"menubar-shortcut\"\n      className={cn(\n        \"text-muted-foreground ml-auto text-xs tracking-widest\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction MenubarSub({\n  ...props\n}: React.ComponentProps<typeof MenubarPrimitive.Sub>) {\n  return <MenubarPrimitive.Sub data-slot=\"menubar-sub\" {...props} />\n}\n\nfunction MenubarSubTrigger({\n  className,\n  inset,\n  children,\n  ...props\n}: React.ComponentProps<typeof MenubarPrimitive.SubTrigger> & {\n  inset?: boolean\n}) {\n  return (\n    <MenubarPrimitive.SubTrigger\n      data-slot=\"menubar-sub-trigger\"\n      data-inset={inset}\n      className={cn(\n        \"focus:bg-accent focus:text-accent-foreground data-[state=open]:bg-accent data-[state=open]:text-accent-foreground flex cursor-default items-center rounded-sm px-2 py-1.5 text-sm outline-none select-none data-[inset]:pl-8\",\n        className\n      )}\n      {...props}\n    >\n      {children}\n      <ChevronRightIcon className=\"ml-auto h-4 w-4\" />\n    </MenubarPrimitive.SubTrigger>\n  )\n}\n\nfunction MenubarSubContent({\n  className,\n  ...props\n}: React.ComponentProps<typeof MenubarPrimitive.SubContent>) {\n  return (\n    <MenubarPrimitive.SubContent\n      data-slot=\"menubar-sub-content\"\n      className={cn(\n        \"bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 min-w-[8rem] origin-(--radix-menubar-content-transform-origin) overflow-hidden rounded-md border p-1 shadow-lg\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport {\n  Menubar,\n  MenubarPortal,\n  MenubarMenu,\n  MenubarTrigger,\n  MenubarContent,\n  MenubarGroup,\n  MenubarSeparator,\n  MenubarLabel,\n  MenubarItem,\n  MenubarShortcut,\n  MenubarCheckboxItem,\n  MenubarRadioGroup,\n  MenubarRadioItem,\n  MenubarSub,\n  MenubarSubTrigger,\n  MenubarSubContent,\n}\n", "import type { <PERSON><PERSON>, StoryObj } from '@storybook/react';\n\nimport {\n  Menubar,\n  MenubarCheckboxItem,\n  MenubarContent,\n  MenubarGroup,\n  MenubarItem,\n  MenubarLabel,\n  MenubarMenu,\n  MenubarRadioGroup,\n  MenubarRadioItem,\n  MenubarSeparator,\n  MenubarShortcut,\n  MenubarSub,\n  MenubarSubContent,\n  MenubarSubTrigger,\n  MenubarTrigger,\n} from '@repo/design-system/components/ui/menubar';\n\n/**\n * A visually persistent menu common in desktop applications that provides\n * quick access to a consistent set of commands.\n */\nconst meta = {\n  title: 'ui/Menubar',\n  component: Menubar,\n  tags: ['autodocs'],\n  argTypes: {},\n\n  render: (args) => (\n    <Menubar {...args}>\n      <MenubarMenu>\n        <MenubarTrigger>File</MenubarTrigger>\n        <MenubarContent>\n          <MenubarItem>\n            New Tab <MenubarShortcut>⌘T</MenubarShortcut>\n          </MenubarItem>\n          <MenubarItem>New Window</MenubarItem>\n          <MenubarSeparator />\n          <MenubarItem disabled>Share</MenubarItem>\n          <MenubarSeparator />\n          <MenubarItem>Print</MenubarItem>\n        </MenubarContent>\n      </MenubarMenu>\n    </Menubar>\n  ),\n  parameters: {\n    layout: 'centered',\n  },\n} satisfies Meta<typeof Menubar>;\n\nexport default meta;\n\ntype Story = StoryObj<typeof meta>;\n\n/**\n * The default form of the menubar.\n */\nexport const Default: Story = {};\n\n/**\n * A menubar with a submenu.\n */\nexport const WithSubmenu: Story = {\n  render: (args) => (\n    <Menubar {...args}>\n      <MenubarMenu>\n        <MenubarTrigger>Actions</MenubarTrigger>\n        <MenubarContent>\n          <MenubarItem>Download</MenubarItem>\n          <MenubarSub>\n            <MenubarSubTrigger>Share</MenubarSubTrigger>\n            <MenubarSubContent>\n              <MenubarItem>Email link</MenubarItem>\n              <MenubarItem>Messages</MenubarItem>\n              <MenubarItem>Notes</MenubarItem>\n            </MenubarSubContent>\n          </MenubarSub>\n        </MenubarContent>\n      </MenubarMenu>\n    </Menubar>\n  ),\n};\n\n/**\n * A menubar with radio items.\n */\nexport const WithRadioItems: Story = {\n  render: (args) => (\n    <Menubar {...args}>\n      <MenubarMenu>\n        <MenubarTrigger>View</MenubarTrigger>\n        <MenubarContent>\n          <MenubarLabel inset>Device Size</MenubarLabel>\n          <MenubarRadioGroup value=\"md\">\n            <MenubarRadioItem value=\"sm\">Small</MenubarRadioItem>\n            <MenubarRadioItem value=\"md\">Medium</MenubarRadioItem>\n            <MenubarRadioItem value=\"lg\">Large</MenubarRadioItem>\n          </MenubarRadioGroup>\n        </MenubarContent>\n      </MenubarMenu>\n    </Menubar>\n  ),\n};\n\n/**\n * A menubar with checkbox items.\n */\nexport const WithCheckboxItems: Story = {\n  render: (args) => (\n    <Menubar {...args}>\n      <MenubarMenu>\n        <MenubarTrigger>Filters</MenubarTrigger>\n        <MenubarContent>\n          <MenubarItem>Show All</MenubarItem>\n          <MenubarGroup>\n            <MenubarCheckboxItem checked>Unread</MenubarCheckboxItem>\n            <MenubarCheckboxItem checked>Important</MenubarCheckboxItem>\n            <MenubarCheckboxItem>Flagged</MenubarCheckboxItem>\n          </MenubarGroup>\n        </MenubarContent>\n      </MenubarMenu>\n    </Menubar>\n  ),\n};\n"], "names": [], "sourceRoot": ""}