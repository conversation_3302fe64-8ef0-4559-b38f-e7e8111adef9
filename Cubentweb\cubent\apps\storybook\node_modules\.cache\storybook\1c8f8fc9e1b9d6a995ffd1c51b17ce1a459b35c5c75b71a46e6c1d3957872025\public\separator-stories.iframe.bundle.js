"use strict";
(self["webpackChunkstorybook"] = self["webpackChunkstorybook"] || []).push([["separator-stories"],{

/***/ "../../node_modules/.pnpm/@radix-ui+react-separator@1_2ce8aa190072d6fd618d2cdb4281497c/node_modules/@radix-ui/react-separator/dist/index.mjs":
/*!***************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@radix-ui+react-separator@1_2ce8aa190072d6fd618d2cdb4281497c/node_modules/@radix-ui/react-separator/dist/index.mjs ***!
  \***************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   Root: () => (/* binding */ Root),
/* harmony export */   Separator: () => (/* binding */ Separator)
/* harmony export */ });
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ "../../node_modules/.pnpm/next@15.3.2_@babel+core@7.2_09efc9edc30971f6ad8d5c21a0ab8f1f/node_modules/next/dist/compiled/react/index.js");
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);
/* harmony import */ var _radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @radix-ui/react-primitive */ "../../node_modules/.pnpm/@radix-ui+react-primitive@2_0f3a82528133a7d37b0572d0c112c6a5/node_modules/@radix-ui/react-primitive/dist/index.mjs");
/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react/jsx-runtime */ "../../node_modules/.pnpm/next@15.3.2_@babel+core@7.2_09efc9edc30971f6ad8d5c21a0ab8f1f/node_modules/next/dist/compiled/react/jsx-runtime.js");
// src/separator.tsx



var NAME = "Separator";
var DEFAULT_ORIENTATION = "horizontal";
var ORIENTATIONS = ["horizontal", "vertical"];
var Separator = react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef) => {
  const { decorative, orientation: orientationProp = DEFAULT_ORIENTATION, ...domProps } = props;
  const orientation = isValidOrientation(orientationProp) ? orientationProp : DEFAULT_ORIENTATION;
  const ariaOrientation = orientation === "vertical" ? orientation : void 0;
  const semanticProps = decorative ? { role: "none" } : { "aria-orientation": ariaOrientation, role: "separator" };
  return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(
    _radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_2__.Primitive.div,
    {
      "data-orientation": orientation,
      ...semanticProps,
      ...domProps,
      ref: forwardedRef
    }
  );
});
Separator.displayName = NAME;
function isValidOrientation(orientation) {
  return ORIENTATIONS.includes(orientation);
}
var Root = Separator;

//# sourceMappingURL=index.mjs.map


/***/ }),

/***/ "../../packages/design-system/components/ui/separator.tsx":
/*!****************************************************************!*\
  !*** ../../packages/design-system/components/ui/separator.tsx ***!
  \****************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   Separator: () => (/* binding */ Separator)
/* harmony export */ });
/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ "../../node_modules/.pnpm/next@15.3.2_@babel+core@7.2_09efc9edc30971f6ad8d5c21a0ab8f1f/node_modules/next/dist/compiled/react/jsx-dev-runtime.js");
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ "../../node_modules/.pnpm/next@15.3.2_@babel+core@7.2_09efc9edc30971f6ad8d5c21a0ab8f1f/node_modules/next/dist/compiled/react/index.js");
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);
/* harmony import */ var _radix_ui_react_separator__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @radix-ui/react-separator */ "../../node_modules/.pnpm/@radix-ui+react-separator@1_2ce8aa190072d6fd618d2cdb4281497c/node_modules/@radix-ui/react-separator/dist/index.mjs");
/* harmony import */ var _repo_design_system_lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @repo/design-system/lib/utils */ "../../packages/design-system/lib/utils.ts");
/* provided dependency */ var __react_refresh_utils__ = __webpack_require__(/*! ../../node_modules/.pnpm/@pmmmwh+react-refresh-webpa_ef086d3919e106717f56f099979b20b9/node_modules/@pmmmwh/react-refresh-webpack-plugin/lib/runtime/RefreshUtils.js */ "../../node_modules/.pnpm/@pmmmwh+react-refresh-webpa_ef086d3919e106717f56f099979b20b9/node_modules/@pmmmwh/react-refresh-webpack-plugin/lib/runtime/RefreshUtils.js");
__webpack_require__.$Refresh$.runtime = __webpack_require__(/*! ../../node_modules/.pnpm/react-refresh@0.14.2/node_modules/react-refresh/runtime.js */ "../../node_modules/.pnpm/react-refresh@0.14.2/node_modules/react-refresh/runtime.js");

"use client";




function Separator({ className, orientation = "horizontal", decorative = true, ...props }) {
    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_separator__WEBPACK_IMPORTED_MODULE_3__.Root, {
        "data-slot": "separator-root",
        decorative: decorative,
        orientation: orientation,
        className: (0,_repo_design_system_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)("bg-border shrink-0 data-[orientation=horizontal]:h-px data-[orientation=horizontal]:w-full data-[orientation=vertical]:h-full data-[orientation=vertical]:w-px", className),
        ...props
    }, void 0, false, {
        fileName: "C:\\Users\\<USER>\\Documents\\Cubentweb\\cubent\\packages\\design-system\\components\\ui\\separator.tsx",
        lineNumber: 15,
        columnNumber: 5
    }, this);
}
_c = Separator;

Separator.__docgenInfo = {
    "description": "",
    "methods": [],
    "displayName": "Separator",
    "props": {
        "orientation": {
            "defaultValue": {
                "value": "\"horizontal\"",
                "computed": false
            },
            "required": false
        },
        "decorative": {
            "defaultValue": {
                "value": "true",
                "computed": false
            },
            "required": false
        }
    }
};
var _c;
__webpack_require__.$Refresh$.register(_c, "Separator");


const $ReactRefreshModuleId$ = __webpack_require__.$Refresh$.moduleId;
const $ReactRefreshCurrentExports$ = __react_refresh_utils__.getModuleExports(
	$ReactRefreshModuleId$
);

function $ReactRefreshModuleRuntime$(exports) {
	if (true) {
		let errorOverlay;
		if (true) {
			errorOverlay = false;
		}
		let testMode;
		if (typeof __react_refresh_test__ !== 'undefined') {
			testMode = __react_refresh_test__;
		}
		return __react_refresh_utils__.executeRuntime(
			exports,
			$ReactRefreshModuleId$,
			module.hot,
			errorOverlay,
			testMode
		);
	}
}

if (typeof Promise !== 'undefined' && $ReactRefreshCurrentExports$ instanceof Promise) {
	$ReactRefreshCurrentExports$.then($ReactRefreshModuleRuntime$);
} else {
	$ReactRefreshModuleRuntime$($ReactRefreshCurrentExports$);
}

/***/ }),

/***/ "./stories/separator.stories.tsx":
/*!***************************************!*\
  !*** ./stories/separator.stories.tsx ***!
  \***************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   Horizontal: () => (/* binding */ Horizontal),
/* harmony export */   Vertical: () => (/* binding */ Vertical),
/* harmony export */   __namedExportsOrder: () => (/* binding */ __namedExportsOrder),
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ "../../node_modules/.pnpm/next@15.3.2_@babel+core@7.2_09efc9edc30971f6ad8d5c21a0ab8f1f/node_modules/next/dist/compiled/react/jsx-dev-runtime.js");
/* harmony import */ var _repo_design_system_components_ui_separator__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @repo/design-system/components/ui/separator */ "../../packages/design-system/components/ui/separator.tsx");
/* provided dependency */ var __react_refresh_utils__ = __webpack_require__(/*! ../../node_modules/.pnpm/@pmmmwh+react-refresh-webpa_ef086d3919e106717f56f099979b20b9/node_modules/@pmmmwh/react-refresh-webpack-plugin/lib/runtime/RefreshUtils.js */ "../../node_modules/.pnpm/@pmmmwh+react-refresh-webpa_ef086d3919e106717f56f099979b20b9/node_modules/@pmmmwh/react-refresh-webpack-plugin/lib/runtime/RefreshUtils.js");
__webpack_require__.$Refresh$.runtime = __webpack_require__(/*! ../../node_modules/.pnpm/react-refresh@0.14.2/node_modules/react-refresh/runtime.js */ "../../node_modules/.pnpm/react-refresh@0.14.2/node_modules/react-refresh/runtime.js");



/**
 * Visually or semantically separates content.
 */
const meta = {
  title: 'ui/Separator',
  component: _repo_design_system_components_ui_separator__WEBPACK_IMPORTED_MODULE_1__.Separator,
  tags: ['autodocs'],
  argTypes: {},
  parameters: {
    docs: {
      description: {
        component: "Visually or semantically separates content."
      }
    }
  }
};
/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (meta);
/**
 * The default form of the separator.
 */
const Horizontal = {
  render: () => /*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)("div", {
    className: "flex gap-2",
    children: [/*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)("div", {
      children: "Left"
    }, void 0, false, {
      fileName: "C:\\Users\\<USER>\\Documents\\Cubentweb\\cubent\\apps\\storybook\\stories\\separator.stories.tsx",
      lineNumber: 25,
      columnNumber: 7
    }, undefined), /*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_repo_design_system_components_ui_separator__WEBPACK_IMPORTED_MODULE_1__.Separator, {
      orientation: "vertical",
      className: "h-auto"
    }, void 0, false, {
      fileName: "C:\\Users\\<USER>\\Documents\\Cubentweb\\cubent\\apps\\storybook\\stories\\separator.stories.tsx",
      lineNumber: 26,
      columnNumber: 7
    }, undefined), /*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)("div", {
      children: "Right"
    }, void 0, false, {
      fileName: "C:\\Users\\<USER>\\Documents\\Cubentweb\\cubent\\apps\\storybook\\stories\\separator.stories.tsx",
      lineNumber: 27,
      columnNumber: 7
    }, undefined)]
  }, void 0, true, {
    fileName: "C:\\Users\\<USER>\\Documents\\Cubentweb\\cubent\\apps\\storybook\\stories\\separator.stories.tsx",
    lineNumber: 24,
    columnNumber: 5
  }, undefined)
};
/**
 * A vertical separator.
 */
const Vertical = {
  render: () => /*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)("div", {
    className: "grid gap-2",
    children: [/*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)("div", {
      children: "Top"
    }, void 0, false, {
      fileName: "C:\\Users\\<USER>\\Documents\\Cubentweb\\cubent\\apps\\storybook\\stories\\separator.stories.tsx",
      lineNumber: 38,
      columnNumber: 7
    }, undefined), /*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_repo_design_system_components_ui_separator__WEBPACK_IMPORTED_MODULE_1__.Separator, {
      orientation: "horizontal"
    }, void 0, false, {
      fileName: "C:\\Users\\<USER>\\Documents\\Cubentweb\\cubent\\apps\\storybook\\stories\\separator.stories.tsx",
      lineNumber: 39,
      columnNumber: 7
    }, undefined), /*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)("div", {
      children: "Bottom"
    }, void 0, false, {
      fileName: "C:\\Users\\<USER>\\Documents\\Cubentweb\\cubent\\apps\\storybook\\stories\\separator.stories.tsx",
      lineNumber: 40,
      columnNumber: 7
    }, undefined)]
  }, void 0, true, {
    fileName: "C:\\Users\\<USER>\\Documents\\Cubentweb\\cubent\\apps\\storybook\\stories\\separator.stories.tsx",
    lineNumber: 37,
    columnNumber: 5
  }, undefined)
};
;
const __namedExportsOrder = ["Horizontal", "Vertical"];
Horizontal.parameters = {
  ...Horizontal.parameters,
  docs: {
    ...Horizontal.parameters?.docs,
    source: {
      originalSource: "{\n  render: () => <div className=\"flex gap-2\">\r\n      <div>Left</div>\r\n      <Separator orientation=\"vertical\" className=\"h-auto\" />\r\n      <div>Right</div>\r\n    </div>\n}",
      ...Horizontal.parameters?.docs?.source
    },
    description: {
      story: "The default form of the separator.",
      ...Horizontal.parameters?.docs?.description
    }
  }
};
Vertical.parameters = {
  ...Vertical.parameters,
  docs: {
    ...Vertical.parameters?.docs,
    source: {
      originalSource: "{\n  render: () => <div className=\"grid gap-2\">\r\n      <div>Top</div>\r\n      <Separator orientation=\"horizontal\" />\r\n      <div>Bottom</div>\r\n    </div>\n}",
      ...Vertical.parameters?.docs?.source
    },
    description: {
      story: "A vertical separator.",
      ...Vertical.parameters?.docs?.description
    }
  }
};

const $ReactRefreshModuleId$ = __webpack_require__.$Refresh$.moduleId;
const $ReactRefreshCurrentExports$ = __react_refresh_utils__.getModuleExports(
	$ReactRefreshModuleId$
);

function $ReactRefreshModuleRuntime$(exports) {
	if (true) {
		let errorOverlay;
		if (true) {
			errorOverlay = false;
		}
		let testMode;
		if (typeof __react_refresh_test__ !== 'undefined') {
			testMode = __react_refresh_test__;
		}
		return __react_refresh_utils__.executeRuntime(
			exports,
			$ReactRefreshModuleId$,
			module.hot,
			errorOverlay,
			testMode
		);
	}
}

if (typeof Promise !== 'undefined' && $ReactRefreshCurrentExports$ instanceof Promise) {
	$ReactRefreshCurrentExports$.then($ReactRefreshModuleRuntime$);
} else {
	$ReactRefreshModuleRuntime$($ReactRefreshCurrentExports$);
}

/***/ })

}]);
//# sourceMappingURL=separator-stories.iframe.bundle.js.map