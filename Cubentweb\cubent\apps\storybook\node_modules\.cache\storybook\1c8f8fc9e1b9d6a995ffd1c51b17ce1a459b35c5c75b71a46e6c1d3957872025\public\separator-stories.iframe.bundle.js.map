{"version": 3, "file": "separator-stories.iframe.bundle.js", "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAIA;AACA;;;;;;;;;;;;;;;;;;;;;;;AC/BA;;AAEA;AACA;AAEA;AAEA;AAMA;AAEA;AACA;AACA;AACA;AAIA;;;;;;AAGA;AAlBA;AAoBA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACzBA;AAEA;;;AAGA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AAEA;AAIA;;;AAGA;AACA;AACA;AACA;AAAA;;;;;AACA;AAAA;AAAA;;;;;AACA;AAAA;;;;;;;;;;;AAGA;AAEA;;;AAGA;AACA;AACA;AACA;AAAA;;;;;AACA;AAAA;;;;;AACA;AAAA;;;;;;;;;;;AAGA", "sources": ["webpack://storybook/../../node_modules/.pnpm/@radix-ui+react-separator@1_2ce8aa190072d6fd618d2cdb4281497c/node_modules/@radix-ui/react-separator/dist/index.mjs", "webpack://storybook/../../packages/design-system/components/ui/separator.tsx", "webpack://storybook/./stories/separator.stories.tsx"], "sourcesContent": ["// src/separator.tsx\nimport * as React from \"react\";\nimport { Primitive } from \"@radix-ui/react-primitive\";\nimport { jsx } from \"react/jsx-runtime\";\nvar NAME = \"Separator\";\nvar DEFAULT_ORIENTATION = \"horizontal\";\nvar ORIENTATIONS = [\"horizontal\", \"vertical\"];\nvar Separator = React.forwardRef((props, forwardedRef) => {\n  const { decorative, orientation: orientationProp = DEFAULT_ORIENTATION, ...domProps } = props;\n  const orientation = isValidOrientation(orientationProp) ? orientationProp : DEFAULT_ORIENTATION;\n  const ariaOrientation = orientation === \"vertical\" ? orientation : void 0;\n  const semanticProps = decorative ? { role: \"none\" } : { \"aria-orientation\": ariaOrientation, role: \"separator\" };\n  return /* @__PURE__ */ jsx(\n    Primitive.div,\n    {\n      \"data-orientation\": orientation,\n      ...semanticProps,\n      ...domProps,\n      ref: forwardedRef\n    }\n  );\n});\nSeparator.displayName = NAME;\nfunction isValidOrientation(orientation) {\n  return ORIENTATIONS.includes(orientation);\n}\nvar Root = Separator;\nexport {\n  Root,\n  Separator\n};\n//# sourceMappingURL=index.mjs.map\n", "\"use client\"\n\nimport * as React from \"react\"\nimport * as SeparatorPrimitive from \"@radix-ui/react-separator\"\n\nimport { cn } from \"@repo/design-system/lib/utils\"\n\nfunction Separator({\n  className,\n  orientation = \"horizontal\",\n  decorative = true,\n  ...props\n}: React.ComponentProps<typeof SeparatorPrimitive.Root>) {\n  return (\n    <SeparatorPrimitive.Root\n      data-slot=\"separator-root\"\n      decorative={decorative}\n      orientation={orientation}\n      className={cn(\n        \"bg-border shrink-0 data-[orientation=horizontal]:h-px data-[orientation=horizontal]:w-full data-[orientation=vertical]:h-full data-[orientation=vertical]:w-px\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Separator }\n", "import type { Meta, StoryObj } from '@storybook/react';\n\nimport { Separator } from '@repo/design-system/components/ui/separator';\n\n/**\n * Visually or semantically separates content.\n */\nconst meta = {\n  title: 'ui/Separator',\n  component: Separator,\n  tags: ['autodocs'],\n  argTypes: {},\n} satisfies Meta<typeof Separator>;\n\nexport default meta;\n\ntype Story = StoryObj<typeof meta>;\n\n/**\n * The default form of the separator.\n */\nexport const Horizontal: Story = {\n  render: () => (\n    <div className=\"flex gap-2\">\n      <div>Left</div>\n      <Separator orientation=\"vertical\" className=\"h-auto\" />\n      <div>Right</div>\n    </div>\n  ),\n};\n\n/**\n * A vertical separator.\n */\nexport const Vertical: Story = {\n  render: () => (\n    <div className=\"grid gap-2\">\n      <div>Top</div>\n      <Separator orientation=\"horizontal\" />\n      <div>Bottom</div>\n    </div>\n  ),\n};\n"], "names": [], "sourceRoot": ""}