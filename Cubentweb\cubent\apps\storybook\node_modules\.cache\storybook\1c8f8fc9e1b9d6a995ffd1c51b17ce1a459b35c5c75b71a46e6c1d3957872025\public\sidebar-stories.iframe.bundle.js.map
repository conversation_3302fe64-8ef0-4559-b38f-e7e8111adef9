{"version": 3, "file": "sidebar-stories.iframe.bundle.js", "mappings": ";;;;;;;;;;;;;;;;;;;;;;;AAAA;;AAEA;AACA;AAEA;AAEA;AAIA;AAEA;AACA;AAIA;;;;;;AAGA;;AAEA;AAIA;AAEA;AACA;AACA;;;;;;AAGA;AAXA;AAaA;AAIA;AAEA;AACA;AAIA;;;;;;AAGA;AAdA;AAgBA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACpDA;AACA;AACA;AAEA;AAEA;AACA;AAAA;AAAA;AAAA;;;;;;AACA;AAFA;AAIA;AACA;AAEA;AACA;AAIA;;;;;;AAGA;AAXA;AAaA;AACA;AAEA;AACA;AACA;;;;;;AAGA;AARA;AAUA;AAOA;AAEA;AAEA;AACA;AACA;;;;;;AAGA;AAhBA;AAkBA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;;;;;;AAGA;AAXA;AAaA;AAKA;AAEA;AACA;AACA;AACA;AACA;AAEA;;;;;;;;;;AAGA;AAhBA;AAkBA;AAIA;AAEA;AACA;AACA;AACA;AACA;;AAEA;AAAA;;;;;;AACA;AAAA;AAAA;;;;;;;;;;;;AAGA;AAhBA;AAkBA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACpGA;AACA;AACA;AAEA;AAEA;AAGA;AACA;AACA;AAEA;AAEA;AAEA;AAEA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAGA;AAUA;AAEA;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AACA;;;;;;AAGA;AAnBA;AAqBA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC1DA;;AAEA;AAEA;AAGA;AAAA;AAAA;;;;;;AACA;AAJA;AAMA;AAGA;AAEA;AACA;;;;;;AAGA;AATA;AAWA;AAGA;AAEA;AACA;;;;;;AAGA;AATA;AAWA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AChCA;AAEA;AAEA;AACA;AAEA;AACA;AACA;AAMA;;;;;;AAGA;AAdA;AAgBA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACpBA;;AAEA;AACA;AAEA;AAEA;AAMA;AAEA;AACA;AACA;AACA;AAIA;;;;;;AAGA;AAlBA;AAoBA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC3BA;AAEA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AAOA;AACA;AAOA;AACA;AACA;AACA;AACA;AACA;AAYA;AAEA;;AACA;AACA;AACA;AACA;AAEA;AACA;AAPA;AASA;;AAaA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAGA;AACA;AAAA;AACA;AAAA;AAAA;AAAA;;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAEA;AACA;AAAA;AACA;AAAA;AACA;AAIA;AACA;AACA;AACA;;AAEA;AACA;AAAA;;AACA;AAAA;AAAA;AAAA;AAEA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAGA;AACA;AACA;AAAA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AAEA;AAIA;AAEA;;;;;;;;;;;;;;;;AAKA;;;AAnFA;;;AAbA;AAkGA;;AAYA;AAEA;AACA;AAEA;AACA;AAIA;AAEA;;;;;;AAGA;AAEA;AACA;AACA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AAEA;;AAEA;AAAA;;AACA;AAAA;;;;;;AACA;AAAA;;;;;;;;;;;;AAEA;AAAA;AAAA;;;;;;;;;;;;;;;;;AAIA;AAEA;AAEA;AACA;AACA;AACA;AACA;AACA;;AAGA;AACA;AACA;;;;;;AASA;AACA;AACA;AAMA;AAKA;AAEA;AACA;AACA;AACA;AAEA;;;;;;;;;;;;;;;;;AAKA;;;AAxFA;;;AAZA;AAsGA;;AAKA;AAEA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;;;;AACA;AAAA;AAAA;;;;;;;;;;;;AAGA;;;AAnBA;;;AALA;AA0BA;;AACA;AAEA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AASA;;;;;;AAGA;;;AAtBA;;;AADA;AAyBA;AACA;AAEA;AACA;AAKA;;;;;;AAGA;AAZA;AAcA;AAIA;AAEA;AACA;AACA;AACA;;;;;;AAGA;AAZA;AAcA;AACA;AAEA;AACA;AACA;AACA;;;;;;AAGA;AATA;AAWA;AACA;AAEA;AACA;AACA;AACA;;;;;;AAGA;AATA;AAWA;AAIA;AAEA;AACA;AACA;AACA;;;;;;AAGA;AAZA;AAcA;AACA;AAEA;AACA;AACA;AAIA;;;;;;AAGA;AAZA;AAcA;AACA;AAEA;AACA;AACA;AACA;;;;;;AAGA;AATA;AAWA;AAKA;AAEA;AAEA;AACA;AACA;AAKA;;;;;;AAGA;AAnBA;AAqBA;AAKA;AAEA;AAEA;AACA;AACA;AAGA;AAIA;;;;;;AAGA;AArBA;AAuBA;AAIA;AAEA;AACA;AACA;AACA;;;;;;AAGA;AAZA;AAcA;AACA;AAEA;AACA;AACA;AACA;;;;;;AAGA;AATA;AAWA;AACA;AAEA;AACA;AACA;AACA;;;;;;AAGA;AATA;AAWA;AAGA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAGA;;AAaA;AACA;AAEA;AAEA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AACA;;;;;;AAIA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AAEA;;AAEA;AAAA;AAAA;;;;;;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;AAIA;;;AAlCA;;;AAdA;AAkDA;AASA;AAEA;AAEA;AACA;AACA;AAGA;AASA;;;;;;AAGA;AA9BA;AAgCA;AAIA;AAEA;AACA;AACA;AASA;;;;;;AAGA;AApBA;AAsBA;;AAOA;AACA;AAAA;AACA;AACA;AAAA;AAEA;AAEA;AACA;AACA;AACA;;AAEA;AAEA;AACA;;;;;;AAGA;AACA;AACA;AACA;AAEA;AACA;;;;;;;;;;;;AAKA;;AApCA;AAsCA;AACA;AAEA;AACA;AACA;AAKA;;;;;;AAGA;AAbA;AAeA;AAIA;AAEA;AACA;AACA;AACA;;;;;;AAGA;;AAEA;AAWA;AAEA;AAEA;AACA;AACA;AACA;AACA;AAQA;;;;;;AAGA;;AAEA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC5rBA;AAEA;AACA;AAEA;AACA;AACA;;;;;;AAGA;AARA;AAUA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACZA;AAEA;AAEA;AACA;AAEA;AAAA;AACA;AACA;AAAA;AACA;AACA;;AACA;AACA;AACA;AAAA;;AACA;AAAA;AAEA;AACA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACDA;AAUA;AAKA;AAQA;AAKA;AAUA;AACA;AAmBA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AAIA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AAEA;AAEA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AAEA;AACA;AACA;AAEA;AACA;AACA;AAEA;AAEA;AACA;AACA;AACA;AAEA;AACA;AACA;AAEA;AACA;AACA;AAEA;AACA;AACA;AAEA;AAEA;AACA;AACA;AACA;AAEA;AACA;AACA;AAEA;AACA;AACA;AAEA;AACA;AACA;AAEA;AACA;AACA;AAEA;AAEA;AACA;AACA;AACA;AAEA;AACA;AACA;AAEA;AACA;AACA;AAEA;AACA;AACA;AAEA;AACA;AACA;AAEA;AAEA;AAEA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AAEA;AAEA;AACA;;AACA;AAEA;AAEA;AAAA;AACA;AACA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AAEA;AAAA;AACA;AAAA;;;;;;;;;;AAEA;AAAA;AACA;AAAA;AACA;;;;;AAEA;AAAA;AACA;;;;;;;;;;AAGA;AAAA;;;;;;;;;;;;;;;AAGA;AACA;AACA;AACA;AACA;AAEA;AAAA;AAAA;;;;;AAGA;AAGA;AACA;AAEA;AAAA;AACA;AAAA;;;;;;;;;;AAEA;AACA;;;;;;AARA;;;;AAaA;;;;AACA;AAAA;AACA;AAAA;AACA;AAAA;;;;;;;;;;AAEA;AAAA;AAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASA;AACA;AACA;AAAA;;;;;AACA;AACA;AAiBA;AAhBA;AAEA;AACA;AACA;AAEA;AACA;AAAA;AACA;AAAA;AACA;;;;AACA;AAAA;;;;;AACA;AAAA;;;;;;;;;;;;;;;AAGA;AACA;AACA;AAEA;AAAA;AACA;AAAA;AACA;AAAA;;;;;;;;;;;;;;;;AAHA;;;;;;;;;;;;;;;;;;;;AAhBA;;;;;;;;;;;;;;;AA+BA;AAAA;AACA;AAAA;;;;;AACA;AACA;AAEA;AAAA;AACA;AAAA;AACA;;;;AACA;AAAA;;;;;;;;;;;;;;;AAGA;AACA;AAAA;AACA;AAAA;AACA;;;;AACA;AAAA;AAAA;;;;;;;;;;;;;;;AAGA;AACA;AACA;AACA;AAEA;AACA;AAAA;;;;;AACA;AAAA;;;;;;;;;;AAEA;AACA;AAAA;;;;;AACA;AAAA;;;;;;;;;;AAEA;;;;AACA;AACA;AAAA;;;;;AACA;AAAA;;;;;;;;;;;;;;;;;;;;;AA9BA;;;;AAoCA;AACA;AAAA;AACA;AAAA;;;;;AACA;AAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAMA;AACA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AAEA;AAAA;AACA;AACA;AACA;;;;;AAEA;AAAA;AAAA;;;;;;;;;;AAIA;AAAA;AACA;AAAA;AACA;;;;;AAEA;AAAA;AACA;;;;;;;;;;AAGA;AAAA;;;;;;;;;;;;;;;AAGA;AACA;AACA;AACA;AACA;AAEA;AAAA;AACA;AAAA;AACA;AAAA;AACA;AACA;AACA;;;;;AAEA;AAAA;AAAA;;;;;;;;;;AAIA;AAAA;AACA;AAAA;AACA;;;;;AAEA;AAAA;AACA;;;;;;;;;;;;;;;;;;;;AAKA;;;;AACA;AACA;AACA;;;;AAAA;;;;;;;;;;AAIA;;;;AACA;AACA;AACA;;;;AAAA;;;;;AAGA;AACA;;;;AAAA;;;;;AAGA;AACA;;;;AAAA;;;;;;;;;;AAIA;;;;AACA;AACA;;;;AAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQA;;;;;;;;;AAEA;AACA;AAAA;AACA;AAAA;AACA;AAAA;;;;;AACA;AAAA;AAAA;;;;;AACA;AACA;AACA;AAAA;AACA;AAAA;AAAA;;;;;;;;;;AAIA;AAAA;;;;;AACA;AACA;AAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAMA;AAAA;AACA;AAAA;AACA;AAAA;;;;;AACA;AAAA;;;;;AACA;AAAA;;;;;;;;;;AAEA;AAAA;;;;;;;;;;;;;;;;;;;;;AAKA;AACA;AACA", "sources": ["webpack://storybook/../../packages/design-system/components/ui/avatar.tsx", "webpack://storybook/../../packages/design-system/components/ui/breadcrumb.tsx", "webpack://storybook/../../packages/design-system/components/ui/button.tsx", "webpack://storybook/../../packages/design-system/components/ui/collapsible.tsx", "webpack://storybook/../../packages/design-system/components/ui/input.tsx", "webpack://storybook/../../packages/design-system/components/ui/separator.tsx", "webpack://storybook/../../packages/design-system/components/ui/sidebar.tsx", "webpack://storybook/../../packages/design-system/components/ui/skeleton.tsx", "webpack://storybook/../../packages/design-system/hooks/use-mobile.ts", "webpack://storybook/./stories/sidebar.stories.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as AvatarPrimitive from \"@radix-ui/react-avatar\"\n\nimport { cn } from \"@repo/design-system/lib/utils\"\n\nfunction Avatar({\n  className,\n  ...props\n}: React.ComponentProps<typeof AvatarPrimitive.Root>) {\n  return (\n    <AvatarPrimitive.Root\n      data-slot=\"avatar\"\n      className={cn(\n        \"relative flex size-8 shrink-0 overflow-hidden rounded-full\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction AvatarImage({\n  className,\n  ...props\n}: React.ComponentProps<typeof AvatarPrimitive.Image>) {\n  return (\n    <AvatarPrimitive.Image\n      data-slot=\"avatar-image\"\n      className={cn(\"aspect-square size-full\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction AvatarFallback({\n  className,\n  ...props\n}: React.ComponentProps<typeof AvatarPrimitive.Fallback>) {\n  return (\n    <AvatarPrimitive.Fallback\n      data-slot=\"avatar-fallback\"\n      className={cn(\n        \"bg-muted flex size-full items-center justify-center rounded-full\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Avatar, AvatarImage, AvatarFallback }\n", "import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { ChevronRight, MoreHorizontal } from \"lucide-react\"\n\nimport { cn } from \"@repo/design-system/lib/utils\"\n\nfunction Breadcrumb({ ...props }: React.ComponentProps<\"nav\">) {\n  return <nav aria-label=\"breadcrumb\" data-slot=\"breadcrumb\" {...props} />\n}\n\nfunction BreadcrumbList({ className, ...props }: React.ComponentProps<\"ol\">) {\n  return (\n    <ol\n      data-slot=\"breadcrumb-list\"\n      className={cn(\n        \"text-muted-foreground flex flex-wrap items-center gap-1.5 text-sm break-words sm:gap-2.5\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction BreadcrumbItem({ className, ...props }: React.ComponentProps<\"li\">) {\n  return (\n    <li\n      data-slot=\"breadcrumb-item\"\n      className={cn(\"inline-flex items-center gap-1.5\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction BreadcrumbLink({\n  asChild,\n  className,\n  ...props\n}: React.ComponentProps<\"a\"> & {\n  asChild?: boolean\n}) {\n  const Comp = asChild ? Slot : \"a\"\n\n  return (\n    <Comp\n      data-slot=\"breadcrumb-link\"\n      className={cn(\"hover:text-foreground transition-colors\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction BreadcrumbPage({ className, ...props }: React.ComponentProps<\"span\">) {\n  return (\n    <span\n      data-slot=\"breadcrumb-page\"\n      role=\"link\"\n      aria-disabled=\"true\"\n      aria-current=\"page\"\n      className={cn(\"text-foreground font-normal\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction BreadcrumbSeparator({\n  children,\n  className,\n  ...props\n}: React.ComponentProps<\"li\">) {\n  return (\n    <li\n      data-slot=\"breadcrumb-separator\"\n      role=\"presentation\"\n      aria-hidden=\"true\"\n      className={cn(\"[&>svg]:size-3.5\", className)}\n      {...props}\n    >\n      {children ?? <ChevronRight />}\n    </li>\n  )\n}\n\nfunction BreadcrumbEllipsis({\n  className,\n  ...props\n}: React.ComponentProps<\"span\">) {\n  return (\n    <span\n      data-slot=\"breadcrumb-ellipsis\"\n      role=\"presentation\"\n      aria-hidden=\"true\"\n      className={cn(\"flex size-9 items-center justify-center\", className)}\n      {...props}\n    >\n      <MoreHorizontal className=\"size-4\" />\n      <span className=\"sr-only\">More</span>\n    </span>\n  )\n}\n\nexport {\n  Breadcrumb,\n  BreadcrumbList,\n  BreadcrumbItem,\n  BreadcrumbLink,\n  BreadcrumbPage,\n  BreadcrumbSeparator,\n  BreadcrumbEllipsis,\n}\n", "import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@repo/design-system/lib/utils\"\n\nconst buttonVariants = cva(\n  \"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90\",\n        destructive:\n          \"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\n        outline:\n          \"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50\",\n        secondary:\n          \"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80\",\n        ghost:\n          \"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50\",\n        link: \"text-primary underline-offset-4 hover:underline\",\n      },\n      size: {\n        default: \"h-9 px-4 py-2 has-[>svg]:px-3\",\n        sm: \"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5\",\n        lg: \"h-10 rounded-md px-6 has-[>svg]:px-4\",\n        icon: \"size-9\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  }\n)\n\nfunction Button({\n  className,\n  variant,\n  size,\n  asChild = false,\n  ...props\n}: React.ComponentProps<\"button\"> &\n  VariantProps<typeof buttonVariants> & {\n    asChild?: boolean\n  }) {\n  const Comp = asChild ? Slot : \"button\"\n\n  return (\n    <Comp\n      data-slot=\"button\"\n      className={cn(buttonVariants({ variant, size, className }))}\n      {...props}\n    />\n  )\n}\n\nexport { Button, buttonVariants }\n", "\"use client\"\n\nimport * as CollapsiblePrimitive from \"@radix-ui/react-collapsible\"\n\nfunction Collapsible({\n  ...props\n}: React.ComponentProps<typeof CollapsiblePrimitive.Root>) {\n  return <CollapsiblePrimitive.Root data-slot=\"collapsible\" {...props} />\n}\n\nfunction CollapsibleTrigger({\n  ...props\n}: React.ComponentProps<typeof CollapsiblePrimitive.CollapsibleTrigger>) {\n  return (\n    <CollapsiblePrimitive.CollapsibleTrigger\n      data-slot=\"collapsible-trigger\"\n      {...props}\n    />\n  )\n}\n\nfunction CollapsibleContent({\n  ...props\n}: React.ComponentProps<typeof CollapsiblePrimitive.CollapsibleContent>) {\n  return (\n    <CollapsiblePrimitive.CollapsibleContent\n      data-slot=\"collapsible-content\"\n      {...props}\n    />\n  )\n}\n\nexport { Collapsible, CollapsibleTrigger, CollapsibleContent }\n", "import * as React from \"react\"\n\nimport { cn } from \"@repo/design-system/lib/utils\"\n\nfunction Input({ className, type, ...props }: React.ComponentProps<\"input\">) {\n  return (\n    <input\n      type={type}\n      data-slot=\"input\"\n      className={cn(\n        \"file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm\",\n        \"focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]\",\n        \"aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Input }\n", "\"use client\"\n\nimport * as React from \"react\"\nimport * as SeparatorPrimitive from \"@radix-ui/react-separator\"\n\nimport { cn } from \"@repo/design-system/lib/utils\"\n\nfunction Separator({\n  className,\n  orientation = \"horizontal\",\n  decorative = true,\n  ...props\n}: React.ComponentProps<typeof SeparatorPrimitive.Root>) {\n  return (\n    <SeparatorPrimitive.Root\n      data-slot=\"separator-root\"\n      decorative={decorative}\n      orientation={orientation}\n      className={cn(\n        \"bg-border shrink-0 data-[orientation=horizontal]:h-px data-[orientation=horizontal]:w-full data-[orientation=vertical]:h-full data-[orientation=vertical]:w-px\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Separator }\n", "\"use client\"\n\nimport * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { VariantProps, cva } from \"class-variance-authority\"\nimport { PanelLeftIcon } from \"lucide-react\"\n\nimport { useIsMobile } from \"@repo/design-system/hooks/use-mobile\"\nimport { cn } from \"@repo/design-system/lib/utils\"\nimport { Button } from \"@repo/design-system/components/ui/button\"\nimport { Input } from \"@repo/design-system/components/ui/input\"\nimport { Separator } from \"@repo/design-system/components/ui/separator\"\nimport {\n  Sheet,\n  SheetContent,\n  SheetDescription,\n  SheetHeader,\n  SheetTitle,\n} from \"@repo/design-system/components/ui/sheet\"\nimport { Skeleton } from \"@repo/design-system/components/ui/skeleton\"\nimport {\n  Tooltip,\n  TooltipContent,\n  TooltipProvider,\n  TooltipTrigger,\n} from \"@repo/design-system/components/ui/tooltip\"\n\nconst SIDEBAR_COOKIE_NAME = \"sidebar_state\"\nconst SIDEBAR_COOKIE_MAX_AGE = 60 * 60 * 24 * 7\nconst SIDEBAR_WIDTH = \"16rem\"\nconst SIDEBAR_WIDTH_MOBILE = \"18rem\"\nconst SIDEBAR_WIDTH_ICON = \"3rem\"\nconst SIDEBAR_KEYBOARD_SHORTCUT = \"b\"\n\ntype SidebarContextProps = {\n  state: \"expanded\" | \"collapsed\"\n  open: boolean\n  setOpen: (open: boolean) => void\n  openMobile: boolean\n  setOpenMobile: (open: boolean) => void\n  isMobile: boolean\n  toggleSidebar: () => void\n}\n\nconst SidebarContext = React.createContext<SidebarContextProps | null>(null)\n\nfunction useSidebar() {\n  const context = React.useContext(SidebarContext)\n  if (!context) {\n    throw new Error(\"useSidebar must be used within a SidebarProvider.\")\n  }\n\n  return context\n}\n\nfunction SidebarProvider({\n  defaultOpen = true,\n  open: openProp,\n  onOpenChange: setOpenProp,\n  className,\n  style,\n  children,\n  ...props\n}: React.ComponentProps<\"div\"> & {\n  defaultOpen?: boolean\n  open?: boolean\n  onOpenChange?: (open: boolean) => void\n}) {\n  const isMobile = useIsMobile()\n  const [openMobile, setOpenMobile] = React.useState(false)\n\n  // This is the internal state of the sidebar.\n  // We use openProp and setOpenProp for control from outside the component.\n  const [_open, _setOpen] = React.useState(defaultOpen)\n  const open = openProp ?? _open\n  const setOpen = React.useCallback(\n    (value: boolean | ((value: boolean) => boolean)) => {\n      const openState = typeof value === \"function\" ? value(open) : value\n      if (setOpenProp) {\n        setOpenProp(openState)\n      } else {\n        _setOpen(openState)\n      }\n\n      // This sets the cookie to keep the sidebar state.\n      document.cookie = `${SIDEBAR_COOKIE_NAME}=${openState}; path=/; max-age=${SIDEBAR_COOKIE_MAX_AGE}`\n    },\n    [setOpenProp, open]\n  )\n\n  // Helper to toggle the sidebar.\n  const toggleSidebar = React.useCallback(() => {\n    return isMobile ? setOpenMobile((open) => !open) : setOpen((open) => !open)\n  }, [isMobile, setOpen, setOpenMobile])\n\n  // Adds a keyboard shortcut to toggle the sidebar.\n  React.useEffect(() => {\n    const handleKeyDown = (event: KeyboardEvent) => {\n      if (\n        event.key === SIDEBAR_KEYBOARD_SHORTCUT &&\n        (event.metaKey || event.ctrlKey)\n      ) {\n        event.preventDefault()\n        toggleSidebar()\n      }\n    }\n\n    window.addEventListener(\"keydown\", handleKeyDown)\n    return () => window.removeEventListener(\"keydown\", handleKeyDown)\n  }, [toggleSidebar])\n\n  // We add a state so that we can do data-state=\"expanded\" or \"collapsed\".\n  // This makes it easier to style the sidebar with Tailwind classes.\n  const state = open ? \"expanded\" : \"collapsed\"\n\n  const contextValue = React.useMemo<SidebarContextProps>(\n    () => ({\n      state,\n      open,\n      setOpen,\n      isMobile,\n      openMobile,\n      setOpenMobile,\n      toggleSidebar,\n    }),\n    [state, open, setOpen, isMobile, openMobile, setOpenMobile, toggleSidebar]\n  )\n\n  return (\n    <SidebarContext.Provider value={contextValue}>\n      <TooltipProvider delayDuration={0}>\n        <div\n          data-slot=\"sidebar-wrapper\"\n          style={\n            {\n              \"--sidebar-width\": SIDEBAR_WIDTH,\n              \"--sidebar-width-icon\": SIDEBAR_WIDTH_ICON,\n              ...style,\n            } as React.CSSProperties\n          }\n          className={cn(\n            \"group/sidebar-wrapper has-data-[variant=inset]:bg-sidebar flex min-h-svh w-full\",\n            className\n          )}\n          {...props}\n        >\n          {children}\n        </div>\n      </TooltipProvider>\n    </SidebarContext.Provider>\n  )\n}\n\nfunction Sidebar({\n  side = \"left\",\n  variant = \"sidebar\",\n  collapsible = \"offcanvas\",\n  className,\n  children,\n  ...props\n}: React.ComponentProps<\"div\"> & {\n  side?: \"left\" | \"right\"\n  variant?: \"sidebar\" | \"floating\" | \"inset\"\n  collapsible?: \"offcanvas\" | \"icon\" | \"none\"\n}) {\n  const { isMobile, state, openMobile, setOpenMobile } = useSidebar()\n\n  if (collapsible === \"none\") {\n    return (\n      <div\n        data-slot=\"sidebar\"\n        className={cn(\n          \"bg-sidebar text-sidebar-foreground flex h-full w-(--sidebar-width) flex-col\",\n          className\n        )}\n        {...props}\n      >\n        {children}\n      </div>\n    )\n  }\n\n  if (isMobile) {\n    return (\n      <Sheet open={openMobile} onOpenChange={setOpenMobile} {...props}>\n        <SheetContent\n          data-sidebar=\"sidebar\"\n          data-slot=\"sidebar\"\n          data-mobile=\"true\"\n          className=\"bg-sidebar text-sidebar-foreground w-(--sidebar-width) p-0 [&>button]:hidden\"\n          style={\n            {\n              \"--sidebar-width\": SIDEBAR_WIDTH_MOBILE,\n            } as React.CSSProperties\n          }\n          side={side}\n        >\n          <SheetHeader className=\"sr-only\">\n            <SheetTitle>Sidebar</SheetTitle>\n            <SheetDescription>Displays the mobile sidebar.</SheetDescription>\n          </SheetHeader>\n          <div className=\"flex h-full w-full flex-col\">{children}</div>\n        </SheetContent>\n      </Sheet>\n    )\n  }\n\n  return (\n    <div\n      className=\"group peer text-sidebar-foreground hidden md:block\"\n      data-state={state}\n      data-collapsible={state === \"collapsed\" ? collapsible : \"\"}\n      data-variant={variant}\n      data-side={side}\n      data-slot=\"sidebar\"\n    >\n      {/* This is what handles the sidebar gap on desktop */}\n      <div\n        data-slot=\"sidebar-gap\"\n        className={cn(\n          \"relative w-(--sidebar-width) bg-transparent transition-[width] duration-200 ease-linear\",\n          \"group-data-[collapsible=offcanvas]:w-0\",\n          \"group-data-[side=right]:rotate-180\",\n          variant === \"floating\" || variant === \"inset\"\n            ? \"group-data-[collapsible=icon]:w-[calc(var(--sidebar-width-icon)+(--spacing(4)))]\"\n            : \"group-data-[collapsible=icon]:w-(--sidebar-width-icon)\"\n        )}\n      />\n      <div\n        data-slot=\"sidebar-container\"\n        className={cn(\n          \"fixed inset-y-0 z-10 hidden h-svh w-(--sidebar-width) transition-[left,right,width] duration-200 ease-linear md:flex\",\n          side === \"left\"\n            ? \"left-0 group-data-[collapsible=offcanvas]:left-[calc(var(--sidebar-width)*-1)]\"\n            : \"right-0 group-data-[collapsible=offcanvas]:right-[calc(var(--sidebar-width)*-1)]\",\n          // Adjust the padding for floating and inset variants.\n          variant === \"floating\" || variant === \"inset\"\n            ? \"p-2 group-data-[collapsible=icon]:w-[calc(var(--sidebar-width-icon)+(--spacing(4))+2px)]\"\n            : \"group-data-[collapsible=icon]:w-(--sidebar-width-icon) group-data-[side=left]:border-r group-data-[side=right]:border-l\",\n          className\n        )}\n        {...props}\n      >\n        <div\n          data-sidebar=\"sidebar\"\n          data-slot=\"sidebar-inner\"\n          className=\"bg-sidebar group-data-[variant=floating]:border-sidebar-border flex h-full w-full flex-col group-data-[variant=floating]:rounded-lg group-data-[variant=floating]:border group-data-[variant=floating]:shadow-sm\"\n        >\n          {children}\n        </div>\n      </div>\n    </div>\n  )\n}\n\nfunction SidebarTrigger({\n  className,\n  onClick,\n  ...props\n}: React.ComponentProps<typeof Button>) {\n  const { toggleSidebar } = useSidebar()\n\n  return (\n    <Button\n      data-sidebar=\"trigger\"\n      data-slot=\"sidebar-trigger\"\n      variant=\"ghost\"\n      size=\"icon\"\n      className={cn(\"size-7\", className)}\n      onClick={(event) => {\n        onClick?.(event)\n        toggleSidebar()\n      }}\n      {...props}\n    >\n      <PanelLeftIcon />\n      <span className=\"sr-only\">Toggle Sidebar</span>\n    </Button>\n  )\n}\n\nfunction SidebarRail({ className, ...props }: React.ComponentProps<\"button\">) {\n  const { toggleSidebar } = useSidebar()\n\n  return (\n    <button\n      data-sidebar=\"rail\"\n      data-slot=\"sidebar-rail\"\n      aria-label=\"Toggle Sidebar\"\n      tabIndex={-1}\n      onClick={toggleSidebar}\n      title=\"Toggle Sidebar\"\n      className={cn(\n        \"hover:after:bg-sidebar-border absolute inset-y-0 z-20 hidden w-4 -translate-x-1/2 transition-all ease-linear group-data-[side=left]:-right-4 group-data-[side=right]:left-0 after:absolute after:inset-y-0 after:left-1/2 after:w-[2px] sm:flex\",\n        \"in-data-[side=left]:cursor-w-resize in-data-[side=right]:cursor-e-resize\",\n        \"[[data-side=left][data-state=collapsed]_&]:cursor-e-resize [[data-side=right][data-state=collapsed]_&]:cursor-w-resize\",\n        \"hover:group-data-[collapsible=offcanvas]:bg-sidebar group-data-[collapsible=offcanvas]:translate-x-0 group-data-[collapsible=offcanvas]:after:left-full\",\n        \"[[data-side=left][data-collapsible=offcanvas]_&]:-right-2\",\n        \"[[data-side=right][data-collapsible=offcanvas]_&]:-left-2\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction SidebarInset({ className, ...props }: React.ComponentProps<\"main\">) {\n  return (\n    <main\n      data-slot=\"sidebar-inset\"\n      className={cn(\n        \"bg-background relative flex w-full flex-1 flex-col\",\n        \"md:peer-data-[variant=inset]:m-2 md:peer-data-[variant=inset]:ml-0 md:peer-data-[variant=inset]:rounded-xl md:peer-data-[variant=inset]:shadow-sm md:peer-data-[variant=inset]:peer-data-[state=collapsed]:ml-2\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction SidebarInput({\n  className,\n  ...props\n}: React.ComponentProps<typeof Input>) {\n  return (\n    <Input\n      data-slot=\"sidebar-input\"\n      data-sidebar=\"input\"\n      className={cn(\"bg-background h-8 w-full shadow-none\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction SidebarHeader({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"sidebar-header\"\n      data-sidebar=\"header\"\n      className={cn(\"flex flex-col gap-2 p-2\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction SidebarFooter({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"sidebar-footer\"\n      data-sidebar=\"footer\"\n      className={cn(\"flex flex-col gap-2 p-2\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction SidebarSeparator({\n  className,\n  ...props\n}: React.ComponentProps<typeof Separator>) {\n  return (\n    <Separator\n      data-slot=\"sidebar-separator\"\n      data-sidebar=\"separator\"\n      className={cn(\"bg-sidebar-border mx-2 w-auto\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction SidebarContent({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"sidebar-content\"\n      data-sidebar=\"content\"\n      className={cn(\n        \"flex min-h-0 flex-1 flex-col gap-2 overflow-auto group-data-[collapsible=icon]:overflow-hidden\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction SidebarGroup({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"sidebar-group\"\n      data-sidebar=\"group\"\n      className={cn(\"relative flex w-full min-w-0 flex-col p-2\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction SidebarGroupLabel({\n  className,\n  asChild = false,\n  ...props\n}: React.ComponentProps<\"div\"> & { asChild?: boolean }) {\n  const Comp = asChild ? Slot : \"div\"\n\n  return (\n    <Comp\n      data-slot=\"sidebar-group-label\"\n      data-sidebar=\"group-label\"\n      className={cn(\n        \"text-sidebar-foreground/70 ring-sidebar-ring flex h-8 shrink-0 items-center rounded-md px-2 text-xs font-medium outline-hidden transition-[margin,opacity] duration-200 ease-linear focus-visible:ring-2 [&>svg]:size-4 [&>svg]:shrink-0\",\n        \"group-data-[collapsible=icon]:-mt-8 group-data-[collapsible=icon]:opacity-0\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction SidebarGroupAction({\n  className,\n  asChild = false,\n  ...props\n}: React.ComponentProps<\"button\"> & { asChild?: boolean }) {\n  const Comp = asChild ? Slot : \"button\"\n\n  return (\n    <Comp\n      data-slot=\"sidebar-group-action\"\n      data-sidebar=\"group-action\"\n      className={cn(\n        \"text-sidebar-foreground ring-sidebar-ring hover:bg-sidebar-accent hover:text-sidebar-accent-foreground absolute top-3.5 right-3 flex aspect-square w-5 items-center justify-center rounded-md p-0 outline-hidden transition-transform focus-visible:ring-2 [&>svg]:size-4 [&>svg]:shrink-0\",\n        // Increases the hit area of the button on mobile.\n        \"after:absolute after:-inset-2 md:after:hidden\",\n        \"group-data-[collapsible=icon]:hidden\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction SidebarGroupContent({\n  className,\n  ...props\n}: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"sidebar-group-content\"\n      data-sidebar=\"group-content\"\n      className={cn(\"w-full text-sm\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction SidebarMenu({ className, ...props }: React.ComponentProps<\"ul\">) {\n  return (\n    <ul\n      data-slot=\"sidebar-menu\"\n      data-sidebar=\"menu\"\n      className={cn(\"flex w-full min-w-0 flex-col gap-1\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction SidebarMenuItem({ className, ...props }: React.ComponentProps<\"li\">) {\n  return (\n    <li\n      data-slot=\"sidebar-menu-item\"\n      data-sidebar=\"menu-item\"\n      className={cn(\"group/menu-item relative\", className)}\n      {...props}\n    />\n  )\n}\n\nconst sidebarMenuButtonVariants = cva(\n  \"peer/menu-button flex w-full items-center gap-2 overflow-hidden rounded-md p-2 text-left text-sm outline-hidden ring-sidebar-ring transition-[width,height,padding] hover:bg-sidebar-accent hover:text-sidebar-accent-foreground focus-visible:ring-2 active:bg-sidebar-accent active:text-sidebar-accent-foreground disabled:pointer-events-none disabled:opacity-50 group-has-data-[sidebar=menu-action]/menu-item:pr-8 aria-disabled:pointer-events-none aria-disabled:opacity-50 data-[active=true]:bg-sidebar-accent data-[active=true]:font-medium data-[active=true]:text-sidebar-accent-foreground data-[state=open]:hover:bg-sidebar-accent data-[state=open]:hover:text-sidebar-accent-foreground group-data-[collapsible=icon]:size-8! group-data-[collapsible=icon]:p-2! [&>span:last-child]:truncate [&>svg]:size-4 [&>svg]:shrink-0\",\n  {\n    variants: {\n      variant: {\n        default: \"hover:bg-sidebar-accent hover:text-sidebar-accent-foreground\",\n        outline:\n          \"bg-background shadow-[0_0_0_1px_hsl(var(--sidebar-border))] hover:bg-sidebar-accent hover:text-sidebar-accent-foreground hover:shadow-[0_0_0_1px_hsl(var(--sidebar-accent))]\",\n      },\n      size: {\n        default: \"h-8 text-sm\",\n        sm: \"h-7 text-xs\",\n        lg: \"h-12 text-sm group-data-[collapsible=icon]:p-0!\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  }\n)\n\nfunction SidebarMenuButton({\n  asChild = false,\n  isActive = false,\n  variant = \"default\",\n  size = \"default\",\n  tooltip,\n  className,\n  ...props\n}: React.ComponentProps<\"button\"> & {\n  asChild?: boolean\n  isActive?: boolean\n  tooltip?: string | React.ComponentProps<typeof TooltipContent>\n} & VariantProps<typeof sidebarMenuButtonVariants>) {\n  const Comp = asChild ? Slot : \"button\"\n  const { isMobile, state } = useSidebar()\n\n  const button = (\n    <Comp\n      data-slot=\"sidebar-menu-button\"\n      data-sidebar=\"menu-button\"\n      data-size={size}\n      data-active={isActive}\n      className={cn(sidebarMenuButtonVariants({ variant, size }), className)}\n      {...props}\n    />\n  )\n\n  if (!tooltip) {\n    return button\n  }\n\n  if (typeof tooltip === \"string\") {\n    tooltip = {\n      children: tooltip,\n    }\n  }\n\n  return (\n    <Tooltip>\n      <TooltipTrigger asChild>{button}</TooltipTrigger>\n      <TooltipContent\n        side=\"right\"\n        align=\"center\"\n        hidden={state !== \"collapsed\" || isMobile}\n        {...tooltip}\n      />\n    </Tooltip>\n  )\n}\n\nfunction SidebarMenuAction({\n  className,\n  asChild = false,\n  showOnHover = false,\n  ...props\n}: React.ComponentProps<\"button\"> & {\n  asChild?: boolean\n  showOnHover?: boolean\n}) {\n  const Comp = asChild ? Slot : \"button\"\n\n  return (\n    <Comp\n      data-slot=\"sidebar-menu-action\"\n      data-sidebar=\"menu-action\"\n      className={cn(\n        \"text-sidebar-foreground ring-sidebar-ring hover:bg-sidebar-accent hover:text-sidebar-accent-foreground peer-hover/menu-button:text-sidebar-accent-foreground absolute top-1.5 right-1 flex aspect-square w-5 items-center justify-center rounded-md p-0 outline-hidden transition-transform focus-visible:ring-2 [&>svg]:size-4 [&>svg]:shrink-0\",\n        // Increases the hit area of the button on mobile.\n        \"after:absolute after:-inset-2 md:after:hidden\",\n        \"peer-data-[size=sm]/menu-button:top-1\",\n        \"peer-data-[size=default]/menu-button:top-1.5\",\n        \"peer-data-[size=lg]/menu-button:top-2.5\",\n        \"group-data-[collapsible=icon]:hidden\",\n        showOnHover &&\n          \"peer-data-[active=true]/menu-button:text-sidebar-accent-foreground group-focus-within/menu-item:opacity-100 group-hover/menu-item:opacity-100 data-[state=open]:opacity-100 md:opacity-0\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction SidebarMenuBadge({\n  className,\n  ...props\n}: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"sidebar-menu-badge\"\n      data-sidebar=\"menu-badge\"\n      className={cn(\n        \"text-sidebar-foreground pointer-events-none absolute right-1 flex h-5 min-w-5 items-center justify-center rounded-md px-1 text-xs font-medium tabular-nums select-none\",\n        \"peer-hover/menu-button:text-sidebar-accent-foreground peer-data-[active=true]/menu-button:text-sidebar-accent-foreground\",\n        \"peer-data-[size=sm]/menu-button:top-1\",\n        \"peer-data-[size=default]/menu-button:top-1.5\",\n        \"peer-data-[size=lg]/menu-button:top-2.5\",\n        \"group-data-[collapsible=icon]:hidden\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction SidebarMenuSkeleton({\n  className,\n  showIcon = false,\n  ...props\n}: React.ComponentProps<\"div\"> & {\n  showIcon?: boolean\n}) {\n  // Random width between 50 to 90%.\n  const width = React.useMemo(() => {\n    return `${Math.floor(Math.random() * 40) + 50}%`\n  }, [])\n\n  return (\n    <div\n      data-slot=\"sidebar-menu-skeleton\"\n      data-sidebar=\"menu-skeleton\"\n      className={cn(\"flex h-8 items-center gap-2 rounded-md px-2\", className)}\n      {...props}\n    >\n      {showIcon && (\n        <Skeleton\n          className=\"size-4 rounded-md\"\n          data-sidebar=\"menu-skeleton-icon\"\n        />\n      )}\n      <Skeleton\n        className=\"h-4 max-w-(--skeleton-width) flex-1\"\n        data-sidebar=\"menu-skeleton-text\"\n        style={\n          {\n            \"--skeleton-width\": width,\n          } as React.CSSProperties\n        }\n      />\n    </div>\n  )\n}\n\nfunction SidebarMenuSub({ className, ...props }: React.ComponentProps<\"ul\">) {\n  return (\n    <ul\n      data-slot=\"sidebar-menu-sub\"\n      data-sidebar=\"menu-sub\"\n      className={cn(\n        \"border-sidebar-border mx-3.5 flex min-w-0 translate-x-px flex-col gap-1 border-l px-2.5 py-0.5\",\n        \"group-data-[collapsible=icon]:hidden\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction SidebarMenuSubItem({\n  className,\n  ...props\n}: React.ComponentProps<\"li\">) {\n  return (\n    <li\n      data-slot=\"sidebar-menu-sub-item\"\n      data-sidebar=\"menu-sub-item\"\n      className={cn(\"group/menu-sub-item relative\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction SidebarMenuSubButton({\n  asChild = false,\n  size = \"md\",\n  isActive = false,\n  className,\n  ...props\n}: React.ComponentProps<\"a\"> & {\n  asChild?: boolean\n  size?: \"sm\" | \"md\"\n  isActive?: boolean\n}) {\n  const Comp = asChild ? Slot : \"a\"\n\n  return (\n    <Comp\n      data-slot=\"sidebar-menu-sub-button\"\n      data-sidebar=\"menu-sub-button\"\n      data-size={size}\n      data-active={isActive}\n      className={cn(\n        \"text-sidebar-foreground ring-sidebar-ring hover:bg-sidebar-accent hover:text-sidebar-accent-foreground active:bg-sidebar-accent active:text-sidebar-accent-foreground [&>svg]:text-sidebar-accent-foreground flex h-7 min-w-0 -translate-x-px items-center gap-2 overflow-hidden rounded-md px-2 outline-hidden focus-visible:ring-2 disabled:pointer-events-none disabled:opacity-50 aria-disabled:pointer-events-none aria-disabled:opacity-50 [&>span:last-child]:truncate [&>svg]:size-4 [&>svg]:shrink-0\",\n        \"data-[active=true]:bg-sidebar-accent data-[active=true]:text-sidebar-accent-foreground\",\n        size === \"sm\" && \"text-xs\",\n        size === \"md\" && \"text-sm\",\n        \"group-data-[collapsible=icon]:hidden\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport {\n  Sidebar,\n  SidebarContent,\n  SidebarFooter,\n  SidebarGroup,\n  SidebarGroupAction,\n  SidebarGroupContent,\n  SidebarGroupLabel,\n  SidebarHeader,\n  SidebarInput,\n  SidebarInset,\n  SidebarMenu,\n  SidebarMenuAction,\n  SidebarMenuBadge,\n  SidebarMenuButton,\n  SidebarMenuItem,\n  SidebarMenuSkeleton,\n  SidebarMenuSub,\n  SidebarMenuSubButton,\n  SidebarMenuSubItem,\n  SidebarProvider,\n  SidebarRail,\n  SidebarSeparator,\n  SidebarTrigger,\n  useSidebar,\n}\n", "import { cn } from \"@repo/design-system/lib/utils\"\n\nfunction Skeleton({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"skeleton\"\n      className={cn(\"bg-accent animate-pulse rounded-md\", className)}\n      {...props}\n    />\n  )\n}\n\nexport { Skeleton }\n", "import * as React from \"react\"\n\nconst MO<PERSON>LE_BREAKPOINT = 768\n\nexport function useIsMobile() {\n  const [isMobile, setIsMobile] = React.useState<boolean | undefined>(undefined)\n\n  React.useEffect(() => {\n    const mql = window.matchMedia(`(max-width: ${MOBILE_BREAKPOINT - 1}px)`)\n    const onChange = () => {\n      setIsMobile(window.innerWidth < MOBILE_BREAKPOINT)\n    }\n    mql.addEventListener(\"change\", onChange)\n    setIsMobile(window.innerWidth < MOBILE_BREAKPOINT)\n    return () => mql.removeEventListener(\"change\", onChange)\n  }, [])\n\n  return !!isMobile\n}\n", "import type { <PERSON><PERSON>, StoryObj } from '@storybook/react';\nimport {\n  AudioWaveform,\n  BadgeCheck,\n  Bell,\n  BookOpen,\n  Bot,\n  ChevronRight,\n  ChevronsUpDown,\n  Command,\n  CreditCard,\n  Folder,\n  Forward,\n  Frame,\n  GalleryVerticalEnd,\n  LogOut,\n  // biome-ignore lint/suspicious/noShadowRestrictedNames: \"icon name\"\n  Map,\n  MoreHorizontal,\n  PieChart,\n  Plus,\n  Settings2,\n  Sparkles,\n  SquareTerminal,\n  Trash2,\n} from 'lucide-react';\n\nimport {\n  Avatar,\n  AvatarFallback,\n  AvatarImage,\n} from '@repo/design-system/components/ui/avatar';\nimport {\n  Breadcrumb,\n  BreadcrumbItem,\n  BreadcrumbLink,\n  BreadcrumbList,\n  BreadcrumbPage,\n  BreadcrumbSeparator,\n} from '@repo/design-system/components/ui/breadcrumb';\nimport {\n  Collapsible,\n  CollapsibleContent,\n  CollapsibleTrigger,\n} from '@repo/design-system/components/ui/collapsible';\nimport {\n  DropdownMenu,\n  DropdownMenuContent,\n  DropdownMenuGroup,\n  DropdownMenuItem,\n  DropdownMenuLabel,\n  DropdownMenuSeparator,\n  DropdownMenuShortcut,\n  DropdownMenuTrigger,\n} from '@repo/design-system/components/ui/dropdown-menu';\nimport { Separator } from '@repo/design-system/components/ui/separator';\nimport {\n  Sidebar,\n  SidebarContent,\n  SidebarFooter,\n  SidebarGroup,\n  SidebarGroupLabel,\n  SidebarHeader,\n  SidebarInset,\n  SidebarMenu,\n  SidebarMenuAction,\n  SidebarMenuButton,\n  SidebarMenuItem,\n  SidebarMenuSub,\n  SidebarMenuSubButton,\n  SidebarMenuSubItem,\n  SidebarProvider,\n  SidebarRail,\n  SidebarTrigger,\n} from '@repo/design-system/components/ui/sidebar';\nimport { useState } from 'react';\n\nconst meta: Meta<typeof Sidebar> = {\n  title: 'ui/Sidebar',\n  component: Sidebar,\n  tags: ['autodocs'],\n  argTypes: {},\n};\nexport default meta;\n\ntype Story = StoryObj<typeof Sidebar>;\n\nconst data = {\n  user: {\n    name: 'shadcn',\n    email: '<EMAIL>',\n    avatar: '/avatars/shadcn.jpg',\n  },\n  teams: [\n    {\n      name: 'Acme Inc',\n      logo: GalleryVerticalEnd,\n      plan: 'Enterprise',\n    },\n    {\n      name: 'Acme Corp.',\n      logo: AudioWaveform,\n      plan: 'Startup',\n    },\n    {\n      name: 'Evil Corp.',\n      logo: Command,\n      plan: 'Free',\n    },\n  ],\n  navMain: [\n    {\n      title: 'Playground',\n      url: '#',\n      icon: SquareTerminal,\n      isActive: true,\n      items: [\n        {\n          title: 'History',\n          url: '#',\n        },\n        {\n          title: 'Starred',\n          url: '#',\n        },\n        {\n          title: 'Settings',\n          url: '#',\n        },\n      ],\n    },\n    {\n      title: 'Models',\n      url: '#',\n      icon: Bot,\n      items: [\n        {\n          title: 'Genesis',\n          url: '#',\n        },\n        {\n          title: 'Explorer',\n          url: '#',\n        },\n        {\n          title: 'Quantum',\n          url: '#',\n        },\n      ],\n    },\n    {\n      title: 'Documentation',\n      url: '#',\n      icon: BookOpen,\n      items: [\n        {\n          title: 'Introduction',\n          url: '#',\n        },\n        {\n          title: 'Get Started',\n          url: '#',\n        },\n        {\n          title: 'Tutorials',\n          url: '#',\n        },\n        {\n          title: 'Changelog',\n          url: '#',\n        },\n      ],\n    },\n    {\n      title: 'Settings',\n      url: '#',\n      icon: Settings2,\n      items: [\n        {\n          title: 'General',\n          url: '#',\n        },\n        {\n          title: 'Team',\n          url: '#',\n        },\n        {\n          title: 'Billing',\n          url: '#',\n        },\n        {\n          title: 'Limits',\n          url: '#',\n        },\n      ],\n    },\n  ],\n  projects: [\n    {\n      name: 'Design Engineering',\n      url: '#',\n      icon: Frame,\n    },\n    {\n      name: 'Sales & Marketing',\n      url: '#',\n      icon: PieChart,\n    },\n    {\n      name: 'Travel',\n      url: '#',\n      icon: Map,\n    },\n  ],\n};\n\nexport const Base: Story = {\n  render: () => {\n    const [activeTeam, setActiveTeam] = useState(data.teams[0]);\n\n    return (\n      <SidebarProvider>\n        <Sidebar collapsible=\"icon\">\n          <SidebarHeader>\n            <SidebarMenu>\n              <SidebarMenuItem>\n                <DropdownMenu>\n                  <DropdownMenuTrigger asChild>\n                    <SidebarMenuButton\n                      size=\"lg\"\n                      className=\"data-[state=open]:bg-sidebar-accent data-[state=open]:text-sidebar-accent-foreground\"\n                    >\n                      <div className=\"flex aspect-square size-8 items-center justify-center rounded-lg bg-sidebar-primary text-sidebar-primary-foreground\">\n                        <activeTeam.logo className=\"size-4\" />\n                      </div>\n                      <div className=\"grid flex-1 text-left text-sm leading-tight\">\n                        <span className=\"truncate font-semibold\">\n                          {activeTeam.name}\n                        </span>\n                        <span className=\"truncate text-xs\">\n                          {activeTeam.plan}\n                        </span>\n                      </div>\n                      <ChevronsUpDown className=\"ml-auto\" />\n                    </SidebarMenuButton>\n                  </DropdownMenuTrigger>\n                  <DropdownMenuContent\n                    className=\"w-[--radix-dropdown-menu-trigger-width] min-w-56 rounded-lg\"\n                    align=\"start\"\n                    side=\"bottom\"\n                    sideOffset={4}\n                  >\n                    <DropdownMenuLabel className=\"text-muted-foreground text-xs\">\n                      Teams\n                    </DropdownMenuLabel>\n                    {data.teams.map((team, index) => (\n                      <DropdownMenuItem\n                        key={team.name}\n                        onClick={() => setActiveTeam(team)}\n                        className=\"gap-2 p-2\"\n                      >\n                        <div className=\"flex size-6 items-center justify-center rounded-sm border\">\n                          <team.logo className=\"size-4 shrink-0\" />\n                        </div>\n                        {team.name}\n                        <DropdownMenuShortcut>\n                          ⌘{index + 1}\n                        </DropdownMenuShortcut>\n                      </DropdownMenuItem>\n                    ))}\n                    <DropdownMenuSeparator />\n                    <DropdownMenuItem className=\"gap-2 p-2\">\n                      <div className=\"flex size-6 items-center justify-center rounded-md border bg-background\">\n                        <Plus className=\"size-4\" />\n                      </div>\n                      <div className=\"font-medium text-muted-foreground\">\n                        Add team\n                      </div>\n                    </DropdownMenuItem>\n                  </DropdownMenuContent>\n                </DropdownMenu>\n              </SidebarMenuItem>\n            </SidebarMenu>\n          </SidebarHeader>\n          <SidebarContent>\n            <SidebarGroup>\n              <SidebarGroupLabel>Platform</SidebarGroupLabel>\n              <SidebarMenu>\n                {data.navMain.map((item) => (\n                  <Collapsible\n                    key={item.title}\n                    asChild\n                    defaultOpen={item.isActive}\n                    className=\"group/collapsible\"\n                  >\n                    <SidebarMenuItem>\n                      <CollapsibleTrigger asChild>\n                        <SidebarMenuButton tooltip={item.title}>\n                          {item.icon && <item.icon />}\n                          <span>{item.title}</span>\n                          <ChevronRight className=\"ml-auto transition-transform duration-200 group-data-[state=open]/collapsible:rotate-90\" />\n                        </SidebarMenuButton>\n                      </CollapsibleTrigger>\n                      <CollapsibleContent>\n                        <SidebarMenuSub>\n                          {item.items?.map((subItem) => (\n                            <SidebarMenuSubItem key={subItem.title}>\n                              <SidebarMenuSubButton asChild>\n                                <a href={subItem.url}>\n                                  <span>{subItem.title}</span>\n                                </a>\n                              </SidebarMenuSubButton>\n                            </SidebarMenuSubItem>\n                          ))}\n                        </SidebarMenuSub>\n                      </CollapsibleContent>\n                    </SidebarMenuItem>\n                  </Collapsible>\n                ))}\n              </SidebarMenu>\n            </SidebarGroup>\n            <SidebarGroup className=\"group-data-[collapsible=icon]:hidden\">\n              <SidebarGroupLabel>Projects</SidebarGroupLabel>\n              <SidebarMenu>\n                {data.projects.map((item) => (\n                  <SidebarMenuItem key={item.name}>\n                    <SidebarMenuButton asChild>\n                      <a href={item.url}>\n                        <item.icon />\n                        <span>{item.name}</span>\n                      </a>\n                    </SidebarMenuButton>\n                    <DropdownMenu>\n                      <DropdownMenuTrigger asChild>\n                        <SidebarMenuAction showOnHover>\n                          <MoreHorizontal />\n                          <span className=\"sr-only\">More</span>\n                        </SidebarMenuAction>\n                      </DropdownMenuTrigger>\n                      <DropdownMenuContent\n                        className=\"w-48 rounded-lg\"\n                        side=\"bottom\"\n                        align=\"end\"\n                      >\n                        <DropdownMenuItem>\n                          <Folder className=\"text-muted-foreground\" />\n                          <span>View Project</span>\n                        </DropdownMenuItem>\n                        <DropdownMenuItem>\n                          <Forward className=\"text-muted-foreground\" />\n                          <span>Share Project</span>\n                        </DropdownMenuItem>\n                        <DropdownMenuSeparator />\n                        <DropdownMenuItem>\n                          <Trash2 className=\"text-muted-foreground\" />\n                          <span>Delete Project</span>\n                        </DropdownMenuItem>\n                      </DropdownMenuContent>\n                    </DropdownMenu>\n                  </SidebarMenuItem>\n                ))}\n                <SidebarMenuItem>\n                  <SidebarMenuButton className=\"text-sidebar-foreground/70\">\n                    <MoreHorizontal className=\"text-sidebar-foreground/70\" />\n                    <span>More</span>\n                  </SidebarMenuButton>\n                </SidebarMenuItem>\n              </SidebarMenu>\n            </SidebarGroup>\n          </SidebarContent>\n          <SidebarFooter>\n            <SidebarMenu>\n              <SidebarMenuItem>\n                <DropdownMenu>\n                  <DropdownMenuTrigger asChild>\n                    <SidebarMenuButton\n                      size=\"lg\"\n                      className=\"data-[state=open]:bg-sidebar-accent data-[state=open]:text-sidebar-accent-foreground\"\n                    >\n                      <Avatar className=\"h-8 w-8 rounded-lg\">\n                        <AvatarImage\n                          src={data.user.avatar}\n                          alt={data.user.name}\n                        />\n                        <AvatarFallback className=\"rounded-lg\">\n                          CN\n                        </AvatarFallback>\n                      </Avatar>\n                      <div className=\"grid flex-1 text-left text-sm leading-tight\">\n                        <span className=\"truncate font-semibold\">\n                          {data.user.name}\n                        </span>\n                        <span className=\"truncate text-xs\">\n                          {data.user.email}\n                        </span>\n                      </div>\n                      <ChevronsUpDown className=\"ml-auto size-4\" />\n                    </SidebarMenuButton>\n                  </DropdownMenuTrigger>\n                  <DropdownMenuContent\n                    className=\"w-[--radix-dropdown-menu-trigger-width] min-w-56 rounded-lg\"\n                    side=\"bottom\"\n                    align=\"end\"\n                    sideOffset={4}\n                  >\n                    <DropdownMenuLabel className=\"p-0 font-normal\">\n                      <div className=\"flex items-center gap-2 px-1 py-1.5 text-left text-sm\">\n                        <Avatar className=\"h-8 w-8 rounded-lg\">\n                          <AvatarImage\n                            src={data.user.avatar}\n                            alt={data.user.name}\n                          />\n                          <AvatarFallback className=\"rounded-lg\">\n                            CN\n                          </AvatarFallback>\n                        </Avatar>\n                        <div className=\"grid flex-1 text-left text-sm leading-tight\">\n                          <span className=\"truncate font-semibold\">\n                            {data.user.name}\n                          </span>\n                          <span className=\"truncate text-xs\">\n                            {data.user.email}\n                          </span>\n                        </div>\n                      </div>\n                    </DropdownMenuLabel>\n                    <DropdownMenuSeparator />\n                    <DropdownMenuGroup>\n                      <DropdownMenuItem>\n                        <Sparkles />\n                        Upgrade to Pro\n                      </DropdownMenuItem>\n                    </DropdownMenuGroup>\n                    <DropdownMenuSeparator />\n                    <DropdownMenuGroup>\n                      <DropdownMenuItem>\n                        <BadgeCheck />\n                        Account\n                      </DropdownMenuItem>\n                      <DropdownMenuItem>\n                        <CreditCard />\n                        Billing\n                      </DropdownMenuItem>\n                      <DropdownMenuItem>\n                        <Bell />\n                        Notifications\n                      </DropdownMenuItem>\n                    </DropdownMenuGroup>\n                    <DropdownMenuSeparator />\n                    <DropdownMenuItem>\n                      <LogOut />\n                      Log out\n                    </DropdownMenuItem>\n                  </DropdownMenuContent>\n                </DropdownMenu>\n              </SidebarMenuItem>\n            </SidebarMenu>\n          </SidebarFooter>\n          <SidebarRail />\n        </Sidebar>\n        <SidebarInset>\n          <header className=\"flex h-16 shrink-0 items-center gap-2 transition-[width,height] ease-linear group-has-[[data-collapsible=icon]]/sidebar-wrapper:h-12\">\n            <div className=\"flex items-center gap-2 px-4\">\n              <SidebarTrigger className=\"-ml-1\" />\n              <Separator orientation=\"vertical\" className=\"mr-2 h-4\" />\n              <Breadcrumb>\n                <BreadcrumbList>\n                  <BreadcrumbItem className=\"hidden md:block\">\n                    <BreadcrumbLink href=\"#\">\n                      Building Your Application\n                    </BreadcrumbLink>\n                  </BreadcrumbItem>\n                  <BreadcrumbSeparator className=\"hidden md:block\" />\n                  <BreadcrumbItem>\n                    <BreadcrumbPage>Data Fetching</BreadcrumbPage>\n                  </BreadcrumbItem>\n                </BreadcrumbList>\n              </Breadcrumb>\n            </div>\n          </header>\n          <div className=\"flex flex-1 flex-col gap-4 p-4 pt-0\">\n            <div className=\"grid auto-rows-min gap-4 md:grid-cols-3\">\n              <div className=\"aspect-video rounded-xl bg-muted/50\" />\n              <div className=\"aspect-video rounded-xl bg-muted/50\" />\n              <div className=\"aspect-video rounded-xl bg-muted/50\" />\n            </div>\n            <div className=\"min-h-[100vh] flex-1 rounded-xl bg-muted/50 md:min-h-min\" />\n          </div>\n        </SidebarInset>\n      </SidebarProvider>\n    );\n  },\n  args: {},\n};\n"], "names": [], "sourceRoot": ""}