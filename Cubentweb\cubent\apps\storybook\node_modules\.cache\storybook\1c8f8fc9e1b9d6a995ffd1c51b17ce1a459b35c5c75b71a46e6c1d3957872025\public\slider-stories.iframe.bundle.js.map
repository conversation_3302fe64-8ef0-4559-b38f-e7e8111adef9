{"version": 3, "file": "slider-stories.iframe.bundle.js", "mappings": ";;;;;;;;;;;;;;;;;;;;;;;AAAA;AAEA;AACA;AAEA;AAEA;;AAQA;AACA;AAKA;AAAA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAGA;AAEA;AACA;AACA;AACA;AACA;AACA;AAIA;;AAEA;AACA;AACA;AAIA;AACA;AACA;;;;;;;;;;;AAKA;AAAA;AAAA;AAEA;AAEA;AADA;;;;;;;;;;;AAMA;;AArDA;AAuDA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC5DA;AAEA;;;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AAEA;AAIA;;;AAGA;AAEA;;;AAGA;AACA;AACA;AACA;AACA;AAEA;;;AAGA;AACA;AACA;AACA;AACA", "sources": ["webpack://storybook/../../packages/design-system/components/ui/slider.tsx", "webpack://storybook/./stories/slider.stories.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as SliderPrimitive from \"@radix-ui/react-slider\"\n\nimport { cn } from \"@repo/design-system/lib/utils\"\n\nfunction Slider({\n  className,\n  defaultValue,\n  value,\n  min = 0,\n  max = 100,\n  ...props\n}: React.ComponentProps<typeof SliderPrimitive.Root>) {\n  const _values = React.useMemo(\n    () =>\n      Array.isArray(value)\n        ? value\n        : Array.isArray(defaultValue)\n          ? defaultValue\n          : [min, max],\n    [value, defaultValue, min, max]\n  )\n\n  return (\n    <SliderPrimitive.Root\n      data-slot=\"slider\"\n      defaultValue={defaultValue}\n      value={value}\n      min={min}\n      max={max}\n      className={cn(\n        \"relative flex w-full touch-none items-center select-none data-[disabled]:opacity-50 data-[orientation=vertical]:h-full data-[orientation=vertical]:min-h-44 data-[orientation=vertical]:w-auto data-[orientation=vertical]:flex-col\",\n        className\n      )}\n      {...props}\n    >\n      <SliderPrimitive.Track\n        data-slot=\"slider-track\"\n        className={cn(\n          \"bg-muted relative grow overflow-hidden rounded-full data-[orientation=horizontal]:h-1.5 data-[orientation=horizontal]:w-full data-[orientation=vertical]:h-full data-[orientation=vertical]:w-1.5\"\n        )}\n      >\n        <SliderPrimitive.Range\n          data-slot=\"slider-range\"\n          className={cn(\n            \"bg-primary absolute data-[orientation=horizontal]:h-full data-[orientation=vertical]:w-full\"\n          )}\n        />\n      </SliderPrimitive.Track>\n      {Array.from({ length: _values.length }, (_, index) => (\n        <SliderPrimitive.Thumb\n          data-slot=\"slider-thumb\"\n          key={index}\n          className=\"border-primary bg-background ring-ring/50 block size-4 shrink-0 rounded-full border shadow-sm transition-[color,box-shadow] hover:ring-4 focus-visible:ring-4 focus-visible:outline-hidden disabled:pointer-events-none disabled:opacity-50\"\n        />\n      ))}\n    </SliderPrimitive.Root>\n  )\n}\n\nexport { Slider }\n", "import type { Meta, StoryObj } from '@storybook/react';\n\nimport { Slider } from '@repo/design-system/components/ui/slider';\n\n/**\n * An input where the user selects a value from within a given range.\n */\nconst meta = {\n  title: 'ui/Slider',\n  component: Slider,\n  tags: ['autodocs'],\n  argTypes: {},\n  args: {\n    defaultValue: [33],\n    max: 100,\n    step: 1,\n  },\n} satisfies Meta<typeof Slider>;\n\nexport default meta;\n\ntype Story = StoryObj<typeof meta>;\n\n/**\n * The default form of the slider.\n */\nexport const Default: Story = {};\n\n/**\n * Use the `inverted` prop to have the slider fill from right to left.\n */\nexport const Inverted: Story = {\n  args: {\n    inverted: true,\n  },\n};\n\n/**\n * Use the `disabled` prop to disable the slider.\n */\nexport const Disabled: Story = {\n  args: {\n    disabled: true,\n  },\n};\n"], "names": [], "sourceRoot": ""}