{"version": 3, "file": "tabs-stories.iframe.bundle.js", "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;AAAA;;AAEA;AACA;AAEA;AAEA;AAIA;AAEA;AACA;AACA;;;;;;AAGA;AAXA;AAaA;AAIA;AAEA;AACA;AAIA;;;;;;AAGA;AAdA;AAgBA;AAIA;AAEA;AACA;AAIA;;;;;;AAGA;AAdA;AAgBA;AAIA;AAEA;AACA;AACA;;;;;;AAGA;AAXA;AAaA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC/DA;AAOA;;;;AAIA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AACA;AAAA;AAAA;;;;;AACA;AAAA;AAAA;;;;;;;;;;AAEA;AAAA;AAAA;;;;;AAGA;AAAA;AAAA;;;;;;;;;;;AAGA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AAEA;AAIA;;;AAGA", "sources": ["webpack://storybook/../../packages/design-system/components/ui/tabs.tsx", "webpack://storybook/./stories/tabs.stories.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as TabsPrimitive from \"@radix-ui/react-tabs\"\n\nimport { cn } from \"@repo/design-system/lib/utils\"\n\nfunction Tabs({\n  className,\n  ...props\n}: React.ComponentProps<typeof TabsPrimitive.Root>) {\n  return (\n    <TabsPrimitive.Root\n      data-slot=\"tabs\"\n      className={cn(\"flex flex-col gap-2\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction TabsList({\n  className,\n  ...props\n}: React.ComponentProps<typeof TabsPrimitive.List>) {\n  return (\n    <TabsPrimitive.List\n      data-slot=\"tabs-list\"\n      className={cn(\n        \"bg-muted text-muted-foreground inline-flex h-9 w-fit items-center justify-center rounded-lg p-[3px]\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction TabsTrigger({\n  className,\n  ...props\n}: React.ComponentProps<typeof TabsPrimitive.Trigger>) {\n  return (\n    <TabsPrimitive.Trigger\n      data-slot=\"tabs-trigger\"\n      className={cn(\n        \"data-[state=active]:bg-background dark:data-[state=active]:text-foreground focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:outline-ring dark:data-[state=active]:border-input dark:data-[state=active]:bg-input/30 text-foreground dark:text-muted-foreground inline-flex h-[calc(100%-1px)] flex-1 items-center justify-center gap-1.5 rounded-md border border-transparent px-2 py-1 text-sm font-medium whitespace-nowrap transition-[color,box-shadow] focus-visible:ring-[3px] focus-visible:outline-1 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:shadow-sm [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction TabsContent({\n  className,\n  ...props\n}: React.ComponentProps<typeof TabsPrimitive.Content>) {\n  return (\n    <TabsPrimitive.Content\n      data-slot=\"tabs-content\"\n      className={cn(\"flex-1 outline-none\", className)}\n      {...props}\n    />\n  )\n}\n\nexport { Tabs, TabsList, TabsTrigger, TabsContent }\n", "import type { <PERSON><PERSON>, StoryObj } from '@storybook/react';\n\nimport {\n  Ta<PERSON>,\n  <PERSON><PERSON><PERSON>ontent,\n  <PERSON><PERSON><PERSON><PERSON>,\n  TabsTrigger,\n} from '@repo/design-system/components/ui/tabs';\n\n/**\n * A set of layered sections of content—known as tab panels—that are displayed\n * one at a time.\n */\nconst meta = {\n  title: 'ui/Tabs',\n  component: Tabs,\n  tags: ['autodocs'],\n  argTypes: {},\n  args: {\n    defaultValue: 'account',\n    className: 'w-96',\n  },\n  render: (args) => (\n    <Tabs {...args}>\n      <TabsList className=\"grid grid-cols-2\">\n        <TabsTrigger value=\"account\">Account</TabsTrigger>\n        <TabsTrigger value=\"password\">Password</TabsTrigger>\n      </TabsList>\n      <TabsContent value=\"account\">\n        Make changes to your account here.\n      </TabsContent>\n      <TabsContent value=\"password\">Change your password here.</TabsContent>\n    </Tabs>\n  ),\n  parameters: {\n    layout: 'centered',\n  },\n} satisfies Meta<typeof Tabs>;\n\nexport default meta;\n\ntype Story = StoryObj<typeof meta>;\n\n/**\n * The default form of the tabs.\n */\nexport const Default: Story = {};\n"], "names": [], "sourceRoot": ""}