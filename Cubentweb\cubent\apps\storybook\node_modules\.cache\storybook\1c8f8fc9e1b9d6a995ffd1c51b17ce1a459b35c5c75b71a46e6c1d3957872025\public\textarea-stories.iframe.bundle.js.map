{"version": 3, "file": "textarea-stories.iframe.bundle.js", "mappings": ";;;;;;;;;;;;;;;;;;;;;AAAA;AAEA;AAEA;AACA;AAEA;AACA;AAIA;;;;;;AAGA;AAXA;AAaA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACfA;AAEA;;;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AAEA;AAIA;;;AAGA;AAEA;;;AAGA;AACA;AACA;AACA;AACA;AAEA;;;;AAIA;AACA;AACA;AACA;AAAA;AAAA;;;;;AACA;AAAA;AAAA;;;;;;;;;;;AAGA;AAEA;;;;AAIA;AACA;AACA;AACA;AAAA;AAAA;;;;;AACA;AAAA;AAAA;;;;;AACA;AAAA;AAAA;;;;;;;;;;;AAKA;AAEA;;;;AAIA;AACA;AACA;AACA;AAAA;;;;;AACA;AACA;AACA;AACA;;;;;;;;;;;AAKA", "sources": ["webpack://storybook/../../packages/design-system/components/ui/textarea.tsx", "webpack://storybook/./stories/textarea.stories.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@repo/design-system/lib/utils\"\n\nfunction Textarea({ className, ...props }: React.ComponentProps<\"textarea\">) {\n  return (\n    <textarea\n      data-slot=\"textarea\"\n      className={cn(\n        \"border-input placeholder:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 flex field-sizing-content min-h-16 w-full rounded-md border bg-transparent px-3 py-2 text-base shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 md:text-sm\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Textarea }\n", "import type { Meta, StoryObj } from '@storybook/react';\n\nimport { Textarea } from '@repo/design-system/components/ui/textarea';\n\n/**\n * Displays a form textarea or a component that looks like a textarea.\n */\nconst meta = {\n  title: 'ui/Textarea',\n  component: Textarea,\n  tags: ['autodocs'],\n  argTypes: {},\n  args: {\n    placeholder: 'Type your message here.',\n    disabled: false,\n  },\n} satisfies Meta<typeof Textarea>;\n\nexport default meta;\n\ntype Story = StoryObj<typeof meta>;\n\n/**\n * The default form of the textarea.\n */\nexport const Default: Story = {};\n\n/**\n * Use the `disabled` prop to disable the textarea.\n */\nexport const Disabled: Story = {\n  args: {\n    disabled: true,\n  },\n};\n\n/**\n * Use the `Label` component to includes a clear, descriptive label above or\n * alongside the text area to guide users.\n */\nexport const WithLabel: Story = {\n  render: (args) => (\n    <div className=\"grid w-full gap-1.5\">\n      <label htmlFor=\"message\">Your message</label>\n      <Textarea {...args} id=\"message\" />\n    </div>\n  ),\n};\n\n/**\n * Use a text element below the text area to provide additional instructions\n * or information to users.\n */\nexport const WithText: Story = {\n  render: (args) => (\n    <div className=\"grid w-full gap-1.5\">\n      <label htmlFor=\"message-2\">Your Message</label>\n      <Textarea {...args} id=\"message-2\" />\n      <p className=\"text-slate-500 text-sm\">\n        Your message will be copied to the support team.\n      </p>\n    </div>\n  ),\n};\n\n/**\n * Use the `Button` component to indicate that the text area can be submitted\n * or used to trigger an action.\n */\nexport const WithButton: Story = {\n  render: (args) => (\n    <div className=\"grid w-full gap-2\">\n      <Textarea {...args} />\n      <button\n        className=\"rounded bg-primary px-4 py-2 text-primary-foreground\"\n        type=\"submit\"\n      >\n        Send Message\n      </button>\n    </div>\n  ),\n};\n"], "names": [], "sourceRoot": ""}