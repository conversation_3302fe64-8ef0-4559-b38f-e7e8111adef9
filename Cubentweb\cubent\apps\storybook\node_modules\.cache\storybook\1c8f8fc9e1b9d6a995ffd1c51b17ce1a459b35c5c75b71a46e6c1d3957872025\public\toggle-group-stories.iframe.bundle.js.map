{"version": 3, "file": "toggle-group-stories.iframe.bundle.js", "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AAEA;AACA;AAGA;AACA;AAEA;AAGA;AACA;AACA;AAEA;AAQA;AAEA;AACA;AACA;AACA;AAIA;AAEA;AAAA;AAAA;AAAA;AAAA;AACA;;;;;;;;;;;AAIA;AAxBA;AA0BA;;AAQA;AAEA;AAEA;AACA;AACA;AACA;AAEA;AACA;AACA;AAIA;AAEA;;;;;;AAGA;;AA5BA;AA8BA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACxEA;;AAEA;AACA;AACA;AAEA;AAEA;AAGA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAGA;AAOA;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AACA;;;;;;AAGA;AAdA;AAgBA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC7CA;AAEA;AAKA;;;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AACA;AAAA;;;;;;;;;;AAEA;AAAA;AAAA;AACA;AAAA;;;;;;;;;;AAEA;AAAA;AAAA;AACA;AAAA;;;;;;;;;;;;;;;;AAIA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AAEA;AAIA;;;AAGA;AAEA;;;;AAIA;AACA;AACA;AACA;AACA;AAEA;;;;AAIA;AACA;AACA;AACA;AACA;AAEA;;;;AAIA;AACA;AACA;AACA;AACA;AAEA;;;;AAIA;AACA;AACA;AACA;AACA;AAEA;;;AAGA;AACA;AACA;AACA;AACA", "sources": ["webpack://storybook/../../packages/design-system/components/ui/toggle-group.tsx", "webpack://storybook/../../packages/design-system/components/ui/toggle.tsx", "webpack://storybook/./stories/toggle-group.stories.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as ToggleGroupPrimitive from \"@radix-ui/react-toggle-group\"\nimport { type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@repo/design-system/lib/utils\"\nimport { toggleVariants } from \"@repo/design-system/components/ui/toggle\"\n\nconst ToggleGroupContext = React.createContext<\n  VariantProps<typeof toggleVariants>\n>({\n  size: \"default\",\n  variant: \"default\",\n})\n\nfunction ToggleGroup({\n  className,\n  variant,\n  size,\n  children,\n  ...props\n}: React.ComponentProps<typeof ToggleGroupPrimitive.Root> &\n  VariantProps<typeof toggleVariants>) {\n  return (\n    <ToggleGroupPrimitive.Root\n      data-slot=\"toggle-group\"\n      data-variant={variant}\n      data-size={size}\n      className={cn(\n        \"group/toggle-group flex w-fit items-center rounded-md data-[variant=outline]:shadow-xs\",\n        className\n      )}\n      {...props}\n    >\n      <ToggleGroupContext.Provider value={{ variant, size }}>\n        {children}\n      </ToggleGroupContext.Provider>\n    </ToggleGroupPrimitive.Root>\n  )\n}\n\nfunction ToggleGroupItem({\n  className,\n  children,\n  variant,\n  size,\n  ...props\n}: React.ComponentProps<typeof ToggleGroupPrimitive.Item> &\n  VariantProps<typeof toggleVariants>) {\n  const context = React.useContext(ToggleGroupContext)\n\n  return (\n    <ToggleGroupPrimitive.Item\n      data-slot=\"toggle-group-item\"\n      data-variant={context.variant || variant}\n      data-size={context.size || size}\n      className={cn(\n        toggleVariants({\n          variant: context.variant || variant,\n          size: context.size || size,\n        }),\n        \"min-w-0 flex-1 shrink-0 rounded-none shadow-none first:rounded-l-md last:rounded-r-md focus:z-10 focus-visible:z-10 data-[variant=outline]:border-l-0 data-[variant=outline]:first:border-l\",\n        className\n      )}\n      {...props}\n    >\n      {children}\n    </ToggleGroupPrimitive.Item>\n  )\n}\n\nexport { ToggleGroup, ToggleGroupItem }\n", "\"use client\"\n\nimport * as React from \"react\"\nimport * as TogglePrimitive from \"@radix-ui/react-toggle\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@repo/design-system/lib/utils\"\n\nconst toggleVariants = cva(\n  \"inline-flex items-center justify-center gap-2 rounded-md text-sm font-medium hover:bg-muted hover:text-muted-foreground disabled:pointer-events-none disabled:opacity-50 data-[state=on]:bg-accent data-[state=on]:text-accent-foreground [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 [&_svg]:shrink-0 focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] outline-none transition-[color,box-shadow] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive whitespace-nowrap\",\n  {\n    variants: {\n      variant: {\n        default: \"bg-transparent\",\n        outline:\n          \"border border-input bg-transparent shadow-xs hover:bg-accent hover:text-accent-foreground\",\n      },\n      size: {\n        default: \"h-9 px-2 min-w-9\",\n        sm: \"h-8 px-1.5 min-w-8\",\n        lg: \"h-10 px-2.5 min-w-10\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  }\n)\n\nfunction Toggle({\n  className,\n  variant,\n  size,\n  ...props\n}: React.ComponentProps<typeof TogglePrimitive.Root> &\n  VariantProps<typeof toggleVariants>) {\n  return (\n    <TogglePrimitive.Root\n      data-slot=\"toggle\"\n      className={cn(toggleVariants({ variant, size, className }))}\n      {...props}\n    />\n  )\n}\n\nexport { Toggle, toggleVariants }\n", "import type { <PERSON><PERSON>, StoryObj } from '@storybook/react';\nimport { Bold, Italic, Underline } from 'lucide-react';\n\nimport {\n  ToggleGroup,\n  ToggleGroupItem,\n} from '@repo/design-system/components/ui/toggle-group';\n\n/**\n * A set of two-state buttons that can be toggled on or off.\n */\nconst meta = {\n  title: 'ui/ToggleGroup',\n  component: ToggleGroup,\n  tags: ['autodocs'],\n  argTypes: {\n    type: {\n      options: ['multiple', 'single'],\n      control: { type: 'radio' },\n    },\n  },\n  args: {\n    variant: 'default',\n    size: 'default',\n    type: 'multiple',\n    disabled: false,\n  },\n  render: (args) => (\n    <ToggleGroup {...args}>\n      <ToggleGroupItem value=\"bold\" aria-label=\"Toggle bold\">\n        <Bold className=\"h-4 w-4\" />\n      </ToggleGroupItem>\n      <ToggleGroupItem value=\"italic\" aria-label=\"Toggle italic\">\n        <Italic className=\"h-4 w-4\" />\n      </ToggleGroupItem>\n      <ToggleGroupItem value=\"underline\" aria-label=\"Toggle underline\">\n        <Underline className=\"h-4 w-4\" />\n      </ToggleGroupItem>\n    </ToggleGroup>\n  ),\n  parameters: {\n    layout: 'centered',\n  },\n} satisfies Meta<typeof ToggleGroup>;\n\nexport default meta;\n\ntype Story = StoryObj<typeof meta>;\n\n/**\n * The default form of the toggle group.\n */\nexport const Default: Story = {};\n\n/**\n * Use the `outline` variant to emphasizing the individuality of each button\n * while keeping them visually cohesive.\n */\nexport const Outline: Story = {\n  args: {\n    variant: 'outline',\n  },\n};\n\n/**\n * Use the `single` type to create exclusive selection within the button\n * group, allowing only one button to be active at a time.\n */\nexport const Single: Story = {\n  args: {\n    type: 'single',\n  },\n};\n\n/**\n * Use the `sm` size for a compact version of the button group, featuring\n * smaller buttons for spaces with limited real estate.\n */\nexport const Small: Story = {\n  args: {\n    size: 'sm',\n  },\n};\n\n/**\n * Use the `lg` size for a more prominent version of the button group, featuring\n * larger buttons for emphasis.\n */\nexport const Large: Story = {\n  args: {\n    size: 'lg',\n  },\n};\n\n/**\n * Add the `disabled` prop to a button to prevent interactions.\n */\nexport const Disabled: Story = {\n  args: {\n    disabled: true,\n  },\n};\n"], "names": [], "sourceRoot": ""}