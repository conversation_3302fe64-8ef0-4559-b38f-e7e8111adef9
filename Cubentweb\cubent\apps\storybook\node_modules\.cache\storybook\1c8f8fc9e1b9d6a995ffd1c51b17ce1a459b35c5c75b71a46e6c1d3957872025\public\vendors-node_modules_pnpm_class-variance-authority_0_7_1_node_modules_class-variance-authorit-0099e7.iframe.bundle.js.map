{"version": 3, "file": "vendors-node_modules_pnpm_class-variance-authority_0_7_1_node_modules_class-variance-authorit-0099e7.iframe.bundle.js", "mappings": ";;;;;;;;;;;;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;;;;;;ACvDA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;;;;;ACrCA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;;ACtCA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;;;;;;ACroDA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;;;;;;AC1CA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;;AC7BA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;;;;ACpBA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;;;;AChBA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;;;;;;AChBA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "sources": ["webpack://storybook/../../node_modules/.pnpm/class-variance-authority@0.7.1/node_modules/class-variance-authority/dist/index.mjs", "webpack://storybook/../../node_modules/.pnpm/embla-carousel-react@8.6.0_react@19.1.0/node_modules/embla-carousel-react/esm/embla-carousel-react.esm.js", "webpack://storybook/../../node_modules/.pnpm/embla-carousel-reactive-utils@8.6.0_embla-carousel@8.6.0/node_modules/embla-carousel-reactive-utils/esm/embla-carousel-reactive-utils.esm.js", "webpack://storybook/../../node_modules/.pnpm/embla-carousel@8.6.0/node_modules/embla-carousel/esm/embla-carousel.esm.js", "webpack://storybook/../../node_modules/.pnpm/lucide-react@0.511.0_react@19.1.0/node_modules/lucide-react/dist/esm/Icon.js", "webpack://storybook/../../node_modules/.pnpm/lucide-react@0.511.0_react@19.1.0/node_modules/lucide-react/dist/esm/createLucideIcon.js", "webpack://storybook/../../node_modules/.pnpm/lucide-react@0.511.0_react@19.1.0/node_modules/lucide-react/dist/esm/defaultAttributes.js", "webpack://storybook/../../node_modules/.pnpm/lucide-react@0.511.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/arrow-left.js", "webpack://storybook/../../node_modules/.pnpm/lucide-react@0.511.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/arrow-right.js", "webpack://storybook/../../node_modules/.pnpm/lucide-react@0.511.0_react@19.1.0/node_modules/lucide-react/dist/esm/shared/src/utils.js"], "sourcesContent": ["/**\n * Copyright 2022 Joe Bell. All rights reserved.\n *\n * This file is licensed to you under the Apache License, Version 2.0\n * (the \"License\"); you may not use this file except in compliance with the\n * License. You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS, WITHOUT\n * WARRANTIES OR REPRESENTATIONS OF ANY KIND, either express or implied. See the\n * License for the specific language governing permissions and limitations under\n * the License.\n */ import { clsx } from \"clsx\";\nconst falsyToString = (value)=>typeof value === \"boolean\" ? `${value}` : value === 0 ? \"0\" : value;\nexport const cx = clsx;\nexport const cva = (base, config)=>(props)=>{\n        var _config_compoundVariants;\n        if ((config === null || config === void 0 ? void 0 : config.variants) == null) return cx(base, props === null || props === void 0 ? void 0 : props.class, props === null || props === void 0 ? void 0 : props.className);\n        const { variants, defaultVariants } = config;\n        const getVariantClassNames = Object.keys(variants).map((variant)=>{\n            const variantProp = props === null || props === void 0 ? void 0 : props[variant];\n            const defaultVariantProp = defaultVariants === null || defaultVariants === void 0 ? void 0 : defaultVariants[variant];\n            if (variantProp === null) return null;\n            const variantKey = falsyToString(variantProp) || falsyToString(defaultVariantProp);\n            return variants[variant][variantKey];\n        });\n        const propsWithoutUndefined = props && Object.entries(props).reduce((acc, param)=>{\n            let [key, value] = param;\n            if (value === undefined) {\n                return acc;\n            }\n            acc[key] = value;\n            return acc;\n        }, {});\n        const getCompoundVariantClassNames = config === null || config === void 0 ? void 0 : (_config_compoundVariants = config.compoundVariants) === null || _config_compoundVariants === void 0 ? void 0 : _config_compoundVariants.reduce((acc, param)=>{\n            let { class: cvClass, className: cvClassName, ...compoundVariantOptions } = param;\n            return Object.entries(compoundVariantOptions).every((param)=>{\n                let [key, value] = param;\n                return Array.isArray(value) ? value.includes({\n                    ...defaultVariants,\n                    ...propsWithoutUndefined\n                }[key]) : ({\n                    ...defaultVariants,\n                    ...propsWithoutUndefined\n                })[key] === value;\n            }) ? [\n                ...acc,\n                cvClass,\n                cvClassName\n            ] : acc;\n        }, []);\n        return cx(base, getVariantClassNames, getCompoundVariantClassNames, props === null || props === void 0 ? void 0 : props.class, props === null || props === void 0 ? void 0 : props.className);\n    };\n\n", "import { useRef, useState, useCallback, useEffect } from 'react';\nimport { areOptionsEqual, arePluginsEqual, canUseDOM } from 'embla-carousel-reactive-utils';\nimport EmblaCarousel from 'embla-carousel';\n\nfunction useEmblaCarousel(options = {}, plugins = []) {\n  const storedOptions = useRef(options);\n  const storedPlugins = useRef(plugins);\n  const [emblaApi, setEmblaApi] = useState();\n  const [viewport, setViewport] = useState();\n  const reInit = useCallback(() => {\n    if (emblaApi) emblaApi.reInit(storedOptions.current, storedPlugins.current);\n  }, [emblaApi]);\n  useEffect(() => {\n    if (areOptionsEqual(storedOptions.current, options)) return;\n    storedOptions.current = options;\n    reInit();\n  }, [options, reInit]);\n  useEffect(() => {\n    if (arePluginsEqual(storedPlugins.current, plugins)) return;\n    storedPlugins.current = plugins;\n    reInit();\n  }, [plugins, reInit]);\n  useEffect(() => {\n    if (canUseDOM() && viewport) {\n      EmblaCarousel.globalOptions = useEmblaCarousel.globalOptions;\n      const newEmblaApi = EmblaCarousel(viewport, storedOptions.current, storedPlugins.current);\n      setEmblaApi(newEmblaApi);\n      return () => newEmblaApi.destroy();\n    } else {\n      setEmblaApi(undefined);\n    }\n  }, [viewport, setEmblaApi]);\n  return [setViewport, emblaApi];\n}\nuseEmblaCarousel.globalOptions = undefined;\n\nexport { useEmblaCarousel as default };\n//# sourceMappingURL=embla-carousel-react.esm.js.map\n", "function isObject(subject) {\n  return Object.prototype.toString.call(subject) === '[object Object]';\n}\nfunction isRecord(subject) {\n  return isObject(subject) || Array.isArray(subject);\n}\nfunction canUseDOM() {\n  return !!(typeof window !== 'undefined' && window.document && window.document.createElement);\n}\nfunction areOptionsEqual(optionsA, optionsB) {\n  const optionsAKeys = Object.keys(optionsA);\n  const optionsBKeys = Object.keys(optionsB);\n  if (optionsAKeys.length !== optionsBKeys.length) return false;\n  const breakpointsA = JSON.stringify(Object.keys(optionsA.breakpoints || {}));\n  const breakpointsB = JSON.stringify(Object.keys(optionsB.breakpoints || {}));\n  if (breakpointsA !== breakpointsB) return false;\n  return optionsAKeys.every(key => {\n    const valueA = optionsA[key];\n    const valueB = optionsB[key];\n    if (typeof valueA === 'function') return `${valueA}` === `${valueB}`;\n    if (!isRecord(valueA) || !isRecord(valueB)) return valueA === valueB;\n    return areOptionsEqual(valueA, valueB);\n  });\n}\nfunction sortAndMapPluginToOptions(plugins) {\n  return plugins.concat().sort((a, b) => a.name > b.name ? 1 : -1).map(plugin => plugin.options);\n}\nfunction arePluginsEqual(pluginsA, pluginsB) {\n  if (pluginsA.length !== pluginsB.length) return false;\n  const optionsA = sortAndMapPluginToOptions(pluginsA);\n  const optionsB = sortAndMapPluginToOptions(pluginsB);\n  return optionsA.every((optionA, index) => {\n    const optionB = optionsB[index];\n    return areOptionsEqual(optionA, optionB);\n  });\n}\n\nexport { areOptionsEqual, arePluginsEqual, canUseDOM, sortAndMapPluginToOptions };\n//# sourceMappingURL=embla-carousel-reactive-utils.esm.js.map\n", "function isNumber(subject) {\n  return typeof subject === 'number';\n}\nfunction isString(subject) {\n  return typeof subject === 'string';\n}\nfunction isBoolean(subject) {\n  return typeof subject === 'boolean';\n}\nfunction isObject(subject) {\n  return Object.prototype.toString.call(subject) === '[object Object]';\n}\nfunction mathAbs(n) {\n  return Math.abs(n);\n}\nfunction mathSign(n) {\n  return Math.sign(n);\n}\nfunction deltaAbs(valueB, valueA) {\n  return mathAbs(valueB - valueA);\n}\nfunction factorAbs(valueB, valueA) {\n  if (valueB === 0 || valueA === 0) return 0;\n  if (mathAbs(valueB) <= mathAbs(valueA)) return 0;\n  const diff = deltaAbs(mathAbs(valueB), mathAbs(valueA));\n  return mathAbs(diff / valueB);\n}\nfunction roundToTwoDecimals(num) {\n  return Math.round(num * 100) / 100;\n}\nfunction arrayKeys(array) {\n  return objectKeys(array).map(Number);\n}\nfunction arrayLast(array) {\n  return array[arrayLastIndex(array)];\n}\nfunction arrayLastIndex(array) {\n  return Math.max(0, array.length - 1);\n}\nfunction arrayIsLastIndex(array, index) {\n  return index === arrayLastIndex(array);\n}\nfunction arrayFromNumber(n, startAt = 0) {\n  return Array.from(Array(n), (_, i) => startAt + i);\n}\nfunction objectKeys(object) {\n  return Object.keys(object);\n}\nfunction objectsMergeDeep(objectA, objectB) {\n  return [objectA, objectB].reduce((mergedObjects, currentObject) => {\n    objectKeys(currentObject).forEach(key => {\n      const valueA = mergedObjects[key];\n      const valueB = currentObject[key];\n      const areObjects = isObject(valueA) && isObject(valueB);\n      mergedObjects[key] = areObjects ? objectsMergeDeep(valueA, valueB) : valueB;\n    });\n    return mergedObjects;\n  }, {});\n}\nfunction isMouseEvent(evt, ownerWindow) {\n  return typeof ownerWindow.MouseEvent !== 'undefined' && evt instanceof ownerWindow.MouseEvent;\n}\n\nfunction Alignment(align, viewSize) {\n  const predefined = {\n    start,\n    center,\n    end\n  };\n  function start() {\n    return 0;\n  }\n  function center(n) {\n    return end(n) / 2;\n  }\n  function end(n) {\n    return viewSize - n;\n  }\n  function measure(n, index) {\n    if (isString(align)) return predefined[align](n);\n    return align(viewSize, n, index);\n  }\n  const self = {\n    measure\n  };\n  return self;\n}\n\nfunction EventStore() {\n  let listeners = [];\n  function add(node, type, handler, options = {\n    passive: true\n  }) {\n    let removeListener;\n    if ('addEventListener' in node) {\n      node.addEventListener(type, handler, options);\n      removeListener = () => node.removeEventListener(type, handler, options);\n    } else {\n      const legacyMediaQueryList = node;\n      legacyMediaQueryList.addListener(handler);\n      removeListener = () => legacyMediaQueryList.removeListener(handler);\n    }\n    listeners.push(removeListener);\n    return self;\n  }\n  function clear() {\n    listeners = listeners.filter(remove => remove());\n  }\n  const self = {\n    add,\n    clear\n  };\n  return self;\n}\n\nfunction Animations(ownerDocument, ownerWindow, update, render) {\n  const documentVisibleHandler = EventStore();\n  const fixedTimeStep = 1000 / 60;\n  let lastTimeStamp = null;\n  let accumulatedTime = 0;\n  let animationId = 0;\n  function init() {\n    documentVisibleHandler.add(ownerDocument, 'visibilitychange', () => {\n      if (ownerDocument.hidden) reset();\n    });\n  }\n  function destroy() {\n    stop();\n    documentVisibleHandler.clear();\n  }\n  function animate(timeStamp) {\n    if (!animationId) return;\n    if (!lastTimeStamp) {\n      lastTimeStamp = timeStamp;\n      update();\n      update();\n    }\n    const timeElapsed = timeStamp - lastTimeStamp;\n    lastTimeStamp = timeStamp;\n    accumulatedTime += timeElapsed;\n    while (accumulatedTime >= fixedTimeStep) {\n      update();\n      accumulatedTime -= fixedTimeStep;\n    }\n    const alpha = accumulatedTime / fixedTimeStep;\n    render(alpha);\n    if (animationId) {\n      animationId = ownerWindow.requestAnimationFrame(animate);\n    }\n  }\n  function start() {\n    if (animationId) return;\n    animationId = ownerWindow.requestAnimationFrame(animate);\n  }\n  function stop() {\n    ownerWindow.cancelAnimationFrame(animationId);\n    lastTimeStamp = null;\n    accumulatedTime = 0;\n    animationId = 0;\n  }\n  function reset() {\n    lastTimeStamp = null;\n    accumulatedTime = 0;\n  }\n  const self = {\n    init,\n    destroy,\n    start,\n    stop,\n    update,\n    render\n  };\n  return self;\n}\n\nfunction Axis(axis, contentDirection) {\n  const isRightToLeft = contentDirection === 'rtl';\n  const isVertical = axis === 'y';\n  const scroll = isVertical ? 'y' : 'x';\n  const cross = isVertical ? 'x' : 'y';\n  const sign = !isVertical && isRightToLeft ? -1 : 1;\n  const startEdge = getStartEdge();\n  const endEdge = getEndEdge();\n  function measureSize(nodeRect) {\n    const {\n      height,\n      width\n    } = nodeRect;\n    return isVertical ? height : width;\n  }\n  function getStartEdge() {\n    if (isVertical) return 'top';\n    return isRightToLeft ? 'right' : 'left';\n  }\n  function getEndEdge() {\n    if (isVertical) return 'bottom';\n    return isRightToLeft ? 'left' : 'right';\n  }\n  function direction(n) {\n    return n * sign;\n  }\n  const self = {\n    scroll,\n    cross,\n    startEdge,\n    endEdge,\n    measureSize,\n    direction\n  };\n  return self;\n}\n\nfunction Limit(min = 0, max = 0) {\n  const length = mathAbs(min - max);\n  function reachedMin(n) {\n    return n < min;\n  }\n  function reachedMax(n) {\n    return n > max;\n  }\n  function reachedAny(n) {\n    return reachedMin(n) || reachedMax(n);\n  }\n  function constrain(n) {\n    if (!reachedAny(n)) return n;\n    return reachedMin(n) ? min : max;\n  }\n  function removeOffset(n) {\n    if (!length) return n;\n    return n - length * Math.ceil((n - max) / length);\n  }\n  const self = {\n    length,\n    max,\n    min,\n    constrain,\n    reachedAny,\n    reachedMax,\n    reachedMin,\n    removeOffset\n  };\n  return self;\n}\n\nfunction Counter(max, start, loop) {\n  const {\n    constrain\n  } = Limit(0, max);\n  const loopEnd = max + 1;\n  let counter = withinLimit(start);\n  function withinLimit(n) {\n    return !loop ? constrain(n) : mathAbs((loopEnd + n) % loopEnd);\n  }\n  function get() {\n    return counter;\n  }\n  function set(n) {\n    counter = withinLimit(n);\n    return self;\n  }\n  function add(n) {\n    return clone().set(get() + n);\n  }\n  function clone() {\n    return Counter(max, get(), loop);\n  }\n  const self = {\n    get,\n    set,\n    add,\n    clone\n  };\n  return self;\n}\n\nfunction DragHandler(axis, rootNode, ownerDocument, ownerWindow, target, dragTracker, location, animation, scrollTo, scrollBody, scrollTarget, index, eventHandler, percentOfView, dragFree, dragThreshold, skipSnaps, baseFriction, watchDrag) {\n  const {\n    cross: crossAxis,\n    direction\n  } = axis;\n  const focusNodes = ['INPUT', 'SELECT', 'TEXTAREA'];\n  const nonPassiveEvent = {\n    passive: false\n  };\n  const initEvents = EventStore();\n  const dragEvents = EventStore();\n  const goToNextThreshold = Limit(50, 225).constrain(percentOfView.measure(20));\n  const snapForceBoost = {\n    mouse: 300,\n    touch: 400\n  };\n  const freeForceBoost = {\n    mouse: 500,\n    touch: 600\n  };\n  const baseSpeed = dragFree ? 43 : 25;\n  let isMoving = false;\n  let startScroll = 0;\n  let startCross = 0;\n  let pointerIsDown = false;\n  let preventScroll = false;\n  let preventClick = false;\n  let isMouse = false;\n  function init(emblaApi) {\n    if (!watchDrag) return;\n    function downIfAllowed(evt) {\n      if (isBoolean(watchDrag) || watchDrag(emblaApi, evt)) down(evt);\n    }\n    const node = rootNode;\n    initEvents.add(node, 'dragstart', evt => evt.preventDefault(), nonPassiveEvent).add(node, 'touchmove', () => undefined, nonPassiveEvent).add(node, 'touchend', () => undefined).add(node, 'touchstart', downIfAllowed).add(node, 'mousedown', downIfAllowed).add(node, 'touchcancel', up).add(node, 'contextmenu', up).add(node, 'click', click, true);\n  }\n  function destroy() {\n    initEvents.clear();\n    dragEvents.clear();\n  }\n  function addDragEvents() {\n    const node = isMouse ? ownerDocument : rootNode;\n    dragEvents.add(node, 'touchmove', move, nonPassiveEvent).add(node, 'touchend', up).add(node, 'mousemove', move, nonPassiveEvent).add(node, 'mouseup', up);\n  }\n  function isFocusNode(node) {\n    const nodeName = node.nodeName || '';\n    return focusNodes.includes(nodeName);\n  }\n  function forceBoost() {\n    const boost = dragFree ? freeForceBoost : snapForceBoost;\n    const type = isMouse ? 'mouse' : 'touch';\n    return boost[type];\n  }\n  function allowedForce(force, targetChanged) {\n    const next = index.add(mathSign(force) * -1);\n    const baseForce = scrollTarget.byDistance(force, !dragFree).distance;\n    if (dragFree || mathAbs(force) < goToNextThreshold) return baseForce;\n    if (skipSnaps && targetChanged) return baseForce * 0.5;\n    return scrollTarget.byIndex(next.get(), 0).distance;\n  }\n  function down(evt) {\n    const isMouseEvt = isMouseEvent(evt, ownerWindow);\n    isMouse = isMouseEvt;\n    preventClick = dragFree && isMouseEvt && !evt.buttons && isMoving;\n    isMoving = deltaAbs(target.get(), location.get()) >= 2;\n    if (isMouseEvt && evt.button !== 0) return;\n    if (isFocusNode(evt.target)) return;\n    pointerIsDown = true;\n    dragTracker.pointerDown(evt);\n    scrollBody.useFriction(0).useDuration(0);\n    target.set(location);\n    addDragEvents();\n    startScroll = dragTracker.readPoint(evt);\n    startCross = dragTracker.readPoint(evt, crossAxis);\n    eventHandler.emit('pointerDown');\n  }\n  function move(evt) {\n    const isTouchEvt = !isMouseEvent(evt, ownerWindow);\n    if (isTouchEvt && evt.touches.length >= 2) return up(evt);\n    const lastScroll = dragTracker.readPoint(evt);\n    const lastCross = dragTracker.readPoint(evt, crossAxis);\n    const diffScroll = deltaAbs(lastScroll, startScroll);\n    const diffCross = deltaAbs(lastCross, startCross);\n    if (!preventScroll && !isMouse) {\n      if (!evt.cancelable) return up(evt);\n      preventScroll = diffScroll > diffCross;\n      if (!preventScroll) return up(evt);\n    }\n    const diff = dragTracker.pointerMove(evt);\n    if (diffScroll > dragThreshold) preventClick = true;\n    scrollBody.useFriction(0.3).useDuration(0.75);\n    animation.start();\n    target.add(direction(diff));\n    evt.preventDefault();\n  }\n  function up(evt) {\n    const currentLocation = scrollTarget.byDistance(0, false);\n    const targetChanged = currentLocation.index !== index.get();\n    const rawForce = dragTracker.pointerUp(evt) * forceBoost();\n    const force = allowedForce(direction(rawForce), targetChanged);\n    const forceFactor = factorAbs(rawForce, force);\n    const speed = baseSpeed - 10 * forceFactor;\n    const friction = baseFriction + forceFactor / 50;\n    preventScroll = false;\n    pointerIsDown = false;\n    dragEvents.clear();\n    scrollBody.useDuration(speed).useFriction(friction);\n    scrollTo.distance(force, !dragFree);\n    isMouse = false;\n    eventHandler.emit('pointerUp');\n  }\n  function click(evt) {\n    if (preventClick) {\n      evt.stopPropagation();\n      evt.preventDefault();\n      preventClick = false;\n    }\n  }\n  function pointerDown() {\n    return pointerIsDown;\n  }\n  const self = {\n    init,\n    destroy,\n    pointerDown\n  };\n  return self;\n}\n\nfunction DragTracker(axis, ownerWindow) {\n  const logInterval = 170;\n  let startEvent;\n  let lastEvent;\n  function readTime(evt) {\n    return evt.timeStamp;\n  }\n  function readPoint(evt, evtAxis) {\n    const property = evtAxis || axis.scroll;\n    const coord = `client${property === 'x' ? 'X' : 'Y'}`;\n    return (isMouseEvent(evt, ownerWindow) ? evt : evt.touches[0])[coord];\n  }\n  function pointerDown(evt) {\n    startEvent = evt;\n    lastEvent = evt;\n    return readPoint(evt);\n  }\n  function pointerMove(evt) {\n    const diff = readPoint(evt) - readPoint(lastEvent);\n    const expired = readTime(evt) - readTime(startEvent) > logInterval;\n    lastEvent = evt;\n    if (expired) startEvent = evt;\n    return diff;\n  }\n  function pointerUp(evt) {\n    if (!startEvent || !lastEvent) return 0;\n    const diffDrag = readPoint(lastEvent) - readPoint(startEvent);\n    const diffTime = readTime(evt) - readTime(startEvent);\n    const expired = readTime(evt) - readTime(lastEvent) > logInterval;\n    const force = diffDrag / diffTime;\n    const isFlick = diffTime && !expired && mathAbs(force) > 0.1;\n    return isFlick ? force : 0;\n  }\n  const self = {\n    pointerDown,\n    pointerMove,\n    pointerUp,\n    readPoint\n  };\n  return self;\n}\n\nfunction NodeRects() {\n  function measure(node) {\n    const {\n      offsetTop,\n      offsetLeft,\n      offsetWidth,\n      offsetHeight\n    } = node;\n    const offset = {\n      top: offsetTop,\n      right: offsetLeft + offsetWidth,\n      bottom: offsetTop + offsetHeight,\n      left: offsetLeft,\n      width: offsetWidth,\n      height: offsetHeight\n    };\n    return offset;\n  }\n  const self = {\n    measure\n  };\n  return self;\n}\n\nfunction PercentOfView(viewSize) {\n  function measure(n) {\n    return viewSize * (n / 100);\n  }\n  const self = {\n    measure\n  };\n  return self;\n}\n\nfunction ResizeHandler(container, eventHandler, ownerWindow, slides, axis, watchResize, nodeRects) {\n  const observeNodes = [container].concat(slides);\n  let resizeObserver;\n  let containerSize;\n  let slideSizes = [];\n  let destroyed = false;\n  function readSize(node) {\n    return axis.measureSize(nodeRects.measure(node));\n  }\n  function init(emblaApi) {\n    if (!watchResize) return;\n    containerSize = readSize(container);\n    slideSizes = slides.map(readSize);\n    function defaultCallback(entries) {\n      for (const entry of entries) {\n        if (destroyed) return;\n        const isContainer = entry.target === container;\n        const slideIndex = slides.indexOf(entry.target);\n        const lastSize = isContainer ? containerSize : slideSizes[slideIndex];\n        const newSize = readSize(isContainer ? container : slides[slideIndex]);\n        const diffSize = mathAbs(newSize - lastSize);\n        if (diffSize >= 0.5) {\n          emblaApi.reInit();\n          eventHandler.emit('resize');\n          break;\n        }\n      }\n    }\n    resizeObserver = new ResizeObserver(entries => {\n      if (isBoolean(watchResize) || watchResize(emblaApi, entries)) {\n        defaultCallback(entries);\n      }\n    });\n    ownerWindow.requestAnimationFrame(() => {\n      observeNodes.forEach(node => resizeObserver.observe(node));\n    });\n  }\n  function destroy() {\n    destroyed = true;\n    if (resizeObserver) resizeObserver.disconnect();\n  }\n  const self = {\n    init,\n    destroy\n  };\n  return self;\n}\n\nfunction ScrollBody(location, offsetLocation, previousLocation, target, baseDuration, baseFriction) {\n  let scrollVelocity = 0;\n  let scrollDirection = 0;\n  let scrollDuration = baseDuration;\n  let scrollFriction = baseFriction;\n  let rawLocation = location.get();\n  let rawLocationPrevious = 0;\n  function seek() {\n    const displacement = target.get() - location.get();\n    const isInstant = !scrollDuration;\n    let scrollDistance = 0;\n    if (isInstant) {\n      scrollVelocity = 0;\n      previousLocation.set(target);\n      location.set(target);\n      scrollDistance = displacement;\n    } else {\n      previousLocation.set(location);\n      scrollVelocity += displacement / scrollDuration;\n      scrollVelocity *= scrollFriction;\n      rawLocation += scrollVelocity;\n      location.add(scrollVelocity);\n      scrollDistance = rawLocation - rawLocationPrevious;\n    }\n    scrollDirection = mathSign(scrollDistance);\n    rawLocationPrevious = rawLocation;\n    return self;\n  }\n  function settled() {\n    const diff = target.get() - offsetLocation.get();\n    return mathAbs(diff) < 0.001;\n  }\n  function duration() {\n    return scrollDuration;\n  }\n  function direction() {\n    return scrollDirection;\n  }\n  function velocity() {\n    return scrollVelocity;\n  }\n  function useBaseDuration() {\n    return useDuration(baseDuration);\n  }\n  function useBaseFriction() {\n    return useFriction(baseFriction);\n  }\n  function useDuration(n) {\n    scrollDuration = n;\n    return self;\n  }\n  function useFriction(n) {\n    scrollFriction = n;\n    return self;\n  }\n  const self = {\n    direction,\n    duration,\n    velocity,\n    seek,\n    settled,\n    useBaseFriction,\n    useBaseDuration,\n    useFriction,\n    useDuration\n  };\n  return self;\n}\n\nfunction ScrollBounds(limit, location, target, scrollBody, percentOfView) {\n  const pullBackThreshold = percentOfView.measure(10);\n  const edgeOffsetTolerance = percentOfView.measure(50);\n  const frictionLimit = Limit(0.1, 0.99);\n  let disabled = false;\n  function shouldConstrain() {\n    if (disabled) return false;\n    if (!limit.reachedAny(target.get())) return false;\n    if (!limit.reachedAny(location.get())) return false;\n    return true;\n  }\n  function constrain(pointerDown) {\n    if (!shouldConstrain()) return;\n    const edge = limit.reachedMin(location.get()) ? 'min' : 'max';\n    const diffToEdge = mathAbs(limit[edge] - location.get());\n    const diffToTarget = target.get() - location.get();\n    const friction = frictionLimit.constrain(diffToEdge / edgeOffsetTolerance);\n    target.subtract(diffToTarget * friction);\n    if (!pointerDown && mathAbs(diffToTarget) < pullBackThreshold) {\n      target.set(limit.constrain(target.get()));\n      scrollBody.useDuration(25).useBaseFriction();\n    }\n  }\n  function toggleActive(active) {\n    disabled = !active;\n  }\n  const self = {\n    shouldConstrain,\n    constrain,\n    toggleActive\n  };\n  return self;\n}\n\nfunction ScrollContain(viewSize, contentSize, snapsAligned, containScroll, pixelTolerance) {\n  const scrollBounds = Limit(-contentSize + viewSize, 0);\n  const snapsBounded = measureBounded();\n  const scrollContainLimit = findScrollContainLimit();\n  const snapsContained = measureContained();\n  function usePixelTolerance(bound, snap) {\n    return deltaAbs(bound, snap) <= 1;\n  }\n  function findScrollContainLimit() {\n    const startSnap = snapsBounded[0];\n    const endSnap = arrayLast(snapsBounded);\n    const min = snapsBounded.lastIndexOf(startSnap);\n    const max = snapsBounded.indexOf(endSnap) + 1;\n    return Limit(min, max);\n  }\n  function measureBounded() {\n    return snapsAligned.map((snapAligned, index) => {\n      const {\n        min,\n        max\n      } = scrollBounds;\n      const snap = scrollBounds.constrain(snapAligned);\n      const isFirst = !index;\n      const isLast = arrayIsLastIndex(snapsAligned, index);\n      if (isFirst) return max;\n      if (isLast) return min;\n      if (usePixelTolerance(min, snap)) return min;\n      if (usePixelTolerance(max, snap)) return max;\n      return snap;\n    }).map(scrollBound => parseFloat(scrollBound.toFixed(3)));\n  }\n  function measureContained() {\n    if (contentSize <= viewSize + pixelTolerance) return [scrollBounds.max];\n    if (containScroll === 'keepSnaps') return snapsBounded;\n    const {\n      min,\n      max\n    } = scrollContainLimit;\n    return snapsBounded.slice(min, max);\n  }\n  const self = {\n    snapsContained,\n    scrollContainLimit\n  };\n  return self;\n}\n\nfunction ScrollLimit(contentSize, scrollSnaps, loop) {\n  const max = scrollSnaps[0];\n  const min = loop ? max - contentSize : arrayLast(scrollSnaps);\n  const limit = Limit(min, max);\n  const self = {\n    limit\n  };\n  return self;\n}\n\nfunction ScrollLooper(contentSize, limit, location, vectors) {\n  const jointSafety = 0.1;\n  const min = limit.min + jointSafety;\n  const max = limit.max + jointSafety;\n  const {\n    reachedMin,\n    reachedMax\n  } = Limit(min, max);\n  function shouldLoop(direction) {\n    if (direction === 1) return reachedMax(location.get());\n    if (direction === -1) return reachedMin(location.get());\n    return false;\n  }\n  function loop(direction) {\n    if (!shouldLoop(direction)) return;\n    const loopDistance = contentSize * (direction * -1);\n    vectors.forEach(v => v.add(loopDistance));\n  }\n  const self = {\n    loop\n  };\n  return self;\n}\n\nfunction ScrollProgress(limit) {\n  const {\n    max,\n    length\n  } = limit;\n  function get(n) {\n    const currentLocation = n - max;\n    return length ? currentLocation / -length : 0;\n  }\n  const self = {\n    get\n  };\n  return self;\n}\n\nfunction ScrollSnaps(axis, alignment, containerRect, slideRects, slidesToScroll) {\n  const {\n    startEdge,\n    endEdge\n  } = axis;\n  const {\n    groupSlides\n  } = slidesToScroll;\n  const alignments = measureSizes().map(alignment.measure);\n  const snaps = measureUnaligned();\n  const snapsAligned = measureAligned();\n  function measureSizes() {\n    return groupSlides(slideRects).map(rects => arrayLast(rects)[endEdge] - rects[0][startEdge]).map(mathAbs);\n  }\n  function measureUnaligned() {\n    return slideRects.map(rect => containerRect[startEdge] - rect[startEdge]).map(snap => -mathAbs(snap));\n  }\n  function measureAligned() {\n    return groupSlides(snaps).map(g => g[0]).map((snap, index) => snap + alignments[index]);\n  }\n  const self = {\n    snaps,\n    snapsAligned\n  };\n  return self;\n}\n\nfunction SlideRegistry(containSnaps, containScroll, scrollSnaps, scrollContainLimit, slidesToScroll, slideIndexes) {\n  const {\n    groupSlides\n  } = slidesToScroll;\n  const {\n    min,\n    max\n  } = scrollContainLimit;\n  const slideRegistry = createSlideRegistry();\n  function createSlideRegistry() {\n    const groupedSlideIndexes = groupSlides(slideIndexes);\n    const doNotContain = !containSnaps || containScroll === 'keepSnaps';\n    if (scrollSnaps.length === 1) return [slideIndexes];\n    if (doNotContain) return groupedSlideIndexes;\n    return groupedSlideIndexes.slice(min, max).map((group, index, groups) => {\n      const isFirst = !index;\n      const isLast = arrayIsLastIndex(groups, index);\n      if (isFirst) {\n        const range = arrayLast(groups[0]) + 1;\n        return arrayFromNumber(range);\n      }\n      if (isLast) {\n        const range = arrayLastIndex(slideIndexes) - arrayLast(groups)[0] + 1;\n        return arrayFromNumber(range, arrayLast(groups)[0]);\n      }\n      return group;\n    });\n  }\n  const self = {\n    slideRegistry\n  };\n  return self;\n}\n\nfunction ScrollTarget(loop, scrollSnaps, contentSize, limit, targetVector) {\n  const {\n    reachedAny,\n    removeOffset,\n    constrain\n  } = limit;\n  function minDistance(distances) {\n    return distances.concat().sort((a, b) => mathAbs(a) - mathAbs(b))[0];\n  }\n  function findTargetSnap(target) {\n    const distance = loop ? removeOffset(target) : constrain(target);\n    const ascDiffsToSnaps = scrollSnaps.map((snap, index) => ({\n      diff: shortcut(snap - distance, 0),\n      index\n    })).sort((d1, d2) => mathAbs(d1.diff) - mathAbs(d2.diff));\n    const {\n      index\n    } = ascDiffsToSnaps[0];\n    return {\n      index,\n      distance\n    };\n  }\n  function shortcut(target, direction) {\n    const targets = [target, target + contentSize, target - contentSize];\n    if (!loop) return target;\n    if (!direction) return minDistance(targets);\n    const matchingTargets = targets.filter(t => mathSign(t) === direction);\n    if (matchingTargets.length) return minDistance(matchingTargets);\n    return arrayLast(targets) - contentSize;\n  }\n  function byIndex(index, direction) {\n    const diffToSnap = scrollSnaps[index] - targetVector.get();\n    const distance = shortcut(diffToSnap, direction);\n    return {\n      index,\n      distance\n    };\n  }\n  function byDistance(distance, snap) {\n    const target = targetVector.get() + distance;\n    const {\n      index,\n      distance: targetSnapDistance\n    } = findTargetSnap(target);\n    const reachedBound = !loop && reachedAny(target);\n    if (!snap || reachedBound) return {\n      index,\n      distance\n    };\n    const diffToSnap = scrollSnaps[index] - targetSnapDistance;\n    const snapDistance = distance + shortcut(diffToSnap, 0);\n    return {\n      index,\n      distance: snapDistance\n    };\n  }\n  const self = {\n    byDistance,\n    byIndex,\n    shortcut\n  };\n  return self;\n}\n\nfunction ScrollTo(animation, indexCurrent, indexPrevious, scrollBody, scrollTarget, targetVector, eventHandler) {\n  function scrollTo(target) {\n    const distanceDiff = target.distance;\n    const indexDiff = target.index !== indexCurrent.get();\n    targetVector.add(distanceDiff);\n    if (distanceDiff) {\n      if (scrollBody.duration()) {\n        animation.start();\n      } else {\n        animation.update();\n        animation.render(1);\n        animation.update();\n      }\n    }\n    if (indexDiff) {\n      indexPrevious.set(indexCurrent.get());\n      indexCurrent.set(target.index);\n      eventHandler.emit('select');\n    }\n  }\n  function distance(n, snap) {\n    const target = scrollTarget.byDistance(n, snap);\n    scrollTo(target);\n  }\n  function index(n, direction) {\n    const targetIndex = indexCurrent.clone().set(n);\n    const target = scrollTarget.byIndex(targetIndex.get(), direction);\n    scrollTo(target);\n  }\n  const self = {\n    distance,\n    index\n  };\n  return self;\n}\n\nfunction SlideFocus(root, slides, slideRegistry, scrollTo, scrollBody, eventStore, eventHandler, watchFocus) {\n  const focusListenerOptions = {\n    passive: true,\n    capture: true\n  };\n  let lastTabPressTime = 0;\n  function init(emblaApi) {\n    if (!watchFocus) return;\n    function defaultCallback(index) {\n      const nowTime = new Date().getTime();\n      const diffTime = nowTime - lastTabPressTime;\n      if (diffTime > 10) return;\n      eventHandler.emit('slideFocusStart');\n      root.scrollLeft = 0;\n      const group = slideRegistry.findIndex(group => group.includes(index));\n      if (!isNumber(group)) return;\n      scrollBody.useDuration(0);\n      scrollTo.index(group, 0);\n      eventHandler.emit('slideFocus');\n    }\n    eventStore.add(document, 'keydown', registerTabPress, false);\n    slides.forEach((slide, slideIndex) => {\n      eventStore.add(slide, 'focus', evt => {\n        if (isBoolean(watchFocus) || watchFocus(emblaApi, evt)) {\n          defaultCallback(slideIndex);\n        }\n      }, focusListenerOptions);\n    });\n  }\n  function registerTabPress(event) {\n    if (event.code === 'Tab') lastTabPressTime = new Date().getTime();\n  }\n  const self = {\n    init\n  };\n  return self;\n}\n\nfunction Vector1D(initialValue) {\n  let value = initialValue;\n  function get() {\n    return value;\n  }\n  function set(n) {\n    value = normalizeInput(n);\n  }\n  function add(n) {\n    value += normalizeInput(n);\n  }\n  function subtract(n) {\n    value -= normalizeInput(n);\n  }\n  function normalizeInput(n) {\n    return isNumber(n) ? n : n.get();\n  }\n  const self = {\n    get,\n    set,\n    add,\n    subtract\n  };\n  return self;\n}\n\nfunction Translate(axis, container) {\n  const translate = axis.scroll === 'x' ? x : y;\n  const containerStyle = container.style;\n  let previousTarget = null;\n  let disabled = false;\n  function x(n) {\n    return `translate3d(${n}px,0px,0px)`;\n  }\n  function y(n) {\n    return `translate3d(0px,${n}px,0px)`;\n  }\n  function to(target) {\n    if (disabled) return;\n    const newTarget = roundToTwoDecimals(axis.direction(target));\n    if (newTarget === previousTarget) return;\n    containerStyle.transform = translate(newTarget);\n    previousTarget = newTarget;\n  }\n  function toggleActive(active) {\n    disabled = !active;\n  }\n  function clear() {\n    if (disabled) return;\n    containerStyle.transform = '';\n    if (!container.getAttribute('style')) container.removeAttribute('style');\n  }\n  const self = {\n    clear,\n    to,\n    toggleActive\n  };\n  return self;\n}\n\nfunction SlideLooper(axis, viewSize, contentSize, slideSizes, slideSizesWithGaps, snaps, scrollSnaps, location, slides) {\n  const roundingSafety = 0.5;\n  const ascItems = arrayKeys(slideSizesWithGaps);\n  const descItems = arrayKeys(slideSizesWithGaps).reverse();\n  const loopPoints = startPoints().concat(endPoints());\n  function removeSlideSizes(indexes, from) {\n    return indexes.reduce((a, i) => {\n      return a - slideSizesWithGaps[i];\n    }, from);\n  }\n  function slidesInGap(indexes, gap) {\n    return indexes.reduce((a, i) => {\n      const remainingGap = removeSlideSizes(a, gap);\n      return remainingGap > 0 ? a.concat([i]) : a;\n    }, []);\n  }\n  function findSlideBounds(offset) {\n    return snaps.map((snap, index) => ({\n      start: snap - slideSizes[index] + roundingSafety + offset,\n      end: snap + viewSize - roundingSafety + offset\n    }));\n  }\n  function findLoopPoints(indexes, offset, isEndEdge) {\n    const slideBounds = findSlideBounds(offset);\n    return indexes.map(index => {\n      const initial = isEndEdge ? 0 : -contentSize;\n      const altered = isEndEdge ? contentSize : 0;\n      const boundEdge = isEndEdge ? 'end' : 'start';\n      const loopPoint = slideBounds[index][boundEdge];\n      return {\n        index,\n        loopPoint,\n        slideLocation: Vector1D(-1),\n        translate: Translate(axis, slides[index]),\n        target: () => location.get() > loopPoint ? initial : altered\n      };\n    });\n  }\n  function startPoints() {\n    const gap = scrollSnaps[0];\n    const indexes = slidesInGap(descItems, gap);\n    return findLoopPoints(indexes, contentSize, false);\n  }\n  function endPoints() {\n    const gap = viewSize - scrollSnaps[0] - 1;\n    const indexes = slidesInGap(ascItems, gap);\n    return findLoopPoints(indexes, -contentSize, true);\n  }\n  function canLoop() {\n    return loopPoints.every(({\n      index\n    }) => {\n      const otherIndexes = ascItems.filter(i => i !== index);\n      return removeSlideSizes(otherIndexes, viewSize) <= 0.1;\n    });\n  }\n  function loop() {\n    loopPoints.forEach(loopPoint => {\n      const {\n        target,\n        translate,\n        slideLocation\n      } = loopPoint;\n      const shiftLocation = target();\n      if (shiftLocation === slideLocation.get()) return;\n      translate.to(shiftLocation);\n      slideLocation.set(shiftLocation);\n    });\n  }\n  function clear() {\n    loopPoints.forEach(loopPoint => loopPoint.translate.clear());\n  }\n  const self = {\n    canLoop,\n    clear,\n    loop,\n    loopPoints\n  };\n  return self;\n}\n\nfunction SlidesHandler(container, eventHandler, watchSlides) {\n  let mutationObserver;\n  let destroyed = false;\n  function init(emblaApi) {\n    if (!watchSlides) return;\n    function defaultCallback(mutations) {\n      for (const mutation of mutations) {\n        if (mutation.type === 'childList') {\n          emblaApi.reInit();\n          eventHandler.emit('slidesChanged');\n          break;\n        }\n      }\n    }\n    mutationObserver = new MutationObserver(mutations => {\n      if (destroyed) return;\n      if (isBoolean(watchSlides) || watchSlides(emblaApi, mutations)) {\n        defaultCallback(mutations);\n      }\n    });\n    mutationObserver.observe(container, {\n      childList: true\n    });\n  }\n  function destroy() {\n    if (mutationObserver) mutationObserver.disconnect();\n    destroyed = true;\n  }\n  const self = {\n    init,\n    destroy\n  };\n  return self;\n}\n\nfunction SlidesInView(container, slides, eventHandler, threshold) {\n  const intersectionEntryMap = {};\n  let inViewCache = null;\n  let notInViewCache = null;\n  let intersectionObserver;\n  let destroyed = false;\n  function init() {\n    intersectionObserver = new IntersectionObserver(entries => {\n      if (destroyed) return;\n      entries.forEach(entry => {\n        const index = slides.indexOf(entry.target);\n        intersectionEntryMap[index] = entry;\n      });\n      inViewCache = null;\n      notInViewCache = null;\n      eventHandler.emit('slidesInView');\n    }, {\n      root: container.parentElement,\n      threshold\n    });\n    slides.forEach(slide => intersectionObserver.observe(slide));\n  }\n  function destroy() {\n    if (intersectionObserver) intersectionObserver.disconnect();\n    destroyed = true;\n  }\n  function createInViewList(inView) {\n    return objectKeys(intersectionEntryMap).reduce((list, slideIndex) => {\n      const index = parseInt(slideIndex);\n      const {\n        isIntersecting\n      } = intersectionEntryMap[index];\n      const inViewMatch = inView && isIntersecting;\n      const notInViewMatch = !inView && !isIntersecting;\n      if (inViewMatch || notInViewMatch) list.push(index);\n      return list;\n    }, []);\n  }\n  function get(inView = true) {\n    if (inView && inViewCache) return inViewCache;\n    if (!inView && notInViewCache) return notInViewCache;\n    const slideIndexes = createInViewList(inView);\n    if (inView) inViewCache = slideIndexes;\n    if (!inView) notInViewCache = slideIndexes;\n    return slideIndexes;\n  }\n  const self = {\n    init,\n    destroy,\n    get\n  };\n  return self;\n}\n\nfunction SlideSizes(axis, containerRect, slideRects, slides, readEdgeGap, ownerWindow) {\n  const {\n    measureSize,\n    startEdge,\n    endEdge\n  } = axis;\n  const withEdgeGap = slideRects[0] && readEdgeGap;\n  const startGap = measureStartGap();\n  const endGap = measureEndGap();\n  const slideSizes = slideRects.map(measureSize);\n  const slideSizesWithGaps = measureWithGaps();\n  function measureStartGap() {\n    if (!withEdgeGap) return 0;\n    const slideRect = slideRects[0];\n    return mathAbs(containerRect[startEdge] - slideRect[startEdge]);\n  }\n  function measureEndGap() {\n    if (!withEdgeGap) return 0;\n    const style = ownerWindow.getComputedStyle(arrayLast(slides));\n    return parseFloat(style.getPropertyValue(`margin-${endEdge}`));\n  }\n  function measureWithGaps() {\n    return slideRects.map((rect, index, rects) => {\n      const isFirst = !index;\n      const isLast = arrayIsLastIndex(rects, index);\n      if (isFirst) return slideSizes[index] + startGap;\n      if (isLast) return slideSizes[index] + endGap;\n      return rects[index + 1][startEdge] - rect[startEdge];\n    }).map(mathAbs);\n  }\n  const self = {\n    slideSizes,\n    slideSizesWithGaps,\n    startGap,\n    endGap\n  };\n  return self;\n}\n\nfunction SlidesToScroll(axis, viewSize, slidesToScroll, loop, containerRect, slideRects, startGap, endGap, pixelTolerance) {\n  const {\n    startEdge,\n    endEdge,\n    direction\n  } = axis;\n  const groupByNumber = isNumber(slidesToScroll);\n  function byNumber(array, groupSize) {\n    return arrayKeys(array).filter(i => i % groupSize === 0).map(i => array.slice(i, i + groupSize));\n  }\n  function bySize(array) {\n    if (!array.length) return [];\n    return arrayKeys(array).reduce((groups, rectB, index) => {\n      const rectA = arrayLast(groups) || 0;\n      const isFirst = rectA === 0;\n      const isLast = rectB === arrayLastIndex(array);\n      const edgeA = containerRect[startEdge] - slideRects[rectA][startEdge];\n      const edgeB = containerRect[startEdge] - slideRects[rectB][endEdge];\n      const gapA = !loop && isFirst ? direction(startGap) : 0;\n      const gapB = !loop && isLast ? direction(endGap) : 0;\n      const chunkSize = mathAbs(edgeB - gapB - (edgeA + gapA));\n      if (index && chunkSize > viewSize + pixelTolerance) groups.push(rectB);\n      if (isLast) groups.push(array.length);\n      return groups;\n    }, []).map((currentSize, index, groups) => {\n      const previousSize = Math.max(groups[index - 1] || 0);\n      return array.slice(previousSize, currentSize);\n    });\n  }\n  function groupSlides(array) {\n    return groupByNumber ? byNumber(array, slidesToScroll) : bySize(array);\n  }\n  const self = {\n    groupSlides\n  };\n  return self;\n}\n\nfunction Engine(root, container, slides, ownerDocument, ownerWindow, options, eventHandler) {\n  // Options\n  const {\n    align,\n    axis: scrollAxis,\n    direction,\n    startIndex,\n    loop,\n    duration,\n    dragFree,\n    dragThreshold,\n    inViewThreshold,\n    slidesToScroll: groupSlides,\n    skipSnaps,\n    containScroll,\n    watchResize,\n    watchSlides,\n    watchDrag,\n    watchFocus\n  } = options;\n  // Measurements\n  const pixelTolerance = 2;\n  const nodeRects = NodeRects();\n  const containerRect = nodeRects.measure(container);\n  const slideRects = slides.map(nodeRects.measure);\n  const axis = Axis(scrollAxis, direction);\n  const viewSize = axis.measureSize(containerRect);\n  const percentOfView = PercentOfView(viewSize);\n  const alignment = Alignment(align, viewSize);\n  const containSnaps = !loop && !!containScroll;\n  const readEdgeGap = loop || !!containScroll;\n  const {\n    slideSizes,\n    slideSizesWithGaps,\n    startGap,\n    endGap\n  } = SlideSizes(axis, containerRect, slideRects, slides, readEdgeGap, ownerWindow);\n  const slidesToScroll = SlidesToScroll(axis, viewSize, groupSlides, loop, containerRect, slideRects, startGap, endGap, pixelTolerance);\n  const {\n    snaps,\n    snapsAligned\n  } = ScrollSnaps(axis, alignment, containerRect, slideRects, slidesToScroll);\n  const contentSize = -arrayLast(snaps) + arrayLast(slideSizesWithGaps);\n  const {\n    snapsContained,\n    scrollContainLimit\n  } = ScrollContain(viewSize, contentSize, snapsAligned, containScroll, pixelTolerance);\n  const scrollSnaps = containSnaps ? snapsContained : snapsAligned;\n  const {\n    limit\n  } = ScrollLimit(contentSize, scrollSnaps, loop);\n  // Indexes\n  const index = Counter(arrayLastIndex(scrollSnaps), startIndex, loop);\n  const indexPrevious = index.clone();\n  const slideIndexes = arrayKeys(slides);\n  // Animation\n  const update = ({\n    dragHandler,\n    scrollBody,\n    scrollBounds,\n    options: {\n      loop\n    }\n  }) => {\n    if (!loop) scrollBounds.constrain(dragHandler.pointerDown());\n    scrollBody.seek();\n  };\n  const render = ({\n    scrollBody,\n    translate,\n    location,\n    offsetLocation,\n    previousLocation,\n    scrollLooper,\n    slideLooper,\n    dragHandler,\n    animation,\n    eventHandler,\n    scrollBounds,\n    options: {\n      loop\n    }\n  }, alpha) => {\n    const shouldSettle = scrollBody.settled();\n    const withinBounds = !scrollBounds.shouldConstrain();\n    const hasSettled = loop ? shouldSettle : shouldSettle && withinBounds;\n    const hasSettledAndIdle = hasSettled && !dragHandler.pointerDown();\n    if (hasSettledAndIdle) animation.stop();\n    const interpolatedLocation = location.get() * alpha + previousLocation.get() * (1 - alpha);\n    offsetLocation.set(interpolatedLocation);\n    if (loop) {\n      scrollLooper.loop(scrollBody.direction());\n      slideLooper.loop();\n    }\n    translate.to(offsetLocation.get());\n    if (hasSettledAndIdle) eventHandler.emit('settle');\n    if (!hasSettled) eventHandler.emit('scroll');\n  };\n  const animation = Animations(ownerDocument, ownerWindow, () => update(engine), alpha => render(engine, alpha));\n  // Shared\n  const friction = 0.68;\n  const startLocation = scrollSnaps[index.get()];\n  const location = Vector1D(startLocation);\n  const previousLocation = Vector1D(startLocation);\n  const offsetLocation = Vector1D(startLocation);\n  const target = Vector1D(startLocation);\n  const scrollBody = ScrollBody(location, offsetLocation, previousLocation, target, duration, friction);\n  const scrollTarget = ScrollTarget(loop, scrollSnaps, contentSize, limit, target);\n  const scrollTo = ScrollTo(animation, index, indexPrevious, scrollBody, scrollTarget, target, eventHandler);\n  const scrollProgress = ScrollProgress(limit);\n  const eventStore = EventStore();\n  const slidesInView = SlidesInView(container, slides, eventHandler, inViewThreshold);\n  const {\n    slideRegistry\n  } = SlideRegistry(containSnaps, containScroll, scrollSnaps, scrollContainLimit, slidesToScroll, slideIndexes);\n  const slideFocus = SlideFocus(root, slides, slideRegistry, scrollTo, scrollBody, eventStore, eventHandler, watchFocus);\n  // Engine\n  const engine = {\n    ownerDocument,\n    ownerWindow,\n    eventHandler,\n    containerRect,\n    slideRects,\n    animation,\n    axis,\n    dragHandler: DragHandler(axis, root, ownerDocument, ownerWindow, target, DragTracker(axis, ownerWindow), location, animation, scrollTo, scrollBody, scrollTarget, index, eventHandler, percentOfView, dragFree, dragThreshold, skipSnaps, friction, watchDrag),\n    eventStore,\n    percentOfView,\n    index,\n    indexPrevious,\n    limit,\n    location,\n    offsetLocation,\n    previousLocation,\n    options,\n    resizeHandler: ResizeHandler(container, eventHandler, ownerWindow, slides, axis, watchResize, nodeRects),\n    scrollBody,\n    scrollBounds: ScrollBounds(limit, offsetLocation, target, scrollBody, percentOfView),\n    scrollLooper: ScrollLooper(contentSize, limit, offsetLocation, [location, offsetLocation, previousLocation, target]),\n    scrollProgress,\n    scrollSnapList: scrollSnaps.map(scrollProgress.get),\n    scrollSnaps,\n    scrollTarget,\n    scrollTo,\n    slideLooper: SlideLooper(axis, viewSize, contentSize, slideSizes, slideSizesWithGaps, snaps, scrollSnaps, offsetLocation, slides),\n    slideFocus,\n    slidesHandler: SlidesHandler(container, eventHandler, watchSlides),\n    slidesInView,\n    slideIndexes,\n    slideRegistry,\n    slidesToScroll,\n    target,\n    translate: Translate(axis, container)\n  };\n  return engine;\n}\n\nfunction EventHandler() {\n  let listeners = {};\n  let api;\n  function init(emblaApi) {\n    api = emblaApi;\n  }\n  function getListeners(evt) {\n    return listeners[evt] || [];\n  }\n  function emit(evt) {\n    getListeners(evt).forEach(e => e(api, evt));\n    return self;\n  }\n  function on(evt, cb) {\n    listeners[evt] = getListeners(evt).concat([cb]);\n    return self;\n  }\n  function off(evt, cb) {\n    listeners[evt] = getListeners(evt).filter(e => e !== cb);\n    return self;\n  }\n  function clear() {\n    listeners = {};\n  }\n  const self = {\n    init,\n    emit,\n    off,\n    on,\n    clear\n  };\n  return self;\n}\n\nconst defaultOptions = {\n  align: 'center',\n  axis: 'x',\n  container: null,\n  slides: null,\n  containScroll: 'trimSnaps',\n  direction: 'ltr',\n  slidesToScroll: 1,\n  inViewThreshold: 0,\n  breakpoints: {},\n  dragFree: false,\n  dragThreshold: 10,\n  loop: false,\n  skipSnaps: false,\n  duration: 25,\n  startIndex: 0,\n  active: true,\n  watchDrag: true,\n  watchResize: true,\n  watchSlides: true,\n  watchFocus: true\n};\n\nfunction OptionsHandler(ownerWindow) {\n  function mergeOptions(optionsA, optionsB) {\n    return objectsMergeDeep(optionsA, optionsB || {});\n  }\n  function optionsAtMedia(options) {\n    const optionsAtMedia = options.breakpoints || {};\n    const matchedMediaOptions = objectKeys(optionsAtMedia).filter(media => ownerWindow.matchMedia(media).matches).map(media => optionsAtMedia[media]).reduce((a, mediaOption) => mergeOptions(a, mediaOption), {});\n    return mergeOptions(options, matchedMediaOptions);\n  }\n  function optionsMediaQueries(optionsList) {\n    return optionsList.map(options => objectKeys(options.breakpoints || {})).reduce((acc, mediaQueries) => acc.concat(mediaQueries), []).map(ownerWindow.matchMedia);\n  }\n  const self = {\n    mergeOptions,\n    optionsAtMedia,\n    optionsMediaQueries\n  };\n  return self;\n}\n\nfunction PluginsHandler(optionsHandler) {\n  let activePlugins = [];\n  function init(emblaApi, plugins) {\n    activePlugins = plugins.filter(({\n      options\n    }) => optionsHandler.optionsAtMedia(options).active !== false);\n    activePlugins.forEach(plugin => plugin.init(emblaApi, optionsHandler));\n    return plugins.reduce((map, plugin) => Object.assign(map, {\n      [plugin.name]: plugin\n    }), {});\n  }\n  function destroy() {\n    activePlugins = activePlugins.filter(plugin => plugin.destroy());\n  }\n  const self = {\n    init,\n    destroy\n  };\n  return self;\n}\n\nfunction EmblaCarousel(root, userOptions, userPlugins) {\n  const ownerDocument = root.ownerDocument;\n  const ownerWindow = ownerDocument.defaultView;\n  const optionsHandler = OptionsHandler(ownerWindow);\n  const pluginsHandler = PluginsHandler(optionsHandler);\n  const mediaHandlers = EventStore();\n  const eventHandler = EventHandler();\n  const {\n    mergeOptions,\n    optionsAtMedia,\n    optionsMediaQueries\n  } = optionsHandler;\n  const {\n    on,\n    off,\n    emit\n  } = eventHandler;\n  const reInit = reActivate;\n  let destroyed = false;\n  let engine;\n  let optionsBase = mergeOptions(defaultOptions, EmblaCarousel.globalOptions);\n  let options = mergeOptions(optionsBase);\n  let pluginList = [];\n  let pluginApis;\n  let container;\n  let slides;\n  function storeElements() {\n    const {\n      container: userContainer,\n      slides: userSlides\n    } = options;\n    const customContainer = isString(userContainer) ? root.querySelector(userContainer) : userContainer;\n    container = customContainer || root.children[0];\n    const customSlides = isString(userSlides) ? container.querySelectorAll(userSlides) : userSlides;\n    slides = [].slice.call(customSlides || container.children);\n  }\n  function createEngine(options) {\n    const engine = Engine(root, container, slides, ownerDocument, ownerWindow, options, eventHandler);\n    if (options.loop && !engine.slideLooper.canLoop()) {\n      const optionsWithoutLoop = Object.assign({}, options, {\n        loop: false\n      });\n      return createEngine(optionsWithoutLoop);\n    }\n    return engine;\n  }\n  function activate(withOptions, withPlugins) {\n    if (destroyed) return;\n    optionsBase = mergeOptions(optionsBase, withOptions);\n    options = optionsAtMedia(optionsBase);\n    pluginList = withPlugins || pluginList;\n    storeElements();\n    engine = createEngine(options);\n    optionsMediaQueries([optionsBase, ...pluginList.map(({\n      options\n    }) => options)]).forEach(query => mediaHandlers.add(query, 'change', reActivate));\n    if (!options.active) return;\n    engine.translate.to(engine.location.get());\n    engine.animation.init();\n    engine.slidesInView.init();\n    engine.slideFocus.init(self);\n    engine.eventHandler.init(self);\n    engine.resizeHandler.init(self);\n    engine.slidesHandler.init(self);\n    if (engine.options.loop) engine.slideLooper.loop();\n    if (container.offsetParent && slides.length) engine.dragHandler.init(self);\n    pluginApis = pluginsHandler.init(self, pluginList);\n  }\n  function reActivate(withOptions, withPlugins) {\n    const startIndex = selectedScrollSnap();\n    deActivate();\n    activate(mergeOptions({\n      startIndex\n    }, withOptions), withPlugins);\n    eventHandler.emit('reInit');\n  }\n  function deActivate() {\n    engine.dragHandler.destroy();\n    engine.eventStore.clear();\n    engine.translate.clear();\n    engine.slideLooper.clear();\n    engine.resizeHandler.destroy();\n    engine.slidesHandler.destroy();\n    engine.slidesInView.destroy();\n    engine.animation.destroy();\n    pluginsHandler.destroy();\n    mediaHandlers.clear();\n  }\n  function destroy() {\n    if (destroyed) return;\n    destroyed = true;\n    mediaHandlers.clear();\n    deActivate();\n    eventHandler.emit('destroy');\n    eventHandler.clear();\n  }\n  function scrollTo(index, jump, direction) {\n    if (!options.active || destroyed) return;\n    engine.scrollBody.useBaseFriction().useDuration(jump === true ? 0 : options.duration);\n    engine.scrollTo.index(index, direction || 0);\n  }\n  function scrollNext(jump) {\n    const next = engine.index.add(1).get();\n    scrollTo(next, jump, -1);\n  }\n  function scrollPrev(jump) {\n    const prev = engine.index.add(-1).get();\n    scrollTo(prev, jump, 1);\n  }\n  function canScrollNext() {\n    const next = engine.index.add(1).get();\n    return next !== selectedScrollSnap();\n  }\n  function canScrollPrev() {\n    const prev = engine.index.add(-1).get();\n    return prev !== selectedScrollSnap();\n  }\n  function scrollSnapList() {\n    return engine.scrollSnapList;\n  }\n  function scrollProgress() {\n    return engine.scrollProgress.get(engine.offsetLocation.get());\n  }\n  function selectedScrollSnap() {\n    return engine.index.get();\n  }\n  function previousScrollSnap() {\n    return engine.indexPrevious.get();\n  }\n  function slidesInView() {\n    return engine.slidesInView.get();\n  }\n  function slidesNotInView() {\n    return engine.slidesInView.get(false);\n  }\n  function plugins() {\n    return pluginApis;\n  }\n  function internalEngine() {\n    return engine;\n  }\n  function rootNode() {\n    return root;\n  }\n  function containerNode() {\n    return container;\n  }\n  function slideNodes() {\n    return slides;\n  }\n  const self = {\n    canScrollNext,\n    canScrollPrev,\n    containerNode,\n    internalEngine,\n    destroy,\n    off,\n    on,\n    emit,\n    plugins,\n    previousScrollSnap,\n    reInit,\n    rootNode,\n    scrollNext,\n    scrollPrev,\n    scrollProgress,\n    scrollSnapList,\n    scrollTo,\n    selectedScrollSnap,\n    slideNodes,\n    slidesInView,\n    slidesNotInView\n  };\n  activate(userOptions, userPlugins);\n  setTimeout(() => eventHandler.emit('init'), 0);\n  return self;\n}\nEmblaCarousel.globalOptions = undefined;\n\nexport { EmblaCarousel as default };\n//# sourceMappingURL=embla-carousel.esm.js.map\n", "/**\n * @license lucide-react v0.511.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport { forwardRef, createElement } from 'react';\nimport defaultAttributes from './defaultAttributes.js';\nimport { mergeClasses, hasA11yProp } from './shared/src/utils.js';\n\nconst Icon = forwardRef(\n  ({\n    color = \"currentColor\",\n    size = 24,\n    strokeWidth = 2,\n    absoluteStrokeWidth,\n    className = \"\",\n    children,\n    iconNode,\n    ...rest\n  }, ref) => createElement(\n    \"svg\",\n    {\n      ref,\n      ...defaultAttributes,\n      width: size,\n      height: size,\n      stroke: color,\n      strokeWidth: absoluteStrokeWidth ? Number(strokeWidth) * 24 / Number(size) : strokeWidth,\n      className: mergeClasses(\"lucide\", className),\n      ...!children && !hasA11yProp(rest) && { \"aria-hidden\": \"true\" },\n      ...rest\n    },\n    [\n      ...iconNode.map(([tag, attrs]) => createElement(tag, attrs)),\n      ...Array.isArray(children) ? children : [children]\n    ]\n  )\n);\n\nexport { Icon as default };\n//# sourceMappingURL=Icon.js.map\n", "/**\n * @license lucide-react v0.511.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport { forwardRef, createElement } from 'react';\nimport { mergeClasses, toKebabCase, toPascalCase } from './shared/src/utils.js';\nimport Icon from './Icon.js';\n\nconst createLucideIcon = (iconName, iconNode) => {\n  const Component = forwardRef(\n    ({ className, ...props }, ref) => createElement(Icon, {\n      ref,\n      iconNode,\n      className: mergeClasses(\n        `lucide-${toKebabCase(toPascalCase(iconName))}`,\n        `lucide-${iconName}`,\n        className\n      ),\n      ...props\n    })\n  );\n  Component.displayName = toPascalCase(iconName);\n  return Component;\n};\n\nexport { createLucideIcon as default };\n//# sourceMappingURL=createLucideIcon.js.map\n", "/**\n * @license lucide-react v0.511.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nvar defaultAttributes = {\n  xmlns: \"http://www.w3.org/2000/svg\",\n  width: 24,\n  height: 24,\n  viewBox: \"0 0 24 24\",\n  fill: \"none\",\n  stroke: \"currentColor\",\n  strokeWidth: 2,\n  strokeLinecap: \"round\",\n  strokeLinejoin: \"round\"\n};\n\nexport { defaultAttributes as default };\n//# sourceMappingURL=defaultAttributes.js.map\n", "/**\n * @license lucide-react v0.511.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\n\nconst __iconNode = [\n  [\"path\", { d: \"m12 19-7-7 7-7\", key: \"1l729n\" }],\n  [\"path\", { d: \"M19 12H5\", key: \"x3x0zl\" }]\n];\nconst ArrowLeft = createLucideIcon(\"arrow-left\", __iconNode);\n\nexport { __iconNode, ArrowLeft as default };\n//# sourceMappingURL=arrow-left.js.map\n", "/**\n * @license lucide-react v0.511.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\n\nconst __iconNode = [\n  [\"path\", { d: \"M5 12h14\", key: \"1ays0h\" }],\n  [\"path\", { d: \"m12 5 7 7-7 7\", key: \"xquz4c\" }]\n];\nconst ArrowRight = createLucideIcon(\"arrow-right\", __iconNode);\n\nexport { __iconNode, ArrowRight as default };\n//# sourceMappingURL=arrow-right.js.map\n", "/**\n * @license lucide-react v0.511.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nconst toKebabCase = (string) => string.replace(/([a-z0-9])([A-Z])/g, \"$1-$2\").toLowerCase();\nconst toCamelCase = (string) => string.replace(\n  /^([A-Z])|[\\s-_]+(\\w)/g,\n  (match, p1, p2) => p2 ? p2.toUpperCase() : p1.toLowerCase()\n);\nconst toPascalCase = (string) => {\n  const camelCase = toCamelCase(string);\n  return camelCase.charAt(0).toUpperCase() + camelCase.slice(1);\n};\nconst mergeClasses = (...classes) => classes.filter((className, index, array) => {\n  return Boolean(className) && className.trim() !== \"\" && array.indexOf(className) === index;\n}).join(\" \").trim();\nconst hasA11yProp = (props) => {\n  for (const prop in props) {\n    if (prop.startsWith(\"aria-\") || prop === \"role\" || prop === \"title\") {\n      return true;\n    }\n  }\n};\n\nexport { hasA11yProp, mergeClasses, toCamelCase, toKebabCase, toPascalCase };\n//# sourceMappingURL=utils.js.map\n"], "names": [], "sourceRoot": ""}