"use strict";
(self["webpackChunkstorybook"] = self["webpackChunkstorybook"] || []).push([["vendors-node_modules_pnpm_hookform_resolvers_5_0_1_r_b279eeaaeb6873fc01f42207d25bc29d_node_mo-2c6d34"],{

/***/ "../../node_modules/.pnpm/@hookform+resolvers@5.0.1_r_b279eeaaeb6873fc01f42207d25bc29d/node_modules/@hookform/resolvers/dist/resolvers.mjs":
/*!*************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@hookform+resolvers@5.0.1_r_b279eeaaeb6873fc01f42207d25bc29d/node_modules/@hookform/resolvers/dist/resolvers.mjs ***!
  \*************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   toNestErrors: () => (/* binding */ s),
/* harmony export */   validateFieldsNatively: () => (/* binding */ o)
/* harmony export */ });
/* harmony import */ var react_hook_form__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-hook-form */ "../../node_modules/.pnpm/react-hook-form@7.56.4_react@19.1.0/node_modules/react-hook-form/dist/index.esm.mjs");
const r=(t,r,o)=>{if(t&&"reportValidity"in t){const s=(0,react_hook_form__WEBPACK_IMPORTED_MODULE_0__.get)(o,r);t.setCustomValidity(s&&s.message||""),t.reportValidity()}},o=(e,t)=>{for(const o in t.fields){const s=t.fields[o];s&&s.ref&&"reportValidity"in s.ref?r(s.ref,o,e):s&&s.refs&&s.refs.forEach(t=>r(t,o,e))}},s=(r,s)=>{s.shouldUseNativeValidation&&o(r,s);const n={};for(const o in r){const f=(0,react_hook_form__WEBPACK_IMPORTED_MODULE_0__.get)(s.fields,o),c=Object.assign(r[o]||{},{ref:f&&f.ref});if(i(s.names||Object.keys(r),o)){const r=Object.assign({},(0,react_hook_form__WEBPACK_IMPORTED_MODULE_0__.get)(n,o));(0,react_hook_form__WEBPACK_IMPORTED_MODULE_0__.set)(r,"root",c),(0,react_hook_form__WEBPACK_IMPORTED_MODULE_0__.set)(n,o,r)}else (0,react_hook_form__WEBPACK_IMPORTED_MODULE_0__.set)(n,o,c)}return n},i=(e,t)=>{const r=n(t);return e.some(e=>n(e).match(`^${r}\\.\\d+`))};function n(e){return e.replace(/\]|\[/g,"")}
//# sourceMappingURL=resolvers.mjs.map


/***/ }),

/***/ "../../node_modules/.pnpm/@hookform+resolvers@5.0.1_r_b279eeaaeb6873fc01f42207d25bc29d/node_modules/@hookform/resolvers/zod/dist/zod.mjs":
/*!***********************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@hookform+resolvers@5.0.1_r_b279eeaaeb6873fc01f42207d25bc29d/node_modules/@hookform/resolvers/zod/dist/zod.mjs ***!
  \***********************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   zodResolver: () => (/* binding */ s)
/* harmony export */ });
/* harmony import */ var _hookform_resolvers__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @hookform/resolvers */ "../../node_modules/.pnpm/@hookform+resolvers@5.0.1_r_b279eeaaeb6873fc01f42207d25bc29d/node_modules/@hookform/resolvers/dist/resolvers.mjs");
/* harmony import */ var react_hook_form__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-hook-form */ "../../node_modules/.pnpm/react-hook-form@7.56.4_react@19.1.0/node_modules/react-hook-form/dist/index.esm.mjs");
function n(r,e){for(var n={};r.length;){var s=r[0],t=s.code,i=s.message,a=s.path.join(".");if(!n[a])if("unionErrors"in s){var u=s.unionErrors[0].errors[0];n[a]={message:u.message,type:u.code}}else n[a]={message:i,type:t};if("unionErrors"in s&&s.unionErrors.forEach(function(e){return e.errors.forEach(function(e){return r.push(e)})}),e){var c=n[a].types,f=c&&c[s.code];n[a]=(0,react_hook_form__WEBPACK_IMPORTED_MODULE_0__.appendErrors)(a,e,n,t,f?[].concat(f,s.message):s.message)}r.shift()}return n}function s(o,s,t){return void 0===t&&(t={}),function(i,a,u){try{return Promise.resolve(function(e,n){try{var a=Promise.resolve(o["sync"===t.mode?"parse":"parseAsync"](i,s)).then(function(e){return u.shouldUseNativeValidation&&(0,_hookform_resolvers__WEBPACK_IMPORTED_MODULE_1__.validateFieldsNatively)({},u),{errors:{},values:t.raw?Object.assign({},i):e}})}catch(r){return n(r)}return a&&a.then?a.then(void 0,n):a}(0,function(r){if(function(r){return Array.isArray(null==r?void 0:r.errors)}(r))return{values:{},errors:(0,_hookform_resolvers__WEBPACK_IMPORTED_MODULE_1__.toNestErrors)(n(r.errors,!u.shouldUseNativeValidation&&"all"===u.criteriaMode),u)};throw r}))}catch(r){return Promise.reject(r)}}}
//# sourceMappingURL=zod.module.js.map


/***/ }),

/***/ "../../node_modules/.pnpm/@radix-ui+react-label@2.1.7_7793a902a18004088009a6b1964436da/node_modules/@radix-ui/react-label/dist/index.mjs":
/*!***********************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@radix-ui+react-label@2.1.7_7793a902a18004088009a6b1964436da/node_modules/@radix-ui/react-label/dist/index.mjs ***!
  \***********************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   Label: () => (/* binding */ Label),
/* harmony export */   Root: () => (/* binding */ Root)
/* harmony export */ });
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ "../../node_modules/.pnpm/next@15.3.2_@babel+core@7.2_09efc9edc30971f6ad8d5c21a0ab8f1f/node_modules/next/dist/compiled/react/index.js");
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);
/* harmony import */ var _radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @radix-ui/react-primitive */ "../../node_modules/.pnpm/@radix-ui+react-primitive@2_0f3a82528133a7d37b0572d0c112c6a5/node_modules/@radix-ui/react-primitive/dist/index.mjs");
/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react/jsx-runtime */ "../../node_modules/.pnpm/next@15.3.2_@babel+core@7.2_09efc9edc30971f6ad8d5c21a0ab8f1f/node_modules/next/dist/compiled/react/jsx-runtime.js");
"use client";

// src/label.tsx



var NAME = "Label";
var Label = react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef) => {
  return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(
    _radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_2__.Primitive.label,
    {
      ...props,
      ref: forwardedRef,
      onMouseDown: (event) => {
        const target = event.target;
        if (target.closest("button, input, select, textarea")) return;
        props.onMouseDown?.(event);
        if (!event.defaultPrevented && event.detail > 1) event.preventDefault();
      }
    }
  );
});
Label.displayName = NAME;
var Root = Label;

//# sourceMappingURL=index.mjs.map


/***/ }),

/***/ "../../node_modules/.pnpm/@storybook+addon-actions@8._fdbb51b12566af5e3312bafa313d86f1/node_modules/@storybook/addon-actions/dist/chunk-4XZ63LWV.mjs":
/*!***********************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@storybook+addon-actions@8._fdbb51b12566af5e3312bafa313d86f1/node_modules/@storybook/addon-actions/dist/chunk-4XZ63LWV.mjs ***!
  \***********************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   ADDON_ID: () => (/* binding */ ADDON_ID),
/* harmony export */   CLEAR_ID: () => (/* binding */ CLEAR_ID),
/* harmony export */   CYCLIC_KEY: () => (/* binding */ CYCLIC_KEY),
/* harmony export */   EVENT_ID: () => (/* binding */ EVENT_ID),
/* harmony export */   PANEL_ID: () => (/* binding */ PANEL_ID),
/* harmony export */   PARAM_KEY: () => (/* binding */ PARAM_KEY),
/* harmony export */   __export: () => (/* binding */ __export),
/* harmony export */   action: () => (/* binding */ action),
/* harmony export */   actions: () => (/* binding */ actions),
/* harmony export */   config: () => (/* binding */ config),
/* harmony export */   configureActions: () => (/* binding */ configureActions)
/* harmony export */ });
/* harmony import */ var storybook_internal_preview_api__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! storybook/internal/preview-api */ "storybook/internal/preview-api");
/* harmony import */ var storybook_internal_preview_api__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(storybook_internal_preview_api__WEBPACK_IMPORTED_MODULE_0__);
/* harmony import */ var storybook_internal_preview_errors__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! storybook/internal/preview-errors */ "storybook/internal/preview-errors");
/* harmony import */ var storybook_internal_preview_errors__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(storybook_internal_preview_errors__WEBPACK_IMPORTED_MODULE_1__);
/* harmony import */ var _storybook_global__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @storybook/global */ "@storybook/global");
/* harmony import */ var _storybook_global__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(_storybook_global__WEBPACK_IMPORTED_MODULE_2__);
/* harmony import */ var uuid__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! uuid */ "../../node_modules/.pnpm/uuid@9.0.1/node_modules/uuid/dist/esm-browser/v4.js");
/* provided dependency */ var console = __webpack_require__(/*! ../../node_modules/.pnpm/console-browserify@1.2.0/node_modules/console-browserify/index.js */ "../../node_modules/.pnpm/console-browserify@1.2.0/node_modules/console-browserify/index.js");





var __defProp=Object.defineProperty;var __export=(target,all)=>{for(var name in all)__defProp(target,name,{get:all[name],enumerable:!0});};var PARAM_KEY="actions",ADDON_ID="storybook/actions",PANEL_ID=`${ADDON_ID}/panel`,EVENT_ID=`${ADDON_ID}/action-event`,CLEAR_ID=`${ADDON_ID}/action-clear`,CYCLIC_KEY="$___storybook.isCyclic";var config={depth:10,clearOnStoryChange:!0,limit:50},configureActions=(options={})=>{Object.assign(config,options);};var findProto=(obj,callback)=>{let proto=Object.getPrototypeOf(obj);return !proto||callback(proto)?proto:findProto(proto,callback)},isReactSyntheticEvent=e=>!!(typeof e=="object"&&e&&findProto(e,proto=>/^Synthetic(?:Base)?Event$/.test(proto.constructor.name))&&typeof e.persist=="function"),serializeArg=a=>{if(isReactSyntheticEvent(a)){let e=Object.create(a.constructor.prototype,Object.getOwnPropertyDescriptors(a));e.persist();let viewDescriptor=Object.getOwnPropertyDescriptor(e,"view"),view=viewDescriptor?.value;return typeof view=="object"&&view?.constructor.name==="Window"&&Object.defineProperty(e,"view",{...viewDescriptor,value:Object.create(view.constructor.prototype)}),e}return a},generateId=()=>typeof crypto=="object"&&typeof crypto.getRandomValues=="function"?(0,uuid__WEBPACK_IMPORTED_MODULE_3__["default"])():Date.now().toString(36)+Math.random().toString(36).substring(2);function action(name,options={}){let actionOptions={...config,...options},handler=function(...args){if(options.implicit){let storyRenderer=("__STORYBOOK_PREVIEW__" in _storybook_global__WEBPACK_IMPORTED_MODULE_2__.global?_storybook_global__WEBPACK_IMPORTED_MODULE_2__.global.__STORYBOOK_PREVIEW__:void 0)?.storyRenders.find(render=>render.phase==="playing"||render.phase==="rendering");if(storyRenderer){let deprecated=!globalThis?.FEATURES?.disallowImplicitActionsInRenderV8,error=new storybook_internal_preview_errors__WEBPACK_IMPORTED_MODULE_1__.ImplicitActionsDuringRendering({phase:storyRenderer.phase,name,deprecated});if(deprecated)console.warn(error);else throw error}}let channel=storybook_internal_preview_api__WEBPACK_IMPORTED_MODULE_0__.addons.getChannel(),id=generateId(),minDepth=5,serializedArgs=args.map(serializeArg),normalizedArgs=args.length>1?serializedArgs:serializedArgs[0],actionDisplayToEmit={id,count:0,data:{name,args:normalizedArgs},options:{...actionOptions,maxDepth:minDepth+(actionOptions.depth||3),allowFunction:actionOptions.allowFunction||!1}};channel.emit(EVENT_ID,actionDisplayToEmit);};return handler.isAction=!0,handler.implicit=options.implicit,handler}var actions=(...args)=>{let options=config,names=args;names.length===1&&Array.isArray(names[0])&&([names]=names),names.length!==1&&typeof names[names.length-1]!="string"&&(options={...config,...names.pop()});let namesObject=names[0];(names.length!==1||typeof namesObject=="string")&&(namesObject={},names.forEach(name=>{namesObject[name]=name;}));let actionsObject={};return Object.keys(namesObject).forEach(name=>{actionsObject[name]=action(namesObject[name],options);}),actionsObject};




/***/ }),

/***/ "../../node_modules/.pnpm/@storybook+addon-actions@8._fdbb51b12566af5e3312bafa313d86f1/node_modules/@storybook/addon-actions/dist/index.mjs":
/*!**************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@storybook+addon-actions@8._fdbb51b12566af5e3312bafa313d86f1/node_modules/@storybook/addon-actions/dist/index.mjs ***!
  \**************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   ADDON_ID: () => (/* reexport safe */ _chunk_4XZ63LWV_mjs__WEBPACK_IMPORTED_MODULE_0__.ADDON_ID),
/* harmony export */   CLEAR_ID: () => (/* reexport safe */ _chunk_4XZ63LWV_mjs__WEBPACK_IMPORTED_MODULE_0__.CLEAR_ID),
/* harmony export */   CYCLIC_KEY: () => (/* reexport safe */ _chunk_4XZ63LWV_mjs__WEBPACK_IMPORTED_MODULE_0__.CYCLIC_KEY),
/* harmony export */   EVENT_ID: () => (/* reexport safe */ _chunk_4XZ63LWV_mjs__WEBPACK_IMPORTED_MODULE_0__.EVENT_ID),
/* harmony export */   PANEL_ID: () => (/* reexport safe */ _chunk_4XZ63LWV_mjs__WEBPACK_IMPORTED_MODULE_0__.PANEL_ID),
/* harmony export */   PARAM_KEY: () => (/* reexport safe */ _chunk_4XZ63LWV_mjs__WEBPACK_IMPORTED_MODULE_0__.PARAM_KEY),
/* harmony export */   action: () => (/* reexport safe */ _chunk_4XZ63LWV_mjs__WEBPACK_IMPORTED_MODULE_0__.action),
/* harmony export */   actions: () => (/* reexport safe */ _chunk_4XZ63LWV_mjs__WEBPACK_IMPORTED_MODULE_0__.actions),
/* harmony export */   config: () => (/* reexport safe */ _chunk_4XZ63LWV_mjs__WEBPACK_IMPORTED_MODULE_0__.config),
/* harmony export */   configureActions: () => (/* reexport safe */ _chunk_4XZ63LWV_mjs__WEBPACK_IMPORTED_MODULE_0__.configureActions),
/* harmony export */   "default": () => (/* binding */ index_default)
/* harmony export */ });
/* harmony import */ var _chunk_4XZ63LWV_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./chunk-4XZ63LWV.mjs */ "../../node_modules/.pnpm/@storybook+addon-actions@8._fdbb51b12566af5e3312bafa313d86f1/node_modules/@storybook/addon-actions/dist/chunk-4XZ63LWV.mjs");
/* harmony import */ var storybook_internal_preview_api__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! storybook/internal/preview-api */ "storybook/internal/preview-api");
/* harmony import */ var storybook_internal_preview_api__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(storybook_internal_preview_api__WEBPACK_IMPORTED_MODULE_1__);
/* harmony import */ var _storybook_global__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @storybook/global */ "@storybook/global");
/* harmony import */ var _storybook_global__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(_storybook_global__WEBPACK_IMPORTED_MODULE_2__);





var preview_exports={};(0,_chunk_4XZ63LWV_mjs__WEBPACK_IMPORTED_MODULE_0__.__export)(preview_exports,{argsEnhancers:()=>argsEnhancers,loaders:()=>loaders});var isInInitialArgs=(name,initialArgs)=>typeof initialArgs[name]>"u"&&!(name in initialArgs),inferActionsFromArgTypesRegex=context=>{let{initialArgs,argTypes,id,parameters:{actions:actions2}}=context;if(!actions2||actions2.disable||!actions2.argTypesRegex||!argTypes)return {};let argTypesRegex=new RegExp(actions2.argTypesRegex);return Object.entries(argTypes).filter(([name])=>!!argTypesRegex.test(name)).reduce((acc,[name,argType])=>(isInInitialArgs(name,initialArgs)&&(acc[name]=(0,_chunk_4XZ63LWV_mjs__WEBPACK_IMPORTED_MODULE_0__.action)(name,{implicit:!0,id})),acc),{})},addActionsFromArgTypes=context=>{let{initialArgs,argTypes,parameters:{actions:actions2}}=context;return actions2?.disable||!argTypes?{}:Object.entries(argTypes).filter(([name,argType])=>!!argType.action).reduce((acc,[name,argType])=>(isInInitialArgs(name,initialArgs)&&(acc[name]=(0,_chunk_4XZ63LWV_mjs__WEBPACK_IMPORTED_MODULE_0__.action)(typeof argType.action=="string"?argType.action:name)),acc),{})};var argsEnhancers=[addActionsFromArgTypes,inferActionsFromArgTypesRegex];var subscribed=!1,logActionsWhenMockCalled=context=>{let{parameters:{actions:actions2}}=context;if(!actions2?.disable&&!subscribed&&"__STORYBOOK_TEST_ON_MOCK_CALL__" in _storybook_global__WEBPACK_IMPORTED_MODULE_2__.global&&typeof _storybook_global__WEBPACK_IMPORTED_MODULE_2__.global.__STORYBOOK_TEST_ON_MOCK_CALL__=="function"){let onMockCall=_storybook_global__WEBPACK_IMPORTED_MODULE_2__.global.__STORYBOOK_TEST_ON_MOCK_CALL__;onMockCall((mock,args)=>{let name=mock.getMockName();name!=="spy"&&(!/^next\/.*::/.test(name)||["next/router::useRouter()","next/navigation::useRouter()","next/navigation::redirect","next/cache::","next/headers::cookies().set","next/headers::cookies().delete","next/headers::headers().set","next/headers::headers().delete"].some(prefix=>name.startsWith(prefix)))&&(0,_chunk_4XZ63LWV_mjs__WEBPACK_IMPORTED_MODULE_0__.action)(name)(args);}),subscribed=!0;}},loaders=[logActionsWhenMockCalled];var index_default=()=>(0,storybook_internal_preview_api__WEBPACK_IMPORTED_MODULE_1__.definePreview)(preview_exports);




/***/ }),

/***/ "../../node_modules/.pnpm/react-hook-form@7.56.4_react@19.1.0/node_modules/react-hook-form/dist/index.esm.mjs":
/*!********************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/react-hook-form@7.56.4_react@19.1.0/node_modules/react-hook-form/dist/index.esm.mjs ***!
  \********************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   Controller: () => (/* binding */ Controller),
/* harmony export */   Form: () => (/* binding */ Form),
/* harmony export */   FormProvider: () => (/* binding */ FormProvider),
/* harmony export */   appendErrors: () => (/* binding */ appendErrors),
/* harmony export */   createFormControl: () => (/* binding */ createFormControl),
/* harmony export */   get: () => (/* binding */ get),
/* harmony export */   set: () => (/* binding */ set),
/* harmony export */   useController: () => (/* binding */ useController),
/* harmony export */   useFieldArray: () => (/* binding */ useFieldArray),
/* harmony export */   useForm: () => (/* binding */ useForm),
/* harmony export */   useFormContext: () => (/* binding */ useFormContext),
/* harmony export */   useFormState: () => (/* binding */ useFormState),
/* harmony export */   useWatch: () => (/* binding */ useWatch)
/* harmony export */ });
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ "../../node_modules/.pnpm/next@15.3.2_@babel+core@7.2_09efc9edc30971f6ad8d5c21a0ab8f1f/node_modules/next/dist/compiled/react/index.js");
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);



var isCheckBoxInput = (element) => element.type === 'checkbox';

var isDateObject = (value) => value instanceof Date;

var isNullOrUndefined = (value) => value == null;

const isObjectType = (value) => typeof value === 'object';
var isObject = (value) => !isNullOrUndefined(value) &&
    !Array.isArray(value) &&
    isObjectType(value) &&
    !isDateObject(value);

var getEventValue = (event) => isObject(event) && event.target
    ? isCheckBoxInput(event.target)
        ? event.target.checked
        : event.target.value
    : event;

var getNodeParentName = (name) => name.substring(0, name.search(/\.\d+(\.|$)/)) || name;

var isNameInFieldArray = (names, name) => names.has(getNodeParentName(name));

var isPlainObject = (tempObject) => {
    const prototypeCopy = tempObject.constructor && tempObject.constructor.prototype;
    return (isObject(prototypeCopy) && prototypeCopy.hasOwnProperty('isPrototypeOf'));
};

var isWeb = typeof window !== 'undefined' &&
    typeof window.HTMLElement !== 'undefined' &&
    typeof document !== 'undefined';

function cloneObject(data) {
    let copy;
    const isArray = Array.isArray(data);
    const isFileListInstance = typeof FileList !== 'undefined' ? data instanceof FileList : false;
    if (data instanceof Date) {
        copy = new Date(data);
    }
    else if (data instanceof Set) {
        copy = new Set(data);
    }
    else if (!(isWeb && (data instanceof Blob || isFileListInstance)) &&
        (isArray || isObject(data))) {
        copy = isArray ? [] : {};
        if (!isArray && !isPlainObject(data)) {
            copy = data;
        }
        else {
            for (const key in data) {
                if (data.hasOwnProperty(key)) {
                    copy[key] = cloneObject(data[key]);
                }
            }
        }
    }
    else {
        return data;
    }
    return copy;
}

var compact = (value) => Array.isArray(value) ? value.filter(Boolean) : [];

var isUndefined = (val) => val === undefined;

var get = (object, path, defaultValue) => {
    if (!path || !isObject(object)) {
        return defaultValue;
    }
    const result = compact(path.split(/[,[\].]+?/)).reduce((result, key) => isNullOrUndefined(result) ? result : result[key], object);
    return isUndefined(result) || result === object
        ? isUndefined(object[path])
            ? defaultValue
            : object[path]
        : result;
};

var isBoolean = (value) => typeof value === 'boolean';

var isKey = (value) => /^\w*$/.test(value);

var stringToPath = (input) => compact(input.replace(/["|']|\]/g, '').split(/\.|\[/));

var set = (object, path, value) => {
    let index = -1;
    const tempPath = isKey(path) ? [path] : stringToPath(path);
    const length = tempPath.length;
    const lastIndex = length - 1;
    while (++index < length) {
        const key = tempPath[index];
        let newValue = value;
        if (index !== lastIndex) {
            const objValue = object[key];
            newValue =
                isObject(objValue) || Array.isArray(objValue)
                    ? objValue
                    : !isNaN(+tempPath[index + 1])
                        ? []
                        : {};
        }
        if (key === '__proto__' || key === 'constructor' || key === 'prototype') {
            return;
        }
        object[key] = newValue;
        object = object[key];
    }
};

const EVENTS = {
    BLUR: 'blur',
    FOCUS_OUT: 'focusout',
    CHANGE: 'change',
};
const VALIDATION_MODE = {
    onBlur: 'onBlur',
    onChange: 'onChange',
    onSubmit: 'onSubmit',
    onTouched: 'onTouched',
    all: 'all',
};
const INPUT_VALIDATION_RULES = {
    max: 'max',
    min: 'min',
    maxLength: 'maxLength',
    minLength: 'minLength',
    pattern: 'pattern',
    required: 'required',
    validate: 'validate',
};

const HookFormContext = react__WEBPACK_IMPORTED_MODULE_0___default().createContext(null);
/**
 * This custom hook allows you to access the form context. useFormContext is intended to be used in deeply nested structures, where it would become inconvenient to pass the context as a prop. To be used with {@link FormProvider}.
 *
 * @remarks
 * [API](https://react-hook-form.com/docs/useformcontext) • [Demo](https://codesandbox.io/s/react-hook-form-v7-form-context-ytudi)
 *
 * @returns return all useForm methods
 *
 * @example
 * ```tsx
 * function App() {
 *   const methods = useForm();
 *   const onSubmit = data => console.log(data);
 *
 *   return (
 *     <FormProvider {...methods} >
 *       <form onSubmit={methods.handleSubmit(onSubmit)}>
 *         <NestedInput />
 *         <input type="submit" />
 *       </form>
 *     </FormProvider>
 *   );
 * }
 *
 *  function NestedInput() {
 *   const { register } = useFormContext(); // retrieve all hook methods
 *   return <input {...register("test")} />;
 * }
 * ```
 */
const useFormContext = () => react__WEBPACK_IMPORTED_MODULE_0___default().useContext(HookFormContext);
/**
 * A provider component that propagates the `useForm` methods to all children components via [React Context](https://reactjs.org/docs/context.html) API. To be used with {@link useFormContext}.
 *
 * @remarks
 * [API](https://react-hook-form.com/docs/useformcontext) • [Demo](https://codesandbox.io/s/react-hook-form-v7-form-context-ytudi)
 *
 * @param props - all useForm methods
 *
 * @example
 * ```tsx
 * function App() {
 *   const methods = useForm();
 *   const onSubmit = data => console.log(data);
 *
 *   return (
 *     <FormProvider {...methods} >
 *       <form onSubmit={methods.handleSubmit(onSubmit)}>
 *         <NestedInput />
 *         <input type="submit" />
 *       </form>
 *     </FormProvider>
 *   );
 * }
 *
 *  function NestedInput() {
 *   const { register } = useFormContext(); // retrieve all hook methods
 *   return <input {...register("test")} />;
 * }
 * ```
 */
const FormProvider = (props) => {
    const { children, ...data } = props;
    return (react__WEBPACK_IMPORTED_MODULE_0___default().createElement(HookFormContext.Provider, { value: data }, children));
};

var getProxyFormState = (formState, control, localProxyFormState, isRoot = true) => {
    const result = {
        defaultValues: control._defaultValues,
    };
    for (const key in formState) {
        Object.defineProperty(result, key, {
            get: () => {
                const _key = key;
                if (control._proxyFormState[_key] !== VALIDATION_MODE.all) {
                    control._proxyFormState[_key] = !isRoot || VALIDATION_MODE.all;
                }
                localProxyFormState && (localProxyFormState[_key] = true);
                return formState[_key];
            },
        });
    }
    return result;
};

const useIsomorphicLayoutEffect = typeof window !== 'undefined' ? react__WEBPACK_IMPORTED_MODULE_0__.useLayoutEffect : react__WEBPACK_IMPORTED_MODULE_0__.useEffect;

/**
 * This custom hook allows you to subscribe to each form state, and isolate the re-render at the custom hook level. It has its scope in terms of form state subscription, so it would not affect other useFormState and useForm. Using this hook can reduce the re-render impact on large and complex form application.
 *
 * @remarks
 * [API](https://react-hook-form.com/docs/useformstate) • [Demo](https://codesandbox.io/s/useformstate-75xly)
 *
 * @param props - include options on specify fields to subscribe. {@link UseFormStateReturn}
 *
 * @example
 * ```tsx
 * function App() {
 *   const { register, handleSubmit, control } = useForm({
 *     defaultValues: {
 *     firstName: "firstName"
 *   }});
 *   const { dirtyFields } = useFormState({
 *     control
 *   });
 *   const onSubmit = (data) => console.log(data);
 *
 *   return (
 *     <form onSubmit={handleSubmit(onSubmit)}>
 *       <input {...register("firstName")} placeholder="First Name" />
 *       {dirtyFields.firstName && <p>Field is dirty.</p>}
 *       <input type="submit" />
 *     </form>
 *   );
 * }
 * ```
 */
function useFormState(props) {
    const methods = useFormContext();
    const { control = methods.control, disabled, name, exact } = props || {};
    const [formState, updateFormState] = react__WEBPACK_IMPORTED_MODULE_0___default().useState(control._formState);
    const _localProxyFormState = react__WEBPACK_IMPORTED_MODULE_0___default().useRef({
        isDirty: false,
        isLoading: false,
        dirtyFields: false,
        touchedFields: false,
        validatingFields: false,
        isValidating: false,
        isValid: false,
        errors: false,
    });
    useIsomorphicLayoutEffect(() => control._subscribe({
        name: name,
        formState: _localProxyFormState.current,
        exact,
        callback: (formState) => {
            !disabled &&
                updateFormState({
                    ...control._formState,
                    ...formState,
                });
        },
    }), [name, disabled, exact]);
    react__WEBPACK_IMPORTED_MODULE_0___default().useEffect(() => {
        _localProxyFormState.current.isValid && control._setValid(true);
    }, [control]);
    return react__WEBPACK_IMPORTED_MODULE_0___default().useMemo(() => getProxyFormState(formState, control, _localProxyFormState.current, false), [formState, control]);
}

var isString = (value) => typeof value === 'string';

var generateWatchOutput = (names, _names, formValues, isGlobal, defaultValue) => {
    if (isString(names)) {
        isGlobal && _names.watch.add(names);
        return get(formValues, names, defaultValue);
    }
    if (Array.isArray(names)) {
        return names.map((fieldName) => (isGlobal && _names.watch.add(fieldName), get(formValues, fieldName)));
    }
    isGlobal && (_names.watchAll = true);
    return formValues;
};

/**
 * Custom hook to subscribe to field change and isolate re-rendering at the component level.
 *
 * @remarks
 *
 * [API](https://react-hook-form.com/docs/usewatch) • [Demo](https://codesandbox.io/s/react-hook-form-v7-ts-usewatch-h9i5e)
 *
 * @example
 * ```tsx
 * const { control } = useForm();
 * const values = useWatch({
 *   name: "fieldName"
 *   control,
 * })
 * ```
 */
function useWatch(props) {
    const methods = useFormContext();
    const { control = methods.control, name, defaultValue, disabled, exact, } = props || {};
    const _defaultValue = react__WEBPACK_IMPORTED_MODULE_0___default().useRef(defaultValue);
    const [value, updateValue] = react__WEBPACK_IMPORTED_MODULE_0___default().useState(control._getWatch(name, _defaultValue.current));
    useIsomorphicLayoutEffect(() => control._subscribe({
        name: name,
        formState: {
            values: true,
        },
        exact,
        callback: (formState) => !disabled &&
            updateValue(generateWatchOutput(name, control._names, formState.values || control._formValues, false, _defaultValue.current)),
    }), [name, control, disabled, exact]);
    react__WEBPACK_IMPORTED_MODULE_0___default().useEffect(() => control._removeUnmounted());
    return value;
}

/**
 * Custom hook to work with controlled component, this function provide you with both form and field level state. Re-render is isolated at the hook level.
 *
 * @remarks
 * [API](https://react-hook-form.com/docs/usecontroller) • [Demo](https://codesandbox.io/s/usecontroller-0o8px)
 *
 * @param props - the path name to the form field value, and validation rules.
 *
 * @returns field properties, field and form state. {@link UseControllerReturn}
 *
 * @example
 * ```tsx
 * function Input(props) {
 *   const { field, fieldState, formState } = useController(props);
 *   return (
 *     <div>
 *       <input {...field} placeholder={props.name} />
 *       <p>{fieldState.isTouched && "Touched"}</p>
 *       <p>{formState.isSubmitted ? "submitted" : ""}</p>
 *     </div>
 *   );
 * }
 * ```
 */
function useController(props) {
    const methods = useFormContext();
    const { name, disabled, control = methods.control, shouldUnregister } = props;
    const isArrayField = isNameInFieldArray(control._names.array, name);
    const value = useWatch({
        control,
        name,
        defaultValue: get(control._formValues, name, get(control._defaultValues, name, props.defaultValue)),
        exact: true,
    });
    const formState = useFormState({
        control,
        name,
        exact: true,
    });
    const _props = react__WEBPACK_IMPORTED_MODULE_0___default().useRef(props);
    const _registerProps = react__WEBPACK_IMPORTED_MODULE_0___default().useRef(control.register(name, {
        ...props.rules,
        value,
        ...(isBoolean(props.disabled) ? { disabled: props.disabled } : {}),
    }));
    const fieldState = react__WEBPACK_IMPORTED_MODULE_0___default().useMemo(() => Object.defineProperties({}, {
        invalid: {
            enumerable: true,
            get: () => !!get(formState.errors, name),
        },
        isDirty: {
            enumerable: true,
            get: () => !!get(formState.dirtyFields, name),
        },
        isTouched: {
            enumerable: true,
            get: () => !!get(formState.touchedFields, name),
        },
        isValidating: {
            enumerable: true,
            get: () => !!get(formState.validatingFields, name),
        },
        error: {
            enumerable: true,
            get: () => get(formState.errors, name),
        },
    }), [formState, name]);
    const onChange = react__WEBPACK_IMPORTED_MODULE_0___default().useCallback((event) => _registerProps.current.onChange({
        target: {
            value: getEventValue(event),
            name: name,
        },
        type: EVENTS.CHANGE,
    }), [name]);
    const onBlur = react__WEBPACK_IMPORTED_MODULE_0___default().useCallback(() => _registerProps.current.onBlur({
        target: {
            value: get(control._formValues, name),
            name: name,
        },
        type: EVENTS.BLUR,
    }), [name, control._formValues]);
    const ref = react__WEBPACK_IMPORTED_MODULE_0___default().useCallback((elm) => {
        const field = get(control._fields, name);
        if (field && elm) {
            field._f.ref = {
                focus: () => elm.focus(),
                select: () => elm.select(),
                setCustomValidity: (message) => elm.setCustomValidity(message),
                reportValidity: () => elm.reportValidity(),
            };
        }
    }, [control._fields, name]);
    const field = react__WEBPACK_IMPORTED_MODULE_0___default().useMemo(() => ({
        name,
        value,
        ...(isBoolean(disabled) || formState.disabled
            ? { disabled: formState.disabled || disabled }
            : {}),
        onChange,
        onBlur,
        ref,
    }), [name, disabled, formState.disabled, onChange, onBlur, ref, value]);
    react__WEBPACK_IMPORTED_MODULE_0___default().useEffect(() => {
        const _shouldUnregisterField = control._options.shouldUnregister || shouldUnregister;
        control.register(name, {
            ..._props.current.rules,
            ...(isBoolean(_props.current.disabled)
                ? { disabled: _props.current.disabled }
                : {}),
        });
        const updateMounted = (name, value) => {
            const field = get(control._fields, name);
            if (field && field._f) {
                field._f.mount = value;
            }
        };
        updateMounted(name, true);
        if (_shouldUnregisterField) {
            const value = cloneObject(get(control._options.defaultValues, name));
            set(control._defaultValues, name, value);
            if (isUndefined(get(control._formValues, name))) {
                set(control._formValues, name, value);
            }
        }
        !isArrayField && control.register(name);
        return () => {
            (isArrayField
                ? _shouldUnregisterField && !control._state.action
                : _shouldUnregisterField)
                ? control.unregister(name)
                : updateMounted(name, false);
        };
    }, [name, control, isArrayField, shouldUnregister]);
    react__WEBPACK_IMPORTED_MODULE_0___default().useEffect(() => {
        control._setDisabledField({
            disabled,
            name,
        });
    }, [disabled, name, control]);
    return react__WEBPACK_IMPORTED_MODULE_0___default().useMemo(() => ({
        field,
        formState,
        fieldState,
    }), [field, formState, fieldState]);
}

/**
 * Component based on `useController` hook to work with controlled component.
 *
 * @remarks
 * [API](https://react-hook-form.com/docs/usecontroller/controller) • [Demo](https://codesandbox.io/s/react-hook-form-v6-controller-ts-jwyzw) • [Video](https://www.youtube.com/watch?v=N2UNk_UCVyA)
 *
 * @param props - the path name to the form field value, and validation rules.
 *
 * @returns provide field handler functions, field and form state.
 *
 * @example
 * ```tsx
 * function App() {
 *   const { control } = useForm<FormValues>({
 *     defaultValues: {
 *       test: ""
 *     }
 *   });
 *
 *   return (
 *     <form>
 *       <Controller
 *         control={control}
 *         name="test"
 *         render={({ field: { onChange, onBlur, value, ref }, formState, fieldState }) => (
 *           <>
 *             <input
 *               onChange={onChange} // send value to hook form
 *               onBlur={onBlur} // notify when input is touched
 *               value={value} // return updated value
 *               ref={ref} // set ref for focus management
 *             />
 *             <p>{formState.isSubmitted ? "submitted" : ""}</p>
 *             <p>{fieldState.isTouched ? "touched" : ""}</p>
 *           </>
 *         )}
 *       />
 *     </form>
 *   );
 * }
 * ```
 */
const Controller = (props) => props.render(useController(props));

const flatten = (obj) => {
    const output = {};
    for (const key of Object.keys(obj)) {
        if (isObjectType(obj[key]) && obj[key] !== null) {
            const nested = flatten(obj[key]);
            for (const nestedKey of Object.keys(nested)) {
                output[`${key}.${nestedKey}`] = nested[nestedKey];
            }
        }
        else {
            output[key] = obj[key];
        }
    }
    return output;
};

const POST_REQUEST = 'post';
/**
 * Form component to manage submission.
 *
 * @param props - to setup submission detail. {@link FormProps}
 *
 * @returns form component or headless render prop.
 *
 * @example
 * ```tsx
 * function App() {
 *   const { control, formState: { errors } } = useForm();
 *
 *   return (
 *     <Form action="/api" control={control}>
 *       <input {...register("name")} />
 *       <p>{errors?.root?.server && 'Server error'}</p>
 *       <button>Submit</button>
 *     </Form>
 *   );
 * }
 * ```
 */
function Form(props) {
    const methods = useFormContext();
    const [mounted, setMounted] = react__WEBPACK_IMPORTED_MODULE_0___default().useState(false);
    const { control = methods.control, onSubmit, children, action, method = POST_REQUEST, headers, encType, onError, render, onSuccess, validateStatus, ...rest } = props;
    const submit = async (event) => {
        let hasError = false;
        let type = '';
        await control.handleSubmit(async (data) => {
            const formData = new FormData();
            let formDataJson = '';
            try {
                formDataJson = JSON.stringify(data);
            }
            catch (_a) { }
            const flattenFormValues = flatten(control._formValues);
            for (const key in flattenFormValues) {
                formData.append(key, flattenFormValues[key]);
            }
            if (onSubmit) {
                await onSubmit({
                    data,
                    event,
                    method,
                    formData,
                    formDataJson,
                });
            }
            if (action) {
                try {
                    const shouldStringifySubmissionData = [
                        headers && headers['Content-Type'],
                        encType,
                    ].some((value) => value && value.includes('json'));
                    const response = await fetch(String(action), {
                        method,
                        headers: {
                            ...headers,
                            ...(encType ? { 'Content-Type': encType } : {}),
                        },
                        body: shouldStringifySubmissionData ? formDataJson : formData,
                    });
                    if (response &&
                        (validateStatus
                            ? !validateStatus(response.status)
                            : response.status < 200 || response.status >= 300)) {
                        hasError = true;
                        onError && onError({ response });
                        type = String(response.status);
                    }
                    else {
                        onSuccess && onSuccess({ response });
                    }
                }
                catch (error) {
                    hasError = true;
                    onError && onError({ error });
                }
            }
        })(event);
        if (hasError && props.control) {
            props.control._subjects.state.next({
                isSubmitSuccessful: false,
            });
            props.control.setError('root.server', {
                type,
            });
        }
    };
    react__WEBPACK_IMPORTED_MODULE_0___default().useEffect(() => {
        setMounted(true);
    }, []);
    return render ? (react__WEBPACK_IMPORTED_MODULE_0___default().createElement((react__WEBPACK_IMPORTED_MODULE_0___default().Fragment), null, render({
        submit,
    }))) : (react__WEBPACK_IMPORTED_MODULE_0___default().createElement("form", { noValidate: mounted, action: action, method: method, encType: encType, onSubmit: submit, ...rest }, children));
}

var appendErrors = (name, validateAllFieldCriteria, errors, type, message) => validateAllFieldCriteria
    ? {
        ...errors[name],
        types: {
            ...(errors[name] && errors[name].types ? errors[name].types : {}),
            [type]: message || true,
        },
    }
    : {};

var convertToArrayPayload = (value) => (Array.isArray(value) ? value : [value]);

var createSubject = () => {
    let _observers = [];
    const next = (value) => {
        for (const observer of _observers) {
            observer.next && observer.next(value);
        }
    };
    const subscribe = (observer) => {
        _observers.push(observer);
        return {
            unsubscribe: () => {
                _observers = _observers.filter((o) => o !== observer);
            },
        };
    };
    const unsubscribe = () => {
        _observers = [];
    };
    return {
        get observers() {
            return _observers;
        },
        next,
        subscribe,
        unsubscribe,
    };
};

var isPrimitive = (value) => isNullOrUndefined(value) || !isObjectType(value);

function deepEqual(object1, object2) {
    if (isPrimitive(object1) || isPrimitive(object2)) {
        return object1 === object2;
    }
    if (isDateObject(object1) && isDateObject(object2)) {
        return object1.getTime() === object2.getTime();
    }
    const keys1 = Object.keys(object1);
    const keys2 = Object.keys(object2);
    if (keys1.length !== keys2.length) {
        return false;
    }
    for (const key of keys1) {
        const val1 = object1[key];
        if (!keys2.includes(key)) {
            return false;
        }
        if (key !== 'ref') {
            const val2 = object2[key];
            if ((isDateObject(val1) && isDateObject(val2)) ||
                (isObject(val1) && isObject(val2)) ||
                (Array.isArray(val1) && Array.isArray(val2))
                ? !deepEqual(val1, val2)
                : val1 !== val2) {
                return false;
            }
        }
    }
    return true;
}

var isEmptyObject = (value) => isObject(value) && !Object.keys(value).length;

var isFileInput = (element) => element.type === 'file';

var isFunction = (value) => typeof value === 'function';

var isHTMLElement = (value) => {
    if (!isWeb) {
        return false;
    }
    const owner = value ? value.ownerDocument : 0;
    return (value instanceof
        (owner && owner.defaultView ? owner.defaultView.HTMLElement : HTMLElement));
};

var isMultipleSelect = (element) => element.type === `select-multiple`;

var isRadioInput = (element) => element.type === 'radio';

var isRadioOrCheckbox = (ref) => isRadioInput(ref) || isCheckBoxInput(ref);

var live = (ref) => isHTMLElement(ref) && ref.isConnected;

function baseGet(object, updatePath) {
    const length = updatePath.slice(0, -1).length;
    let index = 0;
    while (index < length) {
        object = isUndefined(object) ? index++ : object[updatePath[index++]];
    }
    return object;
}
function isEmptyArray(obj) {
    for (const key in obj) {
        if (obj.hasOwnProperty(key) && !isUndefined(obj[key])) {
            return false;
        }
    }
    return true;
}
function unset(object, path) {
    const paths = Array.isArray(path)
        ? path
        : isKey(path)
            ? [path]
            : stringToPath(path);
    const childObject = paths.length === 1 ? object : baseGet(object, paths);
    const index = paths.length - 1;
    const key = paths[index];
    if (childObject) {
        delete childObject[key];
    }
    if (index !== 0 &&
        ((isObject(childObject) && isEmptyObject(childObject)) ||
            (Array.isArray(childObject) && isEmptyArray(childObject)))) {
        unset(object, paths.slice(0, -1));
    }
    return object;
}

var objectHasFunction = (data) => {
    for (const key in data) {
        if (isFunction(data[key])) {
            return true;
        }
    }
    return false;
};

function markFieldsDirty(data, fields = {}) {
    const isParentNodeArray = Array.isArray(data);
    if (isObject(data) || isParentNodeArray) {
        for (const key in data) {
            if (Array.isArray(data[key]) ||
                (isObject(data[key]) && !objectHasFunction(data[key]))) {
                fields[key] = Array.isArray(data[key]) ? [] : {};
                markFieldsDirty(data[key], fields[key]);
            }
            else if (!isNullOrUndefined(data[key])) {
                fields[key] = true;
            }
        }
    }
    return fields;
}
function getDirtyFieldsFromDefaultValues(data, formValues, dirtyFieldsFromValues) {
    const isParentNodeArray = Array.isArray(data);
    if (isObject(data) || isParentNodeArray) {
        for (const key in data) {
            if (Array.isArray(data[key]) ||
                (isObject(data[key]) && !objectHasFunction(data[key]))) {
                if (isUndefined(formValues) ||
                    isPrimitive(dirtyFieldsFromValues[key])) {
                    dirtyFieldsFromValues[key] = Array.isArray(data[key])
                        ? markFieldsDirty(data[key], [])
                        : { ...markFieldsDirty(data[key]) };
                }
                else {
                    getDirtyFieldsFromDefaultValues(data[key], isNullOrUndefined(formValues) ? {} : formValues[key], dirtyFieldsFromValues[key]);
                }
            }
            else {
                dirtyFieldsFromValues[key] = !deepEqual(data[key], formValues[key]);
            }
        }
    }
    return dirtyFieldsFromValues;
}
var getDirtyFields = (defaultValues, formValues) => getDirtyFieldsFromDefaultValues(defaultValues, formValues, markFieldsDirty(formValues));

const defaultResult = {
    value: false,
    isValid: false,
};
const validResult = { value: true, isValid: true };
var getCheckboxValue = (options) => {
    if (Array.isArray(options)) {
        if (options.length > 1) {
            const values = options
                .filter((option) => option && option.checked && !option.disabled)
                .map((option) => option.value);
            return { value: values, isValid: !!values.length };
        }
        return options[0].checked && !options[0].disabled
            ? // @ts-expect-error expected to work in the browser
                options[0].attributes && !isUndefined(options[0].attributes.value)
                    ? isUndefined(options[0].value) || options[0].value === ''
                        ? validResult
                        : { value: options[0].value, isValid: true }
                    : validResult
            : defaultResult;
    }
    return defaultResult;
};

var getFieldValueAs = (value, { valueAsNumber, valueAsDate, setValueAs }) => isUndefined(value)
    ? value
    : valueAsNumber
        ? value === ''
            ? NaN
            : value
                ? +value
                : value
        : valueAsDate && isString(value)
            ? new Date(value)
            : setValueAs
                ? setValueAs(value)
                : value;

const defaultReturn = {
    isValid: false,
    value: null,
};
var getRadioValue = (options) => Array.isArray(options)
    ? options.reduce((previous, option) => option && option.checked && !option.disabled
        ? {
            isValid: true,
            value: option.value,
        }
        : previous, defaultReturn)
    : defaultReturn;

function getFieldValue(_f) {
    const ref = _f.ref;
    if (isFileInput(ref)) {
        return ref.files;
    }
    if (isRadioInput(ref)) {
        return getRadioValue(_f.refs).value;
    }
    if (isMultipleSelect(ref)) {
        return [...ref.selectedOptions].map(({ value }) => value);
    }
    if (isCheckBoxInput(ref)) {
        return getCheckboxValue(_f.refs).value;
    }
    return getFieldValueAs(isUndefined(ref.value) ? _f.ref.value : ref.value, _f);
}

var getResolverOptions = (fieldsNames, _fields, criteriaMode, shouldUseNativeValidation) => {
    const fields = {};
    for (const name of fieldsNames) {
        const field = get(_fields, name);
        field && set(fields, name, field._f);
    }
    return {
        criteriaMode,
        names: [...fieldsNames],
        fields,
        shouldUseNativeValidation,
    };
};

var isRegex = (value) => value instanceof RegExp;

var getRuleValue = (rule) => isUndefined(rule)
    ? rule
    : isRegex(rule)
        ? rule.source
        : isObject(rule)
            ? isRegex(rule.value)
                ? rule.value.source
                : rule.value
            : rule;

var getValidationModes = (mode) => ({
    isOnSubmit: !mode || mode === VALIDATION_MODE.onSubmit,
    isOnBlur: mode === VALIDATION_MODE.onBlur,
    isOnChange: mode === VALIDATION_MODE.onChange,
    isOnAll: mode === VALIDATION_MODE.all,
    isOnTouch: mode === VALIDATION_MODE.onTouched,
});

const ASYNC_FUNCTION = 'AsyncFunction';
var hasPromiseValidation = (fieldReference) => !!fieldReference &&
    !!fieldReference.validate &&
    !!((isFunction(fieldReference.validate) &&
        fieldReference.validate.constructor.name === ASYNC_FUNCTION) ||
        (isObject(fieldReference.validate) &&
            Object.values(fieldReference.validate).find((validateFunction) => validateFunction.constructor.name === ASYNC_FUNCTION)));

var hasValidation = (options) => options.mount &&
    (options.required ||
        options.min ||
        options.max ||
        options.maxLength ||
        options.minLength ||
        options.pattern ||
        options.validate);

var isWatched = (name, _names, isBlurEvent) => !isBlurEvent &&
    (_names.watchAll ||
        _names.watch.has(name) ||
        [..._names.watch].some((watchName) => name.startsWith(watchName) &&
            /^\.\w+/.test(name.slice(watchName.length))));

const iterateFieldsByAction = (fields, action, fieldsNames, abortEarly) => {
    for (const key of fieldsNames || Object.keys(fields)) {
        const field = get(fields, key);
        if (field) {
            const { _f, ...currentField } = field;
            if (_f) {
                if (_f.refs && _f.refs[0] && action(_f.refs[0], key) && !abortEarly) {
                    return true;
                }
                else if (_f.ref && action(_f.ref, _f.name) && !abortEarly) {
                    return true;
                }
                else {
                    if (iterateFieldsByAction(currentField, action)) {
                        break;
                    }
                }
            }
            else if (isObject(currentField)) {
                if (iterateFieldsByAction(currentField, action)) {
                    break;
                }
            }
        }
    }
    return;
};

function schemaErrorLookup(errors, _fields, name) {
    const error = get(errors, name);
    if (error || isKey(name)) {
        return {
            error,
            name,
        };
    }
    const names = name.split('.');
    while (names.length) {
        const fieldName = names.join('.');
        const field = get(_fields, fieldName);
        const foundError = get(errors, fieldName);
        if (field && !Array.isArray(field) && name !== fieldName) {
            return { name };
        }
        if (foundError && foundError.type) {
            return {
                name: fieldName,
                error: foundError,
            };
        }
        names.pop();
    }
    return {
        name,
    };
}

var shouldRenderFormState = (formStateData, _proxyFormState, updateFormState, isRoot) => {
    updateFormState(formStateData);
    const { name, ...formState } = formStateData;
    return (isEmptyObject(formState) ||
        Object.keys(formState).length >= Object.keys(_proxyFormState).length ||
        Object.keys(formState).find((key) => _proxyFormState[key] ===
            (!isRoot || VALIDATION_MODE.all)));
};

var shouldSubscribeByName = (name, signalName, exact) => !name ||
    !signalName ||
    name === signalName ||
    convertToArrayPayload(name).some((currentName) => currentName &&
        (exact
            ? currentName === signalName
            : currentName.startsWith(signalName) ||
                signalName.startsWith(currentName)));

var skipValidation = (isBlurEvent, isTouched, isSubmitted, reValidateMode, mode) => {
    if (mode.isOnAll) {
        return false;
    }
    else if (!isSubmitted && mode.isOnTouch) {
        return !(isTouched || isBlurEvent);
    }
    else if (isSubmitted ? reValidateMode.isOnBlur : mode.isOnBlur) {
        return !isBlurEvent;
    }
    else if (isSubmitted ? reValidateMode.isOnChange : mode.isOnChange) {
        return isBlurEvent;
    }
    return true;
};

var unsetEmptyArray = (ref, name) => !compact(get(ref, name)).length && unset(ref, name);

var updateFieldArrayRootError = (errors, error, name) => {
    const fieldArrayErrors = convertToArrayPayload(get(errors, name));
    set(fieldArrayErrors, 'root', error[name]);
    set(errors, name, fieldArrayErrors);
    return errors;
};

var isMessage = (value) => isString(value);

function getValidateError(result, ref, type = 'validate') {
    if (isMessage(result) ||
        (Array.isArray(result) && result.every(isMessage)) ||
        (isBoolean(result) && !result)) {
        return {
            type,
            message: isMessage(result) ? result : '',
            ref,
        };
    }
}

var getValueAndMessage = (validationData) => isObject(validationData) && !isRegex(validationData)
    ? validationData
    : {
        value: validationData,
        message: '',
    };

var validateField = async (field, disabledFieldNames, formValues, validateAllFieldCriteria, shouldUseNativeValidation, isFieldArray) => {
    const { ref, refs, required, maxLength, minLength, min, max, pattern, validate, name, valueAsNumber, mount, } = field._f;
    const inputValue = get(formValues, name);
    if (!mount || disabledFieldNames.has(name)) {
        return {};
    }
    const inputRef = refs ? refs[0] : ref;
    const setCustomValidity = (message) => {
        if (shouldUseNativeValidation && inputRef.reportValidity) {
            inputRef.setCustomValidity(isBoolean(message) ? '' : message || '');
            inputRef.reportValidity();
        }
    };
    const error = {};
    const isRadio = isRadioInput(ref);
    const isCheckBox = isCheckBoxInput(ref);
    const isRadioOrCheckbox = isRadio || isCheckBox;
    const isEmpty = ((valueAsNumber || isFileInput(ref)) &&
        isUndefined(ref.value) &&
        isUndefined(inputValue)) ||
        (isHTMLElement(ref) && ref.value === '') ||
        inputValue === '' ||
        (Array.isArray(inputValue) && !inputValue.length);
    const appendErrorsCurry = appendErrors.bind(null, name, validateAllFieldCriteria, error);
    const getMinMaxMessage = (exceedMax, maxLengthMessage, minLengthMessage, maxType = INPUT_VALIDATION_RULES.maxLength, minType = INPUT_VALIDATION_RULES.minLength) => {
        const message = exceedMax ? maxLengthMessage : minLengthMessage;
        error[name] = {
            type: exceedMax ? maxType : minType,
            message,
            ref,
            ...appendErrorsCurry(exceedMax ? maxType : minType, message),
        };
    };
    if (isFieldArray
        ? !Array.isArray(inputValue) || !inputValue.length
        : required &&
            ((!isRadioOrCheckbox && (isEmpty || isNullOrUndefined(inputValue))) ||
                (isBoolean(inputValue) && !inputValue) ||
                (isCheckBox && !getCheckboxValue(refs).isValid) ||
                (isRadio && !getRadioValue(refs).isValid))) {
        const { value, message } = isMessage(required)
            ? { value: !!required, message: required }
            : getValueAndMessage(required);
        if (value) {
            error[name] = {
                type: INPUT_VALIDATION_RULES.required,
                message,
                ref: inputRef,
                ...appendErrorsCurry(INPUT_VALIDATION_RULES.required, message),
            };
            if (!validateAllFieldCriteria) {
                setCustomValidity(message);
                return error;
            }
        }
    }
    if (!isEmpty && (!isNullOrUndefined(min) || !isNullOrUndefined(max))) {
        let exceedMax;
        let exceedMin;
        const maxOutput = getValueAndMessage(max);
        const minOutput = getValueAndMessage(min);
        if (!isNullOrUndefined(inputValue) && !isNaN(inputValue)) {
            const valueNumber = ref.valueAsNumber ||
                (inputValue ? +inputValue : inputValue);
            if (!isNullOrUndefined(maxOutput.value)) {
                exceedMax = valueNumber > maxOutput.value;
            }
            if (!isNullOrUndefined(minOutput.value)) {
                exceedMin = valueNumber < minOutput.value;
            }
        }
        else {
            const valueDate = ref.valueAsDate || new Date(inputValue);
            const convertTimeToDate = (time) => new Date(new Date().toDateString() + ' ' + time);
            const isTime = ref.type == 'time';
            const isWeek = ref.type == 'week';
            if (isString(maxOutput.value) && inputValue) {
                exceedMax = isTime
                    ? convertTimeToDate(inputValue) > convertTimeToDate(maxOutput.value)
                    : isWeek
                        ? inputValue > maxOutput.value
                        : valueDate > new Date(maxOutput.value);
            }
            if (isString(minOutput.value) && inputValue) {
                exceedMin = isTime
                    ? convertTimeToDate(inputValue) < convertTimeToDate(minOutput.value)
                    : isWeek
                        ? inputValue < minOutput.value
                        : valueDate < new Date(minOutput.value);
            }
        }
        if (exceedMax || exceedMin) {
            getMinMaxMessage(!!exceedMax, maxOutput.message, minOutput.message, INPUT_VALIDATION_RULES.max, INPUT_VALIDATION_RULES.min);
            if (!validateAllFieldCriteria) {
                setCustomValidity(error[name].message);
                return error;
            }
        }
    }
    if ((maxLength || minLength) &&
        !isEmpty &&
        (isString(inputValue) || (isFieldArray && Array.isArray(inputValue)))) {
        const maxLengthOutput = getValueAndMessage(maxLength);
        const minLengthOutput = getValueAndMessage(minLength);
        const exceedMax = !isNullOrUndefined(maxLengthOutput.value) &&
            inputValue.length > +maxLengthOutput.value;
        const exceedMin = !isNullOrUndefined(minLengthOutput.value) &&
            inputValue.length < +minLengthOutput.value;
        if (exceedMax || exceedMin) {
            getMinMaxMessage(exceedMax, maxLengthOutput.message, minLengthOutput.message);
            if (!validateAllFieldCriteria) {
                setCustomValidity(error[name].message);
                return error;
            }
        }
    }
    if (pattern && !isEmpty && isString(inputValue)) {
        const { value: patternValue, message } = getValueAndMessage(pattern);
        if (isRegex(patternValue) && !inputValue.match(patternValue)) {
            error[name] = {
                type: INPUT_VALIDATION_RULES.pattern,
                message,
                ref,
                ...appendErrorsCurry(INPUT_VALIDATION_RULES.pattern, message),
            };
            if (!validateAllFieldCriteria) {
                setCustomValidity(message);
                return error;
            }
        }
    }
    if (validate) {
        if (isFunction(validate)) {
            const result = await validate(inputValue, formValues);
            const validateError = getValidateError(result, inputRef);
            if (validateError) {
                error[name] = {
                    ...validateError,
                    ...appendErrorsCurry(INPUT_VALIDATION_RULES.validate, validateError.message),
                };
                if (!validateAllFieldCriteria) {
                    setCustomValidity(validateError.message);
                    return error;
                }
            }
        }
        else if (isObject(validate)) {
            let validationResult = {};
            for (const key in validate) {
                if (!isEmptyObject(validationResult) && !validateAllFieldCriteria) {
                    break;
                }
                const validateError = getValidateError(await validate[key](inputValue, formValues), inputRef, key);
                if (validateError) {
                    validationResult = {
                        ...validateError,
                        ...appendErrorsCurry(key, validateError.message),
                    };
                    setCustomValidity(validateError.message);
                    if (validateAllFieldCriteria) {
                        error[name] = validationResult;
                    }
                }
            }
            if (!isEmptyObject(validationResult)) {
                error[name] = {
                    ref: inputRef,
                    ...validationResult,
                };
                if (!validateAllFieldCriteria) {
                    return error;
                }
            }
        }
    }
    setCustomValidity(true);
    return error;
};

const defaultOptions = {
    mode: VALIDATION_MODE.onSubmit,
    reValidateMode: VALIDATION_MODE.onChange,
    shouldFocusError: true,
};
function createFormControl(props = {}) {
    let _options = {
        ...defaultOptions,
        ...props,
    };
    let _formState = {
        submitCount: 0,
        isDirty: false,
        isReady: false,
        isLoading: isFunction(_options.defaultValues),
        isValidating: false,
        isSubmitted: false,
        isSubmitting: false,
        isSubmitSuccessful: false,
        isValid: false,
        touchedFields: {},
        dirtyFields: {},
        validatingFields: {},
        errors: _options.errors || {},
        disabled: _options.disabled || false,
    };
    const _fields = {};
    let _defaultValues = isObject(_options.defaultValues) || isObject(_options.values)
        ? cloneObject(_options.defaultValues || _options.values) || {}
        : {};
    let _formValues = _options.shouldUnregister
        ? {}
        : cloneObject(_defaultValues);
    let _state = {
        action: false,
        mount: false,
        watch: false,
    };
    let _names = {
        mount: new Set(),
        disabled: new Set(),
        unMount: new Set(),
        array: new Set(),
        watch: new Set(),
    };
    let delayErrorCallback;
    let timer = 0;
    const _proxyFormState = {
        isDirty: false,
        dirtyFields: false,
        validatingFields: false,
        touchedFields: false,
        isValidating: false,
        isValid: false,
        errors: false,
    };
    let _proxySubscribeFormState = {
        ..._proxyFormState,
    };
    const _subjects = {
        array: createSubject(),
        state: createSubject(),
    };
    const shouldDisplayAllAssociatedErrors = _options.criteriaMode === VALIDATION_MODE.all;
    const debounce = (callback) => (wait) => {
        clearTimeout(timer);
        timer = setTimeout(callback, wait);
    };
    const _setValid = async (shouldUpdateValid) => {
        if (!_options.disabled &&
            (_proxyFormState.isValid ||
                _proxySubscribeFormState.isValid ||
                shouldUpdateValid)) {
            const isValid = _options.resolver
                ? isEmptyObject((await _runSchema()).errors)
                : await executeBuiltInValidation(_fields, true);
            if (isValid !== _formState.isValid) {
                _subjects.state.next({
                    isValid,
                });
            }
        }
    };
    const _updateIsValidating = (names, isValidating) => {
        if (!_options.disabled &&
            (_proxyFormState.isValidating ||
                _proxyFormState.validatingFields ||
                _proxySubscribeFormState.isValidating ||
                _proxySubscribeFormState.validatingFields)) {
            (names || Array.from(_names.mount)).forEach((name) => {
                if (name) {
                    isValidating
                        ? set(_formState.validatingFields, name, isValidating)
                        : unset(_formState.validatingFields, name);
                }
            });
            _subjects.state.next({
                validatingFields: _formState.validatingFields,
                isValidating: !isEmptyObject(_formState.validatingFields),
            });
        }
    };
    const _setFieldArray = (name, values = [], method, args, shouldSetValues = true, shouldUpdateFieldsAndState = true) => {
        if (args && method && !_options.disabled) {
            _state.action = true;
            if (shouldUpdateFieldsAndState && Array.isArray(get(_fields, name))) {
                const fieldValues = method(get(_fields, name), args.argA, args.argB);
                shouldSetValues && set(_fields, name, fieldValues);
            }
            if (shouldUpdateFieldsAndState &&
                Array.isArray(get(_formState.errors, name))) {
                const errors = method(get(_formState.errors, name), args.argA, args.argB);
                shouldSetValues && set(_formState.errors, name, errors);
                unsetEmptyArray(_formState.errors, name);
            }
            if ((_proxyFormState.touchedFields ||
                _proxySubscribeFormState.touchedFields) &&
                shouldUpdateFieldsAndState &&
                Array.isArray(get(_formState.touchedFields, name))) {
                const touchedFields = method(get(_formState.touchedFields, name), args.argA, args.argB);
                shouldSetValues && set(_formState.touchedFields, name, touchedFields);
            }
            if (_proxyFormState.dirtyFields || _proxySubscribeFormState.dirtyFields) {
                _formState.dirtyFields = getDirtyFields(_defaultValues, _formValues);
            }
            _subjects.state.next({
                name,
                isDirty: _getDirty(name, values),
                dirtyFields: _formState.dirtyFields,
                errors: _formState.errors,
                isValid: _formState.isValid,
            });
        }
        else {
            set(_formValues, name, values);
        }
    };
    const updateErrors = (name, error) => {
        set(_formState.errors, name, error);
        _subjects.state.next({
            errors: _formState.errors,
        });
    };
    const _setErrors = (errors) => {
        _formState.errors = errors;
        _subjects.state.next({
            errors: _formState.errors,
            isValid: false,
        });
    };
    const updateValidAndValue = (name, shouldSkipSetValueAs, value, ref) => {
        const field = get(_fields, name);
        if (field) {
            const defaultValue = get(_formValues, name, isUndefined(value) ? get(_defaultValues, name) : value);
            isUndefined(defaultValue) ||
                (ref && ref.defaultChecked) ||
                shouldSkipSetValueAs
                ? set(_formValues, name, shouldSkipSetValueAs ? defaultValue : getFieldValue(field._f))
                : setFieldValue(name, defaultValue);
            _state.mount && _setValid();
        }
    };
    const updateTouchAndDirty = (name, fieldValue, isBlurEvent, shouldDirty, shouldRender) => {
        let shouldUpdateField = false;
        let isPreviousDirty = false;
        const output = {
            name,
        };
        if (!_options.disabled) {
            if (!isBlurEvent || shouldDirty) {
                if (_proxyFormState.isDirty || _proxySubscribeFormState.isDirty) {
                    isPreviousDirty = _formState.isDirty;
                    _formState.isDirty = output.isDirty = _getDirty();
                    shouldUpdateField = isPreviousDirty !== output.isDirty;
                }
                const isCurrentFieldPristine = deepEqual(get(_defaultValues, name), fieldValue);
                isPreviousDirty = !!get(_formState.dirtyFields, name);
                isCurrentFieldPristine
                    ? unset(_formState.dirtyFields, name)
                    : set(_formState.dirtyFields, name, true);
                output.dirtyFields = _formState.dirtyFields;
                shouldUpdateField =
                    shouldUpdateField ||
                        ((_proxyFormState.dirtyFields ||
                            _proxySubscribeFormState.dirtyFields) &&
                            isPreviousDirty !== !isCurrentFieldPristine);
            }
            if (isBlurEvent) {
                const isPreviousFieldTouched = get(_formState.touchedFields, name);
                if (!isPreviousFieldTouched) {
                    set(_formState.touchedFields, name, isBlurEvent);
                    output.touchedFields = _formState.touchedFields;
                    shouldUpdateField =
                        shouldUpdateField ||
                            ((_proxyFormState.touchedFields ||
                                _proxySubscribeFormState.touchedFields) &&
                                isPreviousFieldTouched !== isBlurEvent);
                }
            }
            shouldUpdateField && shouldRender && _subjects.state.next(output);
        }
        return shouldUpdateField ? output : {};
    };
    const shouldRenderByError = (name, isValid, error, fieldState) => {
        const previousFieldError = get(_formState.errors, name);
        const shouldUpdateValid = (_proxyFormState.isValid || _proxySubscribeFormState.isValid) &&
            isBoolean(isValid) &&
            _formState.isValid !== isValid;
        if (_options.delayError && error) {
            delayErrorCallback = debounce(() => updateErrors(name, error));
            delayErrorCallback(_options.delayError);
        }
        else {
            clearTimeout(timer);
            delayErrorCallback = null;
            error
                ? set(_formState.errors, name, error)
                : unset(_formState.errors, name);
        }
        if ((error ? !deepEqual(previousFieldError, error) : previousFieldError) ||
            !isEmptyObject(fieldState) ||
            shouldUpdateValid) {
            const updatedFormState = {
                ...fieldState,
                ...(shouldUpdateValid && isBoolean(isValid) ? { isValid } : {}),
                errors: _formState.errors,
                name,
            };
            _formState = {
                ..._formState,
                ...updatedFormState,
            };
            _subjects.state.next(updatedFormState);
        }
    };
    const _runSchema = async (name) => {
        _updateIsValidating(name, true);
        const result = await _options.resolver(_formValues, _options.context, getResolverOptions(name || _names.mount, _fields, _options.criteriaMode, _options.shouldUseNativeValidation));
        _updateIsValidating(name);
        return result;
    };
    const executeSchemaAndUpdateState = async (names) => {
        const { errors } = await _runSchema(names);
        if (names) {
            for (const name of names) {
                const error = get(errors, name);
                error
                    ? set(_formState.errors, name, error)
                    : unset(_formState.errors, name);
            }
        }
        else {
            _formState.errors = errors;
        }
        return errors;
    };
    const executeBuiltInValidation = async (fields, shouldOnlyCheckValid, context = {
        valid: true,
    }) => {
        for (const name in fields) {
            const field = fields[name];
            if (field) {
                const { _f, ...fieldValue } = field;
                if (_f) {
                    const isFieldArrayRoot = _names.array.has(_f.name);
                    const isPromiseFunction = field._f && hasPromiseValidation(field._f);
                    if (isPromiseFunction && _proxyFormState.validatingFields) {
                        _updateIsValidating([name], true);
                    }
                    const fieldError = await validateField(field, _names.disabled, _formValues, shouldDisplayAllAssociatedErrors, _options.shouldUseNativeValidation && !shouldOnlyCheckValid, isFieldArrayRoot);
                    if (isPromiseFunction && _proxyFormState.validatingFields) {
                        _updateIsValidating([name]);
                    }
                    if (fieldError[_f.name]) {
                        context.valid = false;
                        if (shouldOnlyCheckValid) {
                            break;
                        }
                    }
                    !shouldOnlyCheckValid &&
                        (get(fieldError, _f.name)
                            ? isFieldArrayRoot
                                ? updateFieldArrayRootError(_formState.errors, fieldError, _f.name)
                                : set(_formState.errors, _f.name, fieldError[_f.name])
                            : unset(_formState.errors, _f.name));
                }
                !isEmptyObject(fieldValue) &&
                    (await executeBuiltInValidation(fieldValue, shouldOnlyCheckValid, context));
            }
        }
        return context.valid;
    };
    const _removeUnmounted = () => {
        for (const name of _names.unMount) {
            const field = get(_fields, name);
            field &&
                (field._f.refs
                    ? field._f.refs.every((ref) => !live(ref))
                    : !live(field._f.ref)) &&
                unregister(name);
        }
        _names.unMount = new Set();
    };
    const _getDirty = (name, data) => !_options.disabled &&
        (name && data && set(_formValues, name, data),
            !deepEqual(getValues(), _defaultValues));
    const _getWatch = (names, defaultValue, isGlobal) => generateWatchOutput(names, _names, {
        ...(_state.mount
            ? _formValues
            : isUndefined(defaultValue)
                ? _defaultValues
                : isString(names)
                    ? { [names]: defaultValue }
                    : defaultValue),
    }, isGlobal, defaultValue);
    const _getFieldArray = (name) => compact(get(_state.mount ? _formValues : _defaultValues, name, _options.shouldUnregister ? get(_defaultValues, name, []) : []));
    const setFieldValue = (name, value, options = {}) => {
        const field = get(_fields, name);
        let fieldValue = value;
        if (field) {
            const fieldReference = field._f;
            if (fieldReference) {
                !fieldReference.disabled &&
                    set(_formValues, name, getFieldValueAs(value, fieldReference));
                fieldValue =
                    isHTMLElement(fieldReference.ref) && isNullOrUndefined(value)
                        ? ''
                        : value;
                if (isMultipleSelect(fieldReference.ref)) {
                    [...fieldReference.ref.options].forEach((optionRef) => (optionRef.selected = fieldValue.includes(optionRef.value)));
                }
                else if (fieldReference.refs) {
                    if (isCheckBoxInput(fieldReference.ref)) {
                        fieldReference.refs.forEach((checkboxRef) => {
                            if (!checkboxRef.defaultChecked || !checkboxRef.disabled) {
                                if (Array.isArray(fieldValue)) {
                                    checkboxRef.checked = !!fieldValue.find((data) => data === checkboxRef.value);
                                }
                                else {
                                    checkboxRef.checked =
                                        fieldValue === checkboxRef.value || !!fieldValue;
                                }
                            }
                        });
                    }
                    else {
                        fieldReference.refs.forEach((radioRef) => (radioRef.checked = radioRef.value === fieldValue));
                    }
                }
                else if (isFileInput(fieldReference.ref)) {
                    fieldReference.ref.value = '';
                }
                else {
                    fieldReference.ref.value = fieldValue;
                    if (!fieldReference.ref.type) {
                        _subjects.state.next({
                            name,
                            values: cloneObject(_formValues),
                        });
                    }
                }
            }
        }
        (options.shouldDirty || options.shouldTouch) &&
            updateTouchAndDirty(name, fieldValue, options.shouldTouch, options.shouldDirty, true);
        options.shouldValidate && trigger(name);
    };
    const setValues = (name, value, options) => {
        for (const fieldKey in value) {
            if (!value.hasOwnProperty(fieldKey)) {
                return;
            }
            const fieldValue = value[fieldKey];
            const fieldName = `${name}.${fieldKey}`;
            const field = get(_fields, fieldName);
            (_names.array.has(name) ||
                isObject(fieldValue) ||
                (field && !field._f)) &&
                !isDateObject(fieldValue)
                ? setValues(fieldName, fieldValue, options)
                : setFieldValue(fieldName, fieldValue, options);
        }
    };
    const setValue = (name, value, options = {}) => {
        const field = get(_fields, name);
        const isFieldArray = _names.array.has(name);
        const cloneValue = cloneObject(value);
        set(_formValues, name, cloneValue);
        if (isFieldArray) {
            _subjects.array.next({
                name,
                values: cloneObject(_formValues),
            });
            if ((_proxyFormState.isDirty ||
                _proxyFormState.dirtyFields ||
                _proxySubscribeFormState.isDirty ||
                _proxySubscribeFormState.dirtyFields) &&
                options.shouldDirty) {
                _subjects.state.next({
                    name,
                    dirtyFields: getDirtyFields(_defaultValues, _formValues),
                    isDirty: _getDirty(name, cloneValue),
                });
            }
        }
        else {
            field && !field._f && !isNullOrUndefined(cloneValue)
                ? setValues(name, cloneValue, options)
                : setFieldValue(name, cloneValue, options);
        }
        isWatched(name, _names) && _subjects.state.next({ ..._formState });
        _subjects.state.next({
            name: _state.mount ? name : undefined,
            values: cloneObject(_formValues),
        });
    };
    const onChange = async (event) => {
        _state.mount = true;
        const target = event.target;
        let name = target.name;
        let isFieldValueUpdated = true;
        const field = get(_fields, name);
        const _updateIsFieldValueUpdated = (fieldValue) => {
            isFieldValueUpdated =
                Number.isNaN(fieldValue) ||
                    (isDateObject(fieldValue) && isNaN(fieldValue.getTime())) ||
                    deepEqual(fieldValue, get(_formValues, name, fieldValue));
        };
        const validationModeBeforeSubmit = getValidationModes(_options.mode);
        const validationModeAfterSubmit = getValidationModes(_options.reValidateMode);
        if (field) {
            let error;
            let isValid;
            const fieldValue = target.type
                ? getFieldValue(field._f)
                : getEventValue(event);
            const isBlurEvent = event.type === EVENTS.BLUR || event.type === EVENTS.FOCUS_OUT;
            const shouldSkipValidation = (!hasValidation(field._f) &&
                !_options.resolver &&
                !get(_formState.errors, name) &&
                !field._f.deps) ||
                skipValidation(isBlurEvent, get(_formState.touchedFields, name), _formState.isSubmitted, validationModeAfterSubmit, validationModeBeforeSubmit);
            const watched = isWatched(name, _names, isBlurEvent);
            set(_formValues, name, fieldValue);
            if (isBlurEvent) {
                field._f.onBlur && field._f.onBlur(event);
                delayErrorCallback && delayErrorCallback(0);
            }
            else if (field._f.onChange) {
                field._f.onChange(event);
            }
            const fieldState = updateTouchAndDirty(name, fieldValue, isBlurEvent);
            const shouldRender = !isEmptyObject(fieldState) || watched;
            !isBlurEvent &&
                _subjects.state.next({
                    name,
                    type: event.type,
                    values: cloneObject(_formValues),
                });
            if (shouldSkipValidation) {
                if (_proxyFormState.isValid || _proxySubscribeFormState.isValid) {
                    if (_options.mode === 'onBlur') {
                        if (isBlurEvent) {
                            _setValid();
                        }
                    }
                    else if (!isBlurEvent) {
                        _setValid();
                    }
                }
                return (shouldRender &&
                    _subjects.state.next({ name, ...(watched ? {} : fieldState) }));
            }
            !isBlurEvent && watched && _subjects.state.next({ ..._formState });
            if (_options.resolver) {
                const { errors } = await _runSchema([name]);
                _updateIsFieldValueUpdated(fieldValue);
                if (isFieldValueUpdated) {
                    const previousErrorLookupResult = schemaErrorLookup(_formState.errors, _fields, name);
                    const errorLookupResult = schemaErrorLookup(errors, _fields, previousErrorLookupResult.name || name);
                    error = errorLookupResult.error;
                    name = errorLookupResult.name;
                    isValid = isEmptyObject(errors);
                }
            }
            else {
                _updateIsValidating([name], true);
                error = (await validateField(field, _names.disabled, _formValues, shouldDisplayAllAssociatedErrors, _options.shouldUseNativeValidation))[name];
                _updateIsValidating([name]);
                _updateIsFieldValueUpdated(fieldValue);
                if (isFieldValueUpdated) {
                    if (error) {
                        isValid = false;
                    }
                    else if (_proxyFormState.isValid ||
                        _proxySubscribeFormState.isValid) {
                        isValid = await executeBuiltInValidation(_fields, true);
                    }
                }
            }
            if (isFieldValueUpdated) {
                field._f.deps &&
                    trigger(field._f.deps);
                shouldRenderByError(name, isValid, error, fieldState);
            }
        }
    };
    const _focusInput = (ref, key) => {
        if (get(_formState.errors, key) && ref.focus) {
            ref.focus();
            return 1;
        }
        return;
    };
    const trigger = async (name, options = {}) => {
        let isValid;
        let validationResult;
        const fieldNames = convertToArrayPayload(name);
        if (_options.resolver) {
            const errors = await executeSchemaAndUpdateState(isUndefined(name) ? name : fieldNames);
            isValid = isEmptyObject(errors);
            validationResult = name
                ? !fieldNames.some((name) => get(errors, name))
                : isValid;
        }
        else if (name) {
            validationResult = (await Promise.all(fieldNames.map(async (fieldName) => {
                const field = get(_fields, fieldName);
                return await executeBuiltInValidation(field && field._f ? { [fieldName]: field } : field);
            }))).every(Boolean);
            !(!validationResult && !_formState.isValid) && _setValid();
        }
        else {
            validationResult = isValid = await executeBuiltInValidation(_fields);
        }
        _subjects.state.next({
            ...(!isString(name) ||
                ((_proxyFormState.isValid || _proxySubscribeFormState.isValid) &&
                    isValid !== _formState.isValid)
                ? {}
                : { name }),
            ...(_options.resolver || !name ? { isValid } : {}),
            errors: _formState.errors,
        });
        options.shouldFocus &&
            !validationResult &&
            iterateFieldsByAction(_fields, _focusInput, name ? fieldNames : _names.mount);
        return validationResult;
    };
    const getValues = (fieldNames) => {
        const values = {
            ...(_state.mount ? _formValues : _defaultValues),
        };
        return isUndefined(fieldNames)
            ? values
            : isString(fieldNames)
                ? get(values, fieldNames)
                : fieldNames.map((name) => get(values, name));
    };
    const getFieldState = (name, formState) => ({
        invalid: !!get((formState || _formState).errors, name),
        isDirty: !!get((formState || _formState).dirtyFields, name),
        error: get((formState || _formState).errors, name),
        isValidating: !!get(_formState.validatingFields, name),
        isTouched: !!get((formState || _formState).touchedFields, name),
    });
    const clearErrors = (name) => {
        name &&
            convertToArrayPayload(name).forEach((inputName) => unset(_formState.errors, inputName));
        _subjects.state.next({
            errors: name ? _formState.errors : {},
        });
    };
    const setError = (name, error, options) => {
        const ref = (get(_fields, name, { _f: {} })._f || {}).ref;
        const currentError = get(_formState.errors, name) || {};
        // Don't override existing error messages elsewhere in the object tree.
        const { ref: currentRef, message, type, ...restOfErrorTree } = currentError;
        set(_formState.errors, name, {
            ...restOfErrorTree,
            ...error,
            ref,
        });
        _subjects.state.next({
            name,
            errors: _formState.errors,
            isValid: false,
        });
        options && options.shouldFocus && ref && ref.focus && ref.focus();
    };
    const watch = (name, defaultValue) => isFunction(name)
        ? _subjects.state.subscribe({
            next: (payload) => name(_getWatch(undefined, defaultValue), payload),
        })
        : _getWatch(name, defaultValue, true);
    const _subscribe = (props) => _subjects.state.subscribe({
        next: (formState) => {
            if (shouldSubscribeByName(props.name, formState.name, props.exact) &&
                shouldRenderFormState(formState, props.formState || _proxyFormState, _setFormState, props.reRenderRoot)) {
                props.callback({
                    values: { ..._formValues },
                    ..._formState,
                    ...formState,
                });
            }
        },
    }).unsubscribe;
    const subscribe = (props) => {
        _state.mount = true;
        _proxySubscribeFormState = {
            ..._proxySubscribeFormState,
            ...props.formState,
        };
        return _subscribe({
            ...props,
            formState: _proxySubscribeFormState,
        });
    };
    const unregister = (name, options = {}) => {
        for (const fieldName of name ? convertToArrayPayload(name) : _names.mount) {
            _names.mount.delete(fieldName);
            _names.array.delete(fieldName);
            if (!options.keepValue) {
                unset(_fields, fieldName);
                unset(_formValues, fieldName);
            }
            !options.keepError && unset(_formState.errors, fieldName);
            !options.keepDirty && unset(_formState.dirtyFields, fieldName);
            !options.keepTouched && unset(_formState.touchedFields, fieldName);
            !options.keepIsValidating &&
                unset(_formState.validatingFields, fieldName);
            !_options.shouldUnregister &&
                !options.keepDefaultValue &&
                unset(_defaultValues, fieldName);
        }
        _subjects.state.next({
            values: cloneObject(_formValues),
        });
        _subjects.state.next({
            ..._formState,
            ...(!options.keepDirty ? {} : { isDirty: _getDirty() }),
        });
        !options.keepIsValid && _setValid();
    };
    const _setDisabledField = ({ disabled, name, }) => {
        if ((isBoolean(disabled) && _state.mount) ||
            !!disabled ||
            _names.disabled.has(name)) {
            disabled ? _names.disabled.add(name) : _names.disabled.delete(name);
        }
    };
    const register = (name, options = {}) => {
        let field = get(_fields, name);
        const disabledIsDefined = isBoolean(options.disabled) || isBoolean(_options.disabled);
        set(_fields, name, {
            ...(field || {}),
            _f: {
                ...(field && field._f ? field._f : { ref: { name } }),
                name,
                mount: true,
                ...options,
            },
        });
        _names.mount.add(name);
        if (field) {
            _setDisabledField({
                disabled: isBoolean(options.disabled)
                    ? options.disabled
                    : _options.disabled,
                name,
            });
        }
        else {
            updateValidAndValue(name, true, options.value);
        }
        return {
            ...(disabledIsDefined
                ? { disabled: options.disabled || _options.disabled }
                : {}),
            ...(_options.progressive
                ? {
                    required: !!options.required,
                    min: getRuleValue(options.min),
                    max: getRuleValue(options.max),
                    minLength: getRuleValue(options.minLength),
                    maxLength: getRuleValue(options.maxLength),
                    pattern: getRuleValue(options.pattern),
                }
                : {}),
            name,
            onChange,
            onBlur: onChange,
            ref: (ref) => {
                if (ref) {
                    register(name, options);
                    field = get(_fields, name);
                    const fieldRef = isUndefined(ref.value)
                        ? ref.querySelectorAll
                            ? ref.querySelectorAll('input,select,textarea')[0] || ref
                            : ref
                        : ref;
                    const radioOrCheckbox = isRadioOrCheckbox(fieldRef);
                    const refs = field._f.refs || [];
                    if (radioOrCheckbox
                        ? refs.find((option) => option === fieldRef)
                        : fieldRef === field._f.ref) {
                        return;
                    }
                    set(_fields, name, {
                        _f: {
                            ...field._f,
                            ...(radioOrCheckbox
                                ? {
                                    refs: [
                                        ...refs.filter(live),
                                        fieldRef,
                                        ...(Array.isArray(get(_defaultValues, name)) ? [{}] : []),
                                    ],
                                    ref: { type: fieldRef.type, name },
                                }
                                : { ref: fieldRef }),
                        },
                    });
                    updateValidAndValue(name, false, undefined, fieldRef);
                }
                else {
                    field = get(_fields, name, {});
                    if (field._f) {
                        field._f.mount = false;
                    }
                    (_options.shouldUnregister || options.shouldUnregister) &&
                        !(isNameInFieldArray(_names.array, name) && _state.action) &&
                        _names.unMount.add(name);
                }
            },
        };
    };
    const _focusError = () => _options.shouldFocusError &&
        iterateFieldsByAction(_fields, _focusInput, _names.mount);
    const _disableForm = (disabled) => {
        if (isBoolean(disabled)) {
            _subjects.state.next({ disabled });
            iterateFieldsByAction(_fields, (ref, name) => {
                const currentField = get(_fields, name);
                if (currentField) {
                    ref.disabled = currentField._f.disabled || disabled;
                    if (Array.isArray(currentField._f.refs)) {
                        currentField._f.refs.forEach((inputRef) => {
                            inputRef.disabled = currentField._f.disabled || disabled;
                        });
                    }
                }
            }, 0, false);
        }
    };
    const handleSubmit = (onValid, onInvalid) => async (e) => {
        let onValidError = undefined;
        if (e) {
            e.preventDefault && e.preventDefault();
            e.persist &&
                e.persist();
        }
        let fieldValues = cloneObject(_formValues);
        _subjects.state.next({
            isSubmitting: true,
        });
        if (_options.resolver) {
            const { errors, values } = await _runSchema();
            _formState.errors = errors;
            fieldValues = values;
        }
        else {
            await executeBuiltInValidation(_fields);
        }
        if (_names.disabled.size) {
            for (const name of _names.disabled) {
                set(fieldValues, name, undefined);
            }
        }
        unset(_formState.errors, 'root');
        if (isEmptyObject(_formState.errors)) {
            _subjects.state.next({
                errors: {},
            });
            try {
                await onValid(fieldValues, e);
            }
            catch (error) {
                onValidError = error;
            }
        }
        else {
            if (onInvalid) {
                await onInvalid({ ..._formState.errors }, e);
            }
            _focusError();
            setTimeout(_focusError);
        }
        _subjects.state.next({
            isSubmitted: true,
            isSubmitting: false,
            isSubmitSuccessful: isEmptyObject(_formState.errors) && !onValidError,
            submitCount: _formState.submitCount + 1,
            errors: _formState.errors,
        });
        if (onValidError) {
            throw onValidError;
        }
    };
    const resetField = (name, options = {}) => {
        if (get(_fields, name)) {
            if (isUndefined(options.defaultValue)) {
                setValue(name, cloneObject(get(_defaultValues, name)));
            }
            else {
                setValue(name, options.defaultValue);
                set(_defaultValues, name, cloneObject(options.defaultValue));
            }
            if (!options.keepTouched) {
                unset(_formState.touchedFields, name);
            }
            if (!options.keepDirty) {
                unset(_formState.dirtyFields, name);
                _formState.isDirty = options.defaultValue
                    ? _getDirty(name, cloneObject(get(_defaultValues, name)))
                    : _getDirty();
            }
            if (!options.keepError) {
                unset(_formState.errors, name);
                _proxyFormState.isValid && _setValid();
            }
            _subjects.state.next({ ..._formState });
        }
    };
    const _reset = (formValues, keepStateOptions = {}) => {
        const updatedValues = formValues ? cloneObject(formValues) : _defaultValues;
        const cloneUpdatedValues = cloneObject(updatedValues);
        const isEmptyResetValues = isEmptyObject(formValues);
        const values = isEmptyResetValues ? _defaultValues : cloneUpdatedValues;
        if (!keepStateOptions.keepDefaultValues) {
            _defaultValues = updatedValues;
        }
        if (!keepStateOptions.keepValues) {
            if (keepStateOptions.keepDirtyValues) {
                const fieldsToCheck = new Set([
                    ..._names.mount,
                    ...Object.keys(getDirtyFields(_defaultValues, _formValues)),
                ]);
                for (const fieldName of Array.from(fieldsToCheck)) {
                    get(_formState.dirtyFields, fieldName)
                        ? set(values, fieldName, get(_formValues, fieldName))
                        : setValue(fieldName, get(values, fieldName));
                }
            }
            else {
                if (isWeb && isUndefined(formValues)) {
                    for (const name of _names.mount) {
                        const field = get(_fields, name);
                        if (field && field._f) {
                            const fieldReference = Array.isArray(field._f.refs)
                                ? field._f.refs[0]
                                : field._f.ref;
                            if (isHTMLElement(fieldReference)) {
                                const form = fieldReference.closest('form');
                                if (form) {
                                    form.reset();
                                    break;
                                }
                            }
                        }
                    }
                }
                for (const fieldName of _names.mount) {
                    setValue(fieldName, get(values, fieldName));
                }
            }
            _formValues = cloneObject(values);
            _subjects.array.next({
                values: { ...values },
            });
            _subjects.state.next({
                values: { ...values },
            });
        }
        _names = {
            mount: keepStateOptions.keepDirtyValues ? _names.mount : new Set(),
            unMount: new Set(),
            array: new Set(),
            disabled: new Set(),
            watch: new Set(),
            watchAll: false,
            focus: '',
        };
        _state.mount =
            !_proxyFormState.isValid ||
                !!keepStateOptions.keepIsValid ||
                !!keepStateOptions.keepDirtyValues;
        _state.watch = !!_options.shouldUnregister;
        _subjects.state.next({
            submitCount: keepStateOptions.keepSubmitCount
                ? _formState.submitCount
                : 0,
            isDirty: isEmptyResetValues
                ? false
                : keepStateOptions.keepDirty
                    ? _formState.isDirty
                    : !!(keepStateOptions.keepDefaultValues &&
                        !deepEqual(formValues, _defaultValues)),
            isSubmitted: keepStateOptions.keepIsSubmitted
                ? _formState.isSubmitted
                : false,
            dirtyFields: isEmptyResetValues
                ? {}
                : keepStateOptions.keepDirtyValues
                    ? keepStateOptions.keepDefaultValues && _formValues
                        ? getDirtyFields(_defaultValues, _formValues)
                        : _formState.dirtyFields
                    : keepStateOptions.keepDefaultValues && formValues
                        ? getDirtyFields(_defaultValues, formValues)
                        : keepStateOptions.keepDirty
                            ? _formState.dirtyFields
                            : {},
            touchedFields: keepStateOptions.keepTouched
                ? _formState.touchedFields
                : {},
            errors: keepStateOptions.keepErrors ? _formState.errors : {},
            isSubmitSuccessful: keepStateOptions.keepIsSubmitSuccessful
                ? _formState.isSubmitSuccessful
                : false,
            isSubmitting: false,
        });
    };
    const reset = (formValues, keepStateOptions) => _reset(isFunction(formValues)
        ? formValues(_formValues)
        : formValues, keepStateOptions);
    const setFocus = (name, options = {}) => {
        const field = get(_fields, name);
        const fieldReference = field && field._f;
        if (fieldReference) {
            const fieldRef = fieldReference.refs
                ? fieldReference.refs[0]
                : fieldReference.ref;
            if (fieldRef.focus) {
                fieldRef.focus();
                options.shouldSelect &&
                    isFunction(fieldRef.select) &&
                    fieldRef.select();
            }
        }
    };
    const _setFormState = (updatedFormState) => {
        _formState = {
            ..._formState,
            ...updatedFormState,
        };
    };
    const _resetDefaultValues = () => isFunction(_options.defaultValues) &&
        _options.defaultValues().then((values) => {
            reset(values, _options.resetOptions);
            _subjects.state.next({
                isLoading: false,
            });
        });
    const methods = {
        control: {
            register,
            unregister,
            getFieldState,
            handleSubmit,
            setError,
            _subscribe,
            _runSchema,
            _getWatch,
            _getDirty,
            _setValid,
            _setFieldArray,
            _setDisabledField,
            _setErrors,
            _getFieldArray,
            _reset,
            _resetDefaultValues,
            _removeUnmounted,
            _disableForm,
            _subjects,
            _proxyFormState,
            get _fields() {
                return _fields;
            },
            get _formValues() {
                return _formValues;
            },
            get _state() {
                return _state;
            },
            set _state(value) {
                _state = value;
            },
            get _defaultValues() {
                return _defaultValues;
            },
            get _names() {
                return _names;
            },
            set _names(value) {
                _names = value;
            },
            get _formState() {
                return _formState;
            },
            get _options() {
                return _options;
            },
            set _options(value) {
                _options = {
                    ..._options,
                    ...value,
                };
            },
        },
        subscribe,
        trigger,
        register,
        handleSubmit,
        watch,
        setValue,
        getValues,
        reset,
        resetField,
        clearErrors,
        unregister,
        setError,
        setFocus,
        getFieldState,
    };
    return {
        ...methods,
        formControl: methods,
    };
}

var generateId = () => {
    const d = typeof performance === 'undefined' ? Date.now() : performance.now() * 1000;
    return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, (c) => {
        const r = (Math.random() * 16 + d) % 16 | 0;
        return (c == 'x' ? r : (r & 0x3) | 0x8).toString(16);
    });
};

var getFocusFieldName = (name, index, options = {}) => options.shouldFocus || isUndefined(options.shouldFocus)
    ? options.focusName ||
        `${name}.${isUndefined(options.focusIndex) ? index : options.focusIndex}.`
    : '';

var appendAt = (data, value) => [
    ...data,
    ...convertToArrayPayload(value),
];

var fillEmptyArray = (value) => Array.isArray(value) ? value.map(() => undefined) : undefined;

function insert(data, index, value) {
    return [
        ...data.slice(0, index),
        ...convertToArrayPayload(value),
        ...data.slice(index),
    ];
}

var moveArrayAt = (data, from, to) => {
    if (!Array.isArray(data)) {
        return [];
    }
    if (isUndefined(data[to])) {
        data[to] = undefined;
    }
    data.splice(to, 0, data.splice(from, 1)[0]);
    return data;
};

var prependAt = (data, value) => [
    ...convertToArrayPayload(value),
    ...convertToArrayPayload(data),
];

function removeAtIndexes(data, indexes) {
    let i = 0;
    const temp = [...data];
    for (const index of indexes) {
        temp.splice(index - i, 1);
        i++;
    }
    return compact(temp).length ? temp : [];
}
var removeArrayAt = (data, index) => isUndefined(index)
    ? []
    : removeAtIndexes(data, convertToArrayPayload(index).sort((a, b) => a - b));

var swapArrayAt = (data, indexA, indexB) => {
    [data[indexA], data[indexB]] = [data[indexB], data[indexA]];
};

var updateAt = (fieldValues, index, value) => {
    fieldValues[index] = value;
    return fieldValues;
};

/**
 * A custom hook that exposes convenient methods to perform operations with a list of dynamic inputs that need to be appended, updated, removed etc. • [Demo](https://codesandbox.io/s/react-hook-form-usefieldarray-ssugn) • [Video](https://youtu.be/4MrbfGSFY2A)
 *
 * @remarks
 * [API](https://react-hook-form.com/docs/usefieldarray) • [Demo](https://codesandbox.io/s/react-hook-form-usefieldarray-ssugn)
 *
 * @param props - useFieldArray props
 *
 * @returns methods - functions to manipulate with the Field Arrays (dynamic inputs) {@link UseFieldArrayReturn}
 *
 * @example
 * ```tsx
 * function App() {
 *   const { register, control, handleSubmit, reset, trigger, setError } = useForm({
 *     defaultValues: {
 *       test: []
 *     }
 *   });
 *   const { fields, append } = useFieldArray({
 *     control,
 *     name: "test"
 *   });
 *
 *   return (
 *     <form onSubmit={handleSubmit(data => console.log(data))}>
 *       {fields.map((item, index) => (
 *          <input key={item.id} {...register(`test.${index}.firstName`)}  />
 *       ))}
 *       <button type="button" onClick={() => append({ firstName: "bill" })}>
 *         append
 *       </button>
 *       <input type="submit" />
 *     </form>
 *   );
 * }
 * ```
 */
function useFieldArray(props) {
    const methods = useFormContext();
    const { control = methods.control, name, keyName = 'id', shouldUnregister, rules, } = props;
    const [fields, setFields] = react__WEBPACK_IMPORTED_MODULE_0___default().useState(control._getFieldArray(name));
    const ids = react__WEBPACK_IMPORTED_MODULE_0___default().useRef(control._getFieldArray(name).map(generateId));
    const _fieldIds = react__WEBPACK_IMPORTED_MODULE_0___default().useRef(fields);
    const _name = react__WEBPACK_IMPORTED_MODULE_0___default().useRef(name);
    const _actioned = react__WEBPACK_IMPORTED_MODULE_0___default().useRef(false);
    _name.current = name;
    _fieldIds.current = fields;
    control._names.array.add(name);
    rules &&
        control.register(name, rules);
    react__WEBPACK_IMPORTED_MODULE_0___default().useEffect(() => control._subjects.array.subscribe({
        next: ({ values, name: fieldArrayName, }) => {
            if (fieldArrayName === _name.current || !fieldArrayName) {
                const fieldValues = get(values, _name.current);
                if (Array.isArray(fieldValues)) {
                    setFields(fieldValues);
                    ids.current = fieldValues.map(generateId);
                }
            }
        },
    }).unsubscribe, [control]);
    const updateValues = react__WEBPACK_IMPORTED_MODULE_0___default().useCallback((updatedFieldArrayValues) => {
        _actioned.current = true;
        control._setFieldArray(name, updatedFieldArrayValues);
    }, [control, name]);
    const append = (value, options) => {
        const appendValue = convertToArrayPayload(cloneObject(value));
        const updatedFieldArrayValues = appendAt(control._getFieldArray(name), appendValue);
        control._names.focus = getFocusFieldName(name, updatedFieldArrayValues.length - 1, options);
        ids.current = appendAt(ids.current, appendValue.map(generateId));
        updateValues(updatedFieldArrayValues);
        setFields(updatedFieldArrayValues);
        control._setFieldArray(name, updatedFieldArrayValues, appendAt, {
            argA: fillEmptyArray(value),
        });
    };
    const prepend = (value, options) => {
        const prependValue = convertToArrayPayload(cloneObject(value));
        const updatedFieldArrayValues = prependAt(control._getFieldArray(name), prependValue);
        control._names.focus = getFocusFieldName(name, 0, options);
        ids.current = prependAt(ids.current, prependValue.map(generateId));
        updateValues(updatedFieldArrayValues);
        setFields(updatedFieldArrayValues);
        control._setFieldArray(name, updatedFieldArrayValues, prependAt, {
            argA: fillEmptyArray(value),
        });
    };
    const remove = (index) => {
        const updatedFieldArrayValues = removeArrayAt(control._getFieldArray(name), index);
        ids.current = removeArrayAt(ids.current, index);
        updateValues(updatedFieldArrayValues);
        setFields(updatedFieldArrayValues);
        !Array.isArray(get(control._fields, name)) &&
            set(control._fields, name, undefined);
        control._setFieldArray(name, updatedFieldArrayValues, removeArrayAt, {
            argA: index,
        });
    };
    const insert$1 = (index, value, options) => {
        const insertValue = convertToArrayPayload(cloneObject(value));
        const updatedFieldArrayValues = insert(control._getFieldArray(name), index, insertValue);
        control._names.focus = getFocusFieldName(name, index, options);
        ids.current = insert(ids.current, index, insertValue.map(generateId));
        updateValues(updatedFieldArrayValues);
        setFields(updatedFieldArrayValues);
        control._setFieldArray(name, updatedFieldArrayValues, insert, {
            argA: index,
            argB: fillEmptyArray(value),
        });
    };
    const swap = (indexA, indexB) => {
        const updatedFieldArrayValues = control._getFieldArray(name);
        swapArrayAt(updatedFieldArrayValues, indexA, indexB);
        swapArrayAt(ids.current, indexA, indexB);
        updateValues(updatedFieldArrayValues);
        setFields(updatedFieldArrayValues);
        control._setFieldArray(name, updatedFieldArrayValues, swapArrayAt, {
            argA: indexA,
            argB: indexB,
        }, false);
    };
    const move = (from, to) => {
        const updatedFieldArrayValues = control._getFieldArray(name);
        moveArrayAt(updatedFieldArrayValues, from, to);
        moveArrayAt(ids.current, from, to);
        updateValues(updatedFieldArrayValues);
        setFields(updatedFieldArrayValues);
        control._setFieldArray(name, updatedFieldArrayValues, moveArrayAt, {
            argA: from,
            argB: to,
        }, false);
    };
    const update = (index, value) => {
        const updateValue = cloneObject(value);
        const updatedFieldArrayValues = updateAt(control._getFieldArray(name), index, updateValue);
        ids.current = [...updatedFieldArrayValues].map((item, i) => !item || i === index ? generateId() : ids.current[i]);
        updateValues(updatedFieldArrayValues);
        setFields([...updatedFieldArrayValues]);
        control._setFieldArray(name, updatedFieldArrayValues, updateAt, {
            argA: index,
            argB: updateValue,
        }, true, false);
    };
    const replace = (value) => {
        const updatedFieldArrayValues = convertToArrayPayload(cloneObject(value));
        ids.current = updatedFieldArrayValues.map(generateId);
        updateValues([...updatedFieldArrayValues]);
        setFields([...updatedFieldArrayValues]);
        control._setFieldArray(name, [...updatedFieldArrayValues], (data) => data, {}, true, false);
    };
    react__WEBPACK_IMPORTED_MODULE_0___default().useEffect(() => {
        control._state.action = false;
        isWatched(name, control._names) &&
            control._subjects.state.next({
                ...control._formState,
            });
        if (_actioned.current &&
            (!getValidationModes(control._options.mode).isOnSubmit ||
                control._formState.isSubmitted) &&
            !getValidationModes(control._options.reValidateMode).isOnSubmit) {
            if (control._options.resolver) {
                control._runSchema([name]).then((result) => {
                    const error = get(result.errors, name);
                    const existingError = get(control._formState.errors, name);
                    if (existingError
                        ? (!error && existingError.type) ||
                            (error &&
                                (existingError.type !== error.type ||
                                    existingError.message !== error.message))
                        : error && error.type) {
                        error
                            ? set(control._formState.errors, name, error)
                            : unset(control._formState.errors, name);
                        control._subjects.state.next({
                            errors: control._formState.errors,
                        });
                    }
                });
            }
            else {
                const field = get(control._fields, name);
                if (field &&
                    field._f &&
                    !(getValidationModes(control._options.reValidateMode).isOnSubmit &&
                        getValidationModes(control._options.mode).isOnSubmit)) {
                    validateField(field, control._names.disabled, control._formValues, control._options.criteriaMode === VALIDATION_MODE.all, control._options.shouldUseNativeValidation, true).then((error) => !isEmptyObject(error) &&
                        control._subjects.state.next({
                            errors: updateFieldArrayRootError(control._formState.errors, error, name),
                        }));
                }
            }
        }
        control._subjects.state.next({
            name,
            values: cloneObject(control._formValues),
        });
        control._names.focus &&
            iterateFieldsByAction(control._fields, (ref, key) => {
                if (control._names.focus &&
                    key.startsWith(control._names.focus) &&
                    ref.focus) {
                    ref.focus();
                    return 1;
                }
                return;
            });
        control._names.focus = '';
        control._setValid();
        _actioned.current = false;
    }, [fields, name, control]);
    react__WEBPACK_IMPORTED_MODULE_0___default().useEffect(() => {
        !get(control._formValues, name) && control._setFieldArray(name);
        return () => {
            const updateMounted = (name, value) => {
                const field = get(control._fields, name);
                if (field && field._f) {
                    field._f.mount = value;
                }
            };
            control._options.shouldUnregister || shouldUnregister
                ? control.unregister(name)
                : updateMounted(name, false);
        };
    }, [name, control, keyName, shouldUnregister]);
    return {
        swap: react__WEBPACK_IMPORTED_MODULE_0___default().useCallback(swap, [updateValues, name, control]),
        move: react__WEBPACK_IMPORTED_MODULE_0___default().useCallback(move, [updateValues, name, control]),
        prepend: react__WEBPACK_IMPORTED_MODULE_0___default().useCallback(prepend, [updateValues, name, control]),
        append: react__WEBPACK_IMPORTED_MODULE_0___default().useCallback(append, [updateValues, name, control]),
        remove: react__WEBPACK_IMPORTED_MODULE_0___default().useCallback(remove, [updateValues, name, control]),
        insert: react__WEBPACK_IMPORTED_MODULE_0___default().useCallback(insert$1, [updateValues, name, control]),
        update: react__WEBPACK_IMPORTED_MODULE_0___default().useCallback(update, [updateValues, name, control]),
        replace: react__WEBPACK_IMPORTED_MODULE_0___default().useCallback(replace, [updateValues, name, control]),
        fields: react__WEBPACK_IMPORTED_MODULE_0___default().useMemo(() => fields.map((field, index) => ({
            ...field,
            [keyName]: ids.current[index] || generateId(),
        })), [fields, keyName]),
    };
}

/**
 * Custom hook to manage the entire form.
 *
 * @remarks
 * [API](https://react-hook-form.com/docs/useform) • [Demo](https://codesandbox.io/s/react-hook-form-get-started-ts-5ksmm) • [Video](https://www.youtube.com/watch?v=RkXv4AXXC_4)
 *
 * @param props - form configuration and validation parameters.
 *
 * @returns methods - individual functions to manage the form state. {@link UseFormReturn}
 *
 * @example
 * ```tsx
 * function App() {
 *   const { register, handleSubmit, watch, formState: { errors } } = useForm();
 *   const onSubmit = data => console.log(data);
 *
 *   console.log(watch("example"));
 *
 *   return (
 *     <form onSubmit={handleSubmit(onSubmit)}>
 *       <input defaultValue="test" {...register("example")} />
 *       <input {...register("exampleRequired", { required: true })} />
 *       {errors.exampleRequired && <span>This field is required</span>}
 *       <button>Submit</button>
 *     </form>
 *   );
 * }
 * ```
 */
function useForm(props = {}) {
    const _formControl = react__WEBPACK_IMPORTED_MODULE_0___default().useRef(undefined);
    const _values = react__WEBPACK_IMPORTED_MODULE_0___default().useRef(undefined);
    const [formState, updateFormState] = react__WEBPACK_IMPORTED_MODULE_0___default().useState({
        isDirty: false,
        isValidating: false,
        isLoading: isFunction(props.defaultValues),
        isSubmitted: false,
        isSubmitting: false,
        isSubmitSuccessful: false,
        isValid: false,
        submitCount: 0,
        dirtyFields: {},
        touchedFields: {},
        validatingFields: {},
        errors: props.errors || {},
        disabled: props.disabled || false,
        isReady: false,
        defaultValues: isFunction(props.defaultValues)
            ? undefined
            : props.defaultValues,
    });
    if (!_formControl.current) {
        _formControl.current = {
            ...(props.formControl ? props.formControl : createFormControl(props)),
            formState,
        };
        if (props.formControl &&
            props.defaultValues &&
            !isFunction(props.defaultValues)) {
            props.formControl.reset(props.defaultValues, props.resetOptions);
        }
    }
    const control = _formControl.current.control;
    control._options = props;
    useIsomorphicLayoutEffect(() => {
        const sub = control._subscribe({
            formState: control._proxyFormState,
            callback: () => updateFormState({ ...control._formState }),
            reRenderRoot: true,
        });
        updateFormState((data) => ({
            ...data,
            isReady: true,
        }));
        control._formState.isReady = true;
        return sub;
    }, [control]);
    react__WEBPACK_IMPORTED_MODULE_0___default().useEffect(() => control._disableForm(props.disabled), [control, props.disabled]);
    react__WEBPACK_IMPORTED_MODULE_0___default().useEffect(() => {
        if (props.mode) {
            control._options.mode = props.mode;
        }
        if (props.reValidateMode) {
            control._options.reValidateMode = props.reValidateMode;
        }
        if (props.errors && !isEmptyObject(props.errors)) {
            control._setErrors(props.errors);
        }
    }, [control, props.errors, props.mode, props.reValidateMode]);
    react__WEBPACK_IMPORTED_MODULE_0___default().useEffect(() => {
        props.shouldUnregister &&
            control._subjects.state.next({
                values: control._getWatch(),
            });
    }, [control, props.shouldUnregister]);
    react__WEBPACK_IMPORTED_MODULE_0___default().useEffect(() => {
        if (control._proxyFormState.isDirty) {
            const isDirty = control._getDirty();
            if (isDirty !== formState.isDirty) {
                control._subjects.state.next({
                    isDirty,
                });
            }
        }
    }, [control, formState.isDirty]);
    react__WEBPACK_IMPORTED_MODULE_0___default().useEffect(() => {
        if (props.values && !deepEqual(props.values, _values.current)) {
            control._reset(props.values, control._options.resetOptions);
            _values.current = props.values;
            updateFormState((state) => ({ ...state }));
        }
        else {
            control._resetDefaultValues();
        }
    }, [control, props.values]);
    react__WEBPACK_IMPORTED_MODULE_0___default().useEffect(() => {
        if (!control._state.mount) {
            control._setValid();
            control._state.mount = true;
        }
        if (control._state.watch) {
            control._state.watch = false;
            control._subjects.state.next({ ...control._formState });
        }
        control._removeUnmounted();
    });
    _formControl.current.formState = getProxyFormState(formState, control);
    return _formControl.current;
}


//# sourceMappingURL=index.esm.mjs.map


/***/ }),

/***/ "../../node_modules/.pnpm/zod@3.25.28/node_modules/zod/dist/esm/index.js":
/*!*******************************************************************************!*\
  !*** ../../node_modules/.pnpm/zod@3.25.28/node_modules/zod/dist/esm/index.js ***!
  \*******************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   BRAND: () => (/* reexport safe */ _v3_index_js__WEBPACK_IMPORTED_MODULE_0__.BRAND),
/* harmony export */   DIRTY: () => (/* reexport safe */ _v3_index_js__WEBPACK_IMPORTED_MODULE_0__.DIRTY),
/* harmony export */   EMPTY_PATH: () => (/* reexport safe */ _v3_index_js__WEBPACK_IMPORTED_MODULE_0__.EMPTY_PATH),
/* harmony export */   INVALID: () => (/* reexport safe */ _v3_index_js__WEBPACK_IMPORTED_MODULE_0__.INVALID),
/* harmony export */   NEVER: () => (/* reexport safe */ _v3_index_js__WEBPACK_IMPORTED_MODULE_0__.NEVER),
/* harmony export */   OK: () => (/* reexport safe */ _v3_index_js__WEBPACK_IMPORTED_MODULE_0__.OK),
/* harmony export */   ParseStatus: () => (/* reexport safe */ _v3_index_js__WEBPACK_IMPORTED_MODULE_0__.ParseStatus),
/* harmony export */   Schema: () => (/* reexport safe */ _v3_index_js__WEBPACK_IMPORTED_MODULE_0__.Schema),
/* harmony export */   ZodAny: () => (/* reexport safe */ _v3_index_js__WEBPACK_IMPORTED_MODULE_0__.ZodAny),
/* harmony export */   ZodArray: () => (/* reexport safe */ _v3_index_js__WEBPACK_IMPORTED_MODULE_0__.ZodArray),
/* harmony export */   ZodBigInt: () => (/* reexport safe */ _v3_index_js__WEBPACK_IMPORTED_MODULE_0__.ZodBigInt),
/* harmony export */   ZodBoolean: () => (/* reexport safe */ _v3_index_js__WEBPACK_IMPORTED_MODULE_0__.ZodBoolean),
/* harmony export */   ZodBranded: () => (/* reexport safe */ _v3_index_js__WEBPACK_IMPORTED_MODULE_0__.ZodBranded),
/* harmony export */   ZodCatch: () => (/* reexport safe */ _v3_index_js__WEBPACK_IMPORTED_MODULE_0__.ZodCatch),
/* harmony export */   ZodDate: () => (/* reexport safe */ _v3_index_js__WEBPACK_IMPORTED_MODULE_0__.ZodDate),
/* harmony export */   ZodDefault: () => (/* reexport safe */ _v3_index_js__WEBPACK_IMPORTED_MODULE_0__.ZodDefault),
/* harmony export */   ZodDiscriminatedUnion: () => (/* reexport safe */ _v3_index_js__WEBPACK_IMPORTED_MODULE_0__.ZodDiscriminatedUnion),
/* harmony export */   ZodEffects: () => (/* reexport safe */ _v3_index_js__WEBPACK_IMPORTED_MODULE_0__.ZodEffects),
/* harmony export */   ZodEnum: () => (/* reexport safe */ _v3_index_js__WEBPACK_IMPORTED_MODULE_0__.ZodEnum),
/* harmony export */   ZodError: () => (/* reexport safe */ _v3_index_js__WEBPACK_IMPORTED_MODULE_0__.ZodError),
/* harmony export */   ZodFirstPartyTypeKind: () => (/* reexport safe */ _v3_index_js__WEBPACK_IMPORTED_MODULE_0__.ZodFirstPartyTypeKind),
/* harmony export */   ZodFunction: () => (/* reexport safe */ _v3_index_js__WEBPACK_IMPORTED_MODULE_0__.ZodFunction),
/* harmony export */   ZodIntersection: () => (/* reexport safe */ _v3_index_js__WEBPACK_IMPORTED_MODULE_0__.ZodIntersection),
/* harmony export */   ZodIssueCode: () => (/* reexport safe */ _v3_index_js__WEBPACK_IMPORTED_MODULE_0__.ZodIssueCode),
/* harmony export */   ZodLazy: () => (/* reexport safe */ _v3_index_js__WEBPACK_IMPORTED_MODULE_0__.ZodLazy),
/* harmony export */   ZodLiteral: () => (/* reexport safe */ _v3_index_js__WEBPACK_IMPORTED_MODULE_0__.ZodLiteral),
/* harmony export */   ZodMap: () => (/* reexport safe */ _v3_index_js__WEBPACK_IMPORTED_MODULE_0__.ZodMap),
/* harmony export */   ZodNaN: () => (/* reexport safe */ _v3_index_js__WEBPACK_IMPORTED_MODULE_0__.ZodNaN),
/* harmony export */   ZodNativeEnum: () => (/* reexport safe */ _v3_index_js__WEBPACK_IMPORTED_MODULE_0__.ZodNativeEnum),
/* harmony export */   ZodNever: () => (/* reexport safe */ _v3_index_js__WEBPACK_IMPORTED_MODULE_0__.ZodNever),
/* harmony export */   ZodNull: () => (/* reexport safe */ _v3_index_js__WEBPACK_IMPORTED_MODULE_0__.ZodNull),
/* harmony export */   ZodNullable: () => (/* reexport safe */ _v3_index_js__WEBPACK_IMPORTED_MODULE_0__.ZodNullable),
/* harmony export */   ZodNumber: () => (/* reexport safe */ _v3_index_js__WEBPACK_IMPORTED_MODULE_0__.ZodNumber),
/* harmony export */   ZodObject: () => (/* reexport safe */ _v3_index_js__WEBPACK_IMPORTED_MODULE_0__.ZodObject),
/* harmony export */   ZodOptional: () => (/* reexport safe */ _v3_index_js__WEBPACK_IMPORTED_MODULE_0__.ZodOptional),
/* harmony export */   ZodParsedType: () => (/* reexport safe */ _v3_index_js__WEBPACK_IMPORTED_MODULE_0__.ZodParsedType),
/* harmony export */   ZodPipeline: () => (/* reexport safe */ _v3_index_js__WEBPACK_IMPORTED_MODULE_0__.ZodPipeline),
/* harmony export */   ZodPromise: () => (/* reexport safe */ _v3_index_js__WEBPACK_IMPORTED_MODULE_0__.ZodPromise),
/* harmony export */   ZodReadonly: () => (/* reexport safe */ _v3_index_js__WEBPACK_IMPORTED_MODULE_0__.ZodReadonly),
/* harmony export */   ZodRecord: () => (/* reexport safe */ _v3_index_js__WEBPACK_IMPORTED_MODULE_0__.ZodRecord),
/* harmony export */   ZodSchema: () => (/* reexport safe */ _v3_index_js__WEBPACK_IMPORTED_MODULE_0__.ZodSchema),
/* harmony export */   ZodSet: () => (/* reexport safe */ _v3_index_js__WEBPACK_IMPORTED_MODULE_0__.ZodSet),
/* harmony export */   ZodString: () => (/* reexport safe */ _v3_index_js__WEBPACK_IMPORTED_MODULE_0__.ZodString),
/* harmony export */   ZodSymbol: () => (/* reexport safe */ _v3_index_js__WEBPACK_IMPORTED_MODULE_0__.ZodSymbol),
/* harmony export */   ZodTransformer: () => (/* reexport safe */ _v3_index_js__WEBPACK_IMPORTED_MODULE_0__.ZodTransformer),
/* harmony export */   ZodTuple: () => (/* reexport safe */ _v3_index_js__WEBPACK_IMPORTED_MODULE_0__.ZodTuple),
/* harmony export */   ZodType: () => (/* reexport safe */ _v3_index_js__WEBPACK_IMPORTED_MODULE_0__.ZodType),
/* harmony export */   ZodUndefined: () => (/* reexport safe */ _v3_index_js__WEBPACK_IMPORTED_MODULE_0__.ZodUndefined),
/* harmony export */   ZodUnion: () => (/* reexport safe */ _v3_index_js__WEBPACK_IMPORTED_MODULE_0__.ZodUnion),
/* harmony export */   ZodUnknown: () => (/* reexport safe */ _v3_index_js__WEBPACK_IMPORTED_MODULE_0__.ZodUnknown),
/* harmony export */   ZodVoid: () => (/* reexport safe */ _v3_index_js__WEBPACK_IMPORTED_MODULE_0__.ZodVoid),
/* harmony export */   addIssueToContext: () => (/* reexport safe */ _v3_index_js__WEBPACK_IMPORTED_MODULE_0__.addIssueToContext),
/* harmony export */   any: () => (/* reexport safe */ _v3_index_js__WEBPACK_IMPORTED_MODULE_0__.any),
/* harmony export */   array: () => (/* reexport safe */ _v3_index_js__WEBPACK_IMPORTED_MODULE_0__.array),
/* harmony export */   bigint: () => (/* reexport safe */ _v3_index_js__WEBPACK_IMPORTED_MODULE_0__.bigint),
/* harmony export */   boolean: () => (/* reexport safe */ _v3_index_js__WEBPACK_IMPORTED_MODULE_0__.boolean),
/* harmony export */   coerce: () => (/* reexport safe */ _v3_index_js__WEBPACK_IMPORTED_MODULE_0__.coerce),
/* harmony export */   custom: () => (/* reexport safe */ _v3_index_js__WEBPACK_IMPORTED_MODULE_0__.custom),
/* harmony export */   date: () => (/* reexport safe */ _v3_index_js__WEBPACK_IMPORTED_MODULE_0__.date),
/* harmony export */   datetimeRegex: () => (/* reexport safe */ _v3_index_js__WEBPACK_IMPORTED_MODULE_0__.datetimeRegex),
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__),
/* harmony export */   defaultErrorMap: () => (/* reexport safe */ _v3_index_js__WEBPACK_IMPORTED_MODULE_0__.defaultErrorMap),
/* harmony export */   discriminatedUnion: () => (/* reexport safe */ _v3_index_js__WEBPACK_IMPORTED_MODULE_0__.discriminatedUnion),
/* harmony export */   effect: () => (/* reexport safe */ _v3_index_js__WEBPACK_IMPORTED_MODULE_0__.effect),
/* harmony export */   "enum": () => (/* reexport safe */ _v3_index_js__WEBPACK_IMPORTED_MODULE_0__["enum"]),
/* harmony export */   "function": () => (/* reexport safe */ _v3_index_js__WEBPACK_IMPORTED_MODULE_0__["function"]),
/* harmony export */   getErrorMap: () => (/* reexport safe */ _v3_index_js__WEBPACK_IMPORTED_MODULE_0__.getErrorMap),
/* harmony export */   getParsedType: () => (/* reexport safe */ _v3_index_js__WEBPACK_IMPORTED_MODULE_0__.getParsedType),
/* harmony export */   "instanceof": () => (/* reexport safe */ _v3_index_js__WEBPACK_IMPORTED_MODULE_0__["instanceof"]),
/* harmony export */   intersection: () => (/* reexport safe */ _v3_index_js__WEBPACK_IMPORTED_MODULE_0__.intersection),
/* harmony export */   isAborted: () => (/* reexport safe */ _v3_index_js__WEBPACK_IMPORTED_MODULE_0__.isAborted),
/* harmony export */   isAsync: () => (/* reexport safe */ _v3_index_js__WEBPACK_IMPORTED_MODULE_0__.isAsync),
/* harmony export */   isDirty: () => (/* reexport safe */ _v3_index_js__WEBPACK_IMPORTED_MODULE_0__.isDirty),
/* harmony export */   isValid: () => (/* reexport safe */ _v3_index_js__WEBPACK_IMPORTED_MODULE_0__.isValid),
/* harmony export */   late: () => (/* reexport safe */ _v3_index_js__WEBPACK_IMPORTED_MODULE_0__.late),
/* harmony export */   lazy: () => (/* reexport safe */ _v3_index_js__WEBPACK_IMPORTED_MODULE_0__.lazy),
/* harmony export */   literal: () => (/* reexport safe */ _v3_index_js__WEBPACK_IMPORTED_MODULE_0__.literal),
/* harmony export */   makeIssue: () => (/* reexport safe */ _v3_index_js__WEBPACK_IMPORTED_MODULE_0__.makeIssue),
/* harmony export */   map: () => (/* reexport safe */ _v3_index_js__WEBPACK_IMPORTED_MODULE_0__.map),
/* harmony export */   nan: () => (/* reexport safe */ _v3_index_js__WEBPACK_IMPORTED_MODULE_0__.nan),
/* harmony export */   nativeEnum: () => (/* reexport safe */ _v3_index_js__WEBPACK_IMPORTED_MODULE_0__.nativeEnum),
/* harmony export */   never: () => (/* reexport safe */ _v3_index_js__WEBPACK_IMPORTED_MODULE_0__.never),
/* harmony export */   "null": () => (/* reexport safe */ _v3_index_js__WEBPACK_IMPORTED_MODULE_0__["null"]),
/* harmony export */   nullable: () => (/* reexport safe */ _v3_index_js__WEBPACK_IMPORTED_MODULE_0__.nullable),
/* harmony export */   number: () => (/* reexport safe */ _v3_index_js__WEBPACK_IMPORTED_MODULE_0__.number),
/* harmony export */   object: () => (/* reexport safe */ _v3_index_js__WEBPACK_IMPORTED_MODULE_0__.object),
/* harmony export */   objectUtil: () => (/* reexport safe */ _v3_index_js__WEBPACK_IMPORTED_MODULE_0__.objectUtil),
/* harmony export */   oboolean: () => (/* reexport safe */ _v3_index_js__WEBPACK_IMPORTED_MODULE_0__.oboolean),
/* harmony export */   onumber: () => (/* reexport safe */ _v3_index_js__WEBPACK_IMPORTED_MODULE_0__.onumber),
/* harmony export */   optional: () => (/* reexport safe */ _v3_index_js__WEBPACK_IMPORTED_MODULE_0__.optional),
/* harmony export */   ostring: () => (/* reexport safe */ _v3_index_js__WEBPACK_IMPORTED_MODULE_0__.ostring),
/* harmony export */   pipeline: () => (/* reexport safe */ _v3_index_js__WEBPACK_IMPORTED_MODULE_0__.pipeline),
/* harmony export */   preprocess: () => (/* reexport safe */ _v3_index_js__WEBPACK_IMPORTED_MODULE_0__.preprocess),
/* harmony export */   promise: () => (/* reexport safe */ _v3_index_js__WEBPACK_IMPORTED_MODULE_0__.promise),
/* harmony export */   quotelessJson: () => (/* reexport safe */ _v3_index_js__WEBPACK_IMPORTED_MODULE_0__.quotelessJson),
/* harmony export */   record: () => (/* reexport safe */ _v3_index_js__WEBPACK_IMPORTED_MODULE_0__.record),
/* harmony export */   set: () => (/* reexport safe */ _v3_index_js__WEBPACK_IMPORTED_MODULE_0__.set),
/* harmony export */   setErrorMap: () => (/* reexport safe */ _v3_index_js__WEBPACK_IMPORTED_MODULE_0__.setErrorMap),
/* harmony export */   strictObject: () => (/* reexport safe */ _v3_index_js__WEBPACK_IMPORTED_MODULE_0__.strictObject),
/* harmony export */   string: () => (/* reexport safe */ _v3_index_js__WEBPACK_IMPORTED_MODULE_0__.string),
/* harmony export */   symbol: () => (/* reexport safe */ _v3_index_js__WEBPACK_IMPORTED_MODULE_0__.symbol),
/* harmony export */   transformer: () => (/* reexport safe */ _v3_index_js__WEBPACK_IMPORTED_MODULE_0__.transformer),
/* harmony export */   tuple: () => (/* reexport safe */ _v3_index_js__WEBPACK_IMPORTED_MODULE_0__.tuple),
/* harmony export */   undefined: () => (/* reexport safe */ _v3_index_js__WEBPACK_IMPORTED_MODULE_0__.undefined),
/* harmony export */   union: () => (/* reexport safe */ _v3_index_js__WEBPACK_IMPORTED_MODULE_0__.union),
/* harmony export */   unknown: () => (/* reexport safe */ _v3_index_js__WEBPACK_IMPORTED_MODULE_0__.unknown),
/* harmony export */   util: () => (/* reexport safe */ _v3_index_js__WEBPACK_IMPORTED_MODULE_0__.util),
/* harmony export */   "void": () => (/* reexport safe */ _v3_index_js__WEBPACK_IMPORTED_MODULE_0__["void"]),
/* harmony export */   z: () => (/* reexport safe */ _v3_index_js__WEBPACK_IMPORTED_MODULE_0__.z)
/* harmony export */ });
/* harmony import */ var _v3_index_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./v3/index.js */ "../../node_modules/.pnpm/zod@3.25.28/node_modules/zod/dist/esm/v3/index.js");


/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (_v3_index_js__WEBPACK_IMPORTED_MODULE_0__["default"]);


/***/ }),

/***/ "../../node_modules/.pnpm/zod@3.25.28/node_modules/zod/dist/esm/v3/ZodError.js":
/*!*************************************************************************************!*\
  !*** ../../node_modules/.pnpm/zod@3.25.28/node_modules/zod/dist/esm/v3/ZodError.js ***!
  \*************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   ZodError: () => (/* binding */ ZodError),
/* harmony export */   ZodIssueCode: () => (/* binding */ ZodIssueCode),
/* harmony export */   quotelessJson: () => (/* binding */ quotelessJson)
/* harmony export */ });
/* harmony import */ var _helpers_util_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./helpers/util.js */ "../../node_modules/.pnpm/zod@3.25.28/node_modules/zod/dist/esm/v3/helpers/util.js");

const ZodIssueCode = _helpers_util_js__WEBPACK_IMPORTED_MODULE_0__.util.arrayToEnum([
    "invalid_type",
    "invalid_literal",
    "custom",
    "invalid_union",
    "invalid_union_discriminator",
    "invalid_enum_value",
    "unrecognized_keys",
    "invalid_arguments",
    "invalid_return_type",
    "invalid_date",
    "invalid_string",
    "too_small",
    "too_big",
    "invalid_intersection_types",
    "not_multiple_of",
    "not_finite",
]);
const quotelessJson = (obj) => {
    const json = JSON.stringify(obj, null, 2);
    return json.replace(/"([^"]+)":/g, "$1:");
};
class ZodError extends Error {
    get errors() {
        return this.issues;
    }
    constructor(issues) {
        super();
        this.issues = [];
        this.addIssue = (sub) => {
            this.issues = [...this.issues, sub];
        };
        this.addIssues = (subs = []) => {
            this.issues = [...this.issues, ...subs];
        };
        const actualProto = new.target.prototype;
        if (Object.setPrototypeOf) {
            // eslint-disable-next-line ban/ban
            Object.setPrototypeOf(this, actualProto);
        }
        else {
            this.__proto__ = actualProto;
        }
        this.name = "ZodError";
        this.issues = issues;
    }
    format(_mapper) {
        const mapper = _mapper ||
            function (issue) {
                return issue.message;
            };
        const fieldErrors = { _errors: [] };
        const processError = (error) => {
            for (const issue of error.issues) {
                if (issue.code === "invalid_union") {
                    issue.unionErrors.map(processError);
                }
                else if (issue.code === "invalid_return_type") {
                    processError(issue.returnTypeError);
                }
                else if (issue.code === "invalid_arguments") {
                    processError(issue.argumentsError);
                }
                else if (issue.path.length === 0) {
                    fieldErrors._errors.push(mapper(issue));
                }
                else {
                    let curr = fieldErrors;
                    let i = 0;
                    while (i < issue.path.length) {
                        const el = issue.path[i];
                        const terminal = i === issue.path.length - 1;
                        if (!terminal) {
                            curr[el] = curr[el] || { _errors: [] };
                            // if (typeof el === "string") {
                            //   curr[el] = curr[el] || { _errors: [] };
                            // } else if (typeof el === "number") {
                            //   const errorArray: any = [];
                            //   errorArray._errors = [];
                            //   curr[el] = curr[el] || errorArray;
                            // }
                        }
                        else {
                            curr[el] = curr[el] || { _errors: [] };
                            curr[el]._errors.push(mapper(issue));
                        }
                        curr = curr[el];
                        i++;
                    }
                }
            }
        };
        processError(this);
        return fieldErrors;
    }
    static assert(value) {
        if (!(value instanceof ZodError)) {
            throw new Error(`Not a ZodError: ${value}`);
        }
    }
    toString() {
        return this.message;
    }
    get message() {
        return JSON.stringify(this.issues, _helpers_util_js__WEBPACK_IMPORTED_MODULE_0__.util.jsonStringifyReplacer, 2);
    }
    get isEmpty() {
        return this.issues.length === 0;
    }
    flatten(mapper = (issue) => issue.message) {
        const fieldErrors = {};
        const formErrors = [];
        for (const sub of this.issues) {
            if (sub.path.length > 0) {
                fieldErrors[sub.path[0]] = fieldErrors[sub.path[0]] || [];
                fieldErrors[sub.path[0]].push(mapper(sub));
            }
            else {
                formErrors.push(mapper(sub));
            }
        }
        return { formErrors, fieldErrors };
    }
    get formErrors() {
        return this.flatten();
    }
}
ZodError.create = (issues) => {
    const error = new ZodError(issues);
    return error;
};


/***/ }),

/***/ "../../node_modules/.pnpm/zod@3.25.28/node_modules/zod/dist/esm/v3/errors.js":
/*!***********************************************************************************!*\
  !*** ../../node_modules/.pnpm/zod@3.25.28/node_modules/zod/dist/esm/v3/errors.js ***!
  \***********************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   defaultErrorMap: () => (/* reexport safe */ _locales_en_js__WEBPACK_IMPORTED_MODULE_0__["default"]),
/* harmony export */   getErrorMap: () => (/* binding */ getErrorMap),
/* harmony export */   setErrorMap: () => (/* binding */ setErrorMap)
/* harmony export */ });
/* harmony import */ var _locales_en_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./locales/en.js */ "../../node_modules/.pnpm/zod@3.25.28/node_modules/zod/dist/esm/v3/locales/en.js");

let overrideErrorMap = _locales_en_js__WEBPACK_IMPORTED_MODULE_0__["default"];

function setErrorMap(map) {
    overrideErrorMap = map;
}
function getErrorMap() {
    return overrideErrorMap;
}


/***/ }),

/***/ "../../node_modules/.pnpm/zod@3.25.28/node_modules/zod/dist/esm/v3/external.js":
/*!*************************************************************************************!*\
  !*** ../../node_modules/.pnpm/zod@3.25.28/node_modules/zod/dist/esm/v3/external.js ***!
  \*************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   BRAND: () => (/* reexport safe */ _types_js__WEBPACK_IMPORTED_MODULE_3__.BRAND),
/* harmony export */   DIRTY: () => (/* reexport safe */ _helpers_parseUtil_js__WEBPACK_IMPORTED_MODULE_1__.DIRTY),
/* harmony export */   EMPTY_PATH: () => (/* reexport safe */ _helpers_parseUtil_js__WEBPACK_IMPORTED_MODULE_1__.EMPTY_PATH),
/* harmony export */   INVALID: () => (/* reexport safe */ _helpers_parseUtil_js__WEBPACK_IMPORTED_MODULE_1__.INVALID),
/* harmony export */   NEVER: () => (/* reexport safe */ _types_js__WEBPACK_IMPORTED_MODULE_3__.NEVER),
/* harmony export */   OK: () => (/* reexport safe */ _helpers_parseUtil_js__WEBPACK_IMPORTED_MODULE_1__.OK),
/* harmony export */   ParseStatus: () => (/* reexport safe */ _helpers_parseUtil_js__WEBPACK_IMPORTED_MODULE_1__.ParseStatus),
/* harmony export */   Schema: () => (/* reexport safe */ _types_js__WEBPACK_IMPORTED_MODULE_3__.Schema),
/* harmony export */   ZodAny: () => (/* reexport safe */ _types_js__WEBPACK_IMPORTED_MODULE_3__.ZodAny),
/* harmony export */   ZodArray: () => (/* reexport safe */ _types_js__WEBPACK_IMPORTED_MODULE_3__.ZodArray),
/* harmony export */   ZodBigInt: () => (/* reexport safe */ _types_js__WEBPACK_IMPORTED_MODULE_3__.ZodBigInt),
/* harmony export */   ZodBoolean: () => (/* reexport safe */ _types_js__WEBPACK_IMPORTED_MODULE_3__.ZodBoolean),
/* harmony export */   ZodBranded: () => (/* reexport safe */ _types_js__WEBPACK_IMPORTED_MODULE_3__.ZodBranded),
/* harmony export */   ZodCatch: () => (/* reexport safe */ _types_js__WEBPACK_IMPORTED_MODULE_3__.ZodCatch),
/* harmony export */   ZodDate: () => (/* reexport safe */ _types_js__WEBPACK_IMPORTED_MODULE_3__.ZodDate),
/* harmony export */   ZodDefault: () => (/* reexport safe */ _types_js__WEBPACK_IMPORTED_MODULE_3__.ZodDefault),
/* harmony export */   ZodDiscriminatedUnion: () => (/* reexport safe */ _types_js__WEBPACK_IMPORTED_MODULE_3__.ZodDiscriminatedUnion),
/* harmony export */   ZodEffects: () => (/* reexport safe */ _types_js__WEBPACK_IMPORTED_MODULE_3__.ZodEffects),
/* harmony export */   ZodEnum: () => (/* reexport safe */ _types_js__WEBPACK_IMPORTED_MODULE_3__.ZodEnum),
/* harmony export */   ZodError: () => (/* reexport safe */ _ZodError_js__WEBPACK_IMPORTED_MODULE_4__.ZodError),
/* harmony export */   ZodFirstPartyTypeKind: () => (/* reexport safe */ _types_js__WEBPACK_IMPORTED_MODULE_3__.ZodFirstPartyTypeKind),
/* harmony export */   ZodFunction: () => (/* reexport safe */ _types_js__WEBPACK_IMPORTED_MODULE_3__.ZodFunction),
/* harmony export */   ZodIntersection: () => (/* reexport safe */ _types_js__WEBPACK_IMPORTED_MODULE_3__.ZodIntersection),
/* harmony export */   ZodIssueCode: () => (/* reexport safe */ _ZodError_js__WEBPACK_IMPORTED_MODULE_4__.ZodIssueCode),
/* harmony export */   ZodLazy: () => (/* reexport safe */ _types_js__WEBPACK_IMPORTED_MODULE_3__.ZodLazy),
/* harmony export */   ZodLiteral: () => (/* reexport safe */ _types_js__WEBPACK_IMPORTED_MODULE_3__.ZodLiteral),
/* harmony export */   ZodMap: () => (/* reexport safe */ _types_js__WEBPACK_IMPORTED_MODULE_3__.ZodMap),
/* harmony export */   ZodNaN: () => (/* reexport safe */ _types_js__WEBPACK_IMPORTED_MODULE_3__.ZodNaN),
/* harmony export */   ZodNativeEnum: () => (/* reexport safe */ _types_js__WEBPACK_IMPORTED_MODULE_3__.ZodNativeEnum),
/* harmony export */   ZodNever: () => (/* reexport safe */ _types_js__WEBPACK_IMPORTED_MODULE_3__.ZodNever),
/* harmony export */   ZodNull: () => (/* reexport safe */ _types_js__WEBPACK_IMPORTED_MODULE_3__.ZodNull),
/* harmony export */   ZodNullable: () => (/* reexport safe */ _types_js__WEBPACK_IMPORTED_MODULE_3__.ZodNullable),
/* harmony export */   ZodNumber: () => (/* reexport safe */ _types_js__WEBPACK_IMPORTED_MODULE_3__.ZodNumber),
/* harmony export */   ZodObject: () => (/* reexport safe */ _types_js__WEBPACK_IMPORTED_MODULE_3__.ZodObject),
/* harmony export */   ZodOptional: () => (/* reexport safe */ _types_js__WEBPACK_IMPORTED_MODULE_3__.ZodOptional),
/* harmony export */   ZodParsedType: () => (/* reexport safe */ _helpers_util_js__WEBPACK_IMPORTED_MODULE_2__.ZodParsedType),
/* harmony export */   ZodPipeline: () => (/* reexport safe */ _types_js__WEBPACK_IMPORTED_MODULE_3__.ZodPipeline),
/* harmony export */   ZodPromise: () => (/* reexport safe */ _types_js__WEBPACK_IMPORTED_MODULE_3__.ZodPromise),
/* harmony export */   ZodReadonly: () => (/* reexport safe */ _types_js__WEBPACK_IMPORTED_MODULE_3__.ZodReadonly),
/* harmony export */   ZodRecord: () => (/* reexport safe */ _types_js__WEBPACK_IMPORTED_MODULE_3__.ZodRecord),
/* harmony export */   ZodSchema: () => (/* reexport safe */ _types_js__WEBPACK_IMPORTED_MODULE_3__.ZodSchema),
/* harmony export */   ZodSet: () => (/* reexport safe */ _types_js__WEBPACK_IMPORTED_MODULE_3__.ZodSet),
/* harmony export */   ZodString: () => (/* reexport safe */ _types_js__WEBPACK_IMPORTED_MODULE_3__.ZodString),
/* harmony export */   ZodSymbol: () => (/* reexport safe */ _types_js__WEBPACK_IMPORTED_MODULE_3__.ZodSymbol),
/* harmony export */   ZodTransformer: () => (/* reexport safe */ _types_js__WEBPACK_IMPORTED_MODULE_3__.ZodTransformer),
/* harmony export */   ZodTuple: () => (/* reexport safe */ _types_js__WEBPACK_IMPORTED_MODULE_3__.ZodTuple),
/* harmony export */   ZodType: () => (/* reexport safe */ _types_js__WEBPACK_IMPORTED_MODULE_3__.ZodType),
/* harmony export */   ZodUndefined: () => (/* reexport safe */ _types_js__WEBPACK_IMPORTED_MODULE_3__.ZodUndefined),
/* harmony export */   ZodUnion: () => (/* reexport safe */ _types_js__WEBPACK_IMPORTED_MODULE_3__.ZodUnion),
/* harmony export */   ZodUnknown: () => (/* reexport safe */ _types_js__WEBPACK_IMPORTED_MODULE_3__.ZodUnknown),
/* harmony export */   ZodVoid: () => (/* reexport safe */ _types_js__WEBPACK_IMPORTED_MODULE_3__.ZodVoid),
/* harmony export */   addIssueToContext: () => (/* reexport safe */ _helpers_parseUtil_js__WEBPACK_IMPORTED_MODULE_1__.addIssueToContext),
/* harmony export */   any: () => (/* reexport safe */ _types_js__WEBPACK_IMPORTED_MODULE_3__.any),
/* harmony export */   array: () => (/* reexport safe */ _types_js__WEBPACK_IMPORTED_MODULE_3__.array),
/* harmony export */   bigint: () => (/* reexport safe */ _types_js__WEBPACK_IMPORTED_MODULE_3__.bigint),
/* harmony export */   boolean: () => (/* reexport safe */ _types_js__WEBPACK_IMPORTED_MODULE_3__.boolean),
/* harmony export */   coerce: () => (/* reexport safe */ _types_js__WEBPACK_IMPORTED_MODULE_3__.coerce),
/* harmony export */   custom: () => (/* reexport safe */ _types_js__WEBPACK_IMPORTED_MODULE_3__.custom),
/* harmony export */   date: () => (/* reexport safe */ _types_js__WEBPACK_IMPORTED_MODULE_3__.date),
/* harmony export */   datetimeRegex: () => (/* reexport safe */ _types_js__WEBPACK_IMPORTED_MODULE_3__.datetimeRegex),
/* harmony export */   defaultErrorMap: () => (/* reexport safe */ _errors_js__WEBPACK_IMPORTED_MODULE_0__.defaultErrorMap),
/* harmony export */   discriminatedUnion: () => (/* reexport safe */ _types_js__WEBPACK_IMPORTED_MODULE_3__.discriminatedUnion),
/* harmony export */   effect: () => (/* reexport safe */ _types_js__WEBPACK_IMPORTED_MODULE_3__.effect),
/* harmony export */   "enum": () => (/* reexport safe */ _types_js__WEBPACK_IMPORTED_MODULE_3__["enum"]),
/* harmony export */   "function": () => (/* reexport safe */ _types_js__WEBPACK_IMPORTED_MODULE_3__["function"]),
/* harmony export */   getErrorMap: () => (/* reexport safe */ _errors_js__WEBPACK_IMPORTED_MODULE_0__.getErrorMap),
/* harmony export */   getParsedType: () => (/* reexport safe */ _helpers_util_js__WEBPACK_IMPORTED_MODULE_2__.getParsedType),
/* harmony export */   "instanceof": () => (/* reexport safe */ _types_js__WEBPACK_IMPORTED_MODULE_3__["instanceof"]),
/* harmony export */   intersection: () => (/* reexport safe */ _types_js__WEBPACK_IMPORTED_MODULE_3__.intersection),
/* harmony export */   isAborted: () => (/* reexport safe */ _helpers_parseUtil_js__WEBPACK_IMPORTED_MODULE_1__.isAborted),
/* harmony export */   isAsync: () => (/* reexport safe */ _helpers_parseUtil_js__WEBPACK_IMPORTED_MODULE_1__.isAsync),
/* harmony export */   isDirty: () => (/* reexport safe */ _helpers_parseUtil_js__WEBPACK_IMPORTED_MODULE_1__.isDirty),
/* harmony export */   isValid: () => (/* reexport safe */ _helpers_parseUtil_js__WEBPACK_IMPORTED_MODULE_1__.isValid),
/* harmony export */   late: () => (/* reexport safe */ _types_js__WEBPACK_IMPORTED_MODULE_3__.late),
/* harmony export */   lazy: () => (/* reexport safe */ _types_js__WEBPACK_IMPORTED_MODULE_3__.lazy),
/* harmony export */   literal: () => (/* reexport safe */ _types_js__WEBPACK_IMPORTED_MODULE_3__.literal),
/* harmony export */   makeIssue: () => (/* reexport safe */ _helpers_parseUtil_js__WEBPACK_IMPORTED_MODULE_1__.makeIssue),
/* harmony export */   map: () => (/* reexport safe */ _types_js__WEBPACK_IMPORTED_MODULE_3__.map),
/* harmony export */   nan: () => (/* reexport safe */ _types_js__WEBPACK_IMPORTED_MODULE_3__.nan),
/* harmony export */   nativeEnum: () => (/* reexport safe */ _types_js__WEBPACK_IMPORTED_MODULE_3__.nativeEnum),
/* harmony export */   never: () => (/* reexport safe */ _types_js__WEBPACK_IMPORTED_MODULE_3__.never),
/* harmony export */   "null": () => (/* reexport safe */ _types_js__WEBPACK_IMPORTED_MODULE_3__["null"]),
/* harmony export */   nullable: () => (/* reexport safe */ _types_js__WEBPACK_IMPORTED_MODULE_3__.nullable),
/* harmony export */   number: () => (/* reexport safe */ _types_js__WEBPACK_IMPORTED_MODULE_3__.number),
/* harmony export */   object: () => (/* reexport safe */ _types_js__WEBPACK_IMPORTED_MODULE_3__.object),
/* harmony export */   objectUtil: () => (/* reexport safe */ _helpers_util_js__WEBPACK_IMPORTED_MODULE_2__.objectUtil),
/* harmony export */   oboolean: () => (/* reexport safe */ _types_js__WEBPACK_IMPORTED_MODULE_3__.oboolean),
/* harmony export */   onumber: () => (/* reexport safe */ _types_js__WEBPACK_IMPORTED_MODULE_3__.onumber),
/* harmony export */   optional: () => (/* reexport safe */ _types_js__WEBPACK_IMPORTED_MODULE_3__.optional),
/* harmony export */   ostring: () => (/* reexport safe */ _types_js__WEBPACK_IMPORTED_MODULE_3__.ostring),
/* harmony export */   pipeline: () => (/* reexport safe */ _types_js__WEBPACK_IMPORTED_MODULE_3__.pipeline),
/* harmony export */   preprocess: () => (/* reexport safe */ _types_js__WEBPACK_IMPORTED_MODULE_3__.preprocess),
/* harmony export */   promise: () => (/* reexport safe */ _types_js__WEBPACK_IMPORTED_MODULE_3__.promise),
/* harmony export */   quotelessJson: () => (/* reexport safe */ _ZodError_js__WEBPACK_IMPORTED_MODULE_4__.quotelessJson),
/* harmony export */   record: () => (/* reexport safe */ _types_js__WEBPACK_IMPORTED_MODULE_3__.record),
/* harmony export */   set: () => (/* reexport safe */ _types_js__WEBPACK_IMPORTED_MODULE_3__.set),
/* harmony export */   setErrorMap: () => (/* reexport safe */ _errors_js__WEBPACK_IMPORTED_MODULE_0__.setErrorMap),
/* harmony export */   strictObject: () => (/* reexport safe */ _types_js__WEBPACK_IMPORTED_MODULE_3__.strictObject),
/* harmony export */   string: () => (/* reexport safe */ _types_js__WEBPACK_IMPORTED_MODULE_3__.string),
/* harmony export */   symbol: () => (/* reexport safe */ _types_js__WEBPACK_IMPORTED_MODULE_3__.symbol),
/* harmony export */   transformer: () => (/* reexport safe */ _types_js__WEBPACK_IMPORTED_MODULE_3__.transformer),
/* harmony export */   tuple: () => (/* reexport safe */ _types_js__WEBPACK_IMPORTED_MODULE_3__.tuple),
/* harmony export */   undefined: () => (/* reexport safe */ _types_js__WEBPACK_IMPORTED_MODULE_3__.undefined),
/* harmony export */   union: () => (/* reexport safe */ _types_js__WEBPACK_IMPORTED_MODULE_3__.union),
/* harmony export */   unknown: () => (/* reexport safe */ _types_js__WEBPACK_IMPORTED_MODULE_3__.unknown),
/* harmony export */   util: () => (/* reexport safe */ _helpers_util_js__WEBPACK_IMPORTED_MODULE_2__.util),
/* harmony export */   "void": () => (/* reexport safe */ _types_js__WEBPACK_IMPORTED_MODULE_3__["void"])
/* harmony export */ });
/* harmony import */ var _errors_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./errors.js */ "../../node_modules/.pnpm/zod@3.25.28/node_modules/zod/dist/esm/v3/errors.js");
/* harmony import */ var _helpers_parseUtil_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./helpers/parseUtil.js */ "../../node_modules/.pnpm/zod@3.25.28/node_modules/zod/dist/esm/v3/helpers/parseUtil.js");
/* harmony import */ var _helpers_util_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./helpers/util.js */ "../../node_modules/.pnpm/zod@3.25.28/node_modules/zod/dist/esm/v3/helpers/util.js");
/* harmony import */ var _types_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./types.js */ "../../node_modules/.pnpm/zod@3.25.28/node_modules/zod/dist/esm/v3/types.js");
/* harmony import */ var _ZodError_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./ZodError.js */ "../../node_modules/.pnpm/zod@3.25.28/node_modules/zod/dist/esm/v3/ZodError.js");








/***/ }),

/***/ "../../node_modules/.pnpm/zod@3.25.28/node_modules/zod/dist/esm/v3/helpers/errorUtil.js":
/*!**********************************************************************************************!*\
  !*** ../../node_modules/.pnpm/zod@3.25.28/node_modules/zod/dist/esm/v3/helpers/errorUtil.js ***!
  \**********************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   errorUtil: () => (/* binding */ errorUtil)
/* harmony export */ });
var errorUtil;
(function (errorUtil) {
    errorUtil.errToObj = (message) => typeof message === "string" ? { message } : message || {};
    // biome-ignore lint:
    errorUtil.toString = (message) => typeof message === "string" ? message : message?.message;
})(errorUtil || (errorUtil = {}));


/***/ }),

/***/ "../../node_modules/.pnpm/zod@3.25.28/node_modules/zod/dist/esm/v3/helpers/parseUtil.js":
/*!**********************************************************************************************!*\
  !*** ../../node_modules/.pnpm/zod@3.25.28/node_modules/zod/dist/esm/v3/helpers/parseUtil.js ***!
  \**********************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   DIRTY: () => (/* binding */ DIRTY),
/* harmony export */   EMPTY_PATH: () => (/* binding */ EMPTY_PATH),
/* harmony export */   INVALID: () => (/* binding */ INVALID),
/* harmony export */   OK: () => (/* binding */ OK),
/* harmony export */   ParseStatus: () => (/* binding */ ParseStatus),
/* harmony export */   addIssueToContext: () => (/* binding */ addIssueToContext),
/* harmony export */   isAborted: () => (/* binding */ isAborted),
/* harmony export */   isAsync: () => (/* binding */ isAsync),
/* harmony export */   isDirty: () => (/* binding */ isDirty),
/* harmony export */   isValid: () => (/* binding */ isValid),
/* harmony export */   makeIssue: () => (/* binding */ makeIssue)
/* harmony export */ });
/* harmony import */ var _errors_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../errors.js */ "../../node_modules/.pnpm/zod@3.25.28/node_modules/zod/dist/esm/v3/errors.js");
/* harmony import */ var _locales_en_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../locales/en.js */ "../../node_modules/.pnpm/zod@3.25.28/node_modules/zod/dist/esm/v3/locales/en.js");


const makeIssue = (params) => {
    const { data, path, errorMaps, issueData } = params;
    const fullPath = [...path, ...(issueData.path || [])];
    const fullIssue = {
        ...issueData,
        path: fullPath,
    };
    if (issueData.message !== undefined) {
        return {
            ...issueData,
            path: fullPath,
            message: issueData.message,
        };
    }
    let errorMessage = "";
    const maps = errorMaps
        .filter((m) => !!m)
        .slice()
        .reverse();
    for (const map of maps) {
        errorMessage = map(fullIssue, { data, defaultError: errorMessage }).message;
    }
    return {
        ...issueData,
        path: fullPath,
        message: errorMessage,
    };
};
const EMPTY_PATH = [];
function addIssueToContext(ctx, issueData) {
    const overrideMap = (0,_errors_js__WEBPACK_IMPORTED_MODULE_0__.getErrorMap)();
    const issue = makeIssue({
        issueData: issueData,
        data: ctx.data,
        path: ctx.path,
        errorMaps: [
            ctx.common.contextualErrorMap, // contextual error map is first priority
            ctx.schemaErrorMap, // then schema-bound map if available
            overrideMap, // then global override map
            overrideMap === _locales_en_js__WEBPACK_IMPORTED_MODULE_1__["default"] ? undefined : _locales_en_js__WEBPACK_IMPORTED_MODULE_1__["default"], // then global default map
        ].filter((x) => !!x),
    });
    ctx.common.issues.push(issue);
}
class ParseStatus {
    constructor() {
        this.value = "valid";
    }
    dirty() {
        if (this.value === "valid")
            this.value = "dirty";
    }
    abort() {
        if (this.value !== "aborted")
            this.value = "aborted";
    }
    static mergeArray(status, results) {
        const arrayValue = [];
        for (const s of results) {
            if (s.status === "aborted")
                return INVALID;
            if (s.status === "dirty")
                status.dirty();
            arrayValue.push(s.value);
        }
        return { status: status.value, value: arrayValue };
    }
    static async mergeObjectAsync(status, pairs) {
        const syncPairs = [];
        for (const pair of pairs) {
            const key = await pair.key;
            const value = await pair.value;
            syncPairs.push({
                key,
                value,
            });
        }
        return ParseStatus.mergeObjectSync(status, syncPairs);
    }
    static mergeObjectSync(status, pairs) {
        const finalObject = {};
        for (const pair of pairs) {
            const { key, value } = pair;
            if (key.status === "aborted")
                return INVALID;
            if (value.status === "aborted")
                return INVALID;
            if (key.status === "dirty")
                status.dirty();
            if (value.status === "dirty")
                status.dirty();
            if (key.value !== "__proto__" && (typeof value.value !== "undefined" || pair.alwaysSet)) {
                finalObject[key.value] = value.value;
            }
        }
        return { status: status.value, value: finalObject };
    }
}
const INVALID = Object.freeze({
    status: "aborted",
});
const DIRTY = (value) => ({ status: "dirty", value });
const OK = (value) => ({ status: "valid", value });
const isAborted = (x) => x.status === "aborted";
const isDirty = (x) => x.status === "dirty";
const isValid = (x) => x.status === "valid";
const isAsync = (x) => typeof Promise !== "undefined" && x instanceof Promise;


/***/ }),

/***/ "../../node_modules/.pnpm/zod@3.25.28/node_modules/zod/dist/esm/v3/helpers/util.js":
/*!*****************************************************************************************!*\
  !*** ../../node_modules/.pnpm/zod@3.25.28/node_modules/zod/dist/esm/v3/helpers/util.js ***!
  \*****************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   ZodParsedType: () => (/* binding */ ZodParsedType),
/* harmony export */   getParsedType: () => (/* binding */ getParsedType),
/* harmony export */   objectUtil: () => (/* binding */ objectUtil),
/* harmony export */   util: () => (/* binding */ util)
/* harmony export */ });
var util;
(function (util) {
    util.assertEqual = (_) => { };
    function assertIs(_arg) { }
    util.assertIs = assertIs;
    function assertNever(_x) {
        throw new Error();
    }
    util.assertNever = assertNever;
    util.arrayToEnum = (items) => {
        const obj = {};
        for (const item of items) {
            obj[item] = item;
        }
        return obj;
    };
    util.getValidEnumValues = (obj) => {
        const validKeys = util.objectKeys(obj).filter((k) => typeof obj[obj[k]] !== "number");
        const filtered = {};
        for (const k of validKeys) {
            filtered[k] = obj[k];
        }
        return util.objectValues(filtered);
    };
    util.objectValues = (obj) => {
        return util.objectKeys(obj).map(function (e) {
            return obj[e];
        });
    };
    util.objectKeys = typeof Object.keys === "function" // eslint-disable-line ban/ban
        ? (obj) => Object.keys(obj) // eslint-disable-line ban/ban
        : (object) => {
            const keys = [];
            for (const key in object) {
                if (Object.prototype.hasOwnProperty.call(object, key)) {
                    keys.push(key);
                }
            }
            return keys;
        };
    util.find = (arr, checker) => {
        for (const item of arr) {
            if (checker(item))
                return item;
        }
        return undefined;
    };
    util.isInteger = typeof Number.isInteger === "function"
        ? (val) => Number.isInteger(val) // eslint-disable-line ban/ban
        : (val) => typeof val === "number" && Number.isFinite(val) && Math.floor(val) === val;
    function joinValues(array, separator = " | ") {
        return array.map((val) => (typeof val === "string" ? `'${val}'` : val)).join(separator);
    }
    util.joinValues = joinValues;
    util.jsonStringifyReplacer = (_, value) => {
        if (typeof value === "bigint") {
            return value.toString();
        }
        return value;
    };
})(util || (util = {}));
var objectUtil;
(function (objectUtil) {
    objectUtil.mergeShapes = (first, second) => {
        return {
            ...first,
            ...second, // second overwrites first
        };
    };
})(objectUtil || (objectUtil = {}));
const ZodParsedType = util.arrayToEnum([
    "string",
    "nan",
    "number",
    "integer",
    "float",
    "boolean",
    "date",
    "bigint",
    "symbol",
    "function",
    "undefined",
    "null",
    "array",
    "object",
    "unknown",
    "promise",
    "void",
    "never",
    "map",
    "set",
]);
const getParsedType = (data) => {
    const t = typeof data;
    switch (t) {
        case "undefined":
            return ZodParsedType.undefined;
        case "string":
            return ZodParsedType.string;
        case "number":
            return Number.isNaN(data) ? ZodParsedType.nan : ZodParsedType.number;
        case "boolean":
            return ZodParsedType.boolean;
        case "function":
            return ZodParsedType.function;
        case "bigint":
            return ZodParsedType.bigint;
        case "symbol":
            return ZodParsedType.symbol;
        case "object":
            if (Array.isArray(data)) {
                return ZodParsedType.array;
            }
            if (data === null) {
                return ZodParsedType.null;
            }
            if (data.then && typeof data.then === "function" && data.catch && typeof data.catch === "function") {
                return ZodParsedType.promise;
            }
            if (typeof Map !== "undefined" && data instanceof Map) {
                return ZodParsedType.map;
            }
            if (typeof Set !== "undefined" && data instanceof Set) {
                return ZodParsedType.set;
            }
            if (typeof Date !== "undefined" && data instanceof Date) {
                return ZodParsedType.date;
            }
            return ZodParsedType.object;
        default:
            return ZodParsedType.unknown;
    }
};


/***/ }),

/***/ "../../node_modules/.pnpm/zod@3.25.28/node_modules/zod/dist/esm/v3/index.js":
/*!**********************************************************************************!*\
  !*** ../../node_modules/.pnpm/zod@3.25.28/node_modules/zod/dist/esm/v3/index.js ***!
  \**********************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   BRAND: () => (/* reexport safe */ _external_js__WEBPACK_IMPORTED_MODULE_0__.BRAND),
/* harmony export */   DIRTY: () => (/* reexport safe */ _external_js__WEBPACK_IMPORTED_MODULE_0__.DIRTY),
/* harmony export */   EMPTY_PATH: () => (/* reexport safe */ _external_js__WEBPACK_IMPORTED_MODULE_0__.EMPTY_PATH),
/* harmony export */   INVALID: () => (/* reexport safe */ _external_js__WEBPACK_IMPORTED_MODULE_0__.INVALID),
/* harmony export */   NEVER: () => (/* reexport safe */ _external_js__WEBPACK_IMPORTED_MODULE_0__.NEVER),
/* harmony export */   OK: () => (/* reexport safe */ _external_js__WEBPACK_IMPORTED_MODULE_0__.OK),
/* harmony export */   ParseStatus: () => (/* reexport safe */ _external_js__WEBPACK_IMPORTED_MODULE_0__.ParseStatus),
/* harmony export */   Schema: () => (/* reexport safe */ _external_js__WEBPACK_IMPORTED_MODULE_0__.Schema),
/* harmony export */   ZodAny: () => (/* reexport safe */ _external_js__WEBPACK_IMPORTED_MODULE_0__.ZodAny),
/* harmony export */   ZodArray: () => (/* reexport safe */ _external_js__WEBPACK_IMPORTED_MODULE_0__.ZodArray),
/* harmony export */   ZodBigInt: () => (/* reexport safe */ _external_js__WEBPACK_IMPORTED_MODULE_0__.ZodBigInt),
/* harmony export */   ZodBoolean: () => (/* reexport safe */ _external_js__WEBPACK_IMPORTED_MODULE_0__.ZodBoolean),
/* harmony export */   ZodBranded: () => (/* reexport safe */ _external_js__WEBPACK_IMPORTED_MODULE_0__.ZodBranded),
/* harmony export */   ZodCatch: () => (/* reexport safe */ _external_js__WEBPACK_IMPORTED_MODULE_0__.ZodCatch),
/* harmony export */   ZodDate: () => (/* reexport safe */ _external_js__WEBPACK_IMPORTED_MODULE_0__.ZodDate),
/* harmony export */   ZodDefault: () => (/* reexport safe */ _external_js__WEBPACK_IMPORTED_MODULE_0__.ZodDefault),
/* harmony export */   ZodDiscriminatedUnion: () => (/* reexport safe */ _external_js__WEBPACK_IMPORTED_MODULE_0__.ZodDiscriminatedUnion),
/* harmony export */   ZodEffects: () => (/* reexport safe */ _external_js__WEBPACK_IMPORTED_MODULE_0__.ZodEffects),
/* harmony export */   ZodEnum: () => (/* reexport safe */ _external_js__WEBPACK_IMPORTED_MODULE_0__.ZodEnum),
/* harmony export */   ZodError: () => (/* reexport safe */ _external_js__WEBPACK_IMPORTED_MODULE_0__.ZodError),
/* harmony export */   ZodFirstPartyTypeKind: () => (/* reexport safe */ _external_js__WEBPACK_IMPORTED_MODULE_0__.ZodFirstPartyTypeKind),
/* harmony export */   ZodFunction: () => (/* reexport safe */ _external_js__WEBPACK_IMPORTED_MODULE_0__.ZodFunction),
/* harmony export */   ZodIntersection: () => (/* reexport safe */ _external_js__WEBPACK_IMPORTED_MODULE_0__.ZodIntersection),
/* harmony export */   ZodIssueCode: () => (/* reexport safe */ _external_js__WEBPACK_IMPORTED_MODULE_0__.ZodIssueCode),
/* harmony export */   ZodLazy: () => (/* reexport safe */ _external_js__WEBPACK_IMPORTED_MODULE_0__.ZodLazy),
/* harmony export */   ZodLiteral: () => (/* reexport safe */ _external_js__WEBPACK_IMPORTED_MODULE_0__.ZodLiteral),
/* harmony export */   ZodMap: () => (/* reexport safe */ _external_js__WEBPACK_IMPORTED_MODULE_0__.ZodMap),
/* harmony export */   ZodNaN: () => (/* reexport safe */ _external_js__WEBPACK_IMPORTED_MODULE_0__.ZodNaN),
/* harmony export */   ZodNativeEnum: () => (/* reexport safe */ _external_js__WEBPACK_IMPORTED_MODULE_0__.ZodNativeEnum),
/* harmony export */   ZodNever: () => (/* reexport safe */ _external_js__WEBPACK_IMPORTED_MODULE_0__.ZodNever),
/* harmony export */   ZodNull: () => (/* reexport safe */ _external_js__WEBPACK_IMPORTED_MODULE_0__.ZodNull),
/* harmony export */   ZodNullable: () => (/* reexport safe */ _external_js__WEBPACK_IMPORTED_MODULE_0__.ZodNullable),
/* harmony export */   ZodNumber: () => (/* reexport safe */ _external_js__WEBPACK_IMPORTED_MODULE_0__.ZodNumber),
/* harmony export */   ZodObject: () => (/* reexport safe */ _external_js__WEBPACK_IMPORTED_MODULE_0__.ZodObject),
/* harmony export */   ZodOptional: () => (/* reexport safe */ _external_js__WEBPACK_IMPORTED_MODULE_0__.ZodOptional),
/* harmony export */   ZodParsedType: () => (/* reexport safe */ _external_js__WEBPACK_IMPORTED_MODULE_0__.ZodParsedType),
/* harmony export */   ZodPipeline: () => (/* reexport safe */ _external_js__WEBPACK_IMPORTED_MODULE_0__.ZodPipeline),
/* harmony export */   ZodPromise: () => (/* reexport safe */ _external_js__WEBPACK_IMPORTED_MODULE_0__.ZodPromise),
/* harmony export */   ZodReadonly: () => (/* reexport safe */ _external_js__WEBPACK_IMPORTED_MODULE_0__.ZodReadonly),
/* harmony export */   ZodRecord: () => (/* reexport safe */ _external_js__WEBPACK_IMPORTED_MODULE_0__.ZodRecord),
/* harmony export */   ZodSchema: () => (/* reexport safe */ _external_js__WEBPACK_IMPORTED_MODULE_0__.ZodSchema),
/* harmony export */   ZodSet: () => (/* reexport safe */ _external_js__WEBPACK_IMPORTED_MODULE_0__.ZodSet),
/* harmony export */   ZodString: () => (/* reexport safe */ _external_js__WEBPACK_IMPORTED_MODULE_0__.ZodString),
/* harmony export */   ZodSymbol: () => (/* reexport safe */ _external_js__WEBPACK_IMPORTED_MODULE_0__.ZodSymbol),
/* harmony export */   ZodTransformer: () => (/* reexport safe */ _external_js__WEBPACK_IMPORTED_MODULE_0__.ZodTransformer),
/* harmony export */   ZodTuple: () => (/* reexport safe */ _external_js__WEBPACK_IMPORTED_MODULE_0__.ZodTuple),
/* harmony export */   ZodType: () => (/* reexport safe */ _external_js__WEBPACK_IMPORTED_MODULE_0__.ZodType),
/* harmony export */   ZodUndefined: () => (/* reexport safe */ _external_js__WEBPACK_IMPORTED_MODULE_0__.ZodUndefined),
/* harmony export */   ZodUnion: () => (/* reexport safe */ _external_js__WEBPACK_IMPORTED_MODULE_0__.ZodUnion),
/* harmony export */   ZodUnknown: () => (/* reexport safe */ _external_js__WEBPACK_IMPORTED_MODULE_0__.ZodUnknown),
/* harmony export */   ZodVoid: () => (/* reexport safe */ _external_js__WEBPACK_IMPORTED_MODULE_0__.ZodVoid),
/* harmony export */   addIssueToContext: () => (/* reexport safe */ _external_js__WEBPACK_IMPORTED_MODULE_0__.addIssueToContext),
/* harmony export */   any: () => (/* reexport safe */ _external_js__WEBPACK_IMPORTED_MODULE_0__.any),
/* harmony export */   array: () => (/* reexport safe */ _external_js__WEBPACK_IMPORTED_MODULE_0__.array),
/* harmony export */   bigint: () => (/* reexport safe */ _external_js__WEBPACK_IMPORTED_MODULE_0__.bigint),
/* harmony export */   boolean: () => (/* reexport safe */ _external_js__WEBPACK_IMPORTED_MODULE_0__.boolean),
/* harmony export */   coerce: () => (/* reexport safe */ _external_js__WEBPACK_IMPORTED_MODULE_0__.coerce),
/* harmony export */   custom: () => (/* reexport safe */ _external_js__WEBPACK_IMPORTED_MODULE_0__.custom),
/* harmony export */   date: () => (/* reexport safe */ _external_js__WEBPACK_IMPORTED_MODULE_0__.date),
/* harmony export */   datetimeRegex: () => (/* reexport safe */ _external_js__WEBPACK_IMPORTED_MODULE_0__.datetimeRegex),
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__),
/* harmony export */   defaultErrorMap: () => (/* reexport safe */ _external_js__WEBPACK_IMPORTED_MODULE_0__.defaultErrorMap),
/* harmony export */   discriminatedUnion: () => (/* reexport safe */ _external_js__WEBPACK_IMPORTED_MODULE_0__.discriminatedUnion),
/* harmony export */   effect: () => (/* reexport safe */ _external_js__WEBPACK_IMPORTED_MODULE_0__.effect),
/* harmony export */   "enum": () => (/* reexport safe */ _external_js__WEBPACK_IMPORTED_MODULE_0__["enum"]),
/* harmony export */   "function": () => (/* reexport safe */ _external_js__WEBPACK_IMPORTED_MODULE_0__["function"]),
/* harmony export */   getErrorMap: () => (/* reexport safe */ _external_js__WEBPACK_IMPORTED_MODULE_0__.getErrorMap),
/* harmony export */   getParsedType: () => (/* reexport safe */ _external_js__WEBPACK_IMPORTED_MODULE_0__.getParsedType),
/* harmony export */   "instanceof": () => (/* reexport safe */ _external_js__WEBPACK_IMPORTED_MODULE_0__["instanceof"]),
/* harmony export */   intersection: () => (/* reexport safe */ _external_js__WEBPACK_IMPORTED_MODULE_0__.intersection),
/* harmony export */   isAborted: () => (/* reexport safe */ _external_js__WEBPACK_IMPORTED_MODULE_0__.isAborted),
/* harmony export */   isAsync: () => (/* reexport safe */ _external_js__WEBPACK_IMPORTED_MODULE_0__.isAsync),
/* harmony export */   isDirty: () => (/* reexport safe */ _external_js__WEBPACK_IMPORTED_MODULE_0__.isDirty),
/* harmony export */   isValid: () => (/* reexport safe */ _external_js__WEBPACK_IMPORTED_MODULE_0__.isValid),
/* harmony export */   late: () => (/* reexport safe */ _external_js__WEBPACK_IMPORTED_MODULE_0__.late),
/* harmony export */   lazy: () => (/* reexport safe */ _external_js__WEBPACK_IMPORTED_MODULE_0__.lazy),
/* harmony export */   literal: () => (/* reexport safe */ _external_js__WEBPACK_IMPORTED_MODULE_0__.literal),
/* harmony export */   makeIssue: () => (/* reexport safe */ _external_js__WEBPACK_IMPORTED_MODULE_0__.makeIssue),
/* harmony export */   map: () => (/* reexport safe */ _external_js__WEBPACK_IMPORTED_MODULE_0__.map),
/* harmony export */   nan: () => (/* reexport safe */ _external_js__WEBPACK_IMPORTED_MODULE_0__.nan),
/* harmony export */   nativeEnum: () => (/* reexport safe */ _external_js__WEBPACK_IMPORTED_MODULE_0__.nativeEnum),
/* harmony export */   never: () => (/* reexport safe */ _external_js__WEBPACK_IMPORTED_MODULE_0__.never),
/* harmony export */   "null": () => (/* reexport safe */ _external_js__WEBPACK_IMPORTED_MODULE_0__["null"]),
/* harmony export */   nullable: () => (/* reexport safe */ _external_js__WEBPACK_IMPORTED_MODULE_0__.nullable),
/* harmony export */   number: () => (/* reexport safe */ _external_js__WEBPACK_IMPORTED_MODULE_0__.number),
/* harmony export */   object: () => (/* reexport safe */ _external_js__WEBPACK_IMPORTED_MODULE_0__.object),
/* harmony export */   objectUtil: () => (/* reexport safe */ _external_js__WEBPACK_IMPORTED_MODULE_0__.objectUtil),
/* harmony export */   oboolean: () => (/* reexport safe */ _external_js__WEBPACK_IMPORTED_MODULE_0__.oboolean),
/* harmony export */   onumber: () => (/* reexport safe */ _external_js__WEBPACK_IMPORTED_MODULE_0__.onumber),
/* harmony export */   optional: () => (/* reexport safe */ _external_js__WEBPACK_IMPORTED_MODULE_0__.optional),
/* harmony export */   ostring: () => (/* reexport safe */ _external_js__WEBPACK_IMPORTED_MODULE_0__.ostring),
/* harmony export */   pipeline: () => (/* reexport safe */ _external_js__WEBPACK_IMPORTED_MODULE_0__.pipeline),
/* harmony export */   preprocess: () => (/* reexport safe */ _external_js__WEBPACK_IMPORTED_MODULE_0__.preprocess),
/* harmony export */   promise: () => (/* reexport safe */ _external_js__WEBPACK_IMPORTED_MODULE_0__.promise),
/* harmony export */   quotelessJson: () => (/* reexport safe */ _external_js__WEBPACK_IMPORTED_MODULE_0__.quotelessJson),
/* harmony export */   record: () => (/* reexport safe */ _external_js__WEBPACK_IMPORTED_MODULE_0__.record),
/* harmony export */   set: () => (/* reexport safe */ _external_js__WEBPACK_IMPORTED_MODULE_0__.set),
/* harmony export */   setErrorMap: () => (/* reexport safe */ _external_js__WEBPACK_IMPORTED_MODULE_0__.setErrorMap),
/* harmony export */   strictObject: () => (/* reexport safe */ _external_js__WEBPACK_IMPORTED_MODULE_0__.strictObject),
/* harmony export */   string: () => (/* reexport safe */ _external_js__WEBPACK_IMPORTED_MODULE_0__.string),
/* harmony export */   symbol: () => (/* reexport safe */ _external_js__WEBPACK_IMPORTED_MODULE_0__.symbol),
/* harmony export */   transformer: () => (/* reexport safe */ _external_js__WEBPACK_IMPORTED_MODULE_0__.transformer),
/* harmony export */   tuple: () => (/* reexport safe */ _external_js__WEBPACK_IMPORTED_MODULE_0__.tuple),
/* harmony export */   undefined: () => (/* reexport safe */ _external_js__WEBPACK_IMPORTED_MODULE_0__.undefined),
/* harmony export */   union: () => (/* reexport safe */ _external_js__WEBPACK_IMPORTED_MODULE_0__.union),
/* harmony export */   unknown: () => (/* reexport safe */ _external_js__WEBPACK_IMPORTED_MODULE_0__.unknown),
/* harmony export */   util: () => (/* reexport safe */ _external_js__WEBPACK_IMPORTED_MODULE_0__.util),
/* harmony export */   "void": () => (/* reexport safe */ _external_js__WEBPACK_IMPORTED_MODULE_0__["void"]),
/* harmony export */   z: () => (/* reexport module object */ _external_js__WEBPACK_IMPORTED_MODULE_0__)
/* harmony export */ });
/* harmony import */ var _external_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./external.js */ "../../node_modules/.pnpm/zod@3.25.28/node_modules/zod/dist/esm/v3/external.js");



/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (_external_js__WEBPACK_IMPORTED_MODULE_0__);


/***/ }),

/***/ "../../node_modules/.pnpm/zod@3.25.28/node_modules/zod/dist/esm/v3/locales/en.js":
/*!***************************************************************************************!*\
  !*** ../../node_modules/.pnpm/zod@3.25.28/node_modules/zod/dist/esm/v3/locales/en.js ***!
  \***************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var _ZodError_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../ZodError.js */ "../../node_modules/.pnpm/zod@3.25.28/node_modules/zod/dist/esm/v3/ZodError.js");
/* harmony import */ var _helpers_util_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../helpers/util.js */ "../../node_modules/.pnpm/zod@3.25.28/node_modules/zod/dist/esm/v3/helpers/util.js");


const errorMap = (issue, _ctx) => {
    let message;
    switch (issue.code) {
        case _ZodError_js__WEBPACK_IMPORTED_MODULE_0__.ZodIssueCode.invalid_type:
            if (issue.received === _helpers_util_js__WEBPACK_IMPORTED_MODULE_1__.ZodParsedType.undefined) {
                message = "Required";
            }
            else {
                message = `Expected ${issue.expected}, received ${issue.received}`;
            }
            break;
        case _ZodError_js__WEBPACK_IMPORTED_MODULE_0__.ZodIssueCode.invalid_literal:
            message = `Invalid literal value, expected ${JSON.stringify(issue.expected, _helpers_util_js__WEBPACK_IMPORTED_MODULE_1__.util.jsonStringifyReplacer)}`;
            break;
        case _ZodError_js__WEBPACK_IMPORTED_MODULE_0__.ZodIssueCode.unrecognized_keys:
            message = `Unrecognized key(s) in object: ${_helpers_util_js__WEBPACK_IMPORTED_MODULE_1__.util.joinValues(issue.keys, ", ")}`;
            break;
        case _ZodError_js__WEBPACK_IMPORTED_MODULE_0__.ZodIssueCode.invalid_union:
            message = `Invalid input`;
            break;
        case _ZodError_js__WEBPACK_IMPORTED_MODULE_0__.ZodIssueCode.invalid_union_discriminator:
            message = `Invalid discriminator value. Expected ${_helpers_util_js__WEBPACK_IMPORTED_MODULE_1__.util.joinValues(issue.options)}`;
            break;
        case _ZodError_js__WEBPACK_IMPORTED_MODULE_0__.ZodIssueCode.invalid_enum_value:
            message = `Invalid enum value. Expected ${_helpers_util_js__WEBPACK_IMPORTED_MODULE_1__.util.joinValues(issue.options)}, received '${issue.received}'`;
            break;
        case _ZodError_js__WEBPACK_IMPORTED_MODULE_0__.ZodIssueCode.invalid_arguments:
            message = `Invalid function arguments`;
            break;
        case _ZodError_js__WEBPACK_IMPORTED_MODULE_0__.ZodIssueCode.invalid_return_type:
            message = `Invalid function return type`;
            break;
        case _ZodError_js__WEBPACK_IMPORTED_MODULE_0__.ZodIssueCode.invalid_date:
            message = `Invalid date`;
            break;
        case _ZodError_js__WEBPACK_IMPORTED_MODULE_0__.ZodIssueCode.invalid_string:
            if (typeof issue.validation === "object") {
                if ("includes" in issue.validation) {
                    message = `Invalid input: must include "${issue.validation.includes}"`;
                    if (typeof issue.validation.position === "number") {
                        message = `${message} at one or more positions greater than or equal to ${issue.validation.position}`;
                    }
                }
                else if ("startsWith" in issue.validation) {
                    message = `Invalid input: must start with "${issue.validation.startsWith}"`;
                }
                else if ("endsWith" in issue.validation) {
                    message = `Invalid input: must end with "${issue.validation.endsWith}"`;
                }
                else {
                    _helpers_util_js__WEBPACK_IMPORTED_MODULE_1__.util.assertNever(issue.validation);
                }
            }
            else if (issue.validation !== "regex") {
                message = `Invalid ${issue.validation}`;
            }
            else {
                message = "Invalid";
            }
            break;
        case _ZodError_js__WEBPACK_IMPORTED_MODULE_0__.ZodIssueCode.too_small:
            if (issue.type === "array")
                message = `Array must contain ${issue.exact ? "exactly" : issue.inclusive ? `at least` : `more than`} ${issue.minimum} element(s)`;
            else if (issue.type === "string")
                message = `String must contain ${issue.exact ? "exactly" : issue.inclusive ? `at least` : `over`} ${issue.minimum} character(s)`;
            else if (issue.type === "number")
                message = `Number must be ${issue.exact ? `exactly equal to ` : issue.inclusive ? `greater than or equal to ` : `greater than `}${issue.minimum}`;
            else if (issue.type === "date")
                message = `Date must be ${issue.exact ? `exactly equal to ` : issue.inclusive ? `greater than or equal to ` : `greater than `}${new Date(Number(issue.minimum))}`;
            else
                message = "Invalid input";
            break;
        case _ZodError_js__WEBPACK_IMPORTED_MODULE_0__.ZodIssueCode.too_big:
            if (issue.type === "array")
                message = `Array must contain ${issue.exact ? `exactly` : issue.inclusive ? `at most` : `less than`} ${issue.maximum} element(s)`;
            else if (issue.type === "string")
                message = `String must contain ${issue.exact ? `exactly` : issue.inclusive ? `at most` : `under`} ${issue.maximum} character(s)`;
            else if (issue.type === "number")
                message = `Number must be ${issue.exact ? `exactly` : issue.inclusive ? `less than or equal to` : `less than`} ${issue.maximum}`;
            else if (issue.type === "bigint")
                message = `BigInt must be ${issue.exact ? `exactly` : issue.inclusive ? `less than or equal to` : `less than`} ${issue.maximum}`;
            else if (issue.type === "date")
                message = `Date must be ${issue.exact ? `exactly` : issue.inclusive ? `smaller than or equal to` : `smaller than`} ${new Date(Number(issue.maximum))}`;
            else
                message = "Invalid input";
            break;
        case _ZodError_js__WEBPACK_IMPORTED_MODULE_0__.ZodIssueCode.custom:
            message = `Invalid input`;
            break;
        case _ZodError_js__WEBPACK_IMPORTED_MODULE_0__.ZodIssueCode.invalid_intersection_types:
            message = `Intersection results could not be merged`;
            break;
        case _ZodError_js__WEBPACK_IMPORTED_MODULE_0__.ZodIssueCode.not_multiple_of:
            message = `Number must be a multiple of ${issue.multipleOf}`;
            break;
        case _ZodError_js__WEBPACK_IMPORTED_MODULE_0__.ZodIssueCode.not_finite:
            message = "Number must be finite";
            break;
        default:
            message = _ctx.defaultError;
            _helpers_util_js__WEBPACK_IMPORTED_MODULE_1__.util.assertNever(issue);
    }
    return { message };
};
/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (errorMap);


/***/ }),

/***/ "../../node_modules/.pnpm/zod@3.25.28/node_modules/zod/dist/esm/v3/types.js":
/*!**********************************************************************************!*\
  !*** ../../node_modules/.pnpm/zod@3.25.28/node_modules/zod/dist/esm/v3/types.js ***!
  \**********************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   BRAND: () => (/* binding */ BRAND),
/* harmony export */   NEVER: () => (/* binding */ NEVER),
/* harmony export */   Schema: () => (/* binding */ ZodType),
/* harmony export */   ZodAny: () => (/* binding */ ZodAny),
/* harmony export */   ZodArray: () => (/* binding */ ZodArray),
/* harmony export */   ZodBigInt: () => (/* binding */ ZodBigInt),
/* harmony export */   ZodBoolean: () => (/* binding */ ZodBoolean),
/* harmony export */   ZodBranded: () => (/* binding */ ZodBranded),
/* harmony export */   ZodCatch: () => (/* binding */ ZodCatch),
/* harmony export */   ZodDate: () => (/* binding */ ZodDate),
/* harmony export */   ZodDefault: () => (/* binding */ ZodDefault),
/* harmony export */   ZodDiscriminatedUnion: () => (/* binding */ ZodDiscriminatedUnion),
/* harmony export */   ZodEffects: () => (/* binding */ ZodEffects),
/* harmony export */   ZodEnum: () => (/* binding */ ZodEnum),
/* harmony export */   ZodFirstPartyTypeKind: () => (/* binding */ ZodFirstPartyTypeKind),
/* harmony export */   ZodFunction: () => (/* binding */ ZodFunction),
/* harmony export */   ZodIntersection: () => (/* binding */ ZodIntersection),
/* harmony export */   ZodLazy: () => (/* binding */ ZodLazy),
/* harmony export */   ZodLiteral: () => (/* binding */ ZodLiteral),
/* harmony export */   ZodMap: () => (/* binding */ ZodMap),
/* harmony export */   ZodNaN: () => (/* binding */ ZodNaN),
/* harmony export */   ZodNativeEnum: () => (/* binding */ ZodNativeEnum),
/* harmony export */   ZodNever: () => (/* binding */ ZodNever),
/* harmony export */   ZodNull: () => (/* binding */ ZodNull),
/* harmony export */   ZodNullable: () => (/* binding */ ZodNullable),
/* harmony export */   ZodNumber: () => (/* binding */ ZodNumber),
/* harmony export */   ZodObject: () => (/* binding */ ZodObject),
/* harmony export */   ZodOptional: () => (/* binding */ ZodOptional),
/* harmony export */   ZodPipeline: () => (/* binding */ ZodPipeline),
/* harmony export */   ZodPromise: () => (/* binding */ ZodPromise),
/* harmony export */   ZodReadonly: () => (/* binding */ ZodReadonly),
/* harmony export */   ZodRecord: () => (/* binding */ ZodRecord),
/* harmony export */   ZodSchema: () => (/* binding */ ZodType),
/* harmony export */   ZodSet: () => (/* binding */ ZodSet),
/* harmony export */   ZodString: () => (/* binding */ ZodString),
/* harmony export */   ZodSymbol: () => (/* binding */ ZodSymbol),
/* harmony export */   ZodTransformer: () => (/* binding */ ZodEffects),
/* harmony export */   ZodTuple: () => (/* binding */ ZodTuple),
/* harmony export */   ZodType: () => (/* binding */ ZodType),
/* harmony export */   ZodUndefined: () => (/* binding */ ZodUndefined),
/* harmony export */   ZodUnion: () => (/* binding */ ZodUnion),
/* harmony export */   ZodUnknown: () => (/* binding */ ZodUnknown),
/* harmony export */   ZodVoid: () => (/* binding */ ZodVoid),
/* harmony export */   any: () => (/* binding */ anyType),
/* harmony export */   array: () => (/* binding */ arrayType),
/* harmony export */   bigint: () => (/* binding */ bigIntType),
/* harmony export */   boolean: () => (/* binding */ booleanType),
/* harmony export */   coerce: () => (/* binding */ coerce),
/* harmony export */   custom: () => (/* binding */ custom),
/* harmony export */   date: () => (/* binding */ dateType),
/* harmony export */   datetimeRegex: () => (/* binding */ datetimeRegex),
/* harmony export */   discriminatedUnion: () => (/* binding */ discriminatedUnionType),
/* harmony export */   effect: () => (/* binding */ effectsType),
/* harmony export */   "enum": () => (/* binding */ enumType),
/* harmony export */   "function": () => (/* binding */ functionType),
/* harmony export */   "instanceof": () => (/* binding */ instanceOfType),
/* harmony export */   intersection: () => (/* binding */ intersectionType),
/* harmony export */   late: () => (/* binding */ late),
/* harmony export */   lazy: () => (/* binding */ lazyType),
/* harmony export */   literal: () => (/* binding */ literalType),
/* harmony export */   map: () => (/* binding */ mapType),
/* harmony export */   nan: () => (/* binding */ nanType),
/* harmony export */   nativeEnum: () => (/* binding */ nativeEnumType),
/* harmony export */   never: () => (/* binding */ neverType),
/* harmony export */   "null": () => (/* binding */ nullType),
/* harmony export */   nullable: () => (/* binding */ nullableType),
/* harmony export */   number: () => (/* binding */ numberType),
/* harmony export */   object: () => (/* binding */ objectType),
/* harmony export */   oboolean: () => (/* binding */ oboolean),
/* harmony export */   onumber: () => (/* binding */ onumber),
/* harmony export */   optional: () => (/* binding */ optionalType),
/* harmony export */   ostring: () => (/* binding */ ostring),
/* harmony export */   pipeline: () => (/* binding */ pipelineType),
/* harmony export */   preprocess: () => (/* binding */ preprocessType),
/* harmony export */   promise: () => (/* binding */ promiseType),
/* harmony export */   record: () => (/* binding */ recordType),
/* harmony export */   set: () => (/* binding */ setType),
/* harmony export */   strictObject: () => (/* binding */ strictObjectType),
/* harmony export */   string: () => (/* binding */ stringType),
/* harmony export */   symbol: () => (/* binding */ symbolType),
/* harmony export */   transformer: () => (/* binding */ effectsType),
/* harmony export */   tuple: () => (/* binding */ tupleType),
/* harmony export */   undefined: () => (/* binding */ undefinedType),
/* harmony export */   union: () => (/* binding */ unionType),
/* harmony export */   unknown: () => (/* binding */ unknownType),
/* harmony export */   "void": () => (/* binding */ voidType)
/* harmony export */ });
/* harmony import */ var _ZodError_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./ZodError.js */ "../../node_modules/.pnpm/zod@3.25.28/node_modules/zod/dist/esm/v3/ZodError.js");
/* harmony import */ var _errors_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./errors.js */ "../../node_modules/.pnpm/zod@3.25.28/node_modules/zod/dist/esm/v3/errors.js");
/* harmony import */ var _helpers_errorUtil_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./helpers/errorUtil.js */ "../../node_modules/.pnpm/zod@3.25.28/node_modules/zod/dist/esm/v3/helpers/errorUtil.js");
/* harmony import */ var _helpers_parseUtil_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./helpers/parseUtil.js */ "../../node_modules/.pnpm/zod@3.25.28/node_modules/zod/dist/esm/v3/helpers/parseUtil.js");
/* harmony import */ var _helpers_util_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./helpers/util.js */ "../../node_modules/.pnpm/zod@3.25.28/node_modules/zod/dist/esm/v3/helpers/util.js");
var __classPrivateFieldGet = (undefined && undefined.__classPrivateFieldGet) || function (receiver, state, kind, f) {
    if (kind === "a" && !f) throw new TypeError("Private accessor was defined without a getter");
    if (typeof state === "function" ? receiver !== state || !f : !state.has(receiver)) throw new TypeError("Cannot read private member from an object whose class did not declare it");
    return kind === "m" ? f : kind === "a" ? f.call(receiver) : f ? f.value : state.get(receiver);
};
var __classPrivateFieldSet = (undefined && undefined.__classPrivateFieldSet) || function (receiver, state, value, kind, f) {
    if (kind === "m") throw new TypeError("Private method is not writable");
    if (kind === "a" && !f) throw new TypeError("Private accessor was defined without a setter");
    if (typeof state === "function" ? receiver !== state || !f : !state.has(receiver)) throw new TypeError("Cannot write private member to an object whose class did not declare it");
    return (kind === "a" ? f.call(receiver, value) : f ? f.value = value : state.set(receiver, value)), value;
};
var _ZodEnum_cache, _ZodNativeEnum_cache;





class ParseInputLazyPath {
    constructor(parent, value, path, key) {
        this._cachedPath = [];
        this.parent = parent;
        this.data = value;
        this._path = path;
        this._key = key;
    }
    get path() {
        if (!this._cachedPath.length) {
            if (Array.isArray(this._key)) {
                this._cachedPath.push(...this._path, ...this._key);
            }
            else {
                this._cachedPath.push(...this._path, this._key);
            }
        }
        return this._cachedPath;
    }
}
const handleResult = (ctx, result) => {
    if ((0,_helpers_parseUtil_js__WEBPACK_IMPORTED_MODULE_3__.isValid)(result)) {
        return { success: true, data: result.value };
    }
    else {
        if (!ctx.common.issues.length) {
            throw new Error("Validation failed but no issues detected.");
        }
        return {
            success: false,
            get error() {
                if (this._error)
                    return this._error;
                const error = new _ZodError_js__WEBPACK_IMPORTED_MODULE_0__.ZodError(ctx.common.issues);
                this._error = error;
                return this._error;
            },
        };
    }
};
function processCreateParams(params) {
    if (!params)
        return {};
    const { errorMap, invalid_type_error, required_error, description } = params;
    if (errorMap && (invalid_type_error || required_error)) {
        throw new Error(`Can't use "invalid_type_error" or "required_error" in conjunction with custom error map.`);
    }
    if (errorMap)
        return { errorMap: errorMap, description };
    const customMap = (iss, ctx) => {
        const { message } = params;
        if (iss.code === "invalid_enum_value") {
            return { message: message ?? ctx.defaultError };
        }
        if (typeof ctx.data === "undefined") {
            return { message: message ?? required_error ?? ctx.defaultError };
        }
        if (iss.code !== "invalid_type")
            return { message: ctx.defaultError };
        return { message: message ?? invalid_type_error ?? ctx.defaultError };
    };
    return { errorMap: customMap, description };
}
class ZodType {
    get description() {
        return this._def.description;
    }
    _getType(input) {
        return (0,_helpers_util_js__WEBPACK_IMPORTED_MODULE_4__.getParsedType)(input.data);
    }
    _getOrReturnCtx(input, ctx) {
        return (ctx || {
            common: input.parent.common,
            data: input.data,
            parsedType: (0,_helpers_util_js__WEBPACK_IMPORTED_MODULE_4__.getParsedType)(input.data),
            schemaErrorMap: this._def.errorMap,
            path: input.path,
            parent: input.parent,
        });
    }
    _processInputParams(input) {
        return {
            status: new _helpers_parseUtil_js__WEBPACK_IMPORTED_MODULE_3__.ParseStatus(),
            ctx: {
                common: input.parent.common,
                data: input.data,
                parsedType: (0,_helpers_util_js__WEBPACK_IMPORTED_MODULE_4__.getParsedType)(input.data),
                schemaErrorMap: this._def.errorMap,
                path: input.path,
                parent: input.parent,
            },
        };
    }
    _parseSync(input) {
        const result = this._parse(input);
        if ((0,_helpers_parseUtil_js__WEBPACK_IMPORTED_MODULE_3__.isAsync)(result)) {
            throw new Error("Synchronous parse encountered promise.");
        }
        return result;
    }
    _parseAsync(input) {
        const result = this._parse(input);
        return Promise.resolve(result);
    }
    parse(data, params) {
        const result = this.safeParse(data, params);
        if (result.success)
            return result.data;
        throw result.error;
    }
    safeParse(data, params) {
        const ctx = {
            common: {
                issues: [],
                async: params?.async ?? false,
                contextualErrorMap: params?.errorMap,
            },
            path: params?.path || [],
            schemaErrorMap: this._def.errorMap,
            parent: null,
            data,
            parsedType: (0,_helpers_util_js__WEBPACK_IMPORTED_MODULE_4__.getParsedType)(data),
        };
        const result = this._parseSync({ data, path: ctx.path, parent: ctx });
        return handleResult(ctx, result);
    }
    "~validate"(data) {
        const ctx = {
            common: {
                issues: [],
                async: !!this["~standard"].async,
            },
            path: [],
            schemaErrorMap: this._def.errorMap,
            parent: null,
            data,
            parsedType: (0,_helpers_util_js__WEBPACK_IMPORTED_MODULE_4__.getParsedType)(data),
        };
        if (!this["~standard"].async) {
            try {
                const result = this._parseSync({ data, path: [], parent: ctx });
                return (0,_helpers_parseUtil_js__WEBPACK_IMPORTED_MODULE_3__.isValid)(result)
                    ? {
                        value: result.value,
                    }
                    : {
                        issues: ctx.common.issues,
                    };
            }
            catch (err) {
                if (err?.message?.toLowerCase()?.includes("encountered")) {
                    this["~standard"].async = true;
                }
                ctx.common = {
                    issues: [],
                    async: true,
                };
            }
        }
        return this._parseAsync({ data, path: [], parent: ctx }).then((result) => (0,_helpers_parseUtil_js__WEBPACK_IMPORTED_MODULE_3__.isValid)(result)
            ? {
                value: result.value,
            }
            : {
                issues: ctx.common.issues,
            });
    }
    async parseAsync(data, params) {
        const result = await this.safeParseAsync(data, params);
        if (result.success)
            return result.data;
        throw result.error;
    }
    async safeParseAsync(data, params) {
        const ctx = {
            common: {
                issues: [],
                contextualErrorMap: params?.errorMap,
                async: true,
            },
            path: params?.path || [],
            schemaErrorMap: this._def.errorMap,
            parent: null,
            data,
            parsedType: (0,_helpers_util_js__WEBPACK_IMPORTED_MODULE_4__.getParsedType)(data),
        };
        const maybeAsyncResult = this._parse({ data, path: ctx.path, parent: ctx });
        const result = await ((0,_helpers_parseUtil_js__WEBPACK_IMPORTED_MODULE_3__.isAsync)(maybeAsyncResult) ? maybeAsyncResult : Promise.resolve(maybeAsyncResult));
        return handleResult(ctx, result);
    }
    refine(check, message) {
        const getIssueProperties = (val) => {
            if (typeof message === "string" || typeof message === "undefined") {
                return { message };
            }
            else if (typeof message === "function") {
                return message(val);
            }
            else {
                return message;
            }
        };
        return this._refinement((val, ctx) => {
            const result = check(val);
            const setError = () => ctx.addIssue({
                code: _ZodError_js__WEBPACK_IMPORTED_MODULE_0__.ZodIssueCode.custom,
                ...getIssueProperties(val),
            });
            if (typeof Promise !== "undefined" && result instanceof Promise) {
                return result.then((data) => {
                    if (!data) {
                        setError();
                        return false;
                    }
                    else {
                        return true;
                    }
                });
            }
            if (!result) {
                setError();
                return false;
            }
            else {
                return true;
            }
        });
    }
    refinement(check, refinementData) {
        return this._refinement((val, ctx) => {
            if (!check(val)) {
                ctx.addIssue(typeof refinementData === "function" ? refinementData(val, ctx) : refinementData);
                return false;
            }
            else {
                return true;
            }
        });
    }
    _refinement(refinement) {
        return new ZodEffects({
            schema: this,
            typeName: ZodFirstPartyTypeKind.ZodEffects,
            effect: { type: "refinement", refinement },
        });
    }
    superRefine(refinement) {
        return this._refinement(refinement);
    }
    constructor(def) {
        /** Alias of safeParseAsync */
        this.spa = this.safeParseAsync;
        this._def = def;
        this.parse = this.parse.bind(this);
        this.safeParse = this.safeParse.bind(this);
        this.parseAsync = this.parseAsync.bind(this);
        this.safeParseAsync = this.safeParseAsync.bind(this);
        this.spa = this.spa.bind(this);
        this.refine = this.refine.bind(this);
        this.refinement = this.refinement.bind(this);
        this.superRefine = this.superRefine.bind(this);
        this.optional = this.optional.bind(this);
        this.nullable = this.nullable.bind(this);
        this.nullish = this.nullish.bind(this);
        this.array = this.array.bind(this);
        this.promise = this.promise.bind(this);
        this.or = this.or.bind(this);
        this.and = this.and.bind(this);
        this.transform = this.transform.bind(this);
        this.brand = this.brand.bind(this);
        this.default = this.default.bind(this);
        this.catch = this.catch.bind(this);
        this.describe = this.describe.bind(this);
        this.pipe = this.pipe.bind(this);
        this.readonly = this.readonly.bind(this);
        this.isNullable = this.isNullable.bind(this);
        this.isOptional = this.isOptional.bind(this);
        this["~standard"] = {
            version: 1,
            vendor: "zod",
            validate: (data) => this["~validate"](data),
        };
    }
    optional() {
        return ZodOptional.create(this, this._def);
    }
    nullable() {
        return ZodNullable.create(this, this._def);
    }
    nullish() {
        return this.nullable().optional();
    }
    array() {
        return ZodArray.create(this);
    }
    promise() {
        return ZodPromise.create(this, this._def);
    }
    or(option) {
        return ZodUnion.create([this, option], this._def);
    }
    and(incoming) {
        return ZodIntersection.create(this, incoming, this._def);
    }
    transform(transform) {
        return new ZodEffects({
            ...processCreateParams(this._def),
            schema: this,
            typeName: ZodFirstPartyTypeKind.ZodEffects,
            effect: { type: "transform", transform },
        });
    }
    default(def) {
        const defaultValueFunc = typeof def === "function" ? def : () => def;
        return new ZodDefault({
            ...processCreateParams(this._def),
            innerType: this,
            defaultValue: defaultValueFunc,
            typeName: ZodFirstPartyTypeKind.ZodDefault,
        });
    }
    brand() {
        return new ZodBranded({
            typeName: ZodFirstPartyTypeKind.ZodBranded,
            type: this,
            ...processCreateParams(this._def),
        });
    }
    catch(def) {
        const catchValueFunc = typeof def === "function" ? def : () => def;
        return new ZodCatch({
            ...processCreateParams(this._def),
            innerType: this,
            catchValue: catchValueFunc,
            typeName: ZodFirstPartyTypeKind.ZodCatch,
        });
    }
    describe(description) {
        const This = this.constructor;
        return new This({
            ...this._def,
            description,
        });
    }
    pipe(target) {
        return ZodPipeline.create(this, target);
    }
    readonly() {
        return ZodReadonly.create(this);
    }
    isOptional() {
        return this.safeParse(undefined).success;
    }
    isNullable() {
        return this.safeParse(null).success;
    }
}
const cuidRegex = /^c[^\s-]{8,}$/i;
const cuid2Regex = /^[0-9a-z]+$/;
const ulidRegex = /^[0-9A-HJKMNP-TV-Z]{26}$/i;
// const uuidRegex =
//   /^([a-f0-9]{8}-[a-f0-9]{4}-[1-5][a-f0-9]{3}-[a-f0-9]{4}-[a-f0-9]{12}|00000000-0000-0000-0000-000000000000)$/i;
const uuidRegex = /^[0-9a-fA-F]{8}\b-[0-9a-fA-F]{4}\b-[0-9a-fA-F]{4}\b-[0-9a-fA-F]{4}\b-[0-9a-fA-F]{12}$/i;
const nanoidRegex = /^[a-z0-9_-]{21}$/i;
const jwtRegex = /^[A-Za-z0-9-_]+\.[A-Za-z0-9-_]+\.[A-Za-z0-9-_]*$/;
const durationRegex = /^[-+]?P(?!$)(?:(?:[-+]?\d+Y)|(?:[-+]?\d+[.,]\d+Y$))?(?:(?:[-+]?\d+M)|(?:[-+]?\d+[.,]\d+M$))?(?:(?:[-+]?\d+W)|(?:[-+]?\d+[.,]\d+W$))?(?:(?:[-+]?\d+D)|(?:[-+]?\d+[.,]\d+D$))?(?:T(?=[\d+-])(?:(?:[-+]?\d+H)|(?:[-+]?\d+[.,]\d+H$))?(?:(?:[-+]?\d+M)|(?:[-+]?\d+[.,]\d+M$))?(?:[-+]?\d+(?:[.,]\d+)?S)?)??$/;
// from https://stackoverflow.com/a/46181/1550155
// old version: too slow, didn't support unicode
// const emailRegex = /^((([a-z]|\d|[!#\$%&'\*\+\-\/=\?\^_`{\|}~]|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])+(\.([a-z]|\d|[!#\$%&'\*\+\-\/=\?\^_`{\|}~]|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])+)*)|((\x22)((((\x20|\x09)*(\x0d\x0a))?(\x20|\x09)+)?(([\x01-\x08\x0b\x0c\x0e-\x1f\x7f]|\x21|[\x23-\x5b]|[\x5d-\x7e]|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])|(\\([\x01-\x09\x0b\x0c\x0d-\x7f]|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF]))))*(((\x20|\x09)*(\x0d\x0a))?(\x20|\x09)+)?(\x22)))@((([a-z]|\d|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])|(([a-z]|\d|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])([a-z]|\d|-|\.|_|~|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])*([a-z]|\d|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])))\.)+(([a-z]|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])|(([a-z]|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])([a-z]|\d|-|\.|_|~|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])*([a-z]|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])))$/i;
//old email regex
// const emailRegex = /^(([^<>()[\].,;:\s@"]+(\.[^<>()[\].,;:\s@"]+)*)|(".+"))@((?!-)([^<>()[\].,;:\s@"]+\.)+[^<>()[\].,;:\s@"]{1,})[^-<>()[\].,;:\s@"]$/i;
// eslint-disable-next-line
// const emailRegex =
//   /^(([^<>()[\]\\.,;:\s@\"]+(\.[^<>()[\]\\.,;:\s@\"]+)*)|(\".+\"))@((\[(((25[0-5])|(2[0-4][0-9])|(1[0-9]{2})|([0-9]{1,2}))\.){3}((25[0-5])|(2[0-4][0-9])|(1[0-9]{2})|([0-9]{1,2}))\])|(\[IPv6:(([a-f0-9]{1,4}:){7}|::([a-f0-9]{1,4}:){0,6}|([a-f0-9]{1,4}:){1}:([a-f0-9]{1,4}:){0,5}|([a-f0-9]{1,4}:){2}:([a-f0-9]{1,4}:){0,4}|([a-f0-9]{1,4}:){3}:([a-f0-9]{1,4}:){0,3}|([a-f0-9]{1,4}:){4}:([a-f0-9]{1,4}:){0,2}|([a-f0-9]{1,4}:){5}:([a-f0-9]{1,4}:){0,1})([a-f0-9]{1,4}|(((25[0-5])|(2[0-4][0-9])|(1[0-9]{2})|([0-9]{1,2}))\.){3}((25[0-5])|(2[0-4][0-9])|(1[0-9]{2})|([0-9]{1,2})))\])|([A-Za-z0-9]([A-Za-z0-9-]*[A-Za-z0-9])*(\.[A-Za-z]{2,})+))$/;
// const emailRegex =
//   /^[a-zA-Z0-9\.\!\#\$\%\&\'\*\+\/\=\?\^\_\`\{\|\}\~\-]+@[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)*$/;
// const emailRegex =
//   /^(?:[a-z0-9!#$%&'*+/=?^_`{|}~-]+(?:\.[a-z0-9!#$%&'*+/=?^_`{|}~-]+)*|"(?:[\x01-\x08\x0b\x0c\x0e-\x1f\x21\x23-\x5b\x5d-\x7f]|\\[\x01-\x09\x0b\x0c\x0e-\x7f])*")@(?:(?:[a-z0-9](?:[a-z0-9-]*[a-z0-9])?\.)+[a-z0-9](?:[a-z0-9-]*[a-z0-9])?|\[(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?|[a-z0-9-]*[a-z0-9]:(?:[\x01-\x08\x0b\x0c\x0e-\x1f\x21-\x5a\x53-\x7f]|\\[\x01-\x09\x0b\x0c\x0e-\x7f])+)\])$/i;
const emailRegex = /^(?!\.)(?!.*\.\.)([A-Z0-9_'+\-\.]*)[A-Z0-9_+-]@([A-Z0-9][A-Z0-9\-]*\.)+[A-Z]{2,}$/i;
// const emailRegex =
//   /^[a-z0-9.!#$%&’*+/=?^_`{|}~-]+@[a-z0-9-]+(?:\.[a-z0-9\-]+)*$/i;
// from https://thekevinscott.com/emojis-in-javascript/#writing-a-regular-expression
const _emojiRegex = `^(\\p{Extended_Pictographic}|\\p{Emoji_Component})+$`;
let emojiRegex;
// faster, simpler, safer
const ipv4Regex = /^(?:(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])\.){3}(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])$/;
const ipv4CidrRegex = /^(?:(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])\.){3}(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])\/(3[0-2]|[12]?[0-9])$/;
// const ipv6Regex =
// /^(([a-f0-9]{1,4}:){7}|::([a-f0-9]{1,4}:){0,6}|([a-f0-9]{1,4}:){1}:([a-f0-9]{1,4}:){0,5}|([a-f0-9]{1,4}:){2}:([a-f0-9]{1,4}:){0,4}|([a-f0-9]{1,4}:){3}:([a-f0-9]{1,4}:){0,3}|([a-f0-9]{1,4}:){4}:([a-f0-9]{1,4}:){0,2}|([a-f0-9]{1,4}:){5}:([a-f0-9]{1,4}:){0,1})([a-f0-9]{1,4}|(((25[0-5])|(2[0-4][0-9])|(1[0-9]{2})|([0-9]{1,2}))\.){3}((25[0-5])|(2[0-4][0-9])|(1[0-9]{2})|([0-9]{1,2})))$/;
const ipv6Regex = /^(([0-9a-fA-F]{1,4}:){7,7}[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,7}:|([0-9a-fA-F]{1,4}:){1,6}:[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,5}(:[0-9a-fA-F]{1,4}){1,2}|([0-9a-fA-F]{1,4}:){1,4}(:[0-9a-fA-F]{1,4}){1,3}|([0-9a-fA-F]{1,4}:){1,3}(:[0-9a-fA-F]{1,4}){1,4}|([0-9a-fA-F]{1,4}:){1,2}(:[0-9a-fA-F]{1,4}){1,5}|[0-9a-fA-F]{1,4}:((:[0-9a-fA-F]{1,4}){1,6})|:((:[0-9a-fA-F]{1,4}){1,7}|:)|fe80:(:[0-9a-fA-F]{0,4}){0,4}%[0-9a-zA-Z]{1,}|::(ffff(:0{1,4}){0,1}:){0,1}((25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\.){3,3}(25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])|([0-9a-fA-F]{1,4}:){1,4}:((25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\.){3,3}(25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9]))$/;
const ipv6CidrRegex = /^(([0-9a-fA-F]{1,4}:){7,7}[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,7}:|([0-9a-fA-F]{1,4}:){1,6}:[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,5}(:[0-9a-fA-F]{1,4}){1,2}|([0-9a-fA-F]{1,4}:){1,4}(:[0-9a-fA-F]{1,4}){1,3}|([0-9a-fA-F]{1,4}:){1,3}(:[0-9a-fA-F]{1,4}){1,4}|([0-9a-fA-F]{1,4}:){1,2}(:[0-9a-fA-F]{1,4}){1,5}|[0-9a-fA-F]{1,4}:((:[0-9a-fA-F]{1,4}){1,6})|:((:[0-9a-fA-F]{1,4}){1,7}|:)|fe80:(:[0-9a-fA-F]{0,4}){0,4}%[0-9a-zA-Z]{1,}|::(ffff(:0{1,4}){0,1}:){0,1}((25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\.){3,3}(25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])|([0-9a-fA-F]{1,4}:){1,4}:((25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\.){3,3}(25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9]))\/(12[0-8]|1[01][0-9]|[1-9]?[0-9])$/;
// https://stackoverflow.com/questions/7860392/determine-if-string-is-in-base64-using-javascript
const base64Regex = /^([0-9a-zA-Z+/]{4})*(([0-9a-zA-Z+/]{2}==)|([0-9a-zA-Z+/]{3}=))?$/;
// https://base64.guru/standards/base64url
const base64urlRegex = /^([0-9a-zA-Z-_]{4})*(([0-9a-zA-Z-_]{2}(==)?)|([0-9a-zA-Z-_]{3}(=)?))?$/;
// simple
// const dateRegexSource = `\\d{4}-\\d{2}-\\d{2}`;
// no leap year validation
// const dateRegexSource = `\\d{4}-((0[13578]|10|12)-31|(0[13-9]|1[0-2])-30|(0[1-9]|1[0-2])-(0[1-9]|1\\d|2\\d))`;
// with leap year validation
const dateRegexSource = `((\\d\\d[2468][048]|\\d\\d[13579][26]|\\d\\d0[48]|[02468][048]00|[13579][26]00)-02-29|\\d{4}-((0[13578]|1[02])-(0[1-9]|[12]\\d|3[01])|(0[469]|11)-(0[1-9]|[12]\\d|30)|(02)-(0[1-9]|1\\d|2[0-8])))`;
const dateRegex = new RegExp(`^${dateRegexSource}$`);
function timeRegexSource(args) {
    let secondsRegexSource = `[0-5]\\d`;
    if (args.precision) {
        secondsRegexSource = `${secondsRegexSource}\\.\\d{${args.precision}}`;
    }
    else if (args.precision == null) {
        secondsRegexSource = `${secondsRegexSource}(\\.\\d+)?`;
    }
    const secondsQuantifier = args.precision ? "+" : "?"; // require seconds if precision is nonzero
    return `([01]\\d|2[0-3]):[0-5]\\d(:${secondsRegexSource})${secondsQuantifier}`;
}
function timeRegex(args) {
    return new RegExp(`^${timeRegexSource(args)}$`);
}
// Adapted from https://stackoverflow.com/a/3143231
function datetimeRegex(args) {
    let regex = `${dateRegexSource}T${timeRegexSource(args)}`;
    const opts = [];
    opts.push(args.local ? `Z?` : `Z`);
    if (args.offset)
        opts.push(`([+-]\\d{2}:?\\d{2})`);
    regex = `${regex}(${opts.join("|")})`;
    return new RegExp(`^${regex}$`);
}
function isValidIP(ip, version) {
    if ((version === "v4" || !version) && ipv4Regex.test(ip)) {
        return true;
    }
    if ((version === "v6" || !version) && ipv6Regex.test(ip)) {
        return true;
    }
    return false;
}
function isValidJWT(jwt, alg) {
    if (!jwtRegex.test(jwt))
        return false;
    try {
        const [header] = jwt.split(".");
        // Convert base64url to base64
        const base64 = header
            .replace(/-/g, "+")
            .replace(/_/g, "/")
            .padEnd(header.length + ((4 - (header.length % 4)) % 4), "=");
        const decoded = JSON.parse(atob(base64));
        if (typeof decoded !== "object" || decoded === null)
            return false;
        if ("typ" in decoded && decoded?.typ !== "JWT")
            return false;
        if (!decoded.alg)
            return false;
        if (alg && decoded.alg !== alg)
            return false;
        return true;
    }
    catch {
        return false;
    }
}
function isValidCidr(ip, version) {
    if ((version === "v4" || !version) && ipv4CidrRegex.test(ip)) {
        return true;
    }
    if ((version === "v6" || !version) && ipv6CidrRegex.test(ip)) {
        return true;
    }
    return false;
}
class ZodString extends ZodType {
    _parse(input) {
        if (this._def.coerce) {
            input.data = String(input.data);
        }
        const parsedType = this._getType(input);
        if (parsedType !== _helpers_util_js__WEBPACK_IMPORTED_MODULE_4__.ZodParsedType.string) {
            const ctx = this._getOrReturnCtx(input);
            (0,_helpers_parseUtil_js__WEBPACK_IMPORTED_MODULE_3__.addIssueToContext)(ctx, {
                code: _ZodError_js__WEBPACK_IMPORTED_MODULE_0__.ZodIssueCode.invalid_type,
                expected: _helpers_util_js__WEBPACK_IMPORTED_MODULE_4__.ZodParsedType.string,
                received: ctx.parsedType,
            });
            return _helpers_parseUtil_js__WEBPACK_IMPORTED_MODULE_3__.INVALID;
        }
        const status = new _helpers_parseUtil_js__WEBPACK_IMPORTED_MODULE_3__.ParseStatus();
        let ctx = undefined;
        for (const check of this._def.checks) {
            if (check.kind === "min") {
                if (input.data.length < check.value) {
                    ctx = this._getOrReturnCtx(input, ctx);
                    (0,_helpers_parseUtil_js__WEBPACK_IMPORTED_MODULE_3__.addIssueToContext)(ctx, {
                        code: _ZodError_js__WEBPACK_IMPORTED_MODULE_0__.ZodIssueCode.too_small,
                        minimum: check.value,
                        type: "string",
                        inclusive: true,
                        exact: false,
                        message: check.message,
                    });
                    status.dirty();
                }
            }
            else if (check.kind === "max") {
                if (input.data.length > check.value) {
                    ctx = this._getOrReturnCtx(input, ctx);
                    (0,_helpers_parseUtil_js__WEBPACK_IMPORTED_MODULE_3__.addIssueToContext)(ctx, {
                        code: _ZodError_js__WEBPACK_IMPORTED_MODULE_0__.ZodIssueCode.too_big,
                        maximum: check.value,
                        type: "string",
                        inclusive: true,
                        exact: false,
                        message: check.message,
                    });
                    status.dirty();
                }
            }
            else if (check.kind === "length") {
                const tooBig = input.data.length > check.value;
                const tooSmall = input.data.length < check.value;
                if (tooBig || tooSmall) {
                    ctx = this._getOrReturnCtx(input, ctx);
                    if (tooBig) {
                        (0,_helpers_parseUtil_js__WEBPACK_IMPORTED_MODULE_3__.addIssueToContext)(ctx, {
                            code: _ZodError_js__WEBPACK_IMPORTED_MODULE_0__.ZodIssueCode.too_big,
                            maximum: check.value,
                            type: "string",
                            inclusive: true,
                            exact: true,
                            message: check.message,
                        });
                    }
                    else if (tooSmall) {
                        (0,_helpers_parseUtil_js__WEBPACK_IMPORTED_MODULE_3__.addIssueToContext)(ctx, {
                            code: _ZodError_js__WEBPACK_IMPORTED_MODULE_0__.ZodIssueCode.too_small,
                            minimum: check.value,
                            type: "string",
                            inclusive: true,
                            exact: true,
                            message: check.message,
                        });
                    }
                    status.dirty();
                }
            }
            else if (check.kind === "email") {
                if (!emailRegex.test(input.data)) {
                    ctx = this._getOrReturnCtx(input, ctx);
                    (0,_helpers_parseUtil_js__WEBPACK_IMPORTED_MODULE_3__.addIssueToContext)(ctx, {
                        validation: "email",
                        code: _ZodError_js__WEBPACK_IMPORTED_MODULE_0__.ZodIssueCode.invalid_string,
                        message: check.message,
                    });
                    status.dirty();
                }
            }
            else if (check.kind === "emoji") {
                if (!emojiRegex) {
                    emojiRegex = new RegExp(_emojiRegex, "u");
                }
                if (!emojiRegex.test(input.data)) {
                    ctx = this._getOrReturnCtx(input, ctx);
                    (0,_helpers_parseUtil_js__WEBPACK_IMPORTED_MODULE_3__.addIssueToContext)(ctx, {
                        validation: "emoji",
                        code: _ZodError_js__WEBPACK_IMPORTED_MODULE_0__.ZodIssueCode.invalid_string,
                        message: check.message,
                    });
                    status.dirty();
                }
            }
            else if (check.kind === "uuid") {
                if (!uuidRegex.test(input.data)) {
                    ctx = this._getOrReturnCtx(input, ctx);
                    (0,_helpers_parseUtil_js__WEBPACK_IMPORTED_MODULE_3__.addIssueToContext)(ctx, {
                        validation: "uuid",
                        code: _ZodError_js__WEBPACK_IMPORTED_MODULE_0__.ZodIssueCode.invalid_string,
                        message: check.message,
                    });
                    status.dirty();
                }
            }
            else if (check.kind === "nanoid") {
                if (!nanoidRegex.test(input.data)) {
                    ctx = this._getOrReturnCtx(input, ctx);
                    (0,_helpers_parseUtil_js__WEBPACK_IMPORTED_MODULE_3__.addIssueToContext)(ctx, {
                        validation: "nanoid",
                        code: _ZodError_js__WEBPACK_IMPORTED_MODULE_0__.ZodIssueCode.invalid_string,
                        message: check.message,
                    });
                    status.dirty();
                }
            }
            else if (check.kind === "cuid") {
                if (!cuidRegex.test(input.data)) {
                    ctx = this._getOrReturnCtx(input, ctx);
                    (0,_helpers_parseUtil_js__WEBPACK_IMPORTED_MODULE_3__.addIssueToContext)(ctx, {
                        validation: "cuid",
                        code: _ZodError_js__WEBPACK_IMPORTED_MODULE_0__.ZodIssueCode.invalid_string,
                        message: check.message,
                    });
                    status.dirty();
                }
            }
            else if (check.kind === "cuid2") {
                if (!cuid2Regex.test(input.data)) {
                    ctx = this._getOrReturnCtx(input, ctx);
                    (0,_helpers_parseUtil_js__WEBPACK_IMPORTED_MODULE_3__.addIssueToContext)(ctx, {
                        validation: "cuid2",
                        code: _ZodError_js__WEBPACK_IMPORTED_MODULE_0__.ZodIssueCode.invalid_string,
                        message: check.message,
                    });
                    status.dirty();
                }
            }
            else if (check.kind === "ulid") {
                if (!ulidRegex.test(input.data)) {
                    ctx = this._getOrReturnCtx(input, ctx);
                    (0,_helpers_parseUtil_js__WEBPACK_IMPORTED_MODULE_3__.addIssueToContext)(ctx, {
                        validation: "ulid",
                        code: _ZodError_js__WEBPACK_IMPORTED_MODULE_0__.ZodIssueCode.invalid_string,
                        message: check.message,
                    });
                    status.dirty();
                }
            }
            else if (check.kind === "url") {
                try {
                    new URL(input.data);
                }
                catch {
                    ctx = this._getOrReturnCtx(input, ctx);
                    (0,_helpers_parseUtil_js__WEBPACK_IMPORTED_MODULE_3__.addIssueToContext)(ctx, {
                        validation: "url",
                        code: _ZodError_js__WEBPACK_IMPORTED_MODULE_0__.ZodIssueCode.invalid_string,
                        message: check.message,
                    });
                    status.dirty();
                }
            }
            else if (check.kind === "regex") {
                check.regex.lastIndex = 0;
                const testResult = check.regex.test(input.data);
                if (!testResult) {
                    ctx = this._getOrReturnCtx(input, ctx);
                    (0,_helpers_parseUtil_js__WEBPACK_IMPORTED_MODULE_3__.addIssueToContext)(ctx, {
                        validation: "regex",
                        code: _ZodError_js__WEBPACK_IMPORTED_MODULE_0__.ZodIssueCode.invalid_string,
                        message: check.message,
                    });
                    status.dirty();
                }
            }
            else if (check.kind === "trim") {
                input.data = input.data.trim();
            }
            else if (check.kind === "includes") {
                if (!input.data.includes(check.value, check.position)) {
                    ctx = this._getOrReturnCtx(input, ctx);
                    (0,_helpers_parseUtil_js__WEBPACK_IMPORTED_MODULE_3__.addIssueToContext)(ctx, {
                        code: _ZodError_js__WEBPACK_IMPORTED_MODULE_0__.ZodIssueCode.invalid_string,
                        validation: { includes: check.value, position: check.position },
                        message: check.message,
                    });
                    status.dirty();
                }
            }
            else if (check.kind === "toLowerCase") {
                input.data = input.data.toLowerCase();
            }
            else if (check.kind === "toUpperCase") {
                input.data = input.data.toUpperCase();
            }
            else if (check.kind === "startsWith") {
                if (!input.data.startsWith(check.value)) {
                    ctx = this._getOrReturnCtx(input, ctx);
                    (0,_helpers_parseUtil_js__WEBPACK_IMPORTED_MODULE_3__.addIssueToContext)(ctx, {
                        code: _ZodError_js__WEBPACK_IMPORTED_MODULE_0__.ZodIssueCode.invalid_string,
                        validation: { startsWith: check.value },
                        message: check.message,
                    });
                    status.dirty();
                }
            }
            else if (check.kind === "endsWith") {
                if (!input.data.endsWith(check.value)) {
                    ctx = this._getOrReturnCtx(input, ctx);
                    (0,_helpers_parseUtil_js__WEBPACK_IMPORTED_MODULE_3__.addIssueToContext)(ctx, {
                        code: _ZodError_js__WEBPACK_IMPORTED_MODULE_0__.ZodIssueCode.invalid_string,
                        validation: { endsWith: check.value },
                        message: check.message,
                    });
                    status.dirty();
                }
            }
            else if (check.kind === "datetime") {
                const regex = datetimeRegex(check);
                if (!regex.test(input.data)) {
                    ctx = this._getOrReturnCtx(input, ctx);
                    (0,_helpers_parseUtil_js__WEBPACK_IMPORTED_MODULE_3__.addIssueToContext)(ctx, {
                        code: _ZodError_js__WEBPACK_IMPORTED_MODULE_0__.ZodIssueCode.invalid_string,
                        validation: "datetime",
                        message: check.message,
                    });
                    status.dirty();
                }
            }
            else if (check.kind === "date") {
                const regex = dateRegex;
                if (!regex.test(input.data)) {
                    ctx = this._getOrReturnCtx(input, ctx);
                    (0,_helpers_parseUtil_js__WEBPACK_IMPORTED_MODULE_3__.addIssueToContext)(ctx, {
                        code: _ZodError_js__WEBPACK_IMPORTED_MODULE_0__.ZodIssueCode.invalid_string,
                        validation: "date",
                        message: check.message,
                    });
                    status.dirty();
                }
            }
            else if (check.kind === "time") {
                const regex = timeRegex(check);
                if (!regex.test(input.data)) {
                    ctx = this._getOrReturnCtx(input, ctx);
                    (0,_helpers_parseUtil_js__WEBPACK_IMPORTED_MODULE_3__.addIssueToContext)(ctx, {
                        code: _ZodError_js__WEBPACK_IMPORTED_MODULE_0__.ZodIssueCode.invalid_string,
                        validation: "time",
                        message: check.message,
                    });
                    status.dirty();
                }
            }
            else if (check.kind === "duration") {
                if (!durationRegex.test(input.data)) {
                    ctx = this._getOrReturnCtx(input, ctx);
                    (0,_helpers_parseUtil_js__WEBPACK_IMPORTED_MODULE_3__.addIssueToContext)(ctx, {
                        validation: "duration",
                        code: _ZodError_js__WEBPACK_IMPORTED_MODULE_0__.ZodIssueCode.invalid_string,
                        message: check.message,
                    });
                    status.dirty();
                }
            }
            else if (check.kind === "ip") {
                if (!isValidIP(input.data, check.version)) {
                    ctx = this._getOrReturnCtx(input, ctx);
                    (0,_helpers_parseUtil_js__WEBPACK_IMPORTED_MODULE_3__.addIssueToContext)(ctx, {
                        validation: "ip",
                        code: _ZodError_js__WEBPACK_IMPORTED_MODULE_0__.ZodIssueCode.invalid_string,
                        message: check.message,
                    });
                    status.dirty();
                }
            }
            else if (check.kind === "jwt") {
                if (!isValidJWT(input.data, check.alg)) {
                    ctx = this._getOrReturnCtx(input, ctx);
                    (0,_helpers_parseUtil_js__WEBPACK_IMPORTED_MODULE_3__.addIssueToContext)(ctx, {
                        validation: "jwt",
                        code: _ZodError_js__WEBPACK_IMPORTED_MODULE_0__.ZodIssueCode.invalid_string,
                        message: check.message,
                    });
                    status.dirty();
                }
            }
            else if (check.kind === "cidr") {
                if (!isValidCidr(input.data, check.version)) {
                    ctx = this._getOrReturnCtx(input, ctx);
                    (0,_helpers_parseUtil_js__WEBPACK_IMPORTED_MODULE_3__.addIssueToContext)(ctx, {
                        validation: "cidr",
                        code: _ZodError_js__WEBPACK_IMPORTED_MODULE_0__.ZodIssueCode.invalid_string,
                        message: check.message,
                    });
                    status.dirty();
                }
            }
            else if (check.kind === "base64") {
                if (!base64Regex.test(input.data)) {
                    ctx = this._getOrReturnCtx(input, ctx);
                    (0,_helpers_parseUtil_js__WEBPACK_IMPORTED_MODULE_3__.addIssueToContext)(ctx, {
                        validation: "base64",
                        code: _ZodError_js__WEBPACK_IMPORTED_MODULE_0__.ZodIssueCode.invalid_string,
                        message: check.message,
                    });
                    status.dirty();
                }
            }
            else if (check.kind === "base64url") {
                if (!base64urlRegex.test(input.data)) {
                    ctx = this._getOrReturnCtx(input, ctx);
                    (0,_helpers_parseUtil_js__WEBPACK_IMPORTED_MODULE_3__.addIssueToContext)(ctx, {
                        validation: "base64url",
                        code: _ZodError_js__WEBPACK_IMPORTED_MODULE_0__.ZodIssueCode.invalid_string,
                        message: check.message,
                    });
                    status.dirty();
                }
            }
            else {
                _helpers_util_js__WEBPACK_IMPORTED_MODULE_4__.util.assertNever(check);
            }
        }
        return { status: status.value, value: input.data };
    }
    _regex(regex, validation, message) {
        return this.refinement((data) => regex.test(data), {
            validation,
            code: _ZodError_js__WEBPACK_IMPORTED_MODULE_0__.ZodIssueCode.invalid_string,
            ..._helpers_errorUtil_js__WEBPACK_IMPORTED_MODULE_2__.errorUtil.errToObj(message),
        });
    }
    _addCheck(check) {
        return new ZodString({
            ...this._def,
            checks: [...this._def.checks, check],
        });
    }
    email(message) {
        return this._addCheck({ kind: "email", ..._helpers_errorUtil_js__WEBPACK_IMPORTED_MODULE_2__.errorUtil.errToObj(message) });
    }
    url(message) {
        return this._addCheck({ kind: "url", ..._helpers_errorUtil_js__WEBPACK_IMPORTED_MODULE_2__.errorUtil.errToObj(message) });
    }
    emoji(message) {
        return this._addCheck({ kind: "emoji", ..._helpers_errorUtil_js__WEBPACK_IMPORTED_MODULE_2__.errorUtil.errToObj(message) });
    }
    uuid(message) {
        return this._addCheck({ kind: "uuid", ..._helpers_errorUtil_js__WEBPACK_IMPORTED_MODULE_2__.errorUtil.errToObj(message) });
    }
    nanoid(message) {
        return this._addCheck({ kind: "nanoid", ..._helpers_errorUtil_js__WEBPACK_IMPORTED_MODULE_2__.errorUtil.errToObj(message) });
    }
    cuid(message) {
        return this._addCheck({ kind: "cuid", ..._helpers_errorUtil_js__WEBPACK_IMPORTED_MODULE_2__.errorUtil.errToObj(message) });
    }
    cuid2(message) {
        return this._addCheck({ kind: "cuid2", ..._helpers_errorUtil_js__WEBPACK_IMPORTED_MODULE_2__.errorUtil.errToObj(message) });
    }
    ulid(message) {
        return this._addCheck({ kind: "ulid", ..._helpers_errorUtil_js__WEBPACK_IMPORTED_MODULE_2__.errorUtil.errToObj(message) });
    }
    base64(message) {
        return this._addCheck({ kind: "base64", ..._helpers_errorUtil_js__WEBPACK_IMPORTED_MODULE_2__.errorUtil.errToObj(message) });
    }
    base64url(message) {
        // base64url encoding is a modification of base64 that can safely be used in URLs and filenames
        return this._addCheck({
            kind: "base64url",
            ..._helpers_errorUtil_js__WEBPACK_IMPORTED_MODULE_2__.errorUtil.errToObj(message),
        });
    }
    jwt(options) {
        return this._addCheck({ kind: "jwt", ..._helpers_errorUtil_js__WEBPACK_IMPORTED_MODULE_2__.errorUtil.errToObj(options) });
    }
    ip(options) {
        return this._addCheck({ kind: "ip", ..._helpers_errorUtil_js__WEBPACK_IMPORTED_MODULE_2__.errorUtil.errToObj(options) });
    }
    cidr(options) {
        return this._addCheck({ kind: "cidr", ..._helpers_errorUtil_js__WEBPACK_IMPORTED_MODULE_2__.errorUtil.errToObj(options) });
    }
    datetime(options) {
        if (typeof options === "string") {
            return this._addCheck({
                kind: "datetime",
                precision: null,
                offset: false,
                local: false,
                message: options,
            });
        }
        return this._addCheck({
            kind: "datetime",
            precision: typeof options?.precision === "undefined" ? null : options?.precision,
            offset: options?.offset ?? false,
            local: options?.local ?? false,
            ..._helpers_errorUtil_js__WEBPACK_IMPORTED_MODULE_2__.errorUtil.errToObj(options?.message),
        });
    }
    date(message) {
        return this._addCheck({ kind: "date", message });
    }
    time(options) {
        if (typeof options === "string") {
            return this._addCheck({
                kind: "time",
                precision: null,
                message: options,
            });
        }
        return this._addCheck({
            kind: "time",
            precision: typeof options?.precision === "undefined" ? null : options?.precision,
            ..._helpers_errorUtil_js__WEBPACK_IMPORTED_MODULE_2__.errorUtil.errToObj(options?.message),
        });
    }
    duration(message) {
        return this._addCheck({ kind: "duration", ..._helpers_errorUtil_js__WEBPACK_IMPORTED_MODULE_2__.errorUtil.errToObj(message) });
    }
    regex(regex, message) {
        return this._addCheck({
            kind: "regex",
            regex: regex,
            ..._helpers_errorUtil_js__WEBPACK_IMPORTED_MODULE_2__.errorUtil.errToObj(message),
        });
    }
    includes(value, options) {
        return this._addCheck({
            kind: "includes",
            value: value,
            position: options?.position,
            ..._helpers_errorUtil_js__WEBPACK_IMPORTED_MODULE_2__.errorUtil.errToObj(options?.message),
        });
    }
    startsWith(value, message) {
        return this._addCheck({
            kind: "startsWith",
            value: value,
            ..._helpers_errorUtil_js__WEBPACK_IMPORTED_MODULE_2__.errorUtil.errToObj(message),
        });
    }
    endsWith(value, message) {
        return this._addCheck({
            kind: "endsWith",
            value: value,
            ..._helpers_errorUtil_js__WEBPACK_IMPORTED_MODULE_2__.errorUtil.errToObj(message),
        });
    }
    min(minLength, message) {
        return this._addCheck({
            kind: "min",
            value: minLength,
            ..._helpers_errorUtil_js__WEBPACK_IMPORTED_MODULE_2__.errorUtil.errToObj(message),
        });
    }
    max(maxLength, message) {
        return this._addCheck({
            kind: "max",
            value: maxLength,
            ..._helpers_errorUtil_js__WEBPACK_IMPORTED_MODULE_2__.errorUtil.errToObj(message),
        });
    }
    length(len, message) {
        return this._addCheck({
            kind: "length",
            value: len,
            ..._helpers_errorUtil_js__WEBPACK_IMPORTED_MODULE_2__.errorUtil.errToObj(message),
        });
    }
    /**
     * Equivalent to `.min(1)`
     */
    nonempty(message) {
        return this.min(1, _helpers_errorUtil_js__WEBPACK_IMPORTED_MODULE_2__.errorUtil.errToObj(message));
    }
    trim() {
        return new ZodString({
            ...this._def,
            checks: [...this._def.checks, { kind: "trim" }],
        });
    }
    toLowerCase() {
        return new ZodString({
            ...this._def,
            checks: [...this._def.checks, { kind: "toLowerCase" }],
        });
    }
    toUpperCase() {
        return new ZodString({
            ...this._def,
            checks: [...this._def.checks, { kind: "toUpperCase" }],
        });
    }
    get isDatetime() {
        return !!this._def.checks.find((ch) => ch.kind === "datetime");
    }
    get isDate() {
        return !!this._def.checks.find((ch) => ch.kind === "date");
    }
    get isTime() {
        return !!this._def.checks.find((ch) => ch.kind === "time");
    }
    get isDuration() {
        return !!this._def.checks.find((ch) => ch.kind === "duration");
    }
    get isEmail() {
        return !!this._def.checks.find((ch) => ch.kind === "email");
    }
    get isURL() {
        return !!this._def.checks.find((ch) => ch.kind === "url");
    }
    get isEmoji() {
        return !!this._def.checks.find((ch) => ch.kind === "emoji");
    }
    get isUUID() {
        return !!this._def.checks.find((ch) => ch.kind === "uuid");
    }
    get isNANOID() {
        return !!this._def.checks.find((ch) => ch.kind === "nanoid");
    }
    get isCUID() {
        return !!this._def.checks.find((ch) => ch.kind === "cuid");
    }
    get isCUID2() {
        return !!this._def.checks.find((ch) => ch.kind === "cuid2");
    }
    get isULID() {
        return !!this._def.checks.find((ch) => ch.kind === "ulid");
    }
    get isIP() {
        return !!this._def.checks.find((ch) => ch.kind === "ip");
    }
    get isCIDR() {
        return !!this._def.checks.find((ch) => ch.kind === "cidr");
    }
    get isBase64() {
        return !!this._def.checks.find((ch) => ch.kind === "base64");
    }
    get isBase64url() {
        // base64url encoding is a modification of base64 that can safely be used in URLs and filenames
        return !!this._def.checks.find((ch) => ch.kind === "base64url");
    }
    get minLength() {
        let min = null;
        for (const ch of this._def.checks) {
            if (ch.kind === "min") {
                if (min === null || ch.value > min)
                    min = ch.value;
            }
        }
        return min;
    }
    get maxLength() {
        let max = null;
        for (const ch of this._def.checks) {
            if (ch.kind === "max") {
                if (max === null || ch.value < max)
                    max = ch.value;
            }
        }
        return max;
    }
}
ZodString.create = (params) => {
    return new ZodString({
        checks: [],
        typeName: ZodFirstPartyTypeKind.ZodString,
        coerce: params?.coerce ?? false,
        ...processCreateParams(params),
    });
};
// https://stackoverflow.com/questions/3966484/why-does-modulus-operator-return-fractional-number-in-javascript/31711034#31711034
function floatSafeRemainder(val, step) {
    const valDecCount = (val.toString().split(".")[1] || "").length;
    const stepDecCount = (step.toString().split(".")[1] || "").length;
    const decCount = valDecCount > stepDecCount ? valDecCount : stepDecCount;
    const valInt = Number.parseInt(val.toFixed(decCount).replace(".", ""));
    const stepInt = Number.parseInt(step.toFixed(decCount).replace(".", ""));
    return (valInt % stepInt) / 10 ** decCount;
}
class ZodNumber extends ZodType {
    constructor() {
        super(...arguments);
        this.min = this.gte;
        this.max = this.lte;
        this.step = this.multipleOf;
    }
    _parse(input) {
        if (this._def.coerce) {
            input.data = Number(input.data);
        }
        const parsedType = this._getType(input);
        if (parsedType !== _helpers_util_js__WEBPACK_IMPORTED_MODULE_4__.ZodParsedType.number) {
            const ctx = this._getOrReturnCtx(input);
            (0,_helpers_parseUtil_js__WEBPACK_IMPORTED_MODULE_3__.addIssueToContext)(ctx, {
                code: _ZodError_js__WEBPACK_IMPORTED_MODULE_0__.ZodIssueCode.invalid_type,
                expected: _helpers_util_js__WEBPACK_IMPORTED_MODULE_4__.ZodParsedType.number,
                received: ctx.parsedType,
            });
            return _helpers_parseUtil_js__WEBPACK_IMPORTED_MODULE_3__.INVALID;
        }
        let ctx = undefined;
        const status = new _helpers_parseUtil_js__WEBPACK_IMPORTED_MODULE_3__.ParseStatus();
        for (const check of this._def.checks) {
            if (check.kind === "int") {
                if (!_helpers_util_js__WEBPACK_IMPORTED_MODULE_4__.util.isInteger(input.data)) {
                    ctx = this._getOrReturnCtx(input, ctx);
                    (0,_helpers_parseUtil_js__WEBPACK_IMPORTED_MODULE_3__.addIssueToContext)(ctx, {
                        code: _ZodError_js__WEBPACK_IMPORTED_MODULE_0__.ZodIssueCode.invalid_type,
                        expected: "integer",
                        received: "float",
                        message: check.message,
                    });
                    status.dirty();
                }
            }
            else if (check.kind === "min") {
                const tooSmall = check.inclusive ? input.data < check.value : input.data <= check.value;
                if (tooSmall) {
                    ctx = this._getOrReturnCtx(input, ctx);
                    (0,_helpers_parseUtil_js__WEBPACK_IMPORTED_MODULE_3__.addIssueToContext)(ctx, {
                        code: _ZodError_js__WEBPACK_IMPORTED_MODULE_0__.ZodIssueCode.too_small,
                        minimum: check.value,
                        type: "number",
                        inclusive: check.inclusive,
                        exact: false,
                        message: check.message,
                    });
                    status.dirty();
                }
            }
            else if (check.kind === "max") {
                const tooBig = check.inclusive ? input.data > check.value : input.data >= check.value;
                if (tooBig) {
                    ctx = this._getOrReturnCtx(input, ctx);
                    (0,_helpers_parseUtil_js__WEBPACK_IMPORTED_MODULE_3__.addIssueToContext)(ctx, {
                        code: _ZodError_js__WEBPACK_IMPORTED_MODULE_0__.ZodIssueCode.too_big,
                        maximum: check.value,
                        type: "number",
                        inclusive: check.inclusive,
                        exact: false,
                        message: check.message,
                    });
                    status.dirty();
                }
            }
            else if (check.kind === "multipleOf") {
                if (floatSafeRemainder(input.data, check.value) !== 0) {
                    ctx = this._getOrReturnCtx(input, ctx);
                    (0,_helpers_parseUtil_js__WEBPACK_IMPORTED_MODULE_3__.addIssueToContext)(ctx, {
                        code: _ZodError_js__WEBPACK_IMPORTED_MODULE_0__.ZodIssueCode.not_multiple_of,
                        multipleOf: check.value,
                        message: check.message,
                    });
                    status.dirty();
                }
            }
            else if (check.kind === "finite") {
                if (!Number.isFinite(input.data)) {
                    ctx = this._getOrReturnCtx(input, ctx);
                    (0,_helpers_parseUtil_js__WEBPACK_IMPORTED_MODULE_3__.addIssueToContext)(ctx, {
                        code: _ZodError_js__WEBPACK_IMPORTED_MODULE_0__.ZodIssueCode.not_finite,
                        message: check.message,
                    });
                    status.dirty();
                }
            }
            else {
                _helpers_util_js__WEBPACK_IMPORTED_MODULE_4__.util.assertNever(check);
            }
        }
        return { status: status.value, value: input.data };
    }
    gte(value, message) {
        return this.setLimit("min", value, true, _helpers_errorUtil_js__WEBPACK_IMPORTED_MODULE_2__.errorUtil.toString(message));
    }
    gt(value, message) {
        return this.setLimit("min", value, false, _helpers_errorUtil_js__WEBPACK_IMPORTED_MODULE_2__.errorUtil.toString(message));
    }
    lte(value, message) {
        return this.setLimit("max", value, true, _helpers_errorUtil_js__WEBPACK_IMPORTED_MODULE_2__.errorUtil.toString(message));
    }
    lt(value, message) {
        return this.setLimit("max", value, false, _helpers_errorUtil_js__WEBPACK_IMPORTED_MODULE_2__.errorUtil.toString(message));
    }
    setLimit(kind, value, inclusive, message) {
        return new ZodNumber({
            ...this._def,
            checks: [
                ...this._def.checks,
                {
                    kind,
                    value,
                    inclusive,
                    message: _helpers_errorUtil_js__WEBPACK_IMPORTED_MODULE_2__.errorUtil.toString(message),
                },
            ],
        });
    }
    _addCheck(check) {
        return new ZodNumber({
            ...this._def,
            checks: [...this._def.checks, check],
        });
    }
    int(message) {
        return this._addCheck({
            kind: "int",
            message: _helpers_errorUtil_js__WEBPACK_IMPORTED_MODULE_2__.errorUtil.toString(message),
        });
    }
    positive(message) {
        return this._addCheck({
            kind: "min",
            value: 0,
            inclusive: false,
            message: _helpers_errorUtil_js__WEBPACK_IMPORTED_MODULE_2__.errorUtil.toString(message),
        });
    }
    negative(message) {
        return this._addCheck({
            kind: "max",
            value: 0,
            inclusive: false,
            message: _helpers_errorUtil_js__WEBPACK_IMPORTED_MODULE_2__.errorUtil.toString(message),
        });
    }
    nonpositive(message) {
        return this._addCheck({
            kind: "max",
            value: 0,
            inclusive: true,
            message: _helpers_errorUtil_js__WEBPACK_IMPORTED_MODULE_2__.errorUtil.toString(message),
        });
    }
    nonnegative(message) {
        return this._addCheck({
            kind: "min",
            value: 0,
            inclusive: true,
            message: _helpers_errorUtil_js__WEBPACK_IMPORTED_MODULE_2__.errorUtil.toString(message),
        });
    }
    multipleOf(value, message) {
        return this._addCheck({
            kind: "multipleOf",
            value: value,
            message: _helpers_errorUtil_js__WEBPACK_IMPORTED_MODULE_2__.errorUtil.toString(message),
        });
    }
    finite(message) {
        return this._addCheck({
            kind: "finite",
            message: _helpers_errorUtil_js__WEBPACK_IMPORTED_MODULE_2__.errorUtil.toString(message),
        });
    }
    safe(message) {
        return this._addCheck({
            kind: "min",
            inclusive: true,
            value: Number.MIN_SAFE_INTEGER,
            message: _helpers_errorUtil_js__WEBPACK_IMPORTED_MODULE_2__.errorUtil.toString(message),
        })._addCheck({
            kind: "max",
            inclusive: true,
            value: Number.MAX_SAFE_INTEGER,
            message: _helpers_errorUtil_js__WEBPACK_IMPORTED_MODULE_2__.errorUtil.toString(message),
        });
    }
    get minValue() {
        let min = null;
        for (const ch of this._def.checks) {
            if (ch.kind === "min") {
                if (min === null || ch.value > min)
                    min = ch.value;
            }
        }
        return min;
    }
    get maxValue() {
        let max = null;
        for (const ch of this._def.checks) {
            if (ch.kind === "max") {
                if (max === null || ch.value < max)
                    max = ch.value;
            }
        }
        return max;
    }
    get isInt() {
        return !!this._def.checks.find((ch) => ch.kind === "int" || (ch.kind === "multipleOf" && _helpers_util_js__WEBPACK_IMPORTED_MODULE_4__.util.isInteger(ch.value)));
    }
    get isFinite() {
        let max = null;
        let min = null;
        for (const ch of this._def.checks) {
            if (ch.kind === "finite" || ch.kind === "int" || ch.kind === "multipleOf") {
                return true;
            }
            else if (ch.kind === "min") {
                if (min === null || ch.value > min)
                    min = ch.value;
            }
            else if (ch.kind === "max") {
                if (max === null || ch.value < max)
                    max = ch.value;
            }
        }
        return Number.isFinite(min) && Number.isFinite(max);
    }
}
ZodNumber.create = (params) => {
    return new ZodNumber({
        checks: [],
        typeName: ZodFirstPartyTypeKind.ZodNumber,
        coerce: params?.coerce || false,
        ...processCreateParams(params),
    });
};
class ZodBigInt extends ZodType {
    constructor() {
        super(...arguments);
        this.min = this.gte;
        this.max = this.lte;
    }
    _parse(input) {
        if (this._def.coerce) {
            try {
                input.data = BigInt(input.data);
            }
            catch {
                return this._getInvalidInput(input);
            }
        }
        const parsedType = this._getType(input);
        if (parsedType !== _helpers_util_js__WEBPACK_IMPORTED_MODULE_4__.ZodParsedType.bigint) {
            return this._getInvalidInput(input);
        }
        let ctx = undefined;
        const status = new _helpers_parseUtil_js__WEBPACK_IMPORTED_MODULE_3__.ParseStatus();
        for (const check of this._def.checks) {
            if (check.kind === "min") {
                const tooSmall = check.inclusive ? input.data < check.value : input.data <= check.value;
                if (tooSmall) {
                    ctx = this._getOrReturnCtx(input, ctx);
                    (0,_helpers_parseUtil_js__WEBPACK_IMPORTED_MODULE_3__.addIssueToContext)(ctx, {
                        code: _ZodError_js__WEBPACK_IMPORTED_MODULE_0__.ZodIssueCode.too_small,
                        type: "bigint",
                        minimum: check.value,
                        inclusive: check.inclusive,
                        message: check.message,
                    });
                    status.dirty();
                }
            }
            else if (check.kind === "max") {
                const tooBig = check.inclusive ? input.data > check.value : input.data >= check.value;
                if (tooBig) {
                    ctx = this._getOrReturnCtx(input, ctx);
                    (0,_helpers_parseUtil_js__WEBPACK_IMPORTED_MODULE_3__.addIssueToContext)(ctx, {
                        code: _ZodError_js__WEBPACK_IMPORTED_MODULE_0__.ZodIssueCode.too_big,
                        type: "bigint",
                        maximum: check.value,
                        inclusive: check.inclusive,
                        message: check.message,
                    });
                    status.dirty();
                }
            }
            else if (check.kind === "multipleOf") {
                if (input.data % check.value !== BigInt(0)) {
                    ctx = this._getOrReturnCtx(input, ctx);
                    (0,_helpers_parseUtil_js__WEBPACK_IMPORTED_MODULE_3__.addIssueToContext)(ctx, {
                        code: _ZodError_js__WEBPACK_IMPORTED_MODULE_0__.ZodIssueCode.not_multiple_of,
                        multipleOf: check.value,
                        message: check.message,
                    });
                    status.dirty();
                }
            }
            else {
                _helpers_util_js__WEBPACK_IMPORTED_MODULE_4__.util.assertNever(check);
            }
        }
        return { status: status.value, value: input.data };
    }
    _getInvalidInput(input) {
        const ctx = this._getOrReturnCtx(input);
        (0,_helpers_parseUtil_js__WEBPACK_IMPORTED_MODULE_3__.addIssueToContext)(ctx, {
            code: _ZodError_js__WEBPACK_IMPORTED_MODULE_0__.ZodIssueCode.invalid_type,
            expected: _helpers_util_js__WEBPACK_IMPORTED_MODULE_4__.ZodParsedType.bigint,
            received: ctx.parsedType,
        });
        return _helpers_parseUtil_js__WEBPACK_IMPORTED_MODULE_3__.INVALID;
    }
    gte(value, message) {
        return this.setLimit("min", value, true, _helpers_errorUtil_js__WEBPACK_IMPORTED_MODULE_2__.errorUtil.toString(message));
    }
    gt(value, message) {
        return this.setLimit("min", value, false, _helpers_errorUtil_js__WEBPACK_IMPORTED_MODULE_2__.errorUtil.toString(message));
    }
    lte(value, message) {
        return this.setLimit("max", value, true, _helpers_errorUtil_js__WEBPACK_IMPORTED_MODULE_2__.errorUtil.toString(message));
    }
    lt(value, message) {
        return this.setLimit("max", value, false, _helpers_errorUtil_js__WEBPACK_IMPORTED_MODULE_2__.errorUtil.toString(message));
    }
    setLimit(kind, value, inclusive, message) {
        return new ZodBigInt({
            ...this._def,
            checks: [
                ...this._def.checks,
                {
                    kind,
                    value,
                    inclusive,
                    message: _helpers_errorUtil_js__WEBPACK_IMPORTED_MODULE_2__.errorUtil.toString(message),
                },
            ],
        });
    }
    _addCheck(check) {
        return new ZodBigInt({
            ...this._def,
            checks: [...this._def.checks, check],
        });
    }
    positive(message) {
        return this._addCheck({
            kind: "min",
            value: BigInt(0),
            inclusive: false,
            message: _helpers_errorUtil_js__WEBPACK_IMPORTED_MODULE_2__.errorUtil.toString(message),
        });
    }
    negative(message) {
        return this._addCheck({
            kind: "max",
            value: BigInt(0),
            inclusive: false,
            message: _helpers_errorUtil_js__WEBPACK_IMPORTED_MODULE_2__.errorUtil.toString(message),
        });
    }
    nonpositive(message) {
        return this._addCheck({
            kind: "max",
            value: BigInt(0),
            inclusive: true,
            message: _helpers_errorUtil_js__WEBPACK_IMPORTED_MODULE_2__.errorUtil.toString(message),
        });
    }
    nonnegative(message) {
        return this._addCheck({
            kind: "min",
            value: BigInt(0),
            inclusive: true,
            message: _helpers_errorUtil_js__WEBPACK_IMPORTED_MODULE_2__.errorUtil.toString(message),
        });
    }
    multipleOf(value, message) {
        return this._addCheck({
            kind: "multipleOf",
            value,
            message: _helpers_errorUtil_js__WEBPACK_IMPORTED_MODULE_2__.errorUtil.toString(message),
        });
    }
    get minValue() {
        let min = null;
        for (const ch of this._def.checks) {
            if (ch.kind === "min") {
                if (min === null || ch.value > min)
                    min = ch.value;
            }
        }
        return min;
    }
    get maxValue() {
        let max = null;
        for (const ch of this._def.checks) {
            if (ch.kind === "max") {
                if (max === null || ch.value < max)
                    max = ch.value;
            }
        }
        return max;
    }
}
ZodBigInt.create = (params) => {
    return new ZodBigInt({
        checks: [],
        typeName: ZodFirstPartyTypeKind.ZodBigInt,
        coerce: params?.coerce ?? false,
        ...processCreateParams(params),
    });
};
class ZodBoolean extends ZodType {
    _parse(input) {
        if (this._def.coerce) {
            input.data = Boolean(input.data);
        }
        const parsedType = this._getType(input);
        if (parsedType !== _helpers_util_js__WEBPACK_IMPORTED_MODULE_4__.ZodParsedType.boolean) {
            const ctx = this._getOrReturnCtx(input);
            (0,_helpers_parseUtil_js__WEBPACK_IMPORTED_MODULE_3__.addIssueToContext)(ctx, {
                code: _ZodError_js__WEBPACK_IMPORTED_MODULE_0__.ZodIssueCode.invalid_type,
                expected: _helpers_util_js__WEBPACK_IMPORTED_MODULE_4__.ZodParsedType.boolean,
                received: ctx.parsedType,
            });
            return _helpers_parseUtil_js__WEBPACK_IMPORTED_MODULE_3__.INVALID;
        }
        return (0,_helpers_parseUtil_js__WEBPACK_IMPORTED_MODULE_3__.OK)(input.data);
    }
}
ZodBoolean.create = (params) => {
    return new ZodBoolean({
        typeName: ZodFirstPartyTypeKind.ZodBoolean,
        coerce: params?.coerce || false,
        ...processCreateParams(params),
    });
};
class ZodDate extends ZodType {
    _parse(input) {
        if (this._def.coerce) {
            input.data = new Date(input.data);
        }
        const parsedType = this._getType(input);
        if (parsedType !== _helpers_util_js__WEBPACK_IMPORTED_MODULE_4__.ZodParsedType.date) {
            const ctx = this._getOrReturnCtx(input);
            (0,_helpers_parseUtil_js__WEBPACK_IMPORTED_MODULE_3__.addIssueToContext)(ctx, {
                code: _ZodError_js__WEBPACK_IMPORTED_MODULE_0__.ZodIssueCode.invalid_type,
                expected: _helpers_util_js__WEBPACK_IMPORTED_MODULE_4__.ZodParsedType.date,
                received: ctx.parsedType,
            });
            return _helpers_parseUtil_js__WEBPACK_IMPORTED_MODULE_3__.INVALID;
        }
        if (Number.isNaN(input.data.getTime())) {
            const ctx = this._getOrReturnCtx(input);
            (0,_helpers_parseUtil_js__WEBPACK_IMPORTED_MODULE_3__.addIssueToContext)(ctx, {
                code: _ZodError_js__WEBPACK_IMPORTED_MODULE_0__.ZodIssueCode.invalid_date,
            });
            return _helpers_parseUtil_js__WEBPACK_IMPORTED_MODULE_3__.INVALID;
        }
        const status = new _helpers_parseUtil_js__WEBPACK_IMPORTED_MODULE_3__.ParseStatus();
        let ctx = undefined;
        for (const check of this._def.checks) {
            if (check.kind === "min") {
                if (input.data.getTime() < check.value) {
                    ctx = this._getOrReturnCtx(input, ctx);
                    (0,_helpers_parseUtil_js__WEBPACK_IMPORTED_MODULE_3__.addIssueToContext)(ctx, {
                        code: _ZodError_js__WEBPACK_IMPORTED_MODULE_0__.ZodIssueCode.too_small,
                        message: check.message,
                        inclusive: true,
                        exact: false,
                        minimum: check.value,
                        type: "date",
                    });
                    status.dirty();
                }
            }
            else if (check.kind === "max") {
                if (input.data.getTime() > check.value) {
                    ctx = this._getOrReturnCtx(input, ctx);
                    (0,_helpers_parseUtil_js__WEBPACK_IMPORTED_MODULE_3__.addIssueToContext)(ctx, {
                        code: _ZodError_js__WEBPACK_IMPORTED_MODULE_0__.ZodIssueCode.too_big,
                        message: check.message,
                        inclusive: true,
                        exact: false,
                        maximum: check.value,
                        type: "date",
                    });
                    status.dirty();
                }
            }
            else {
                _helpers_util_js__WEBPACK_IMPORTED_MODULE_4__.util.assertNever(check);
            }
        }
        return {
            status: status.value,
            value: new Date(input.data.getTime()),
        };
    }
    _addCheck(check) {
        return new ZodDate({
            ...this._def,
            checks: [...this._def.checks, check],
        });
    }
    min(minDate, message) {
        return this._addCheck({
            kind: "min",
            value: minDate.getTime(),
            message: _helpers_errorUtil_js__WEBPACK_IMPORTED_MODULE_2__.errorUtil.toString(message),
        });
    }
    max(maxDate, message) {
        return this._addCheck({
            kind: "max",
            value: maxDate.getTime(),
            message: _helpers_errorUtil_js__WEBPACK_IMPORTED_MODULE_2__.errorUtil.toString(message),
        });
    }
    get minDate() {
        let min = null;
        for (const ch of this._def.checks) {
            if (ch.kind === "min") {
                if (min === null || ch.value > min)
                    min = ch.value;
            }
        }
        return min != null ? new Date(min) : null;
    }
    get maxDate() {
        let max = null;
        for (const ch of this._def.checks) {
            if (ch.kind === "max") {
                if (max === null || ch.value < max)
                    max = ch.value;
            }
        }
        return max != null ? new Date(max) : null;
    }
}
ZodDate.create = (params) => {
    return new ZodDate({
        checks: [],
        coerce: params?.coerce || false,
        typeName: ZodFirstPartyTypeKind.ZodDate,
        ...processCreateParams(params),
    });
};
class ZodSymbol extends ZodType {
    _parse(input) {
        const parsedType = this._getType(input);
        if (parsedType !== _helpers_util_js__WEBPACK_IMPORTED_MODULE_4__.ZodParsedType.symbol) {
            const ctx = this._getOrReturnCtx(input);
            (0,_helpers_parseUtil_js__WEBPACK_IMPORTED_MODULE_3__.addIssueToContext)(ctx, {
                code: _ZodError_js__WEBPACK_IMPORTED_MODULE_0__.ZodIssueCode.invalid_type,
                expected: _helpers_util_js__WEBPACK_IMPORTED_MODULE_4__.ZodParsedType.symbol,
                received: ctx.parsedType,
            });
            return _helpers_parseUtil_js__WEBPACK_IMPORTED_MODULE_3__.INVALID;
        }
        return (0,_helpers_parseUtil_js__WEBPACK_IMPORTED_MODULE_3__.OK)(input.data);
    }
}
ZodSymbol.create = (params) => {
    return new ZodSymbol({
        typeName: ZodFirstPartyTypeKind.ZodSymbol,
        ...processCreateParams(params),
    });
};
class ZodUndefined extends ZodType {
    _parse(input) {
        const parsedType = this._getType(input);
        if (parsedType !== _helpers_util_js__WEBPACK_IMPORTED_MODULE_4__.ZodParsedType.undefined) {
            const ctx = this._getOrReturnCtx(input);
            (0,_helpers_parseUtil_js__WEBPACK_IMPORTED_MODULE_3__.addIssueToContext)(ctx, {
                code: _ZodError_js__WEBPACK_IMPORTED_MODULE_0__.ZodIssueCode.invalid_type,
                expected: _helpers_util_js__WEBPACK_IMPORTED_MODULE_4__.ZodParsedType.undefined,
                received: ctx.parsedType,
            });
            return _helpers_parseUtil_js__WEBPACK_IMPORTED_MODULE_3__.INVALID;
        }
        return (0,_helpers_parseUtil_js__WEBPACK_IMPORTED_MODULE_3__.OK)(input.data);
    }
}
ZodUndefined.create = (params) => {
    return new ZodUndefined({
        typeName: ZodFirstPartyTypeKind.ZodUndefined,
        ...processCreateParams(params),
    });
};
class ZodNull extends ZodType {
    _parse(input) {
        const parsedType = this._getType(input);
        if (parsedType !== _helpers_util_js__WEBPACK_IMPORTED_MODULE_4__.ZodParsedType.null) {
            const ctx = this._getOrReturnCtx(input);
            (0,_helpers_parseUtil_js__WEBPACK_IMPORTED_MODULE_3__.addIssueToContext)(ctx, {
                code: _ZodError_js__WEBPACK_IMPORTED_MODULE_0__.ZodIssueCode.invalid_type,
                expected: _helpers_util_js__WEBPACK_IMPORTED_MODULE_4__.ZodParsedType.null,
                received: ctx.parsedType,
            });
            return _helpers_parseUtil_js__WEBPACK_IMPORTED_MODULE_3__.INVALID;
        }
        return (0,_helpers_parseUtil_js__WEBPACK_IMPORTED_MODULE_3__.OK)(input.data);
    }
}
ZodNull.create = (params) => {
    return new ZodNull({
        typeName: ZodFirstPartyTypeKind.ZodNull,
        ...processCreateParams(params),
    });
};
class ZodAny extends ZodType {
    constructor() {
        super(...arguments);
        // to prevent instances of other classes from extending ZodAny. this causes issues with catchall in ZodObject.
        this._any = true;
    }
    _parse(input) {
        return (0,_helpers_parseUtil_js__WEBPACK_IMPORTED_MODULE_3__.OK)(input.data);
    }
}
ZodAny.create = (params) => {
    return new ZodAny({
        typeName: ZodFirstPartyTypeKind.ZodAny,
        ...processCreateParams(params),
    });
};
class ZodUnknown extends ZodType {
    constructor() {
        super(...arguments);
        // required
        this._unknown = true;
    }
    _parse(input) {
        return (0,_helpers_parseUtil_js__WEBPACK_IMPORTED_MODULE_3__.OK)(input.data);
    }
}
ZodUnknown.create = (params) => {
    return new ZodUnknown({
        typeName: ZodFirstPartyTypeKind.ZodUnknown,
        ...processCreateParams(params),
    });
};
class ZodNever extends ZodType {
    _parse(input) {
        const ctx = this._getOrReturnCtx(input);
        (0,_helpers_parseUtil_js__WEBPACK_IMPORTED_MODULE_3__.addIssueToContext)(ctx, {
            code: _ZodError_js__WEBPACK_IMPORTED_MODULE_0__.ZodIssueCode.invalid_type,
            expected: _helpers_util_js__WEBPACK_IMPORTED_MODULE_4__.ZodParsedType.never,
            received: ctx.parsedType,
        });
        return _helpers_parseUtil_js__WEBPACK_IMPORTED_MODULE_3__.INVALID;
    }
}
ZodNever.create = (params) => {
    return new ZodNever({
        typeName: ZodFirstPartyTypeKind.ZodNever,
        ...processCreateParams(params),
    });
};
class ZodVoid extends ZodType {
    _parse(input) {
        const parsedType = this._getType(input);
        if (parsedType !== _helpers_util_js__WEBPACK_IMPORTED_MODULE_4__.ZodParsedType.undefined) {
            const ctx = this._getOrReturnCtx(input);
            (0,_helpers_parseUtil_js__WEBPACK_IMPORTED_MODULE_3__.addIssueToContext)(ctx, {
                code: _ZodError_js__WEBPACK_IMPORTED_MODULE_0__.ZodIssueCode.invalid_type,
                expected: _helpers_util_js__WEBPACK_IMPORTED_MODULE_4__.ZodParsedType.void,
                received: ctx.parsedType,
            });
            return _helpers_parseUtil_js__WEBPACK_IMPORTED_MODULE_3__.INVALID;
        }
        return (0,_helpers_parseUtil_js__WEBPACK_IMPORTED_MODULE_3__.OK)(input.data);
    }
}
ZodVoid.create = (params) => {
    return new ZodVoid({
        typeName: ZodFirstPartyTypeKind.ZodVoid,
        ...processCreateParams(params),
    });
};
class ZodArray extends ZodType {
    _parse(input) {
        const { ctx, status } = this._processInputParams(input);
        const def = this._def;
        if (ctx.parsedType !== _helpers_util_js__WEBPACK_IMPORTED_MODULE_4__.ZodParsedType.array) {
            (0,_helpers_parseUtil_js__WEBPACK_IMPORTED_MODULE_3__.addIssueToContext)(ctx, {
                code: _ZodError_js__WEBPACK_IMPORTED_MODULE_0__.ZodIssueCode.invalid_type,
                expected: _helpers_util_js__WEBPACK_IMPORTED_MODULE_4__.ZodParsedType.array,
                received: ctx.parsedType,
            });
            return _helpers_parseUtil_js__WEBPACK_IMPORTED_MODULE_3__.INVALID;
        }
        if (def.exactLength !== null) {
            const tooBig = ctx.data.length > def.exactLength.value;
            const tooSmall = ctx.data.length < def.exactLength.value;
            if (tooBig || tooSmall) {
                (0,_helpers_parseUtil_js__WEBPACK_IMPORTED_MODULE_3__.addIssueToContext)(ctx, {
                    code: tooBig ? _ZodError_js__WEBPACK_IMPORTED_MODULE_0__.ZodIssueCode.too_big : _ZodError_js__WEBPACK_IMPORTED_MODULE_0__.ZodIssueCode.too_small,
                    minimum: (tooSmall ? def.exactLength.value : undefined),
                    maximum: (tooBig ? def.exactLength.value : undefined),
                    type: "array",
                    inclusive: true,
                    exact: true,
                    message: def.exactLength.message,
                });
                status.dirty();
            }
        }
        if (def.minLength !== null) {
            if (ctx.data.length < def.minLength.value) {
                (0,_helpers_parseUtil_js__WEBPACK_IMPORTED_MODULE_3__.addIssueToContext)(ctx, {
                    code: _ZodError_js__WEBPACK_IMPORTED_MODULE_0__.ZodIssueCode.too_small,
                    minimum: def.minLength.value,
                    type: "array",
                    inclusive: true,
                    exact: false,
                    message: def.minLength.message,
                });
                status.dirty();
            }
        }
        if (def.maxLength !== null) {
            if (ctx.data.length > def.maxLength.value) {
                (0,_helpers_parseUtil_js__WEBPACK_IMPORTED_MODULE_3__.addIssueToContext)(ctx, {
                    code: _ZodError_js__WEBPACK_IMPORTED_MODULE_0__.ZodIssueCode.too_big,
                    maximum: def.maxLength.value,
                    type: "array",
                    inclusive: true,
                    exact: false,
                    message: def.maxLength.message,
                });
                status.dirty();
            }
        }
        if (ctx.common.async) {
            return Promise.all([...ctx.data].map((item, i) => {
                return def.type._parseAsync(new ParseInputLazyPath(ctx, item, ctx.path, i));
            })).then((result) => {
                return _helpers_parseUtil_js__WEBPACK_IMPORTED_MODULE_3__.ParseStatus.mergeArray(status, result);
            });
        }
        const result = [...ctx.data].map((item, i) => {
            return def.type._parseSync(new ParseInputLazyPath(ctx, item, ctx.path, i));
        });
        return _helpers_parseUtil_js__WEBPACK_IMPORTED_MODULE_3__.ParseStatus.mergeArray(status, result);
    }
    get element() {
        return this._def.type;
    }
    min(minLength, message) {
        return new ZodArray({
            ...this._def,
            minLength: { value: minLength, message: _helpers_errorUtil_js__WEBPACK_IMPORTED_MODULE_2__.errorUtil.toString(message) },
        });
    }
    max(maxLength, message) {
        return new ZodArray({
            ...this._def,
            maxLength: { value: maxLength, message: _helpers_errorUtil_js__WEBPACK_IMPORTED_MODULE_2__.errorUtil.toString(message) },
        });
    }
    length(len, message) {
        return new ZodArray({
            ...this._def,
            exactLength: { value: len, message: _helpers_errorUtil_js__WEBPACK_IMPORTED_MODULE_2__.errorUtil.toString(message) },
        });
    }
    nonempty(message) {
        return this.min(1, message);
    }
}
ZodArray.create = (schema, params) => {
    return new ZodArray({
        type: schema,
        minLength: null,
        maxLength: null,
        exactLength: null,
        typeName: ZodFirstPartyTypeKind.ZodArray,
        ...processCreateParams(params),
    });
};
function deepPartialify(schema) {
    if (schema instanceof ZodObject) {
        const newShape = {};
        for (const key in schema.shape) {
            const fieldSchema = schema.shape[key];
            newShape[key] = ZodOptional.create(deepPartialify(fieldSchema));
        }
        return new ZodObject({
            ...schema._def,
            shape: () => newShape,
        });
    }
    else if (schema instanceof ZodArray) {
        return new ZodArray({
            ...schema._def,
            type: deepPartialify(schema.element),
        });
    }
    else if (schema instanceof ZodOptional) {
        return ZodOptional.create(deepPartialify(schema.unwrap()));
    }
    else if (schema instanceof ZodNullable) {
        return ZodNullable.create(deepPartialify(schema.unwrap()));
    }
    else if (schema instanceof ZodTuple) {
        return ZodTuple.create(schema.items.map((item) => deepPartialify(item)));
    }
    else {
        return schema;
    }
}
class ZodObject extends ZodType {
    constructor() {
        super(...arguments);
        this._cached = null;
        /**
         * @deprecated In most cases, this is no longer needed - unknown properties are now silently stripped.
         * If you want to pass through unknown properties, use `.passthrough()` instead.
         */
        this.nonstrict = this.passthrough;
        // extend<
        //   Augmentation extends ZodRawShape,
        //   NewOutput extends util.flatten<{
        //     [k in keyof Augmentation | keyof Output]: k extends keyof Augmentation
        //       ? Augmentation[k]["_output"]
        //       : k extends keyof Output
        //       ? Output[k]
        //       : never;
        //   }>,
        //   NewInput extends util.flatten<{
        //     [k in keyof Augmentation | keyof Input]: k extends keyof Augmentation
        //       ? Augmentation[k]["_input"]
        //       : k extends keyof Input
        //       ? Input[k]
        //       : never;
        //   }>
        // >(
        //   augmentation: Augmentation
        // ): ZodObject<
        //   extendShape<T, Augmentation>,
        //   UnknownKeys,
        //   Catchall,
        //   NewOutput,
        //   NewInput
        // > {
        //   return new ZodObject({
        //     ...this._def,
        //     shape: () => ({
        //       ...this._def.shape(),
        //       ...augmentation,
        //     }),
        //   }) as any;
        // }
        /**
         * @deprecated Use `.extend` instead
         *  */
        this.augment = this.extend;
    }
    _getCached() {
        if (this._cached !== null)
            return this._cached;
        const shape = this._def.shape();
        const keys = _helpers_util_js__WEBPACK_IMPORTED_MODULE_4__.util.objectKeys(shape);
        this._cached = { shape, keys };
        return this._cached;
    }
    _parse(input) {
        const parsedType = this._getType(input);
        if (parsedType !== _helpers_util_js__WEBPACK_IMPORTED_MODULE_4__.ZodParsedType.object) {
            const ctx = this._getOrReturnCtx(input);
            (0,_helpers_parseUtil_js__WEBPACK_IMPORTED_MODULE_3__.addIssueToContext)(ctx, {
                code: _ZodError_js__WEBPACK_IMPORTED_MODULE_0__.ZodIssueCode.invalid_type,
                expected: _helpers_util_js__WEBPACK_IMPORTED_MODULE_4__.ZodParsedType.object,
                received: ctx.parsedType,
            });
            return _helpers_parseUtil_js__WEBPACK_IMPORTED_MODULE_3__.INVALID;
        }
        const { status, ctx } = this._processInputParams(input);
        const { shape, keys: shapeKeys } = this._getCached();
        const extraKeys = [];
        if (!(this._def.catchall instanceof ZodNever && this._def.unknownKeys === "strip")) {
            for (const key in ctx.data) {
                if (!shapeKeys.includes(key)) {
                    extraKeys.push(key);
                }
            }
        }
        const pairs = [];
        for (const key of shapeKeys) {
            const keyValidator = shape[key];
            const value = ctx.data[key];
            pairs.push({
                key: { status: "valid", value: key },
                value: keyValidator._parse(new ParseInputLazyPath(ctx, value, ctx.path, key)),
                alwaysSet: key in ctx.data,
            });
        }
        if (this._def.catchall instanceof ZodNever) {
            const unknownKeys = this._def.unknownKeys;
            if (unknownKeys === "passthrough") {
                for (const key of extraKeys) {
                    pairs.push({
                        key: { status: "valid", value: key },
                        value: { status: "valid", value: ctx.data[key] },
                    });
                }
            }
            else if (unknownKeys === "strict") {
                if (extraKeys.length > 0) {
                    (0,_helpers_parseUtil_js__WEBPACK_IMPORTED_MODULE_3__.addIssueToContext)(ctx, {
                        code: _ZodError_js__WEBPACK_IMPORTED_MODULE_0__.ZodIssueCode.unrecognized_keys,
                        keys: extraKeys,
                    });
                    status.dirty();
                }
            }
            else if (unknownKeys === "strip") {
            }
            else {
                throw new Error(`Internal ZodObject error: invalid unknownKeys value.`);
            }
        }
        else {
            // run catchall validation
            const catchall = this._def.catchall;
            for (const key of extraKeys) {
                const value = ctx.data[key];
                pairs.push({
                    key: { status: "valid", value: key },
                    value: catchall._parse(new ParseInputLazyPath(ctx, value, ctx.path, key) //, ctx.child(key), value, getParsedType(value)
                    ),
                    alwaysSet: key in ctx.data,
                });
            }
        }
        if (ctx.common.async) {
            return Promise.resolve()
                .then(async () => {
                const syncPairs = [];
                for (const pair of pairs) {
                    const key = await pair.key;
                    const value = await pair.value;
                    syncPairs.push({
                        key,
                        value,
                        alwaysSet: pair.alwaysSet,
                    });
                }
                return syncPairs;
            })
                .then((syncPairs) => {
                return _helpers_parseUtil_js__WEBPACK_IMPORTED_MODULE_3__.ParseStatus.mergeObjectSync(status, syncPairs);
            });
        }
        else {
            return _helpers_parseUtil_js__WEBPACK_IMPORTED_MODULE_3__.ParseStatus.mergeObjectSync(status, pairs);
        }
    }
    get shape() {
        return this._def.shape();
    }
    strict(message) {
        _helpers_errorUtil_js__WEBPACK_IMPORTED_MODULE_2__.errorUtil.errToObj;
        return new ZodObject({
            ...this._def,
            unknownKeys: "strict",
            ...(message !== undefined
                ? {
                    errorMap: (issue, ctx) => {
                        const defaultError = this._def.errorMap?.(issue, ctx).message ?? ctx.defaultError;
                        if (issue.code === "unrecognized_keys")
                            return {
                                message: _helpers_errorUtil_js__WEBPACK_IMPORTED_MODULE_2__.errorUtil.errToObj(message).message ?? defaultError,
                            };
                        return {
                            message: defaultError,
                        };
                    },
                }
                : {}),
        });
    }
    strip() {
        return new ZodObject({
            ...this._def,
            unknownKeys: "strip",
        });
    }
    passthrough() {
        return new ZodObject({
            ...this._def,
            unknownKeys: "passthrough",
        });
    }
    // const AugmentFactory =
    //   <Def extends ZodObjectDef>(def: Def) =>
    //   <Augmentation extends ZodRawShape>(
    //     augmentation: Augmentation
    //   ): ZodObject<
    //     extendShape<ReturnType<Def["shape"]>, Augmentation>,
    //     Def["unknownKeys"],
    //     Def["catchall"]
    //   > => {
    //     return new ZodObject({
    //       ...def,
    //       shape: () => ({
    //         ...def.shape(),
    //         ...augmentation,
    //       }),
    //     }) as any;
    //   };
    extend(augmentation) {
        return new ZodObject({
            ...this._def,
            shape: () => ({
                ...this._def.shape(),
                ...augmentation,
            }),
        });
    }
    /**
     * Prior to zod@1.0.12 there was a bug in the
     * inferred type of merged objects. Please
     * upgrade if you are experiencing issues.
     */
    merge(merging) {
        const merged = new ZodObject({
            unknownKeys: merging._def.unknownKeys,
            catchall: merging._def.catchall,
            shape: () => ({
                ...this._def.shape(),
                ...merging._def.shape(),
            }),
            typeName: ZodFirstPartyTypeKind.ZodObject,
        });
        return merged;
    }
    // merge<
    //   Incoming extends AnyZodObject,
    //   Augmentation extends Incoming["shape"],
    //   NewOutput extends {
    //     [k in keyof Augmentation | keyof Output]: k extends keyof Augmentation
    //       ? Augmentation[k]["_output"]
    //       : k extends keyof Output
    //       ? Output[k]
    //       : never;
    //   },
    //   NewInput extends {
    //     [k in keyof Augmentation | keyof Input]: k extends keyof Augmentation
    //       ? Augmentation[k]["_input"]
    //       : k extends keyof Input
    //       ? Input[k]
    //       : never;
    //   }
    // >(
    //   merging: Incoming
    // ): ZodObject<
    //   extendShape<T, ReturnType<Incoming["_def"]["shape"]>>,
    //   Incoming["_def"]["unknownKeys"],
    //   Incoming["_def"]["catchall"],
    //   NewOutput,
    //   NewInput
    // > {
    //   const merged: any = new ZodObject({
    //     unknownKeys: merging._def.unknownKeys,
    //     catchall: merging._def.catchall,
    //     shape: () =>
    //       objectUtil.mergeShapes(this._def.shape(), merging._def.shape()),
    //     typeName: ZodFirstPartyTypeKind.ZodObject,
    //   }) as any;
    //   return merged;
    // }
    setKey(key, schema) {
        return this.augment({ [key]: schema });
    }
    // merge<Incoming extends AnyZodObject>(
    //   merging: Incoming
    // ): //ZodObject<T & Incoming["_shape"], UnknownKeys, Catchall> = (merging) => {
    // ZodObject<
    //   extendShape<T, ReturnType<Incoming["_def"]["shape"]>>,
    //   Incoming["_def"]["unknownKeys"],
    //   Incoming["_def"]["catchall"]
    // > {
    //   // const mergedShape = objectUtil.mergeShapes(
    //   //   this._def.shape(),
    //   //   merging._def.shape()
    //   // );
    //   const merged: any = new ZodObject({
    //     unknownKeys: merging._def.unknownKeys,
    //     catchall: merging._def.catchall,
    //     shape: () =>
    //       objectUtil.mergeShapes(this._def.shape(), merging._def.shape()),
    //     typeName: ZodFirstPartyTypeKind.ZodObject,
    //   }) as any;
    //   return merged;
    // }
    catchall(index) {
        return new ZodObject({
            ...this._def,
            catchall: index,
        });
    }
    pick(mask) {
        const shape = {};
        for (const key of _helpers_util_js__WEBPACK_IMPORTED_MODULE_4__.util.objectKeys(mask)) {
            if (mask[key] && this.shape[key]) {
                shape[key] = this.shape[key];
            }
        }
        return new ZodObject({
            ...this._def,
            shape: () => shape,
        });
    }
    omit(mask) {
        const shape = {};
        for (const key of _helpers_util_js__WEBPACK_IMPORTED_MODULE_4__.util.objectKeys(this.shape)) {
            if (!mask[key]) {
                shape[key] = this.shape[key];
            }
        }
        return new ZodObject({
            ...this._def,
            shape: () => shape,
        });
    }
    /**
     * @deprecated
     */
    deepPartial() {
        return deepPartialify(this);
    }
    partial(mask) {
        const newShape = {};
        for (const key of _helpers_util_js__WEBPACK_IMPORTED_MODULE_4__.util.objectKeys(this.shape)) {
            const fieldSchema = this.shape[key];
            if (mask && !mask[key]) {
                newShape[key] = fieldSchema;
            }
            else {
                newShape[key] = fieldSchema.optional();
            }
        }
        return new ZodObject({
            ...this._def,
            shape: () => newShape,
        });
    }
    required(mask) {
        const newShape = {};
        for (const key of _helpers_util_js__WEBPACK_IMPORTED_MODULE_4__.util.objectKeys(this.shape)) {
            if (mask && !mask[key]) {
                newShape[key] = this.shape[key];
            }
            else {
                const fieldSchema = this.shape[key];
                let newField = fieldSchema;
                while (newField instanceof ZodOptional) {
                    newField = newField._def.innerType;
                }
                newShape[key] = newField;
            }
        }
        return new ZodObject({
            ...this._def,
            shape: () => newShape,
        });
    }
    keyof() {
        return createZodEnum(_helpers_util_js__WEBPACK_IMPORTED_MODULE_4__.util.objectKeys(this.shape));
    }
}
ZodObject.create = (shape, params) => {
    return new ZodObject({
        shape: () => shape,
        unknownKeys: "strip",
        catchall: ZodNever.create(),
        typeName: ZodFirstPartyTypeKind.ZodObject,
        ...processCreateParams(params),
    });
};
ZodObject.strictCreate = (shape, params) => {
    return new ZodObject({
        shape: () => shape,
        unknownKeys: "strict",
        catchall: ZodNever.create(),
        typeName: ZodFirstPartyTypeKind.ZodObject,
        ...processCreateParams(params),
    });
};
ZodObject.lazycreate = (shape, params) => {
    return new ZodObject({
        shape,
        unknownKeys: "strip",
        catchall: ZodNever.create(),
        typeName: ZodFirstPartyTypeKind.ZodObject,
        ...processCreateParams(params),
    });
};
class ZodUnion extends ZodType {
    _parse(input) {
        const { ctx } = this._processInputParams(input);
        const options = this._def.options;
        function handleResults(results) {
            // return first issue-free validation if it exists
            for (const result of results) {
                if (result.result.status === "valid") {
                    return result.result;
                }
            }
            for (const result of results) {
                if (result.result.status === "dirty") {
                    // add issues from dirty option
                    ctx.common.issues.push(...result.ctx.common.issues);
                    return result.result;
                }
            }
            // return invalid
            const unionErrors = results.map((result) => new _ZodError_js__WEBPACK_IMPORTED_MODULE_0__.ZodError(result.ctx.common.issues));
            (0,_helpers_parseUtil_js__WEBPACK_IMPORTED_MODULE_3__.addIssueToContext)(ctx, {
                code: _ZodError_js__WEBPACK_IMPORTED_MODULE_0__.ZodIssueCode.invalid_union,
                unionErrors,
            });
            return _helpers_parseUtil_js__WEBPACK_IMPORTED_MODULE_3__.INVALID;
        }
        if (ctx.common.async) {
            return Promise.all(options.map(async (option) => {
                const childCtx = {
                    ...ctx,
                    common: {
                        ...ctx.common,
                        issues: [],
                    },
                    parent: null,
                };
                return {
                    result: await option._parseAsync({
                        data: ctx.data,
                        path: ctx.path,
                        parent: childCtx,
                    }),
                    ctx: childCtx,
                };
            })).then(handleResults);
        }
        else {
            let dirty = undefined;
            const issues = [];
            for (const option of options) {
                const childCtx = {
                    ...ctx,
                    common: {
                        ...ctx.common,
                        issues: [],
                    },
                    parent: null,
                };
                const result = option._parseSync({
                    data: ctx.data,
                    path: ctx.path,
                    parent: childCtx,
                });
                if (result.status === "valid") {
                    return result;
                }
                else if (result.status === "dirty" && !dirty) {
                    dirty = { result, ctx: childCtx };
                }
                if (childCtx.common.issues.length) {
                    issues.push(childCtx.common.issues);
                }
            }
            if (dirty) {
                ctx.common.issues.push(...dirty.ctx.common.issues);
                return dirty.result;
            }
            const unionErrors = issues.map((issues) => new _ZodError_js__WEBPACK_IMPORTED_MODULE_0__.ZodError(issues));
            (0,_helpers_parseUtil_js__WEBPACK_IMPORTED_MODULE_3__.addIssueToContext)(ctx, {
                code: _ZodError_js__WEBPACK_IMPORTED_MODULE_0__.ZodIssueCode.invalid_union,
                unionErrors,
            });
            return _helpers_parseUtil_js__WEBPACK_IMPORTED_MODULE_3__.INVALID;
        }
    }
    get options() {
        return this._def.options;
    }
}
ZodUnion.create = (types, params) => {
    return new ZodUnion({
        options: types,
        typeName: ZodFirstPartyTypeKind.ZodUnion,
        ...processCreateParams(params),
    });
};
/////////////////////////////////////////////////////
/////////////////////////////////////////////////////
//////////                                 //////////
//////////      ZodDiscriminatedUnion      //////////
//////////                                 //////////
/////////////////////////////////////////////////////
/////////////////////////////////////////////////////
const getDiscriminator = (type) => {
    if (type instanceof ZodLazy) {
        return getDiscriminator(type.schema);
    }
    else if (type instanceof ZodEffects) {
        return getDiscriminator(type.innerType());
    }
    else if (type instanceof ZodLiteral) {
        return [type.value];
    }
    else if (type instanceof ZodEnum) {
        return type.options;
    }
    else if (type instanceof ZodNativeEnum) {
        // eslint-disable-next-line ban/ban
        return _helpers_util_js__WEBPACK_IMPORTED_MODULE_4__.util.objectValues(type.enum);
    }
    else if (type instanceof ZodDefault) {
        return getDiscriminator(type._def.innerType);
    }
    else if (type instanceof ZodUndefined) {
        return [undefined];
    }
    else if (type instanceof ZodNull) {
        return [null];
    }
    else if (type instanceof ZodOptional) {
        return [undefined, ...getDiscriminator(type.unwrap())];
    }
    else if (type instanceof ZodNullable) {
        return [null, ...getDiscriminator(type.unwrap())];
    }
    else if (type instanceof ZodBranded) {
        return getDiscriminator(type.unwrap());
    }
    else if (type instanceof ZodReadonly) {
        return getDiscriminator(type.unwrap());
    }
    else if (type instanceof ZodCatch) {
        return getDiscriminator(type._def.innerType);
    }
    else {
        return [];
    }
};
class ZodDiscriminatedUnion extends ZodType {
    _parse(input) {
        const { ctx } = this._processInputParams(input);
        if (ctx.parsedType !== _helpers_util_js__WEBPACK_IMPORTED_MODULE_4__.ZodParsedType.object) {
            (0,_helpers_parseUtil_js__WEBPACK_IMPORTED_MODULE_3__.addIssueToContext)(ctx, {
                code: _ZodError_js__WEBPACK_IMPORTED_MODULE_0__.ZodIssueCode.invalid_type,
                expected: _helpers_util_js__WEBPACK_IMPORTED_MODULE_4__.ZodParsedType.object,
                received: ctx.parsedType,
            });
            return _helpers_parseUtil_js__WEBPACK_IMPORTED_MODULE_3__.INVALID;
        }
        const discriminator = this.discriminator;
        const discriminatorValue = ctx.data[discriminator];
        const option = this.optionsMap.get(discriminatorValue);
        if (!option) {
            (0,_helpers_parseUtil_js__WEBPACK_IMPORTED_MODULE_3__.addIssueToContext)(ctx, {
                code: _ZodError_js__WEBPACK_IMPORTED_MODULE_0__.ZodIssueCode.invalid_union_discriminator,
                options: Array.from(this.optionsMap.keys()),
                path: [discriminator],
            });
            return _helpers_parseUtil_js__WEBPACK_IMPORTED_MODULE_3__.INVALID;
        }
        if (ctx.common.async) {
            return option._parseAsync({
                data: ctx.data,
                path: ctx.path,
                parent: ctx,
            });
        }
        else {
            return option._parseSync({
                data: ctx.data,
                path: ctx.path,
                parent: ctx,
            });
        }
    }
    get discriminator() {
        return this._def.discriminator;
    }
    get options() {
        return this._def.options;
    }
    get optionsMap() {
        return this._def.optionsMap;
    }
    /**
     * The constructor of the discriminated union schema. Its behaviour is very similar to that of the normal z.union() constructor.
     * However, it only allows a union of objects, all of which need to share a discriminator property. This property must
     * have a different value for each object in the union.
     * @param discriminator the name of the discriminator property
     * @param types an array of object schemas
     * @param params
     */
    static create(discriminator, options, params) {
        // Get all the valid discriminator values
        const optionsMap = new Map();
        // try {
        for (const type of options) {
            const discriminatorValues = getDiscriminator(type.shape[discriminator]);
            if (!discriminatorValues.length) {
                throw new Error(`A discriminator value for key \`${discriminator}\` could not be extracted from all schema options`);
            }
            for (const value of discriminatorValues) {
                if (optionsMap.has(value)) {
                    throw new Error(`Discriminator property ${String(discriminator)} has duplicate value ${String(value)}`);
                }
                optionsMap.set(value, type);
            }
        }
        return new ZodDiscriminatedUnion({
            typeName: ZodFirstPartyTypeKind.ZodDiscriminatedUnion,
            discriminator,
            options,
            optionsMap,
            ...processCreateParams(params),
        });
    }
}
function mergeValues(a, b) {
    const aType = (0,_helpers_util_js__WEBPACK_IMPORTED_MODULE_4__.getParsedType)(a);
    const bType = (0,_helpers_util_js__WEBPACK_IMPORTED_MODULE_4__.getParsedType)(b);
    if (a === b) {
        return { valid: true, data: a };
    }
    else if (aType === _helpers_util_js__WEBPACK_IMPORTED_MODULE_4__.ZodParsedType.object && bType === _helpers_util_js__WEBPACK_IMPORTED_MODULE_4__.ZodParsedType.object) {
        const bKeys = _helpers_util_js__WEBPACK_IMPORTED_MODULE_4__.util.objectKeys(b);
        const sharedKeys = _helpers_util_js__WEBPACK_IMPORTED_MODULE_4__.util.objectKeys(a).filter((key) => bKeys.indexOf(key) !== -1);
        const newObj = { ...a, ...b };
        for (const key of sharedKeys) {
            const sharedValue = mergeValues(a[key], b[key]);
            if (!sharedValue.valid) {
                return { valid: false };
            }
            newObj[key] = sharedValue.data;
        }
        return { valid: true, data: newObj };
    }
    else if (aType === _helpers_util_js__WEBPACK_IMPORTED_MODULE_4__.ZodParsedType.array && bType === _helpers_util_js__WEBPACK_IMPORTED_MODULE_4__.ZodParsedType.array) {
        if (a.length !== b.length) {
            return { valid: false };
        }
        const newArray = [];
        for (let index = 0; index < a.length; index++) {
            const itemA = a[index];
            const itemB = b[index];
            const sharedValue = mergeValues(itemA, itemB);
            if (!sharedValue.valid) {
                return { valid: false };
            }
            newArray.push(sharedValue.data);
        }
        return { valid: true, data: newArray };
    }
    else if (aType === _helpers_util_js__WEBPACK_IMPORTED_MODULE_4__.ZodParsedType.date && bType === _helpers_util_js__WEBPACK_IMPORTED_MODULE_4__.ZodParsedType.date && +a === +b) {
        return { valid: true, data: a };
    }
    else {
        return { valid: false };
    }
}
class ZodIntersection extends ZodType {
    _parse(input) {
        const { status, ctx } = this._processInputParams(input);
        const handleParsed = (parsedLeft, parsedRight) => {
            if ((0,_helpers_parseUtil_js__WEBPACK_IMPORTED_MODULE_3__.isAborted)(parsedLeft) || (0,_helpers_parseUtil_js__WEBPACK_IMPORTED_MODULE_3__.isAborted)(parsedRight)) {
                return _helpers_parseUtil_js__WEBPACK_IMPORTED_MODULE_3__.INVALID;
            }
            const merged = mergeValues(parsedLeft.value, parsedRight.value);
            if (!merged.valid) {
                (0,_helpers_parseUtil_js__WEBPACK_IMPORTED_MODULE_3__.addIssueToContext)(ctx, {
                    code: _ZodError_js__WEBPACK_IMPORTED_MODULE_0__.ZodIssueCode.invalid_intersection_types,
                });
                return _helpers_parseUtil_js__WEBPACK_IMPORTED_MODULE_3__.INVALID;
            }
            if ((0,_helpers_parseUtil_js__WEBPACK_IMPORTED_MODULE_3__.isDirty)(parsedLeft) || (0,_helpers_parseUtil_js__WEBPACK_IMPORTED_MODULE_3__.isDirty)(parsedRight)) {
                status.dirty();
            }
            return { status: status.value, value: merged.data };
        };
        if (ctx.common.async) {
            return Promise.all([
                this._def.left._parseAsync({
                    data: ctx.data,
                    path: ctx.path,
                    parent: ctx,
                }),
                this._def.right._parseAsync({
                    data: ctx.data,
                    path: ctx.path,
                    parent: ctx,
                }),
            ]).then(([left, right]) => handleParsed(left, right));
        }
        else {
            return handleParsed(this._def.left._parseSync({
                data: ctx.data,
                path: ctx.path,
                parent: ctx,
            }), this._def.right._parseSync({
                data: ctx.data,
                path: ctx.path,
                parent: ctx,
            }));
        }
    }
}
ZodIntersection.create = (left, right, params) => {
    return new ZodIntersection({
        left: left,
        right: right,
        typeName: ZodFirstPartyTypeKind.ZodIntersection,
        ...processCreateParams(params),
    });
};
// type ZodTupleItems = [ZodTypeAny, ...ZodTypeAny[]];
class ZodTuple extends ZodType {
    _parse(input) {
        const { status, ctx } = this._processInputParams(input);
        if (ctx.parsedType !== _helpers_util_js__WEBPACK_IMPORTED_MODULE_4__.ZodParsedType.array) {
            (0,_helpers_parseUtil_js__WEBPACK_IMPORTED_MODULE_3__.addIssueToContext)(ctx, {
                code: _ZodError_js__WEBPACK_IMPORTED_MODULE_0__.ZodIssueCode.invalid_type,
                expected: _helpers_util_js__WEBPACK_IMPORTED_MODULE_4__.ZodParsedType.array,
                received: ctx.parsedType,
            });
            return _helpers_parseUtil_js__WEBPACK_IMPORTED_MODULE_3__.INVALID;
        }
        if (ctx.data.length < this._def.items.length) {
            (0,_helpers_parseUtil_js__WEBPACK_IMPORTED_MODULE_3__.addIssueToContext)(ctx, {
                code: _ZodError_js__WEBPACK_IMPORTED_MODULE_0__.ZodIssueCode.too_small,
                minimum: this._def.items.length,
                inclusive: true,
                exact: false,
                type: "array",
            });
            return _helpers_parseUtil_js__WEBPACK_IMPORTED_MODULE_3__.INVALID;
        }
        const rest = this._def.rest;
        if (!rest && ctx.data.length > this._def.items.length) {
            (0,_helpers_parseUtil_js__WEBPACK_IMPORTED_MODULE_3__.addIssueToContext)(ctx, {
                code: _ZodError_js__WEBPACK_IMPORTED_MODULE_0__.ZodIssueCode.too_big,
                maximum: this._def.items.length,
                inclusive: true,
                exact: false,
                type: "array",
            });
            status.dirty();
        }
        const items = [...ctx.data]
            .map((item, itemIndex) => {
            const schema = this._def.items[itemIndex] || this._def.rest;
            if (!schema)
                return null;
            return schema._parse(new ParseInputLazyPath(ctx, item, ctx.path, itemIndex));
        })
            .filter((x) => !!x); // filter nulls
        if (ctx.common.async) {
            return Promise.all(items).then((results) => {
                return _helpers_parseUtil_js__WEBPACK_IMPORTED_MODULE_3__.ParseStatus.mergeArray(status, results);
            });
        }
        else {
            return _helpers_parseUtil_js__WEBPACK_IMPORTED_MODULE_3__.ParseStatus.mergeArray(status, items);
        }
    }
    get items() {
        return this._def.items;
    }
    rest(rest) {
        return new ZodTuple({
            ...this._def,
            rest,
        });
    }
}
ZodTuple.create = (schemas, params) => {
    if (!Array.isArray(schemas)) {
        throw new Error("You must pass an array of schemas to z.tuple([ ... ])");
    }
    return new ZodTuple({
        items: schemas,
        typeName: ZodFirstPartyTypeKind.ZodTuple,
        rest: null,
        ...processCreateParams(params),
    });
};
class ZodRecord extends ZodType {
    get keySchema() {
        return this._def.keyType;
    }
    get valueSchema() {
        return this._def.valueType;
    }
    _parse(input) {
        const { status, ctx } = this._processInputParams(input);
        if (ctx.parsedType !== _helpers_util_js__WEBPACK_IMPORTED_MODULE_4__.ZodParsedType.object) {
            (0,_helpers_parseUtil_js__WEBPACK_IMPORTED_MODULE_3__.addIssueToContext)(ctx, {
                code: _ZodError_js__WEBPACK_IMPORTED_MODULE_0__.ZodIssueCode.invalid_type,
                expected: _helpers_util_js__WEBPACK_IMPORTED_MODULE_4__.ZodParsedType.object,
                received: ctx.parsedType,
            });
            return _helpers_parseUtil_js__WEBPACK_IMPORTED_MODULE_3__.INVALID;
        }
        const pairs = [];
        const keyType = this._def.keyType;
        const valueType = this._def.valueType;
        for (const key in ctx.data) {
            pairs.push({
                key: keyType._parse(new ParseInputLazyPath(ctx, key, ctx.path, key)),
                value: valueType._parse(new ParseInputLazyPath(ctx, ctx.data[key], ctx.path, key)),
                alwaysSet: key in ctx.data,
            });
        }
        if (ctx.common.async) {
            return _helpers_parseUtil_js__WEBPACK_IMPORTED_MODULE_3__.ParseStatus.mergeObjectAsync(status, pairs);
        }
        else {
            return _helpers_parseUtil_js__WEBPACK_IMPORTED_MODULE_3__.ParseStatus.mergeObjectSync(status, pairs);
        }
    }
    get element() {
        return this._def.valueType;
    }
    static create(first, second, third) {
        if (second instanceof ZodType) {
            return new ZodRecord({
                keyType: first,
                valueType: second,
                typeName: ZodFirstPartyTypeKind.ZodRecord,
                ...processCreateParams(third),
            });
        }
        return new ZodRecord({
            keyType: ZodString.create(),
            valueType: first,
            typeName: ZodFirstPartyTypeKind.ZodRecord,
            ...processCreateParams(second),
        });
    }
}
class ZodMap extends ZodType {
    get keySchema() {
        return this._def.keyType;
    }
    get valueSchema() {
        return this._def.valueType;
    }
    _parse(input) {
        const { status, ctx } = this._processInputParams(input);
        if (ctx.parsedType !== _helpers_util_js__WEBPACK_IMPORTED_MODULE_4__.ZodParsedType.map) {
            (0,_helpers_parseUtil_js__WEBPACK_IMPORTED_MODULE_3__.addIssueToContext)(ctx, {
                code: _ZodError_js__WEBPACK_IMPORTED_MODULE_0__.ZodIssueCode.invalid_type,
                expected: _helpers_util_js__WEBPACK_IMPORTED_MODULE_4__.ZodParsedType.map,
                received: ctx.parsedType,
            });
            return _helpers_parseUtil_js__WEBPACK_IMPORTED_MODULE_3__.INVALID;
        }
        const keyType = this._def.keyType;
        const valueType = this._def.valueType;
        const pairs = [...ctx.data.entries()].map(([key, value], index) => {
            return {
                key: keyType._parse(new ParseInputLazyPath(ctx, key, ctx.path, [index, "key"])),
                value: valueType._parse(new ParseInputLazyPath(ctx, value, ctx.path, [index, "value"])),
            };
        });
        if (ctx.common.async) {
            const finalMap = new Map();
            return Promise.resolve().then(async () => {
                for (const pair of pairs) {
                    const key = await pair.key;
                    const value = await pair.value;
                    if (key.status === "aborted" || value.status === "aborted") {
                        return _helpers_parseUtil_js__WEBPACK_IMPORTED_MODULE_3__.INVALID;
                    }
                    if (key.status === "dirty" || value.status === "dirty") {
                        status.dirty();
                    }
                    finalMap.set(key.value, value.value);
                }
                return { status: status.value, value: finalMap };
            });
        }
        else {
            const finalMap = new Map();
            for (const pair of pairs) {
                const key = pair.key;
                const value = pair.value;
                if (key.status === "aborted" || value.status === "aborted") {
                    return _helpers_parseUtil_js__WEBPACK_IMPORTED_MODULE_3__.INVALID;
                }
                if (key.status === "dirty" || value.status === "dirty") {
                    status.dirty();
                }
                finalMap.set(key.value, value.value);
            }
            return { status: status.value, value: finalMap };
        }
    }
}
ZodMap.create = (keyType, valueType, params) => {
    return new ZodMap({
        valueType,
        keyType,
        typeName: ZodFirstPartyTypeKind.ZodMap,
        ...processCreateParams(params),
    });
};
class ZodSet extends ZodType {
    _parse(input) {
        const { status, ctx } = this._processInputParams(input);
        if (ctx.parsedType !== _helpers_util_js__WEBPACK_IMPORTED_MODULE_4__.ZodParsedType.set) {
            (0,_helpers_parseUtil_js__WEBPACK_IMPORTED_MODULE_3__.addIssueToContext)(ctx, {
                code: _ZodError_js__WEBPACK_IMPORTED_MODULE_0__.ZodIssueCode.invalid_type,
                expected: _helpers_util_js__WEBPACK_IMPORTED_MODULE_4__.ZodParsedType.set,
                received: ctx.parsedType,
            });
            return _helpers_parseUtil_js__WEBPACK_IMPORTED_MODULE_3__.INVALID;
        }
        const def = this._def;
        if (def.minSize !== null) {
            if (ctx.data.size < def.minSize.value) {
                (0,_helpers_parseUtil_js__WEBPACK_IMPORTED_MODULE_3__.addIssueToContext)(ctx, {
                    code: _ZodError_js__WEBPACK_IMPORTED_MODULE_0__.ZodIssueCode.too_small,
                    minimum: def.minSize.value,
                    type: "set",
                    inclusive: true,
                    exact: false,
                    message: def.minSize.message,
                });
                status.dirty();
            }
        }
        if (def.maxSize !== null) {
            if (ctx.data.size > def.maxSize.value) {
                (0,_helpers_parseUtil_js__WEBPACK_IMPORTED_MODULE_3__.addIssueToContext)(ctx, {
                    code: _ZodError_js__WEBPACK_IMPORTED_MODULE_0__.ZodIssueCode.too_big,
                    maximum: def.maxSize.value,
                    type: "set",
                    inclusive: true,
                    exact: false,
                    message: def.maxSize.message,
                });
                status.dirty();
            }
        }
        const valueType = this._def.valueType;
        function finalizeSet(elements) {
            const parsedSet = new Set();
            for (const element of elements) {
                if (element.status === "aborted")
                    return _helpers_parseUtil_js__WEBPACK_IMPORTED_MODULE_3__.INVALID;
                if (element.status === "dirty")
                    status.dirty();
                parsedSet.add(element.value);
            }
            return { status: status.value, value: parsedSet };
        }
        const elements = [...ctx.data.values()].map((item, i) => valueType._parse(new ParseInputLazyPath(ctx, item, ctx.path, i)));
        if (ctx.common.async) {
            return Promise.all(elements).then((elements) => finalizeSet(elements));
        }
        else {
            return finalizeSet(elements);
        }
    }
    min(minSize, message) {
        return new ZodSet({
            ...this._def,
            minSize: { value: minSize, message: _helpers_errorUtil_js__WEBPACK_IMPORTED_MODULE_2__.errorUtil.toString(message) },
        });
    }
    max(maxSize, message) {
        return new ZodSet({
            ...this._def,
            maxSize: { value: maxSize, message: _helpers_errorUtil_js__WEBPACK_IMPORTED_MODULE_2__.errorUtil.toString(message) },
        });
    }
    size(size, message) {
        return this.min(size, message).max(size, message);
    }
    nonempty(message) {
        return this.min(1, message);
    }
}
ZodSet.create = (valueType, params) => {
    return new ZodSet({
        valueType,
        minSize: null,
        maxSize: null,
        typeName: ZodFirstPartyTypeKind.ZodSet,
        ...processCreateParams(params),
    });
};
class ZodFunction extends ZodType {
    constructor() {
        super(...arguments);
        this.validate = this.implement;
    }
    _parse(input) {
        const { ctx } = this._processInputParams(input);
        if (ctx.parsedType !== _helpers_util_js__WEBPACK_IMPORTED_MODULE_4__.ZodParsedType.function) {
            (0,_helpers_parseUtil_js__WEBPACK_IMPORTED_MODULE_3__.addIssueToContext)(ctx, {
                code: _ZodError_js__WEBPACK_IMPORTED_MODULE_0__.ZodIssueCode.invalid_type,
                expected: _helpers_util_js__WEBPACK_IMPORTED_MODULE_4__.ZodParsedType.function,
                received: ctx.parsedType,
            });
            return _helpers_parseUtil_js__WEBPACK_IMPORTED_MODULE_3__.INVALID;
        }
        function makeArgsIssue(args, error) {
            return (0,_helpers_parseUtil_js__WEBPACK_IMPORTED_MODULE_3__.makeIssue)({
                data: args,
                path: ctx.path,
                errorMaps: [ctx.common.contextualErrorMap, ctx.schemaErrorMap, (0,_errors_js__WEBPACK_IMPORTED_MODULE_1__.getErrorMap)(), _errors_js__WEBPACK_IMPORTED_MODULE_1__.defaultErrorMap].filter((x) => !!x),
                issueData: {
                    code: _ZodError_js__WEBPACK_IMPORTED_MODULE_0__.ZodIssueCode.invalid_arguments,
                    argumentsError: error,
                },
            });
        }
        function makeReturnsIssue(returns, error) {
            return (0,_helpers_parseUtil_js__WEBPACK_IMPORTED_MODULE_3__.makeIssue)({
                data: returns,
                path: ctx.path,
                errorMaps: [ctx.common.contextualErrorMap, ctx.schemaErrorMap, (0,_errors_js__WEBPACK_IMPORTED_MODULE_1__.getErrorMap)(), _errors_js__WEBPACK_IMPORTED_MODULE_1__.defaultErrorMap].filter((x) => !!x),
                issueData: {
                    code: _ZodError_js__WEBPACK_IMPORTED_MODULE_0__.ZodIssueCode.invalid_return_type,
                    returnTypeError: error,
                },
            });
        }
        const params = { errorMap: ctx.common.contextualErrorMap };
        const fn = ctx.data;
        if (this._def.returns instanceof ZodPromise) {
            // Would love a way to avoid disabling this rule, but we need
            // an alias (using an arrow function was what caused 2651).
            // eslint-disable-next-line @typescript-eslint/no-this-alias
            const me = this;
            return (0,_helpers_parseUtil_js__WEBPACK_IMPORTED_MODULE_3__.OK)(async function (...args) {
                const error = new _ZodError_js__WEBPACK_IMPORTED_MODULE_0__.ZodError([]);
                const parsedArgs = await me._def.args.parseAsync(args, params).catch((e) => {
                    error.addIssue(makeArgsIssue(args, e));
                    throw error;
                });
                const result = await Reflect.apply(fn, this, parsedArgs);
                const parsedReturns = await me._def.returns._def.type
                    .parseAsync(result, params)
                    .catch((e) => {
                    error.addIssue(makeReturnsIssue(result, e));
                    throw error;
                });
                return parsedReturns;
            });
        }
        else {
            // Would love a way to avoid disabling this rule, but we need
            // an alias (using an arrow function was what caused 2651).
            // eslint-disable-next-line @typescript-eslint/no-this-alias
            const me = this;
            return (0,_helpers_parseUtil_js__WEBPACK_IMPORTED_MODULE_3__.OK)(function (...args) {
                const parsedArgs = me._def.args.safeParse(args, params);
                if (!parsedArgs.success) {
                    throw new _ZodError_js__WEBPACK_IMPORTED_MODULE_0__.ZodError([makeArgsIssue(args, parsedArgs.error)]);
                }
                const result = Reflect.apply(fn, this, parsedArgs.data);
                const parsedReturns = me._def.returns.safeParse(result, params);
                if (!parsedReturns.success) {
                    throw new _ZodError_js__WEBPACK_IMPORTED_MODULE_0__.ZodError([makeReturnsIssue(result, parsedReturns.error)]);
                }
                return parsedReturns.data;
            });
        }
    }
    parameters() {
        return this._def.args;
    }
    returnType() {
        return this._def.returns;
    }
    args(...items) {
        return new ZodFunction({
            ...this._def,
            args: ZodTuple.create(items).rest(ZodUnknown.create()),
        });
    }
    returns(returnType) {
        return new ZodFunction({
            ...this._def,
            returns: returnType,
        });
    }
    implement(func) {
        const validatedFunc = this.parse(func);
        return validatedFunc;
    }
    strictImplement(func) {
        const validatedFunc = this.parse(func);
        return validatedFunc;
    }
    static create(args, returns, params) {
        return new ZodFunction({
            args: (args ? args : ZodTuple.create([]).rest(ZodUnknown.create())),
            returns: returns || ZodUnknown.create(),
            typeName: ZodFirstPartyTypeKind.ZodFunction,
            ...processCreateParams(params),
        });
    }
}
class ZodLazy extends ZodType {
    get schema() {
        return this._def.getter();
    }
    _parse(input) {
        const { ctx } = this._processInputParams(input);
        const lazySchema = this._def.getter();
        return lazySchema._parse({ data: ctx.data, path: ctx.path, parent: ctx });
    }
}
ZodLazy.create = (getter, params) => {
    return new ZodLazy({
        getter: getter,
        typeName: ZodFirstPartyTypeKind.ZodLazy,
        ...processCreateParams(params),
    });
};
class ZodLiteral extends ZodType {
    _parse(input) {
        if (input.data !== this._def.value) {
            const ctx = this._getOrReturnCtx(input);
            (0,_helpers_parseUtil_js__WEBPACK_IMPORTED_MODULE_3__.addIssueToContext)(ctx, {
                received: ctx.data,
                code: _ZodError_js__WEBPACK_IMPORTED_MODULE_0__.ZodIssueCode.invalid_literal,
                expected: this._def.value,
            });
            return _helpers_parseUtil_js__WEBPACK_IMPORTED_MODULE_3__.INVALID;
        }
        return { status: "valid", value: input.data };
    }
    get value() {
        return this._def.value;
    }
}
ZodLiteral.create = (value, params) => {
    return new ZodLiteral({
        value: value,
        typeName: ZodFirstPartyTypeKind.ZodLiteral,
        ...processCreateParams(params),
    });
};
function createZodEnum(values, params) {
    return new ZodEnum({
        values,
        typeName: ZodFirstPartyTypeKind.ZodEnum,
        ...processCreateParams(params),
    });
}
class ZodEnum extends ZodType {
    constructor() {
        super(...arguments);
        _ZodEnum_cache.set(this, void 0);
    }
    _parse(input) {
        if (typeof input.data !== "string") {
            const ctx = this._getOrReturnCtx(input);
            const expectedValues = this._def.values;
            (0,_helpers_parseUtil_js__WEBPACK_IMPORTED_MODULE_3__.addIssueToContext)(ctx, {
                expected: _helpers_util_js__WEBPACK_IMPORTED_MODULE_4__.util.joinValues(expectedValues),
                received: ctx.parsedType,
                code: _ZodError_js__WEBPACK_IMPORTED_MODULE_0__.ZodIssueCode.invalid_type,
            });
            return _helpers_parseUtil_js__WEBPACK_IMPORTED_MODULE_3__.INVALID;
        }
        if (!__classPrivateFieldGet(this, _ZodEnum_cache, "f")) {
            __classPrivateFieldSet(this, _ZodEnum_cache, new Set(this._def.values), "f");
        }
        if (!__classPrivateFieldGet(this, _ZodEnum_cache, "f").has(input.data)) {
            const ctx = this._getOrReturnCtx(input);
            const expectedValues = this._def.values;
            (0,_helpers_parseUtil_js__WEBPACK_IMPORTED_MODULE_3__.addIssueToContext)(ctx, {
                received: ctx.data,
                code: _ZodError_js__WEBPACK_IMPORTED_MODULE_0__.ZodIssueCode.invalid_enum_value,
                options: expectedValues,
            });
            return _helpers_parseUtil_js__WEBPACK_IMPORTED_MODULE_3__.INVALID;
        }
        return (0,_helpers_parseUtil_js__WEBPACK_IMPORTED_MODULE_3__.OK)(input.data);
    }
    get options() {
        return this._def.values;
    }
    get enum() {
        const enumValues = {};
        for (const val of this._def.values) {
            enumValues[val] = val;
        }
        return enumValues;
    }
    get Values() {
        const enumValues = {};
        for (const val of this._def.values) {
            enumValues[val] = val;
        }
        return enumValues;
    }
    get Enum() {
        const enumValues = {};
        for (const val of this._def.values) {
            enumValues[val] = val;
        }
        return enumValues;
    }
    extract(values, newDef = this._def) {
        return ZodEnum.create(values, {
            ...this._def,
            ...newDef,
        });
    }
    exclude(values, newDef = this._def) {
        return ZodEnum.create(this.options.filter((opt) => !values.includes(opt)), {
            ...this._def,
            ...newDef,
        });
    }
}
_ZodEnum_cache = new WeakMap();
ZodEnum.create = createZodEnum;
class ZodNativeEnum extends ZodType {
    constructor() {
        super(...arguments);
        _ZodNativeEnum_cache.set(this, void 0);
    }
    _parse(input) {
        const nativeEnumValues = _helpers_util_js__WEBPACK_IMPORTED_MODULE_4__.util.getValidEnumValues(this._def.values);
        const ctx = this._getOrReturnCtx(input);
        if (ctx.parsedType !== _helpers_util_js__WEBPACK_IMPORTED_MODULE_4__.ZodParsedType.string && ctx.parsedType !== _helpers_util_js__WEBPACK_IMPORTED_MODULE_4__.ZodParsedType.number) {
            const expectedValues = _helpers_util_js__WEBPACK_IMPORTED_MODULE_4__.util.objectValues(nativeEnumValues);
            (0,_helpers_parseUtil_js__WEBPACK_IMPORTED_MODULE_3__.addIssueToContext)(ctx, {
                expected: _helpers_util_js__WEBPACK_IMPORTED_MODULE_4__.util.joinValues(expectedValues),
                received: ctx.parsedType,
                code: _ZodError_js__WEBPACK_IMPORTED_MODULE_0__.ZodIssueCode.invalid_type,
            });
            return _helpers_parseUtil_js__WEBPACK_IMPORTED_MODULE_3__.INVALID;
        }
        if (!__classPrivateFieldGet(this, _ZodNativeEnum_cache, "f")) {
            __classPrivateFieldSet(this, _ZodNativeEnum_cache, new Set(_helpers_util_js__WEBPACK_IMPORTED_MODULE_4__.util.getValidEnumValues(this._def.values)), "f");
        }
        if (!__classPrivateFieldGet(this, _ZodNativeEnum_cache, "f").has(input.data)) {
            const expectedValues = _helpers_util_js__WEBPACK_IMPORTED_MODULE_4__.util.objectValues(nativeEnumValues);
            (0,_helpers_parseUtil_js__WEBPACK_IMPORTED_MODULE_3__.addIssueToContext)(ctx, {
                received: ctx.data,
                code: _ZodError_js__WEBPACK_IMPORTED_MODULE_0__.ZodIssueCode.invalid_enum_value,
                options: expectedValues,
            });
            return _helpers_parseUtil_js__WEBPACK_IMPORTED_MODULE_3__.INVALID;
        }
        return (0,_helpers_parseUtil_js__WEBPACK_IMPORTED_MODULE_3__.OK)(input.data);
    }
    get enum() {
        return this._def.values;
    }
}
_ZodNativeEnum_cache = new WeakMap();
ZodNativeEnum.create = (values, params) => {
    return new ZodNativeEnum({
        values: values,
        typeName: ZodFirstPartyTypeKind.ZodNativeEnum,
        ...processCreateParams(params),
    });
};
class ZodPromise extends ZodType {
    unwrap() {
        return this._def.type;
    }
    _parse(input) {
        const { ctx } = this._processInputParams(input);
        if (ctx.parsedType !== _helpers_util_js__WEBPACK_IMPORTED_MODULE_4__.ZodParsedType.promise && ctx.common.async === false) {
            (0,_helpers_parseUtil_js__WEBPACK_IMPORTED_MODULE_3__.addIssueToContext)(ctx, {
                code: _ZodError_js__WEBPACK_IMPORTED_MODULE_0__.ZodIssueCode.invalid_type,
                expected: _helpers_util_js__WEBPACK_IMPORTED_MODULE_4__.ZodParsedType.promise,
                received: ctx.parsedType,
            });
            return _helpers_parseUtil_js__WEBPACK_IMPORTED_MODULE_3__.INVALID;
        }
        const promisified = ctx.parsedType === _helpers_util_js__WEBPACK_IMPORTED_MODULE_4__.ZodParsedType.promise ? ctx.data : Promise.resolve(ctx.data);
        return (0,_helpers_parseUtil_js__WEBPACK_IMPORTED_MODULE_3__.OK)(promisified.then((data) => {
            return this._def.type.parseAsync(data, {
                path: ctx.path,
                errorMap: ctx.common.contextualErrorMap,
            });
        }));
    }
}
ZodPromise.create = (schema, params) => {
    return new ZodPromise({
        type: schema,
        typeName: ZodFirstPartyTypeKind.ZodPromise,
        ...processCreateParams(params),
    });
};
class ZodEffects extends ZodType {
    innerType() {
        return this._def.schema;
    }
    sourceType() {
        return this._def.schema._def.typeName === ZodFirstPartyTypeKind.ZodEffects
            ? this._def.schema.sourceType()
            : this._def.schema;
    }
    _parse(input) {
        const { status, ctx } = this._processInputParams(input);
        const effect = this._def.effect || null;
        const checkCtx = {
            addIssue: (arg) => {
                (0,_helpers_parseUtil_js__WEBPACK_IMPORTED_MODULE_3__.addIssueToContext)(ctx, arg);
                if (arg.fatal) {
                    status.abort();
                }
                else {
                    status.dirty();
                }
            },
            get path() {
                return ctx.path;
            },
        };
        checkCtx.addIssue = checkCtx.addIssue.bind(checkCtx);
        if (effect.type === "preprocess") {
            const processed = effect.transform(ctx.data, checkCtx);
            if (ctx.common.async) {
                return Promise.resolve(processed).then(async (processed) => {
                    if (status.value === "aborted")
                        return _helpers_parseUtil_js__WEBPACK_IMPORTED_MODULE_3__.INVALID;
                    const result = await this._def.schema._parseAsync({
                        data: processed,
                        path: ctx.path,
                        parent: ctx,
                    });
                    if (result.status === "aborted")
                        return _helpers_parseUtil_js__WEBPACK_IMPORTED_MODULE_3__.INVALID;
                    if (result.status === "dirty")
                        return (0,_helpers_parseUtil_js__WEBPACK_IMPORTED_MODULE_3__.DIRTY)(result.value);
                    if (status.value === "dirty")
                        return (0,_helpers_parseUtil_js__WEBPACK_IMPORTED_MODULE_3__.DIRTY)(result.value);
                    return result;
                });
            }
            else {
                if (status.value === "aborted")
                    return _helpers_parseUtil_js__WEBPACK_IMPORTED_MODULE_3__.INVALID;
                const result = this._def.schema._parseSync({
                    data: processed,
                    path: ctx.path,
                    parent: ctx,
                });
                if (result.status === "aborted")
                    return _helpers_parseUtil_js__WEBPACK_IMPORTED_MODULE_3__.INVALID;
                if (result.status === "dirty")
                    return (0,_helpers_parseUtil_js__WEBPACK_IMPORTED_MODULE_3__.DIRTY)(result.value);
                if (status.value === "dirty")
                    return (0,_helpers_parseUtil_js__WEBPACK_IMPORTED_MODULE_3__.DIRTY)(result.value);
                return result;
            }
        }
        if (effect.type === "refinement") {
            const executeRefinement = (acc) => {
                const result = effect.refinement(acc, checkCtx);
                if (ctx.common.async) {
                    return Promise.resolve(result);
                }
                if (result instanceof Promise) {
                    throw new Error("Async refinement encountered during synchronous parse operation. Use .parseAsync instead.");
                }
                return acc;
            };
            if (ctx.common.async === false) {
                const inner = this._def.schema._parseSync({
                    data: ctx.data,
                    path: ctx.path,
                    parent: ctx,
                });
                if (inner.status === "aborted")
                    return _helpers_parseUtil_js__WEBPACK_IMPORTED_MODULE_3__.INVALID;
                if (inner.status === "dirty")
                    status.dirty();
                // return value is ignored
                executeRefinement(inner.value);
                return { status: status.value, value: inner.value };
            }
            else {
                return this._def.schema._parseAsync({ data: ctx.data, path: ctx.path, parent: ctx }).then((inner) => {
                    if (inner.status === "aborted")
                        return _helpers_parseUtil_js__WEBPACK_IMPORTED_MODULE_3__.INVALID;
                    if (inner.status === "dirty")
                        status.dirty();
                    return executeRefinement(inner.value).then(() => {
                        return { status: status.value, value: inner.value };
                    });
                });
            }
        }
        if (effect.type === "transform") {
            if (ctx.common.async === false) {
                const base = this._def.schema._parseSync({
                    data: ctx.data,
                    path: ctx.path,
                    parent: ctx,
                });
                if (!(0,_helpers_parseUtil_js__WEBPACK_IMPORTED_MODULE_3__.isValid)(base))
                    return base;
                const result = effect.transform(base.value, checkCtx);
                if (result instanceof Promise) {
                    throw new Error(`Asynchronous transform encountered during synchronous parse operation. Use .parseAsync instead.`);
                }
                return { status: status.value, value: result };
            }
            else {
                return this._def.schema._parseAsync({ data: ctx.data, path: ctx.path, parent: ctx }).then((base) => {
                    if (!(0,_helpers_parseUtil_js__WEBPACK_IMPORTED_MODULE_3__.isValid)(base))
                        return base;
                    return Promise.resolve(effect.transform(base.value, checkCtx)).then((result) => ({
                        status: status.value,
                        value: result,
                    }));
                });
            }
        }
        _helpers_util_js__WEBPACK_IMPORTED_MODULE_4__.util.assertNever(effect);
    }
}
ZodEffects.create = (schema, effect, params) => {
    return new ZodEffects({
        schema,
        typeName: ZodFirstPartyTypeKind.ZodEffects,
        effect,
        ...processCreateParams(params),
    });
};
ZodEffects.createWithPreprocess = (preprocess, schema, params) => {
    return new ZodEffects({
        schema,
        effect: { type: "preprocess", transform: preprocess },
        typeName: ZodFirstPartyTypeKind.ZodEffects,
        ...processCreateParams(params),
    });
};

class ZodOptional extends ZodType {
    _parse(input) {
        const parsedType = this._getType(input);
        if (parsedType === _helpers_util_js__WEBPACK_IMPORTED_MODULE_4__.ZodParsedType.undefined) {
            return (0,_helpers_parseUtil_js__WEBPACK_IMPORTED_MODULE_3__.OK)(undefined);
        }
        return this._def.innerType._parse(input);
    }
    unwrap() {
        return this._def.innerType;
    }
}
ZodOptional.create = (type, params) => {
    return new ZodOptional({
        innerType: type,
        typeName: ZodFirstPartyTypeKind.ZodOptional,
        ...processCreateParams(params),
    });
};
class ZodNullable extends ZodType {
    _parse(input) {
        const parsedType = this._getType(input);
        if (parsedType === _helpers_util_js__WEBPACK_IMPORTED_MODULE_4__.ZodParsedType.null) {
            return (0,_helpers_parseUtil_js__WEBPACK_IMPORTED_MODULE_3__.OK)(null);
        }
        return this._def.innerType._parse(input);
    }
    unwrap() {
        return this._def.innerType;
    }
}
ZodNullable.create = (type, params) => {
    return new ZodNullable({
        innerType: type,
        typeName: ZodFirstPartyTypeKind.ZodNullable,
        ...processCreateParams(params),
    });
};
class ZodDefault extends ZodType {
    _parse(input) {
        const { ctx } = this._processInputParams(input);
        let data = ctx.data;
        if (ctx.parsedType === _helpers_util_js__WEBPACK_IMPORTED_MODULE_4__.ZodParsedType.undefined) {
            data = this._def.defaultValue();
        }
        return this._def.innerType._parse({
            data,
            path: ctx.path,
            parent: ctx,
        });
    }
    removeDefault() {
        return this._def.innerType;
    }
}
ZodDefault.create = (type, params) => {
    return new ZodDefault({
        innerType: type,
        typeName: ZodFirstPartyTypeKind.ZodDefault,
        defaultValue: typeof params.default === "function" ? params.default : () => params.default,
        ...processCreateParams(params),
    });
};
class ZodCatch extends ZodType {
    _parse(input) {
        const { ctx } = this._processInputParams(input);
        // newCtx is used to not collect issues from inner types in ctx
        const newCtx = {
            ...ctx,
            common: {
                ...ctx.common,
                issues: [],
            },
        };
        const result = this._def.innerType._parse({
            data: newCtx.data,
            path: newCtx.path,
            parent: {
                ...newCtx,
            },
        });
        if ((0,_helpers_parseUtil_js__WEBPACK_IMPORTED_MODULE_3__.isAsync)(result)) {
            return result.then((result) => {
                return {
                    status: "valid",
                    value: result.status === "valid"
                        ? result.value
                        : this._def.catchValue({
                            get error() {
                                return new _ZodError_js__WEBPACK_IMPORTED_MODULE_0__.ZodError(newCtx.common.issues);
                            },
                            input: newCtx.data,
                        }),
                };
            });
        }
        else {
            return {
                status: "valid",
                value: result.status === "valid"
                    ? result.value
                    : this._def.catchValue({
                        get error() {
                            return new _ZodError_js__WEBPACK_IMPORTED_MODULE_0__.ZodError(newCtx.common.issues);
                        },
                        input: newCtx.data,
                    }),
            };
        }
    }
    removeCatch() {
        return this._def.innerType;
    }
}
ZodCatch.create = (type, params) => {
    return new ZodCatch({
        innerType: type,
        typeName: ZodFirstPartyTypeKind.ZodCatch,
        catchValue: typeof params.catch === "function" ? params.catch : () => params.catch,
        ...processCreateParams(params),
    });
};
class ZodNaN extends ZodType {
    _parse(input) {
        const parsedType = this._getType(input);
        if (parsedType !== _helpers_util_js__WEBPACK_IMPORTED_MODULE_4__.ZodParsedType.nan) {
            const ctx = this._getOrReturnCtx(input);
            (0,_helpers_parseUtil_js__WEBPACK_IMPORTED_MODULE_3__.addIssueToContext)(ctx, {
                code: _ZodError_js__WEBPACK_IMPORTED_MODULE_0__.ZodIssueCode.invalid_type,
                expected: _helpers_util_js__WEBPACK_IMPORTED_MODULE_4__.ZodParsedType.nan,
                received: ctx.parsedType,
            });
            return _helpers_parseUtil_js__WEBPACK_IMPORTED_MODULE_3__.INVALID;
        }
        return { status: "valid", value: input.data };
    }
}
ZodNaN.create = (params) => {
    return new ZodNaN({
        typeName: ZodFirstPartyTypeKind.ZodNaN,
        ...processCreateParams(params),
    });
};
const BRAND = Symbol("zod_brand");
class ZodBranded extends ZodType {
    _parse(input) {
        const { ctx } = this._processInputParams(input);
        const data = ctx.data;
        return this._def.type._parse({
            data,
            path: ctx.path,
            parent: ctx,
        });
    }
    unwrap() {
        return this._def.type;
    }
}
class ZodPipeline extends ZodType {
    _parse(input) {
        const { status, ctx } = this._processInputParams(input);
        if (ctx.common.async) {
            const handleAsync = async () => {
                const inResult = await this._def.in._parseAsync({
                    data: ctx.data,
                    path: ctx.path,
                    parent: ctx,
                });
                if (inResult.status === "aborted")
                    return _helpers_parseUtil_js__WEBPACK_IMPORTED_MODULE_3__.INVALID;
                if (inResult.status === "dirty") {
                    status.dirty();
                    return (0,_helpers_parseUtil_js__WEBPACK_IMPORTED_MODULE_3__.DIRTY)(inResult.value);
                }
                else {
                    return this._def.out._parseAsync({
                        data: inResult.value,
                        path: ctx.path,
                        parent: ctx,
                    });
                }
            };
            return handleAsync();
        }
        else {
            const inResult = this._def.in._parseSync({
                data: ctx.data,
                path: ctx.path,
                parent: ctx,
            });
            if (inResult.status === "aborted")
                return _helpers_parseUtil_js__WEBPACK_IMPORTED_MODULE_3__.INVALID;
            if (inResult.status === "dirty") {
                status.dirty();
                return {
                    status: "dirty",
                    value: inResult.value,
                };
            }
            else {
                return this._def.out._parseSync({
                    data: inResult.value,
                    path: ctx.path,
                    parent: ctx,
                });
            }
        }
    }
    static create(a, b) {
        return new ZodPipeline({
            in: a,
            out: b,
            typeName: ZodFirstPartyTypeKind.ZodPipeline,
        });
    }
}
class ZodReadonly extends ZodType {
    _parse(input) {
        const result = this._def.innerType._parse(input);
        const freeze = (data) => {
            if ((0,_helpers_parseUtil_js__WEBPACK_IMPORTED_MODULE_3__.isValid)(data)) {
                data.value = Object.freeze(data.value);
            }
            return data;
        };
        return (0,_helpers_parseUtil_js__WEBPACK_IMPORTED_MODULE_3__.isAsync)(result) ? result.then((data) => freeze(data)) : freeze(result);
    }
    unwrap() {
        return this._def.innerType;
    }
}
ZodReadonly.create = (type, params) => {
    return new ZodReadonly({
        innerType: type,
        typeName: ZodFirstPartyTypeKind.ZodReadonly,
        ...processCreateParams(params),
    });
};
////////////////////////////////////////
////////////////////////////////////////
//////////                    //////////
//////////      z.custom      //////////
//////////                    //////////
////////////////////////////////////////
////////////////////////////////////////
function cleanParams(params, data) {
    const p = typeof params === "function" ? params(data) : typeof params === "string" ? { message: params } : params;
    const p2 = typeof p === "string" ? { message: p } : p;
    return p2;
}
function custom(check, _params = {}, 
/**
 * @deprecated
 *
 * Pass `fatal` into the params object instead:
 *
 * ```ts
 * z.string().custom((val) => val.length > 5, { fatal: false })
 * ```
 *
 */
fatal) {
    if (check)
        return ZodAny.create().superRefine((data, ctx) => {
            const r = check(data);
            if (r instanceof Promise) {
                return r.then((r) => {
                    if (!r) {
                        const params = cleanParams(_params, data);
                        const _fatal = params.fatal ?? fatal ?? true;
                        ctx.addIssue({ code: "custom", ...params, fatal: _fatal });
                    }
                });
            }
            if (!r) {
                const params = cleanParams(_params, data);
                const _fatal = params.fatal ?? fatal ?? true;
                ctx.addIssue({ code: "custom", ...params, fatal: _fatal });
            }
            return;
        });
    return ZodAny.create();
}

const late = {
    object: ZodObject.lazycreate,
};
var ZodFirstPartyTypeKind;
(function (ZodFirstPartyTypeKind) {
    ZodFirstPartyTypeKind["ZodString"] = "ZodString";
    ZodFirstPartyTypeKind["ZodNumber"] = "ZodNumber";
    ZodFirstPartyTypeKind["ZodNaN"] = "ZodNaN";
    ZodFirstPartyTypeKind["ZodBigInt"] = "ZodBigInt";
    ZodFirstPartyTypeKind["ZodBoolean"] = "ZodBoolean";
    ZodFirstPartyTypeKind["ZodDate"] = "ZodDate";
    ZodFirstPartyTypeKind["ZodSymbol"] = "ZodSymbol";
    ZodFirstPartyTypeKind["ZodUndefined"] = "ZodUndefined";
    ZodFirstPartyTypeKind["ZodNull"] = "ZodNull";
    ZodFirstPartyTypeKind["ZodAny"] = "ZodAny";
    ZodFirstPartyTypeKind["ZodUnknown"] = "ZodUnknown";
    ZodFirstPartyTypeKind["ZodNever"] = "ZodNever";
    ZodFirstPartyTypeKind["ZodVoid"] = "ZodVoid";
    ZodFirstPartyTypeKind["ZodArray"] = "ZodArray";
    ZodFirstPartyTypeKind["ZodObject"] = "ZodObject";
    ZodFirstPartyTypeKind["ZodUnion"] = "ZodUnion";
    ZodFirstPartyTypeKind["ZodDiscriminatedUnion"] = "ZodDiscriminatedUnion";
    ZodFirstPartyTypeKind["ZodIntersection"] = "ZodIntersection";
    ZodFirstPartyTypeKind["ZodTuple"] = "ZodTuple";
    ZodFirstPartyTypeKind["ZodRecord"] = "ZodRecord";
    ZodFirstPartyTypeKind["ZodMap"] = "ZodMap";
    ZodFirstPartyTypeKind["ZodSet"] = "ZodSet";
    ZodFirstPartyTypeKind["ZodFunction"] = "ZodFunction";
    ZodFirstPartyTypeKind["ZodLazy"] = "ZodLazy";
    ZodFirstPartyTypeKind["ZodLiteral"] = "ZodLiteral";
    ZodFirstPartyTypeKind["ZodEnum"] = "ZodEnum";
    ZodFirstPartyTypeKind["ZodEffects"] = "ZodEffects";
    ZodFirstPartyTypeKind["ZodNativeEnum"] = "ZodNativeEnum";
    ZodFirstPartyTypeKind["ZodOptional"] = "ZodOptional";
    ZodFirstPartyTypeKind["ZodNullable"] = "ZodNullable";
    ZodFirstPartyTypeKind["ZodDefault"] = "ZodDefault";
    ZodFirstPartyTypeKind["ZodCatch"] = "ZodCatch";
    ZodFirstPartyTypeKind["ZodPromise"] = "ZodPromise";
    ZodFirstPartyTypeKind["ZodBranded"] = "ZodBranded";
    ZodFirstPartyTypeKind["ZodPipeline"] = "ZodPipeline";
    ZodFirstPartyTypeKind["ZodReadonly"] = "ZodReadonly";
})(ZodFirstPartyTypeKind || (ZodFirstPartyTypeKind = {}));
// requires TS 4.4+
class Class {
    constructor(..._) { }
}
const instanceOfType = (
// const instanceOfType = <T extends new (...args: any[]) => any>(
cls, params = {
    message: `Input not instance of ${cls.name}`,
}) => custom((data) => data instanceof cls, params);
const stringType = ZodString.create;
const numberType = ZodNumber.create;
const nanType = ZodNaN.create;
const bigIntType = ZodBigInt.create;
const booleanType = ZodBoolean.create;
const dateType = ZodDate.create;
const symbolType = ZodSymbol.create;
const undefinedType = ZodUndefined.create;
const nullType = ZodNull.create;
const anyType = ZodAny.create;
const unknownType = ZodUnknown.create;
const neverType = ZodNever.create;
const voidType = ZodVoid.create;
const arrayType = ZodArray.create;
const objectType = ZodObject.create;
const strictObjectType = ZodObject.strictCreate;
const unionType = ZodUnion.create;
const discriminatedUnionType = ZodDiscriminatedUnion.create;
const intersectionType = ZodIntersection.create;
const tupleType = ZodTuple.create;
const recordType = ZodRecord.create;
const mapType = ZodMap.create;
const setType = ZodSet.create;
const functionType = ZodFunction.create;
const lazyType = ZodLazy.create;
const literalType = ZodLiteral.create;
const enumType = ZodEnum.create;
const nativeEnumType = ZodNativeEnum.create;
const promiseType = ZodPromise.create;
const effectsType = ZodEffects.create;
const optionalType = ZodOptional.create;
const nullableType = ZodNullable.create;
const preprocessType = ZodEffects.createWithPreprocess;
const pipelineType = ZodPipeline.create;
const ostring = () => stringType().optional();
const onumber = () => numberType().optional();
const oboolean = () => booleanType().optional();
const coerce = {
    string: ((arg) => ZodString.create({ ...arg, coerce: true })),
    number: ((arg) => ZodNumber.create({ ...arg, coerce: true })),
    boolean: ((arg) => ZodBoolean.create({
        ...arg,
        coerce: true,
    })),
    bigint: ((arg) => ZodBigInt.create({ ...arg, coerce: true })),
    date: ((arg) => ZodDate.create({ ...arg, coerce: true })),
};

const NEVER = _helpers_parseUtil_js__WEBPACK_IMPORTED_MODULE_3__.INVALID;


/***/ })

}]);
//# sourceMappingURL=vendors-node_modules_pnpm_hookform_resolvers_5_0_1_r_b279eeaaeb6873fc01f42207d25bc29d_node_mo-2c6d34.iframe.bundle.js.map