{"version": 3, "file": "vendors-node_modules_pnpm_lucide-react_0_511_0_react_19_1_0_node_modules_lucide-react_dist_es-981abe.iframe.bundle.js", "mappings": ";;;;;;;;;;;;;;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;;;;;;AC1CA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;;AC7BA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;;;;ACpBA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;;;;;;ACpBA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC5BA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "sources": ["webpack://storybook/../../node_modules/.pnpm/lucide-react@0.511.0_react@19.1.0/node_modules/lucide-react/dist/esm/Icon.js", "webpack://storybook/../../node_modules/.pnpm/lucide-react@0.511.0_react@19.1.0/node_modules/lucide-react/dist/esm/createLucideIcon.js", "webpack://storybook/../../node_modules/.pnpm/lucide-react@0.511.0_react@19.1.0/node_modules/lucide-react/dist/esm/defaultAttributes.js", "webpack://storybook/../../node_modules/.pnpm/lucide-react@0.511.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/grip-vertical.js", "webpack://storybook/../../node_modules/.pnpm/lucide-react@0.511.0_react@19.1.0/node_modules/lucide-react/dist/esm/shared/src/utils.js", "webpack://storybook/../../node_modules/.pnpm/react-resizable-panels@3.0._fb75ad939e2208ddfe4099c2669d1e86/node_modules/react-resizable-panels/dist/react-resizable-panels.browser.development.js"], "sourcesContent": ["/**\n * @license lucide-react v0.511.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport { forwardRef, createElement } from 'react';\nimport defaultAttributes from './defaultAttributes.js';\nimport { mergeClasses, hasA11yProp } from './shared/src/utils.js';\n\nconst Icon = forwardRef(\n  ({\n    color = \"currentColor\",\n    size = 24,\n    strokeWidth = 2,\n    absoluteStrokeWidth,\n    className = \"\",\n    children,\n    iconNode,\n    ...rest\n  }, ref) => createElement(\n    \"svg\",\n    {\n      ref,\n      ...defaultAttributes,\n      width: size,\n      height: size,\n      stroke: color,\n      strokeWidth: absoluteStrokeWidth ? Number(strokeWidth) * 24 / Number(size) : strokeWidth,\n      className: mergeClasses(\"lucide\", className),\n      ...!children && !hasA11yProp(rest) && { \"aria-hidden\": \"true\" },\n      ...rest\n    },\n    [\n      ...iconNode.map(([tag, attrs]) => createElement(tag, attrs)),\n      ...Array.isArray(children) ? children : [children]\n    ]\n  )\n);\n\nexport { Icon as default };\n//# sourceMappingURL=Icon.js.map\n", "/**\n * @license lucide-react v0.511.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport { forwardRef, createElement } from 'react';\nimport { mergeClasses, toKebabCase, toPascalCase } from './shared/src/utils.js';\nimport Icon from './Icon.js';\n\nconst createLucideIcon = (iconName, iconNode) => {\n  const Component = forwardRef(\n    ({ className, ...props }, ref) => createElement(Icon, {\n      ref,\n      iconNode,\n      className: mergeClasses(\n        `lucide-${toKebabCase(toPascalCase(iconName))}`,\n        `lucide-${iconName}`,\n        className\n      ),\n      ...props\n    })\n  );\n  Component.displayName = toPascalCase(iconName);\n  return Component;\n};\n\nexport { createLucideIcon as default };\n//# sourceMappingURL=createLucideIcon.js.map\n", "/**\n * @license lucide-react v0.511.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nvar defaultAttributes = {\n  xmlns: \"http://www.w3.org/2000/svg\",\n  width: 24,\n  height: 24,\n  viewBox: \"0 0 24 24\",\n  fill: \"none\",\n  stroke: \"currentColor\",\n  strokeWidth: 2,\n  strokeLinecap: \"round\",\n  strokeLinejoin: \"round\"\n};\n\nexport { defaultAttributes as default };\n//# sourceMappingURL=defaultAttributes.js.map\n", "/**\n * @license lucide-react v0.511.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\n\nconst __iconNode = [\n  [\"circle\", { cx: \"9\", cy: \"12\", r: \"1\", key: \"1vctgf\" }],\n  [\"circle\", { cx: \"9\", cy: \"5\", r: \"1\", key: \"hp0tcf\" }],\n  [\"circle\", { cx: \"9\", cy: \"19\", r: \"1\", key: \"fkjjf6\" }],\n  [\"circle\", { cx: \"15\", cy: \"12\", r: \"1\", key: \"1tmaij\" }],\n  [\"circle\", { cx: \"15\", cy: \"5\", r: \"1\", key: \"19l28e\" }],\n  [\"circle\", { cx: \"15\", cy: \"19\", r: \"1\", key: \"f4zoj3\" }]\n];\nconst GripVertical = createLucideIcon(\"grip-vertical\", __iconNode);\n\nexport { __iconNode, GripVertical as default };\n//# sourceMappingURL=grip-vertical.js.map\n", "/**\n * @license lucide-react v0.511.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nconst toKebabCase = (string) => string.replace(/([a-z0-9])([A-Z])/g, \"$1-$2\").toLowerCase();\nconst toCamelCase = (string) => string.replace(\n  /^([A-Z])|[\\s-_]+(\\w)/g,\n  (match, p1, p2) => p2 ? p2.toUpperCase() : p1.toLowerCase()\n);\nconst toPascalCase = (string) => {\n  const camelCase = toCamelCase(string);\n  return camelCase.charAt(0).toUpperCase() + camelCase.slice(1);\n};\nconst mergeClasses = (...classes) => classes.filter((className, index, array) => {\n  return Boolean(className) && className.trim() !== \"\" && array.indexOf(className) === index;\n}).join(\" \").trim();\nconst hasA11yProp = (props) => {\n  for (const prop in props) {\n    if (prop.startsWith(\"aria-\") || prop === \"role\" || prop === \"title\") {\n      return true;\n    }\n  }\n};\n\nexport { hasA11yProp, mergeClasses, toCamelCase, toKebabCase, toPascalCase };\n//# sourceMappingURL=utils.js.map\n", "import * as React from 'react';\nimport { createContext, useLayoutEffect, useRef, forwardRef, createElement, useContext, useImperativeHandle, useState, useCallback, useEffect, useMemo } from 'react';\n\n// The \"contextmenu\" event is not supported as a PointerEvent in all browsers yet, so MouseEvent still need to be handled\n\nconst PanelGroupContext = createContext(null);\nPanelGroupContext.displayName = \"PanelGroupContext\";\n\nconst DATA_ATTRIBUTES = {\n  group: \"data-panel-group\",\n  groupDirection: \"data-panel-group-direction\",\n  groupId: \"data-panel-group-id\",\n  panel: \"data-panel\",\n  panelCollapsible: \"data-panel-collapsible\",\n  panelId: \"data-panel-id\",\n  panelSize: \"data-panel-size\",\n  resizeHandle: \"data-resize-handle\",\n  resizeHandleActive: \"data-resize-handle-active\",\n  resizeHandleEnabled: \"data-panel-resize-handle-enabled\",\n  resizeHandleId: \"data-panel-resize-handle-id\",\n  resizeHandleState: \"data-resize-handle-state\"\n};\nconst PRECISION = 10;\n\nconst useIsomorphicLayoutEffect = useLayoutEffect ;\n\nconst useId = React[\"useId\".toString()];\nconst wrappedUseId = typeof useId === \"function\" ? useId : () => null;\nlet counter = 0;\nfunction useUniqueId(idFromParams = null) {\n  const idFromUseId = wrappedUseId();\n  const idRef = useRef(idFromParams || idFromUseId || null);\n  if (idRef.current === null) {\n    idRef.current = \"\" + counter++;\n  }\n  return idFromParams !== null && idFromParams !== void 0 ? idFromParams : idRef.current;\n}\n\nfunction PanelWithForwardedRef({\n  children,\n  className: classNameFromProps = \"\",\n  collapsedSize,\n  collapsible,\n  defaultSize,\n  forwardedRef,\n  id: idFromProps,\n  maxSize,\n  minSize,\n  onCollapse,\n  onExpand,\n  onResize,\n  order,\n  style: styleFromProps,\n  tagName: Type = \"div\",\n  ...rest\n}) {\n  const context = useContext(PanelGroupContext);\n  if (context === null) {\n    throw Error(`Panel components must be rendered within a PanelGroup container`);\n  }\n  const {\n    collapsePanel,\n    expandPanel,\n    getPanelSize,\n    getPanelStyle,\n    groupId,\n    isPanelCollapsed,\n    reevaluatePanelConstraints,\n    registerPanel,\n    resizePanel,\n    unregisterPanel\n  } = context;\n  const panelId = useUniqueId(idFromProps);\n  const panelDataRef = useRef({\n    callbacks: {\n      onCollapse,\n      onExpand,\n      onResize\n    },\n    constraints: {\n      collapsedSize,\n      collapsible,\n      defaultSize,\n      maxSize,\n      minSize\n    },\n    id: panelId,\n    idIsFromProps: idFromProps !== undefined,\n    order\n  });\n  const devWarningsRef = useRef({\n    didLogMissingDefaultSizeWarning: false\n  });\n\n  // Normally we wouldn't log a warning during render,\n  // but effects don't run on the server, so we can't do it there\n  {\n    if (!devWarningsRef.current.didLogMissingDefaultSizeWarning) ;\n  }\n  useIsomorphicLayoutEffect(() => {\n    const {\n      callbacks,\n      constraints\n    } = panelDataRef.current;\n    const prevConstraints = {\n      ...constraints\n    };\n    panelDataRef.current.id = panelId;\n    panelDataRef.current.idIsFromProps = idFromProps !== undefined;\n    panelDataRef.current.order = order;\n    callbacks.onCollapse = onCollapse;\n    callbacks.onExpand = onExpand;\n    callbacks.onResize = onResize;\n    constraints.collapsedSize = collapsedSize;\n    constraints.collapsible = collapsible;\n    constraints.defaultSize = defaultSize;\n    constraints.maxSize = maxSize;\n    constraints.minSize = minSize;\n\n    // If constraints have changed, we should revisit panel sizes.\n    // This is uncommon but may happen if people are trying to implement pixel based constraints.\n    if (prevConstraints.collapsedSize !== constraints.collapsedSize || prevConstraints.collapsible !== constraints.collapsible || prevConstraints.maxSize !== constraints.maxSize || prevConstraints.minSize !== constraints.minSize) {\n      reevaluatePanelConstraints(panelDataRef.current, prevConstraints);\n    }\n  });\n  useIsomorphicLayoutEffect(() => {\n    const panelData = panelDataRef.current;\n    registerPanel(panelData);\n    return () => {\n      unregisterPanel(panelData);\n    };\n  }, [order, panelId, registerPanel, unregisterPanel]);\n  useImperativeHandle(forwardedRef, () => ({\n    collapse: () => {\n      collapsePanel(panelDataRef.current);\n    },\n    expand: minSize => {\n      expandPanel(panelDataRef.current, minSize);\n    },\n    getId() {\n      return panelId;\n    },\n    getSize() {\n      return getPanelSize(panelDataRef.current);\n    },\n    isCollapsed() {\n      return isPanelCollapsed(panelDataRef.current);\n    },\n    isExpanded() {\n      return !isPanelCollapsed(panelDataRef.current);\n    },\n    resize: size => {\n      resizePanel(panelDataRef.current, size);\n    }\n  }), [collapsePanel, expandPanel, getPanelSize, isPanelCollapsed, panelId, resizePanel]);\n  const style = getPanelStyle(panelDataRef.current, defaultSize);\n  return createElement(Type, {\n    ...rest,\n    children,\n    className: classNameFromProps,\n    id: panelId,\n    style: {\n      ...style,\n      ...styleFromProps\n    },\n    // CSS selectors\n    [DATA_ATTRIBUTES.groupId]: groupId,\n    [DATA_ATTRIBUTES.panel]: \"\",\n    [DATA_ATTRIBUTES.panelCollapsible]: collapsible || undefined,\n    [DATA_ATTRIBUTES.panelId]: panelId,\n    [DATA_ATTRIBUTES.panelSize]: parseFloat(\"\" + style.flexGrow).toFixed(1)\n  });\n}\nconst Panel = forwardRef((props, ref) => createElement(PanelWithForwardedRef, {\n  ...props,\n  forwardedRef: ref\n}));\nPanelWithForwardedRef.displayName = \"Panel\";\nPanel.displayName = \"forwardRef(Panel)\";\n\nlet nonce;\nfunction getNonce() {\n  return nonce;\n}\nfunction setNonce(value) {\n  nonce = value;\n}\n\nlet currentCursorStyle = null;\nlet enabled = true;\nlet prevRuleIndex = -1;\nlet styleElement = null;\nfunction disableGlobalCursorStyles() {\n  enabled = false;\n}\nfunction enableGlobalCursorStyles() {\n  enabled = true;\n}\nfunction getCursorStyle(state, constraintFlags) {\n  if (constraintFlags) {\n    const horizontalMin = (constraintFlags & EXCEEDED_HORIZONTAL_MIN) !== 0;\n    const horizontalMax = (constraintFlags & EXCEEDED_HORIZONTAL_MAX) !== 0;\n    const verticalMin = (constraintFlags & EXCEEDED_VERTICAL_MIN) !== 0;\n    const verticalMax = (constraintFlags & EXCEEDED_VERTICAL_MAX) !== 0;\n    if (horizontalMin) {\n      if (verticalMin) {\n        return \"se-resize\";\n      } else if (verticalMax) {\n        return \"ne-resize\";\n      } else {\n        return \"e-resize\";\n      }\n    } else if (horizontalMax) {\n      if (verticalMin) {\n        return \"sw-resize\";\n      } else if (verticalMax) {\n        return \"nw-resize\";\n      } else {\n        return \"w-resize\";\n      }\n    } else if (verticalMin) {\n      return \"s-resize\";\n    } else if (verticalMax) {\n      return \"n-resize\";\n    }\n  }\n  switch (state) {\n    case \"horizontal\":\n      return \"ew-resize\";\n    case \"intersection\":\n      return \"move\";\n    case \"vertical\":\n      return \"ns-resize\";\n  }\n}\nfunction resetGlobalCursorStyle() {\n  if (styleElement !== null) {\n    document.head.removeChild(styleElement);\n    currentCursorStyle = null;\n    styleElement = null;\n    prevRuleIndex = -1;\n  }\n}\nfunction setGlobalCursorStyle(state, constraintFlags) {\n  var _styleElement$sheet$i, _styleElement$sheet2;\n  if (!enabled) {\n    return;\n  }\n  const style = getCursorStyle(state, constraintFlags);\n  if (currentCursorStyle === style) {\n    return;\n  }\n  currentCursorStyle = style;\n  if (styleElement === null) {\n    styleElement = document.createElement(\"style\");\n    const nonce = getNonce();\n    if (nonce) {\n      styleElement.setAttribute(\"nonce\", nonce);\n    }\n    document.head.appendChild(styleElement);\n  }\n  if (prevRuleIndex >= 0) {\n    var _styleElement$sheet;\n    (_styleElement$sheet = styleElement.sheet) === null || _styleElement$sheet === void 0 ? void 0 : _styleElement$sheet.removeRule(prevRuleIndex);\n  }\n  prevRuleIndex = (_styleElement$sheet$i = (_styleElement$sheet2 = styleElement.sheet) === null || _styleElement$sheet2 === void 0 ? void 0 : _styleElement$sheet2.insertRule(`*{cursor: ${style} !important;}`)) !== null && _styleElement$sheet$i !== void 0 ? _styleElement$sheet$i : -1;\n}\n\nfunction isKeyDown(event) {\n  return event.type === \"keydown\";\n}\nfunction isPointerEvent(event) {\n  return event.type.startsWith(\"pointer\");\n}\nfunction isMouseEvent(event) {\n  return event.type.startsWith(\"mouse\");\n}\n\nfunction getResizeEventCoordinates(event) {\n  if (isPointerEvent(event)) {\n    if (event.isPrimary) {\n      return {\n        x: event.clientX,\n        y: event.clientY\n      };\n    }\n  } else if (isMouseEvent(event)) {\n    return {\n      x: event.clientX,\n      y: event.clientY\n    };\n  }\n  return {\n    x: Infinity,\n    y: Infinity\n  };\n}\n\nfunction getInputType() {\n  if (typeof matchMedia === \"function\") {\n    return matchMedia(\"(pointer:coarse)\").matches ? \"coarse\" : \"fine\";\n  }\n}\n\nfunction intersects(rectOne, rectTwo, strict) {\n  if (strict) {\n    return rectOne.x < rectTwo.x + rectTwo.width && rectOne.x + rectOne.width > rectTwo.x && rectOne.y < rectTwo.y + rectTwo.height && rectOne.y + rectOne.height > rectTwo.y;\n  } else {\n    return rectOne.x <= rectTwo.x + rectTwo.width && rectOne.x + rectOne.width >= rectTwo.x && rectOne.y <= rectTwo.y + rectTwo.height && rectOne.y + rectOne.height >= rectTwo.y;\n  }\n}\n\n// Forked from NPM stacking-order@2.0.0\n\n/**\n * Determine which of two nodes appears in front of the other —\n * if `a` is in front, returns 1, otherwise returns -1\n * @param {HTMLElement | SVGElement} a\n * @param {HTMLElement | SVGElement} b\n */\nfunction compare(a, b) {\n  if (a === b) throw new Error(\"Cannot compare node with itself\");\n  const ancestors = {\n    a: get_ancestors(a),\n    b: get_ancestors(b)\n  };\n  let common_ancestor;\n\n  // remove shared ancestors\n  while (ancestors.a.at(-1) === ancestors.b.at(-1)) {\n    a = ancestors.a.pop();\n    b = ancestors.b.pop();\n    common_ancestor = a;\n  }\n  assert(common_ancestor, \"Stacking order can only be calculated for elements with a common ancestor\");\n  const z_indexes = {\n    a: get_z_index(find_stacking_context(ancestors.a)),\n    b: get_z_index(find_stacking_context(ancestors.b))\n  };\n  if (z_indexes.a === z_indexes.b) {\n    const children = common_ancestor.childNodes;\n    const furthest_ancestors = {\n      a: ancestors.a.at(-1),\n      b: ancestors.b.at(-1)\n    };\n    let i = children.length;\n    while (i--) {\n      const child = children[i];\n      if (child === furthest_ancestors.a) return 1;\n      if (child === furthest_ancestors.b) return -1;\n    }\n  }\n  return Math.sign(z_indexes.a - z_indexes.b);\n}\nconst props = /\\b(?:position|zIndex|opacity|transform|webkitTransform|mixBlendMode|filter|webkitFilter|isolation)\\b/;\n\n/** @param {HTMLElement | SVGElement} node */\nfunction is_flex_item(node) {\n  var _get_parent;\n  // @ts-ignore\n  const display = getComputedStyle((_get_parent = get_parent(node)) !== null && _get_parent !== void 0 ? _get_parent : node).display;\n  return display === \"flex\" || display === \"inline-flex\";\n}\n\n/** @param {HTMLElement | SVGElement} node */\nfunction creates_stacking_context(node) {\n  const style = getComputedStyle(node);\n\n  // https://developer.mozilla.org/en-US/docs/Web/CSS/CSS_Positioning/Understanding_z_index/The_stacking_context\n  if (style.position === \"fixed\") return true;\n  // Forked to fix upstream bug https://github.com/Rich-Harris/stacking-order/issues/3\n  // if (\n  //   (style.zIndex !== \"auto\" && style.position !== \"static\") ||\n  //   is_flex_item(node)\n  // )\n  if (style.zIndex !== \"auto\" && (style.position !== \"static\" || is_flex_item(node))) return true;\n  if (+style.opacity < 1) return true;\n  if (\"transform\" in style && style.transform !== \"none\") return true;\n  if (\"webkitTransform\" in style && style.webkitTransform !== \"none\") return true;\n  if (\"mixBlendMode\" in style && style.mixBlendMode !== \"normal\") return true;\n  if (\"filter\" in style && style.filter !== \"none\") return true;\n  if (\"webkitFilter\" in style && style.webkitFilter !== \"none\") return true;\n  if (\"isolation\" in style && style.isolation === \"isolate\") return true;\n  if (props.test(style.willChange)) return true;\n  // @ts-expect-error\n  if (style.webkitOverflowScrolling === \"touch\") return true;\n  return false;\n}\n\n/** @param {(HTMLElement| SVGElement)[]} nodes */\nfunction find_stacking_context(nodes) {\n  let i = nodes.length;\n  while (i--) {\n    const node = nodes[i];\n    assert(node, \"Missing node\");\n    if (creates_stacking_context(node)) return node;\n  }\n  return null;\n}\n\n/** @param {HTMLElement | SVGElement} node */\nfunction get_z_index(node) {\n  return node && Number(getComputedStyle(node).zIndex) || 0;\n}\n\n/** @param {HTMLElement} node */\nfunction get_ancestors(node) {\n  const ancestors = [];\n  while (node) {\n    ancestors.push(node);\n    // @ts-ignore\n    node = get_parent(node);\n  }\n  return ancestors; // [ node, ... <body>, <html>, document ]\n}\n\n/** @param {HTMLElement} node */\nfunction get_parent(node) {\n  const {\n    parentNode\n  } = node;\n  if (parentNode && parentNode instanceof ShadowRoot) {\n    return parentNode.host;\n  }\n  return parentNode;\n}\n\nconst EXCEEDED_HORIZONTAL_MIN = 0b0001;\nconst EXCEEDED_HORIZONTAL_MAX = 0b0010;\nconst EXCEEDED_VERTICAL_MIN = 0b0100;\nconst EXCEEDED_VERTICAL_MAX = 0b1000;\nconst isCoarsePointer = getInputType() === \"coarse\";\nlet intersectingHandles = [];\nlet isPointerDown = false;\nlet ownerDocumentCounts = new Map();\nlet panelConstraintFlags = new Map();\nconst registeredResizeHandlers = new Set();\nfunction registerResizeHandle(resizeHandleId, element, direction, hitAreaMargins, setResizeHandlerState) {\n  var _ownerDocumentCounts$;\n  const {\n    ownerDocument\n  } = element;\n  const data = {\n    direction,\n    element,\n    hitAreaMargins,\n    setResizeHandlerState\n  };\n  const count = (_ownerDocumentCounts$ = ownerDocumentCounts.get(ownerDocument)) !== null && _ownerDocumentCounts$ !== void 0 ? _ownerDocumentCounts$ : 0;\n  ownerDocumentCounts.set(ownerDocument, count + 1);\n  registeredResizeHandlers.add(data);\n  updateListeners();\n  return function unregisterResizeHandle() {\n    var _ownerDocumentCounts$2;\n    panelConstraintFlags.delete(resizeHandleId);\n    registeredResizeHandlers.delete(data);\n    const count = (_ownerDocumentCounts$2 = ownerDocumentCounts.get(ownerDocument)) !== null && _ownerDocumentCounts$2 !== void 0 ? _ownerDocumentCounts$2 : 1;\n    ownerDocumentCounts.set(ownerDocument, count - 1);\n    updateListeners();\n    if (count === 1) {\n      ownerDocumentCounts.delete(ownerDocument);\n    }\n\n    // If the resize handle that is currently unmounting is intersecting with the pointer,\n    // update the global pointer to account for the change\n    if (intersectingHandles.includes(data)) {\n      const index = intersectingHandles.indexOf(data);\n      if (index >= 0) {\n        intersectingHandles.splice(index, 1);\n      }\n      updateCursor();\n\n      // Also instruct the handle to stop dragging; this prevents the parent group from being left in an inconsistent state\n      // See github.com/bvaughn/react-resizable-panels/issues/402\n      setResizeHandlerState(\"up\", true, null);\n    }\n  };\n}\nfunction handlePointerDown(event) {\n  const {\n    target\n  } = event;\n  const {\n    x,\n    y\n  } = getResizeEventCoordinates(event);\n  isPointerDown = true;\n  recalculateIntersectingHandles({\n    target,\n    x,\n    y\n  });\n  updateListeners();\n  if (intersectingHandles.length > 0) {\n    updateResizeHandlerStates(\"down\", event);\n    event.preventDefault();\n    if (!isWithinResizeHandle(target)) {\n      event.stopImmediatePropagation();\n    }\n  }\n}\nfunction handlePointerMove(event) {\n  const {\n    x,\n    y\n  } = getResizeEventCoordinates(event);\n\n  // Edge case (see #340)\n  // Detect when the pointer has been released outside an iframe on a different domain\n  if (isPointerDown && event.buttons === 0) {\n    isPointerDown = false;\n    updateResizeHandlerStates(\"up\", event);\n  }\n  if (!isPointerDown) {\n    const {\n      target\n    } = event;\n\n    // Recalculate intersecting handles whenever the pointer moves, except if it has already been pressed\n    // at that point, the handles may not move with the pointer (depending on constraints)\n    // but the same set of active handles should be locked until the pointer is released\n    recalculateIntersectingHandles({\n      target,\n      x,\n      y\n    });\n  }\n  updateResizeHandlerStates(\"move\", event);\n\n  // Update cursor based on return value(s) from active handles\n  updateCursor();\n  if (intersectingHandles.length > 0) {\n    event.preventDefault();\n  }\n}\nfunction handlePointerUp(event) {\n  const {\n    target\n  } = event;\n  const {\n    x,\n    y\n  } = getResizeEventCoordinates(event);\n  panelConstraintFlags.clear();\n  isPointerDown = false;\n  if (intersectingHandles.length > 0) {\n    event.preventDefault();\n    if (!isWithinResizeHandle(target)) {\n      event.stopImmediatePropagation();\n    }\n  }\n  updateResizeHandlerStates(\"up\", event);\n  recalculateIntersectingHandles({\n    target,\n    x,\n    y\n  });\n  updateCursor();\n  updateListeners();\n}\nfunction isWithinResizeHandle(element) {\n  let currentElement = element;\n  while (currentElement) {\n    if (currentElement.hasAttribute(DATA_ATTRIBUTES.resizeHandle)) {\n      return true;\n    }\n    currentElement = currentElement.parentElement;\n  }\n  return false;\n}\nfunction recalculateIntersectingHandles({\n  target,\n  x,\n  y\n}) {\n  intersectingHandles.splice(0);\n  let targetElement = null;\n  if (target instanceof HTMLElement || target instanceof SVGElement) {\n    targetElement = target;\n  }\n  registeredResizeHandlers.forEach(data => {\n    const {\n      element: dragHandleElement,\n      hitAreaMargins\n    } = data;\n    const dragHandleRect = dragHandleElement.getBoundingClientRect();\n    const {\n      bottom,\n      left,\n      right,\n      top\n    } = dragHandleRect;\n    const margin = isCoarsePointer ? hitAreaMargins.coarse : hitAreaMargins.fine;\n    const eventIntersects = x >= left - margin && x <= right + margin && y >= top - margin && y <= bottom + margin;\n    if (eventIntersects) {\n      // TRICKY\n      // We listen for pointers events at the root in order to support hit area margins\n      // (determining when the pointer is close enough to an element to be considered a \"hit\")\n      // Clicking on an element \"above\" a handle (e.g. a modal) should prevent a hit though\n      // so at this point we need to compare stacking order of a potentially intersecting drag handle,\n      // and the element that was actually clicked/touched\n      if (targetElement !== null && document.contains(targetElement) && dragHandleElement !== targetElement && !dragHandleElement.contains(targetElement) && !targetElement.contains(dragHandleElement) &&\n      // Calculating stacking order has a cost, so we should avoid it if possible\n      // That is why we only check potentially intersecting handles,\n      // and why we skip if the event target is within the handle's DOM\n      compare(targetElement, dragHandleElement) > 0) {\n        // If the target is above the drag handle, then we also need to confirm they overlap\n        // If they are beside each other (e.g. a panel and its drag handle) then the handle is still interactive\n        //\n        // It's not enough to compare only the target\n        // The target might be a small element inside of a larger container\n        // (For example, a SPAN or a DIV inside of a larger modal dialog)\n        let currentElement = targetElement;\n        let didIntersect = false;\n        while (currentElement) {\n          if (currentElement.contains(dragHandleElement)) {\n            break;\n          } else if (intersects(currentElement.getBoundingClientRect(), dragHandleRect, true)) {\n            didIntersect = true;\n            break;\n          }\n          currentElement = currentElement.parentElement;\n        }\n        if (didIntersect) {\n          return;\n        }\n      }\n      intersectingHandles.push(data);\n    }\n  });\n}\nfunction reportConstraintsViolation(resizeHandleId, flag) {\n  panelConstraintFlags.set(resizeHandleId, flag);\n}\nfunction updateCursor() {\n  let intersectsHorizontal = false;\n  let intersectsVertical = false;\n  intersectingHandles.forEach(data => {\n    const {\n      direction\n    } = data;\n    if (direction === \"horizontal\") {\n      intersectsHorizontal = true;\n    } else {\n      intersectsVertical = true;\n    }\n  });\n  let constraintFlags = 0;\n  panelConstraintFlags.forEach(flag => {\n    constraintFlags |= flag;\n  });\n  if (intersectsHorizontal && intersectsVertical) {\n    setGlobalCursorStyle(\"intersection\", constraintFlags);\n  } else if (intersectsHorizontal) {\n    setGlobalCursorStyle(\"horizontal\", constraintFlags);\n  } else if (intersectsVertical) {\n    setGlobalCursorStyle(\"vertical\", constraintFlags);\n  } else {\n    resetGlobalCursorStyle();\n  }\n}\nlet listenersAbortController = new AbortController();\nfunction updateListeners() {\n  listenersAbortController.abort();\n  listenersAbortController = new AbortController();\n  const options = {\n    capture: true,\n    signal: listenersAbortController.signal\n  };\n  if (!registeredResizeHandlers.size) {\n    return;\n  }\n  if (isPointerDown) {\n    if (intersectingHandles.length > 0) {\n      ownerDocumentCounts.forEach((count, ownerDocument) => {\n        const {\n          body\n        } = ownerDocument;\n        if (count > 0) {\n          body.addEventListener(\"contextmenu\", handlePointerUp, options);\n          body.addEventListener(\"pointerleave\", handlePointerMove, options);\n          body.addEventListener(\"pointermove\", handlePointerMove, options);\n        }\n      });\n    }\n    ownerDocumentCounts.forEach((_, ownerDocument) => {\n      const {\n        body\n      } = ownerDocument;\n      body.addEventListener(\"pointerup\", handlePointerUp, options);\n      body.addEventListener(\"pointercancel\", handlePointerUp, options);\n    });\n  } else {\n    ownerDocumentCounts.forEach((count, ownerDocument) => {\n      const {\n        body\n      } = ownerDocument;\n      if (count > 0) {\n        body.addEventListener(\"pointerdown\", handlePointerDown, options);\n        body.addEventListener(\"pointermove\", handlePointerMove, options);\n      }\n    });\n  }\n}\nfunction updateResizeHandlerStates(action, event) {\n  registeredResizeHandlers.forEach(data => {\n    const {\n      setResizeHandlerState\n    } = data;\n    const isActive = intersectingHandles.includes(data);\n    setResizeHandlerState(action, isActive, event);\n  });\n}\n\nfunction useForceUpdate() {\n  const [_, setCount] = useState(0);\n  return useCallback(() => setCount(prevCount => prevCount + 1), []);\n}\n\nfunction assert(expectedCondition, message) {\n  if (!expectedCondition) {\n    console.error(message);\n    throw Error(message);\n  }\n}\n\nfunction fuzzyCompareNumbers(actual, expected, fractionDigits = PRECISION) {\n  if (actual.toFixed(fractionDigits) === expected.toFixed(fractionDigits)) {\n    return 0;\n  } else {\n    return actual > expected ? 1 : -1;\n  }\n}\nfunction fuzzyNumbersEqual$1(actual, expected, fractionDigits = PRECISION) {\n  return fuzzyCompareNumbers(actual, expected, fractionDigits) === 0;\n}\n\nfunction fuzzyNumbersEqual(actual, expected, fractionDigits) {\n  return fuzzyCompareNumbers(actual, expected, fractionDigits) === 0;\n}\n\nfunction fuzzyLayoutsEqual(actual, expected, fractionDigits) {\n  if (actual.length !== expected.length) {\n    return false;\n  }\n  for (let index = 0; index < actual.length; index++) {\n    const actualSize = actual[index];\n    const expectedSize = expected[index];\n    if (!fuzzyNumbersEqual(actualSize, expectedSize, fractionDigits)) {\n      return false;\n    }\n  }\n  return true;\n}\n\n// Panel size must be in percentages; pixel values should be pre-converted\nfunction resizePanel({\n  panelConstraints: panelConstraintsArray,\n  panelIndex,\n  size\n}) {\n  const panelConstraints = panelConstraintsArray[panelIndex];\n  assert(panelConstraints != null, `Panel constraints not found for index ${panelIndex}`);\n  let {\n    collapsedSize = 0,\n    collapsible,\n    maxSize = 100,\n    minSize = 0\n  } = panelConstraints;\n  if (fuzzyCompareNumbers(size, minSize) < 0) {\n    if (collapsible) {\n      // Collapsible panels should snap closed or open only once they cross the halfway point between collapsed and min size.\n      const halfwayPoint = (collapsedSize + minSize) / 2;\n      if (fuzzyCompareNumbers(size, halfwayPoint) < 0) {\n        size = collapsedSize;\n      } else {\n        size = minSize;\n      }\n    } else {\n      size = minSize;\n    }\n  }\n  size = Math.min(maxSize, size);\n  size = parseFloat(size.toFixed(PRECISION));\n  return size;\n}\n\n// All units must be in percentages; pixel values should be pre-converted\nfunction adjustLayoutByDelta({\n  delta,\n  initialLayout,\n  panelConstraints: panelConstraintsArray,\n  pivotIndices,\n  prevLayout,\n  trigger\n}) {\n  if (fuzzyNumbersEqual(delta, 0)) {\n    return initialLayout;\n  }\n  const nextLayout = [...initialLayout];\n  const [firstPivotIndex, secondPivotIndex] = pivotIndices;\n  assert(firstPivotIndex != null, \"Invalid first pivot index\");\n  assert(secondPivotIndex != null, \"Invalid second pivot index\");\n  let deltaApplied = 0;\n\n  // const DEBUG = [];\n  // DEBUG.push(`adjustLayoutByDelta()`);\n  // DEBUG.push(`  initialLayout: ${initialLayout.join(\", \")}`);\n  // DEBUG.push(`  prevLayout: ${prevLayout.join(\", \")}`);\n  // DEBUG.push(`  delta: ${delta}`);\n  // DEBUG.push(`  pivotIndices: ${pivotIndices.join(\", \")}`);\n  // DEBUG.push(`  trigger: ${trigger}`);\n  // DEBUG.push(\"\");\n\n  // A resizing panel affects the panels before or after it.\n  //\n  // A negative delta means the panel(s) immediately after the resize handle should grow/expand by decreasing its offset.\n  // Other panels may also need to shrink/contract (and shift) to make room, depending on the min weights.\n  //\n  // A positive delta means the panel(s) immediately before the resize handle should \"expand\".\n  // This is accomplished by shrinking/contracting (and shifting) one or more of the panels after the resize handle.\n\n  {\n    // If this is a resize triggered by a keyboard event, our logic for expanding/collapsing is different.\n    // We no longer check the halfway threshold because this may prevent the panel from expanding at all.\n    if (trigger === \"keyboard\") {\n      {\n        // Check if we should expand a collapsed panel\n        const index = delta < 0 ? secondPivotIndex : firstPivotIndex;\n        const panelConstraints = panelConstraintsArray[index];\n        assert(panelConstraints, `Panel constraints not found for index ${index}`);\n        const {\n          collapsedSize = 0,\n          collapsible,\n          minSize = 0\n        } = panelConstraints;\n\n        // DEBUG.push(`edge case check 1: ${index}`);\n        // DEBUG.push(`  -> collapsible? ${collapsible}`);\n        if (collapsible) {\n          const prevSize = initialLayout[index];\n          assert(prevSize != null, `Previous layout not found for panel index ${index}`);\n          if (fuzzyNumbersEqual(prevSize, collapsedSize)) {\n            const localDelta = minSize - prevSize;\n            // DEBUG.push(`  -> expand delta: ${localDelta}`);\n\n            if (fuzzyCompareNumbers(localDelta, Math.abs(delta)) > 0) {\n              delta = delta < 0 ? 0 - localDelta : localDelta;\n              // DEBUG.push(`  -> delta: ${delta}`);\n            }\n          }\n        }\n      }\n\n      {\n        // Check if we should collapse a panel at its minimum size\n        const index = delta < 0 ? firstPivotIndex : secondPivotIndex;\n        const panelConstraints = panelConstraintsArray[index];\n        assert(panelConstraints, `No panel constraints found for index ${index}`);\n        const {\n          collapsedSize = 0,\n          collapsible,\n          minSize = 0\n        } = panelConstraints;\n\n        // DEBUG.push(`edge case check 2: ${index}`);\n        // DEBUG.push(`  -> collapsible? ${collapsible}`);\n        if (collapsible) {\n          const prevSize = initialLayout[index];\n          assert(prevSize != null, `Previous layout not found for panel index ${index}`);\n          if (fuzzyNumbersEqual(prevSize, minSize)) {\n            const localDelta = prevSize - collapsedSize;\n            // DEBUG.push(`  -> expand delta: ${localDelta}`);\n\n            if (fuzzyCompareNumbers(localDelta, Math.abs(delta)) > 0) {\n              delta = delta < 0 ? 0 - localDelta : localDelta;\n              // DEBUG.push(`  -> delta: ${delta}`);\n            }\n          }\n        }\n      }\n    }\n    // DEBUG.push(\"\");\n  }\n\n  {\n    // Pre-calculate max available delta in the opposite direction of our pivot.\n    // This will be the maximum amount we're allowed to expand/contract the panels in the primary direction.\n    // If this amount is less than the requested delta, adjust the requested delta.\n    // If this amount is greater than the requested delta, that's useful information too–\n    // as an expanding panel might change from collapsed to min size.\n\n    const increment = delta < 0 ? 1 : -1;\n    let index = delta < 0 ? secondPivotIndex : firstPivotIndex;\n    let maxAvailableDelta = 0;\n\n    // DEBUG.push(\"pre calc...\");\n    while (true) {\n      const prevSize = initialLayout[index];\n      assert(prevSize != null, `Previous layout not found for panel index ${index}`);\n      const maxSafeSize = resizePanel({\n        panelConstraints: panelConstraintsArray,\n        panelIndex: index,\n        size: 100\n      });\n      const delta = maxSafeSize - prevSize;\n      // DEBUG.push(`  ${index}: ${prevSize} -> ${maxSafeSize}`);\n\n      maxAvailableDelta += delta;\n      index += increment;\n      if (index < 0 || index >= panelConstraintsArray.length) {\n        break;\n      }\n    }\n\n    // DEBUG.push(`  -> max available delta: ${maxAvailableDelta}`);\n    const minAbsDelta = Math.min(Math.abs(delta), Math.abs(maxAvailableDelta));\n    delta = delta < 0 ? 0 - minAbsDelta : minAbsDelta;\n    // DEBUG.push(`  -> adjusted delta: ${delta}`);\n    // DEBUG.push(\"\");\n  }\n\n  {\n    // Delta added to a panel needs to be subtracted from other panels (within the constraints that those panels allow).\n\n    const pivotIndex = delta < 0 ? firstPivotIndex : secondPivotIndex;\n    let index = pivotIndex;\n    while (index >= 0 && index < panelConstraintsArray.length) {\n      const deltaRemaining = Math.abs(delta) - Math.abs(deltaApplied);\n      const prevSize = initialLayout[index];\n      assert(prevSize != null, `Previous layout not found for panel index ${index}`);\n      const unsafeSize = prevSize - deltaRemaining;\n      const safeSize = resizePanel({\n        panelConstraints: panelConstraintsArray,\n        panelIndex: index,\n        size: unsafeSize\n      });\n      if (!fuzzyNumbersEqual(prevSize, safeSize)) {\n        deltaApplied += prevSize - safeSize;\n        nextLayout[index] = safeSize;\n        if (deltaApplied.toPrecision(3).localeCompare(Math.abs(delta).toPrecision(3), undefined, {\n          numeric: true\n        }) >= 0) {\n          break;\n        }\n      }\n      if (delta < 0) {\n        index--;\n      } else {\n        index++;\n      }\n    }\n  }\n  // DEBUG.push(`after 1: ${nextLayout.join(\", \")}`);\n  // DEBUG.push(`  deltaApplied: ${deltaApplied}`);\n  // DEBUG.push(\"\");\n\n  // If we were unable to resize any of the panels panels, return the previous state.\n  // This will essentially bailout and ignore e.g. drags past a panel's boundaries\n  if (fuzzyLayoutsEqual(prevLayout, nextLayout)) {\n    // DEBUG.push(`bailout to previous layout: ${prevLayout.join(\", \")}`);\n    // console.log(DEBUG.join(\"\\n\"));\n\n    return prevLayout;\n  }\n  {\n    // Now distribute the applied delta to the panels in the other direction\n    const pivotIndex = delta < 0 ? secondPivotIndex : firstPivotIndex;\n    const prevSize = initialLayout[pivotIndex];\n    assert(prevSize != null, `Previous layout not found for panel index ${pivotIndex}`);\n    const unsafeSize = prevSize + deltaApplied;\n    const safeSize = resizePanel({\n      panelConstraints: panelConstraintsArray,\n      panelIndex: pivotIndex,\n      size: unsafeSize\n    });\n\n    // Adjust the pivot panel before, but only by the amount that surrounding panels were able to shrink/contract.\n    nextLayout[pivotIndex] = safeSize;\n\n    // Edge case where expanding or contracting one panel caused another one to change collapsed state\n    if (!fuzzyNumbersEqual(safeSize, unsafeSize)) {\n      let deltaRemaining = unsafeSize - safeSize;\n      const pivotIndex = delta < 0 ? secondPivotIndex : firstPivotIndex;\n      let index = pivotIndex;\n      while (index >= 0 && index < panelConstraintsArray.length) {\n        const prevSize = nextLayout[index];\n        assert(prevSize != null, `Previous layout not found for panel index ${index}`);\n        const unsafeSize = prevSize + deltaRemaining;\n        const safeSize = resizePanel({\n          panelConstraints: panelConstraintsArray,\n          panelIndex: index,\n          size: unsafeSize\n        });\n        if (!fuzzyNumbersEqual(prevSize, safeSize)) {\n          deltaRemaining -= safeSize - prevSize;\n          nextLayout[index] = safeSize;\n        }\n        if (fuzzyNumbersEqual(deltaRemaining, 0)) {\n          break;\n        }\n        if (delta > 0) {\n          index--;\n        } else {\n          index++;\n        }\n      }\n    }\n  }\n  // DEBUG.push(`after 2: ${nextLayout.join(\", \")}`);\n  // DEBUG.push(`  deltaApplied: ${deltaApplied}`);\n  // DEBUG.push(\"\");\n\n  const totalSize = nextLayout.reduce((total, size) => size + total, 0);\n  // DEBUG.push(`total size: ${totalSize}`);\n\n  // If our new layout doesn't add up to 100%, that means the requested delta can't be applied\n  // In that case, fall back to our most recent valid layout\n  if (!fuzzyNumbersEqual(totalSize, 100)) {\n    // DEBUG.push(`bailout to previous layout: ${prevLayout.join(\", \")}`);\n    // console.log(DEBUG.join(\"\\n\"));\n\n    return prevLayout;\n  }\n\n  // console.log(DEBUG.join(\"\\n\"));\n  return nextLayout;\n}\n\nfunction calculateAriaValues({\n  layout,\n  panelsArray,\n  pivotIndices\n}) {\n  let currentMinSize = 0;\n  let currentMaxSize = 100;\n  let totalMinSize = 0;\n  let totalMaxSize = 0;\n  const firstIndex = pivotIndices[0];\n  assert(firstIndex != null, \"No pivot index found\");\n\n  // A panel's effective min/max sizes also need to account for other panel's sizes.\n  panelsArray.forEach((panelData, index) => {\n    const {\n      constraints\n    } = panelData;\n    const {\n      maxSize = 100,\n      minSize = 0\n    } = constraints;\n    if (index === firstIndex) {\n      currentMinSize = minSize;\n      currentMaxSize = maxSize;\n    } else {\n      totalMinSize += minSize;\n      totalMaxSize += maxSize;\n    }\n  });\n  const valueMax = Math.min(currentMaxSize, 100 - totalMinSize);\n  const valueMin = Math.max(currentMinSize, 100 - totalMaxSize);\n  const valueNow = layout[firstIndex];\n  return {\n    valueMax,\n    valueMin,\n    valueNow\n  };\n}\n\nfunction getResizeHandleElementsForGroup(groupId, scope = document) {\n  return Array.from(scope.querySelectorAll(`[${DATA_ATTRIBUTES.resizeHandleId}][data-panel-group-id=\"${groupId}\"]`));\n}\n\nfunction getResizeHandleElementIndex(groupId, id, scope = document) {\n  const handles = getResizeHandleElementsForGroup(groupId, scope);\n  const index = handles.findIndex(handle => handle.getAttribute(DATA_ATTRIBUTES.resizeHandleId) === id);\n  return index !== null && index !== void 0 ? index : null;\n}\n\nfunction determinePivotIndices(groupId, dragHandleId, panelGroupElement) {\n  const index = getResizeHandleElementIndex(groupId, dragHandleId, panelGroupElement);\n  return index != null ? [index, index + 1] : [-1, -1];\n}\n\nfunction isHTMLElement(target) {\n  if (target instanceof HTMLElement) {\n    return true;\n  }\n\n  // Fallback to duck typing to handle edge case of portals within a popup window\n  return typeof target === \"object\" && target !== null && \"tagName\" in target && \"getAttribute\" in target;\n}\n\nfunction getPanelGroupElement(id, rootElement = document) {\n  // If the root element is the PanelGroup\n  if (isHTMLElement(rootElement) && rootElement.dataset.panelGroupId == id) {\n    return rootElement;\n  }\n\n  // Else query children\n  const element = rootElement.querySelector(`[data-panel-group][data-panel-group-id=\"${id}\"]`);\n  if (element) {\n    return element;\n  }\n  return null;\n}\n\nfunction getResizeHandleElement(id, scope = document) {\n  const element = scope.querySelector(`[${DATA_ATTRIBUTES.resizeHandleId}=\"${id}\"]`);\n  if (element) {\n    return element;\n  }\n  return null;\n}\n\nfunction getResizeHandlePanelIds(groupId, handleId, panelsArray, scope = document) {\n  var _panelsArray$index$id, _panelsArray$index, _panelsArray$id, _panelsArray;\n  const handle = getResizeHandleElement(handleId, scope);\n  const handles = getResizeHandleElementsForGroup(groupId, scope);\n  const index = handle ? handles.indexOf(handle) : -1;\n  const idBefore = (_panelsArray$index$id = (_panelsArray$index = panelsArray[index]) === null || _panelsArray$index === void 0 ? void 0 : _panelsArray$index.id) !== null && _panelsArray$index$id !== void 0 ? _panelsArray$index$id : null;\n  const idAfter = (_panelsArray$id = (_panelsArray = panelsArray[index + 1]) === null || _panelsArray === void 0 ? void 0 : _panelsArray.id) !== null && _panelsArray$id !== void 0 ? _panelsArray$id : null;\n  return [idBefore, idAfter];\n}\n\n// https://www.w3.org/WAI/ARIA/apg/patterns/windowsplitter/\n\nfunction useWindowSplitterPanelGroupBehavior({\n  committedValuesRef,\n  eagerValuesRef,\n  groupId,\n  layout,\n  panelDataArray,\n  panelGroupElement,\n  setLayout\n}) {\n  const devWarningsRef = useRef({\n    didWarnAboutMissingResizeHandle: false\n  });\n  useIsomorphicLayoutEffect(() => {\n    if (!panelGroupElement) {\n      return;\n    }\n    const resizeHandleElements = getResizeHandleElementsForGroup(groupId, panelGroupElement);\n    for (let index = 0; index < panelDataArray.length - 1; index++) {\n      const {\n        valueMax,\n        valueMin,\n        valueNow\n      } = calculateAriaValues({\n        layout,\n        panelsArray: panelDataArray,\n        pivotIndices: [index, index + 1]\n      });\n      const resizeHandleElement = resizeHandleElements[index];\n      if (resizeHandleElement == null) {\n        {\n          const {\n            didWarnAboutMissingResizeHandle\n          } = devWarningsRef.current;\n          if (!didWarnAboutMissingResizeHandle) {\n            devWarningsRef.current.didWarnAboutMissingResizeHandle = true;\n            console.warn(`WARNING: Missing resize handle for PanelGroup \"${groupId}\"`);\n          }\n        }\n      } else {\n        const panelData = panelDataArray[index];\n        assert(panelData, `No panel data found for index \"${index}\"`);\n        resizeHandleElement.setAttribute(\"aria-controls\", panelData.id);\n        resizeHandleElement.setAttribute(\"aria-valuemax\", \"\" + Math.round(valueMax));\n        resizeHandleElement.setAttribute(\"aria-valuemin\", \"\" + Math.round(valueMin));\n        resizeHandleElement.setAttribute(\"aria-valuenow\", valueNow != null ? \"\" + Math.round(valueNow) : \"\");\n      }\n    }\n    return () => {\n      resizeHandleElements.forEach((resizeHandleElement, index) => {\n        resizeHandleElement.removeAttribute(\"aria-controls\");\n        resizeHandleElement.removeAttribute(\"aria-valuemax\");\n        resizeHandleElement.removeAttribute(\"aria-valuemin\");\n        resizeHandleElement.removeAttribute(\"aria-valuenow\");\n      });\n    };\n  }, [groupId, layout, panelDataArray, panelGroupElement]);\n  useEffect(() => {\n    if (!panelGroupElement) {\n      return;\n    }\n    const eagerValues = eagerValuesRef.current;\n    assert(eagerValues, `Eager values not found`);\n    const {\n      panelDataArray\n    } = eagerValues;\n    const groupElement = getPanelGroupElement(groupId, panelGroupElement);\n    assert(groupElement != null, `No group found for id \"${groupId}\"`);\n    const handles = getResizeHandleElementsForGroup(groupId, panelGroupElement);\n    assert(handles, `No resize handles found for group id \"${groupId}\"`);\n    const cleanupFunctions = handles.map(handle => {\n      const handleId = handle.getAttribute(DATA_ATTRIBUTES.resizeHandleId);\n      assert(handleId, `Resize handle element has no handle id attribute`);\n      const [idBefore, idAfter] = getResizeHandlePanelIds(groupId, handleId, panelDataArray, panelGroupElement);\n      if (idBefore == null || idAfter == null) {\n        return () => {};\n      }\n      const onKeyDown = event => {\n        if (event.defaultPrevented) {\n          return;\n        }\n        switch (event.key) {\n          case \"Enter\":\n            {\n              event.preventDefault();\n              const index = panelDataArray.findIndex(panelData => panelData.id === idBefore);\n              if (index >= 0) {\n                const panelData = panelDataArray[index];\n                assert(panelData, `No panel data found for index ${index}`);\n                const size = layout[index];\n                const {\n                  collapsedSize = 0,\n                  collapsible,\n                  minSize = 0\n                } = panelData.constraints;\n                if (size != null && collapsible) {\n                  const nextLayout = adjustLayoutByDelta({\n                    delta: fuzzyNumbersEqual(size, collapsedSize) ? minSize - collapsedSize : collapsedSize - size,\n                    initialLayout: layout,\n                    panelConstraints: panelDataArray.map(panelData => panelData.constraints),\n                    pivotIndices: determinePivotIndices(groupId, handleId, panelGroupElement),\n                    prevLayout: layout,\n                    trigger: \"keyboard\"\n                  });\n                  if (layout !== nextLayout) {\n                    setLayout(nextLayout);\n                  }\n                }\n              }\n              break;\n            }\n        }\n      };\n      handle.addEventListener(\"keydown\", onKeyDown);\n      return () => {\n        handle.removeEventListener(\"keydown\", onKeyDown);\n      };\n    });\n    return () => {\n      cleanupFunctions.forEach(cleanupFunction => cleanupFunction());\n    };\n  }, [panelGroupElement, committedValuesRef, eagerValuesRef, groupId, layout, panelDataArray, setLayout]);\n}\n\nfunction areEqual(arrayA, arrayB) {\n  if (arrayA.length !== arrayB.length) {\n    return false;\n  }\n  for (let index = 0; index < arrayA.length; index++) {\n    if (arrayA[index] !== arrayB[index]) {\n      return false;\n    }\n  }\n  return true;\n}\n\nfunction getResizeEventCursorPosition(direction, event) {\n  const isHorizontal = direction === \"horizontal\";\n  const {\n    x,\n    y\n  } = getResizeEventCoordinates(event);\n  return isHorizontal ? x : y;\n}\n\nfunction calculateDragOffsetPercentage(event, dragHandleId, direction, initialDragState, panelGroupElement) {\n  const isHorizontal = direction === \"horizontal\";\n  const handleElement = getResizeHandleElement(dragHandleId, panelGroupElement);\n  assert(handleElement, `No resize handle element found for id \"${dragHandleId}\"`);\n  const groupId = handleElement.getAttribute(DATA_ATTRIBUTES.groupId);\n  assert(groupId, `Resize handle element has no group id attribute`);\n  let {\n    initialCursorPosition\n  } = initialDragState;\n  const cursorPosition = getResizeEventCursorPosition(direction, event);\n  const groupElement = getPanelGroupElement(groupId, panelGroupElement);\n  assert(groupElement, `No group element found for id \"${groupId}\"`);\n  const groupRect = groupElement.getBoundingClientRect();\n  const groupSizeInPixels = isHorizontal ? groupRect.width : groupRect.height;\n  const offsetPixels = cursorPosition - initialCursorPosition;\n  const offsetPercentage = offsetPixels / groupSizeInPixels * 100;\n  return offsetPercentage;\n}\n\n// https://developer.mozilla.org/en-US/docs/Web/API/MouseEvent/movementX\nfunction calculateDeltaPercentage(event, dragHandleId, direction, initialDragState, keyboardResizeBy, panelGroupElement) {\n  if (isKeyDown(event)) {\n    const isHorizontal = direction === \"horizontal\";\n    let delta = 0;\n    if (event.shiftKey) {\n      delta = 100;\n    } else if (keyboardResizeBy != null) {\n      delta = keyboardResizeBy;\n    } else {\n      delta = 10;\n    }\n    let movement = 0;\n    switch (event.key) {\n      case \"ArrowDown\":\n        movement = isHorizontal ? 0 : delta;\n        break;\n      case \"ArrowLeft\":\n        movement = isHorizontal ? -delta : 0;\n        break;\n      case \"ArrowRight\":\n        movement = isHorizontal ? delta : 0;\n        break;\n      case \"ArrowUp\":\n        movement = isHorizontal ? 0 : -delta;\n        break;\n      case \"End\":\n        movement = 100;\n        break;\n      case \"Home\":\n        movement = -100;\n        break;\n    }\n    return movement;\n  } else {\n    if (initialDragState == null) {\n      return 0;\n    }\n    return calculateDragOffsetPercentage(event, dragHandleId, direction, initialDragState, panelGroupElement);\n  }\n}\n\nfunction calculateUnsafeDefaultLayout({\n  panelDataArray\n}) {\n  const layout = Array(panelDataArray.length);\n  const panelConstraintsArray = panelDataArray.map(panelData => panelData.constraints);\n  let numPanelsWithSizes = 0;\n  let remainingSize = 100;\n\n  // Distribute default sizes first\n  for (let index = 0; index < panelDataArray.length; index++) {\n    const panelConstraints = panelConstraintsArray[index];\n    assert(panelConstraints, `Panel constraints not found for index ${index}`);\n    const {\n      defaultSize\n    } = panelConstraints;\n    if (defaultSize != null) {\n      numPanelsWithSizes++;\n      layout[index] = defaultSize;\n      remainingSize -= defaultSize;\n    }\n  }\n\n  // Remaining size should be distributed evenly between panels without default sizes\n  for (let index = 0; index < panelDataArray.length; index++) {\n    const panelConstraints = panelConstraintsArray[index];\n    assert(panelConstraints, `Panel constraints not found for index ${index}`);\n    const {\n      defaultSize\n    } = panelConstraints;\n    if (defaultSize != null) {\n      continue;\n    }\n    const numRemainingPanels = panelDataArray.length - numPanelsWithSizes;\n    const size = remainingSize / numRemainingPanels;\n    numPanelsWithSizes++;\n    layout[index] = size;\n    remainingSize -= size;\n  }\n  return layout;\n}\n\n// Layout should be pre-converted into percentages\nfunction callPanelCallbacks(panelsArray, layout, panelIdToLastNotifiedSizeMap) {\n  layout.forEach((size, index) => {\n    const panelData = panelsArray[index];\n    assert(panelData, `Panel data not found for index ${index}`);\n    const {\n      callbacks,\n      constraints,\n      id: panelId\n    } = panelData;\n    const {\n      collapsedSize = 0,\n      collapsible\n    } = constraints;\n    const lastNotifiedSize = panelIdToLastNotifiedSizeMap[panelId];\n    if (lastNotifiedSize == null || size !== lastNotifiedSize) {\n      panelIdToLastNotifiedSizeMap[panelId] = size;\n      const {\n        onCollapse,\n        onExpand,\n        onResize\n      } = callbacks;\n      if (onResize) {\n        onResize(size, lastNotifiedSize);\n      }\n      if (collapsible && (onCollapse || onExpand)) {\n        if (onExpand && (lastNotifiedSize == null || fuzzyNumbersEqual$1(lastNotifiedSize, collapsedSize)) && !fuzzyNumbersEqual$1(size, collapsedSize)) {\n          onExpand();\n        }\n        if (onCollapse && (lastNotifiedSize == null || !fuzzyNumbersEqual$1(lastNotifiedSize, collapsedSize)) && fuzzyNumbersEqual$1(size, collapsedSize)) {\n          onCollapse();\n        }\n      }\n    }\n  });\n}\n\nfunction compareLayouts(a, b) {\n  if (a.length !== b.length) {\n    return false;\n  } else {\n    for (let index = 0; index < a.length; index++) {\n      if (a[index] != b[index]) {\n        return false;\n      }\n    }\n  }\n  return true;\n}\n\n// This method returns a number between 1 and 100 representing\n\n// the % of the group's overall space this panel should occupy.\nfunction computePanelFlexBoxStyle({\n  defaultSize,\n  dragState,\n  layout,\n  panelData,\n  panelIndex,\n  precision = 3\n}) {\n  const size = layout[panelIndex];\n  let flexGrow;\n  if (size == null) {\n    // Initial render (before panels have registered themselves)\n    // In order to support server rendering, fall back to default size if provided\n    flexGrow = defaultSize != undefined ? defaultSize.toPrecision(precision) : \"1\";\n  } else if (panelData.length === 1) {\n    // Special case: Single panel group should always fill full width/height\n    flexGrow = \"1\";\n  } else {\n    flexGrow = size.toPrecision(precision);\n  }\n  return {\n    flexBasis: 0,\n    flexGrow,\n    flexShrink: 1,\n    // Without this, Panel sizes may be unintentionally overridden by their content\n    overflow: \"hidden\",\n    // Disable pointer events inside of a panel during resize\n    // This avoid edge cases like nested iframes\n    pointerEvents: dragState !== null ? \"none\" : undefined\n  };\n}\n\nfunction debounce(callback, durationMs = 10) {\n  let timeoutId = null;\n  let callable = (...args) => {\n    if (timeoutId !== null) {\n      clearTimeout(timeoutId);\n    }\n    timeoutId = setTimeout(() => {\n      callback(...args);\n    }, durationMs);\n  };\n  return callable;\n}\n\n// PanelGroup might be rendering in a server-side environment where localStorage is not available\n// or on a browser with cookies/storage disabled.\n// In either case, this function avoids accessing localStorage until needed,\n// and avoids throwing user-visible errors.\nfunction initializeDefaultStorage(storageObject) {\n  try {\n    if (typeof localStorage !== \"undefined\") {\n      // Bypass this check for future calls\n      storageObject.getItem = name => {\n        return localStorage.getItem(name);\n      };\n      storageObject.setItem = (name, value) => {\n        localStorage.setItem(name, value);\n      };\n    } else {\n      throw new Error(\"localStorage not supported in this environment\");\n    }\n  } catch (error) {\n    console.error(error);\n    storageObject.getItem = () => null;\n    storageObject.setItem = () => {};\n  }\n}\n\nfunction getPanelGroupKey(autoSaveId) {\n  return `react-resizable-panels:${autoSaveId}`;\n}\n\n// Note that Panel ids might be user-provided (stable) or useId generated (non-deterministic)\n// so they should not be used as part of the serialization key.\n// Using the min/max size attributes should work well enough as a backup.\n// Pre-sorting by minSize allows remembering layouts even if panels are re-ordered/dragged.\nfunction getPanelKey(panels) {\n  return panels.map(panel => {\n    const {\n      constraints,\n      id,\n      idIsFromProps,\n      order\n    } = panel;\n    if (idIsFromProps) {\n      return id;\n    } else {\n      return order ? `${order}:${JSON.stringify(constraints)}` : JSON.stringify(constraints);\n    }\n  }).sort((a, b) => a.localeCompare(b)).join(\",\");\n}\nfunction loadSerializedPanelGroupState(autoSaveId, storage) {\n  try {\n    const panelGroupKey = getPanelGroupKey(autoSaveId);\n    const serialized = storage.getItem(panelGroupKey);\n    if (serialized) {\n      const parsed = JSON.parse(serialized);\n      if (typeof parsed === \"object\" && parsed != null) {\n        return parsed;\n      }\n    }\n  } catch (error) {}\n  return null;\n}\nfunction loadPanelGroupState(autoSaveId, panels, storage) {\n  var _loadSerializedPanelG, _state$panelKey;\n  const state = (_loadSerializedPanelG = loadSerializedPanelGroupState(autoSaveId, storage)) !== null && _loadSerializedPanelG !== void 0 ? _loadSerializedPanelG : {};\n  const panelKey = getPanelKey(panels);\n  return (_state$panelKey = state[panelKey]) !== null && _state$panelKey !== void 0 ? _state$panelKey : null;\n}\nfunction savePanelGroupState(autoSaveId, panels, panelSizesBeforeCollapse, sizes, storage) {\n  var _loadSerializedPanelG2;\n  const panelGroupKey = getPanelGroupKey(autoSaveId);\n  const panelKey = getPanelKey(panels);\n  const state = (_loadSerializedPanelG2 = loadSerializedPanelGroupState(autoSaveId, storage)) !== null && _loadSerializedPanelG2 !== void 0 ? _loadSerializedPanelG2 : {};\n  state[panelKey] = {\n    expandToSizes: Object.fromEntries(panelSizesBeforeCollapse.entries()),\n    layout: sizes\n  };\n  try {\n    storage.setItem(panelGroupKey, JSON.stringify(state));\n  } catch (error) {\n    console.error(error);\n  }\n}\n\nfunction validatePanelConstraints({\n  panelConstraints: panelConstraintsArray,\n  panelId,\n  panelIndex\n}) {\n  {\n    const warnings = [];\n    const panelConstraints = panelConstraintsArray[panelIndex];\n    assert(panelConstraints, `No panel constraints found for index ${panelIndex}`);\n    const {\n      collapsedSize = 0,\n      collapsible = false,\n      defaultSize,\n      maxSize = 100,\n      minSize = 0\n    } = panelConstraints;\n    if (minSize > maxSize) {\n      warnings.push(`min size (${minSize}%) should not be greater than max size (${maxSize}%)`);\n    }\n    if (defaultSize != null) {\n      if (defaultSize < 0) {\n        warnings.push(\"default size should not be less than 0\");\n      } else if (defaultSize < minSize && (!collapsible || defaultSize !== collapsedSize)) {\n        warnings.push(\"default size should not be less than min size\");\n      }\n      if (defaultSize > 100) {\n        warnings.push(\"default size should not be greater than 100\");\n      } else if (defaultSize > maxSize) {\n        warnings.push(\"default size should not be greater than max size\");\n      }\n    }\n    if (collapsedSize > minSize) {\n      warnings.push(\"collapsed size should not be greater than min size\");\n    }\n    if (warnings.length > 0) {\n      const name = panelId != null ? `Panel \"${panelId}\"` : \"Panel\";\n      console.warn(`${name} has an invalid configuration:\\n\\n${warnings.join(\"\\n\")}`);\n      return false;\n    }\n  }\n  return true;\n}\n\n// All units must be in percentages; pixel values should be pre-converted\nfunction validatePanelGroupLayout({\n  layout: prevLayout,\n  panelConstraints\n}) {\n  const nextLayout = [...prevLayout];\n  const nextLayoutTotalSize = nextLayout.reduce((accumulated, current) => accumulated + current, 0);\n\n  // Validate layout expectations\n  if (nextLayout.length !== panelConstraints.length) {\n    throw Error(`Invalid ${panelConstraints.length} panel layout: ${nextLayout.map(size => `${size}%`).join(\", \")}`);\n  } else if (!fuzzyNumbersEqual(nextLayoutTotalSize, 100) && nextLayout.length > 0) {\n    // This is not ideal so we should warn about it, but it may be recoverable in some cases\n    // (especially if the amount is small)\n    {\n      console.warn(`WARNING: Invalid layout total size: ${nextLayout.map(size => `${size}%`).join(\", \")}. Layout normalization will be applied.`);\n    }\n    for (let index = 0; index < panelConstraints.length; index++) {\n      const unsafeSize = nextLayout[index];\n      assert(unsafeSize != null, `No layout data found for index ${index}`);\n      const safeSize = 100 / nextLayoutTotalSize * unsafeSize;\n      nextLayout[index] = safeSize;\n    }\n  }\n  let remainingSize = 0;\n\n  // First pass: Validate the proposed layout given each panel's constraints\n  for (let index = 0; index < panelConstraints.length; index++) {\n    const unsafeSize = nextLayout[index];\n    assert(unsafeSize != null, `No layout data found for index ${index}`);\n    const safeSize = resizePanel({\n      panelConstraints,\n      panelIndex: index,\n      size: unsafeSize\n    });\n    if (unsafeSize != safeSize) {\n      remainingSize += unsafeSize - safeSize;\n      nextLayout[index] = safeSize;\n    }\n  }\n\n  // If there is additional, left over space, assign it to any panel(s) that permits it\n  // (It's not worth taking multiple additional passes to evenly distribute)\n  if (!fuzzyNumbersEqual(remainingSize, 0)) {\n    for (let index = 0; index < panelConstraints.length; index++) {\n      const prevSize = nextLayout[index];\n      assert(prevSize != null, `No layout data found for index ${index}`);\n      const unsafeSize = prevSize + remainingSize;\n      const safeSize = resizePanel({\n        panelConstraints,\n        panelIndex: index,\n        size: unsafeSize\n      });\n      if (prevSize !== safeSize) {\n        remainingSize -= safeSize - prevSize;\n        nextLayout[index] = safeSize;\n\n        // Once we've used up the remainder, bail\n        if (fuzzyNumbersEqual(remainingSize, 0)) {\n          break;\n        }\n      }\n    }\n  }\n  return nextLayout;\n}\n\nconst LOCAL_STORAGE_DEBOUNCE_INTERVAL = 100;\nconst defaultStorage = {\n  getItem: name => {\n    initializeDefaultStorage(defaultStorage);\n    return defaultStorage.getItem(name);\n  },\n  setItem: (name, value) => {\n    initializeDefaultStorage(defaultStorage);\n    defaultStorage.setItem(name, value);\n  }\n};\nconst debounceMap = {};\nfunction PanelGroupWithForwardedRef({\n  autoSaveId = null,\n  children,\n  className: classNameFromProps = \"\",\n  direction,\n  forwardedRef,\n  id: idFromProps = null,\n  onLayout = null,\n  keyboardResizeBy = null,\n  storage = defaultStorage,\n  style: styleFromProps,\n  tagName: Type = \"div\",\n  ...rest\n}) {\n  const groupId = useUniqueId(idFromProps);\n  const panelGroupElementRef = useRef(null);\n  const [dragState, setDragState] = useState(null);\n  const [layout, setLayout] = useState([]);\n  const forceUpdate = useForceUpdate();\n  const panelIdToLastNotifiedSizeMapRef = useRef({});\n  const panelSizeBeforeCollapseRef = useRef(new Map());\n  const prevDeltaRef = useRef(0);\n  const committedValuesRef = useRef({\n    autoSaveId,\n    direction,\n    dragState,\n    id: groupId,\n    keyboardResizeBy,\n    onLayout,\n    storage\n  });\n  const eagerValuesRef = useRef({\n    layout,\n    panelDataArray: [],\n    panelDataArrayChanged: false\n  });\n  const devWarningsRef = useRef({\n    didLogIdAndOrderWarning: false,\n    didLogPanelConstraintsWarning: false,\n    prevPanelIds: []\n  });\n  useImperativeHandle(forwardedRef, () => ({\n    getId: () => committedValuesRef.current.id,\n    getLayout: () => {\n      const {\n        layout\n      } = eagerValuesRef.current;\n      return layout;\n    },\n    setLayout: unsafeLayout => {\n      const {\n        onLayout\n      } = committedValuesRef.current;\n      const {\n        layout: prevLayout,\n        panelDataArray\n      } = eagerValuesRef.current;\n      const safeLayout = validatePanelGroupLayout({\n        layout: unsafeLayout,\n        panelConstraints: panelDataArray.map(panelData => panelData.constraints)\n      });\n      if (!areEqual(prevLayout, safeLayout)) {\n        setLayout(safeLayout);\n        eagerValuesRef.current.layout = safeLayout;\n        if (onLayout) {\n          onLayout(safeLayout);\n        }\n        callPanelCallbacks(panelDataArray, safeLayout, panelIdToLastNotifiedSizeMapRef.current);\n      }\n    }\n  }), []);\n  useIsomorphicLayoutEffect(() => {\n    committedValuesRef.current.autoSaveId = autoSaveId;\n    committedValuesRef.current.direction = direction;\n    committedValuesRef.current.dragState = dragState;\n    committedValuesRef.current.id = groupId;\n    committedValuesRef.current.onLayout = onLayout;\n    committedValuesRef.current.storage = storage;\n  });\n  useWindowSplitterPanelGroupBehavior({\n    committedValuesRef,\n    eagerValuesRef,\n    groupId,\n    layout,\n    panelDataArray: eagerValuesRef.current.panelDataArray,\n    setLayout,\n    panelGroupElement: panelGroupElementRef.current\n  });\n  useEffect(() => {\n    const {\n      panelDataArray\n    } = eagerValuesRef.current;\n\n    // If this panel has been configured to persist sizing information, save sizes to local storage.\n    if (autoSaveId) {\n      if (layout.length === 0 || layout.length !== panelDataArray.length) {\n        return;\n      }\n      let debouncedSave = debounceMap[autoSaveId];\n\n      // Limit the frequency of localStorage updates.\n      if (debouncedSave == null) {\n        debouncedSave = debounce(savePanelGroupState, LOCAL_STORAGE_DEBOUNCE_INTERVAL);\n        debounceMap[autoSaveId] = debouncedSave;\n      }\n\n      // Clone mutable data before passing to the debounced function,\n      // else we run the risk of saving an incorrect combination of mutable and immutable values to state.\n      const clonedPanelDataArray = [...panelDataArray];\n      const clonedPanelSizesBeforeCollapse = new Map(panelSizeBeforeCollapseRef.current);\n      debouncedSave(autoSaveId, clonedPanelDataArray, clonedPanelSizesBeforeCollapse, layout, storage);\n    }\n  }, [autoSaveId, layout, storage]);\n\n  // DEV warnings\n  useEffect(() => {\n    {\n      const {\n        panelDataArray\n      } = eagerValuesRef.current;\n      const {\n        didLogIdAndOrderWarning,\n        didLogPanelConstraintsWarning,\n        prevPanelIds\n      } = devWarningsRef.current;\n      if (!didLogIdAndOrderWarning) {\n        const panelIds = panelDataArray.map(({\n          id\n        }) => id);\n        devWarningsRef.current.prevPanelIds = panelIds;\n        const panelsHaveChanged = prevPanelIds.length > 0 && !areEqual(prevPanelIds, panelIds);\n        if (panelsHaveChanged) {\n          if (panelDataArray.find(({\n            idIsFromProps,\n            order\n          }) => !idIsFromProps || order == null)) {\n            devWarningsRef.current.didLogIdAndOrderWarning = true;\n            console.warn(`WARNING: Panel id and order props recommended when panels are dynamically rendered`);\n          }\n        }\n      }\n      if (!didLogPanelConstraintsWarning) {\n        const panelConstraints = panelDataArray.map(panelData => panelData.constraints);\n        for (let panelIndex = 0; panelIndex < panelConstraints.length; panelIndex++) {\n          const panelData = panelDataArray[panelIndex];\n          assert(panelData, `Panel data not found for index ${panelIndex}`);\n          const isValid = validatePanelConstraints({\n            panelConstraints,\n            panelId: panelData.id,\n            panelIndex\n          });\n          if (!isValid) {\n            devWarningsRef.current.didLogPanelConstraintsWarning = true;\n            break;\n          }\n        }\n      }\n    }\n  });\n\n  // External APIs are safe to memoize via committed values ref\n  const collapsePanel = useCallback(panelData => {\n    const {\n      onLayout\n    } = committedValuesRef.current;\n    const {\n      layout: prevLayout,\n      panelDataArray\n    } = eagerValuesRef.current;\n    if (panelData.constraints.collapsible) {\n      const panelConstraintsArray = panelDataArray.map(panelData => panelData.constraints);\n      const {\n        collapsedSize = 0,\n        panelSize,\n        pivotIndices\n      } = panelDataHelper(panelDataArray, panelData, prevLayout);\n      assert(panelSize != null, `Panel size not found for panel \"${panelData.id}\"`);\n      if (!fuzzyNumbersEqual$1(panelSize, collapsedSize)) {\n        // Store size before collapse;\n        // This is the size that gets restored if the expand() API is used.\n        panelSizeBeforeCollapseRef.current.set(panelData.id, panelSize);\n        const isLastPanel = findPanelDataIndex(panelDataArray, panelData) === panelDataArray.length - 1;\n        const delta = isLastPanel ? panelSize - collapsedSize : collapsedSize - panelSize;\n        const nextLayout = adjustLayoutByDelta({\n          delta,\n          initialLayout: prevLayout,\n          panelConstraints: panelConstraintsArray,\n          pivotIndices,\n          prevLayout,\n          trigger: \"imperative-api\"\n        });\n        if (!compareLayouts(prevLayout, nextLayout)) {\n          setLayout(nextLayout);\n          eagerValuesRef.current.layout = nextLayout;\n          if (onLayout) {\n            onLayout(nextLayout);\n          }\n          callPanelCallbacks(panelDataArray, nextLayout, panelIdToLastNotifiedSizeMapRef.current);\n        }\n      }\n    }\n  }, []);\n\n  // External APIs are safe to memoize via committed values ref\n  const expandPanel = useCallback((panelData, minSizeOverride) => {\n    const {\n      onLayout\n    } = committedValuesRef.current;\n    const {\n      layout: prevLayout,\n      panelDataArray\n    } = eagerValuesRef.current;\n    if (panelData.constraints.collapsible) {\n      const panelConstraintsArray = panelDataArray.map(panelData => panelData.constraints);\n      const {\n        collapsedSize = 0,\n        panelSize = 0,\n        minSize: minSizeFromProps = 0,\n        pivotIndices\n      } = panelDataHelper(panelDataArray, panelData, prevLayout);\n      const minSize = minSizeOverride !== null && minSizeOverride !== void 0 ? minSizeOverride : minSizeFromProps;\n      if (fuzzyNumbersEqual$1(panelSize, collapsedSize)) {\n        // Restore this panel to the size it was before it was collapsed, if possible.\n        const prevPanelSize = panelSizeBeforeCollapseRef.current.get(panelData.id);\n        const baseSize = prevPanelSize != null && prevPanelSize >= minSize ? prevPanelSize : minSize;\n        const isLastPanel = findPanelDataIndex(panelDataArray, panelData) === panelDataArray.length - 1;\n        const delta = isLastPanel ? panelSize - baseSize : baseSize - panelSize;\n        const nextLayout = adjustLayoutByDelta({\n          delta,\n          initialLayout: prevLayout,\n          panelConstraints: panelConstraintsArray,\n          pivotIndices,\n          prevLayout,\n          trigger: \"imperative-api\"\n        });\n        if (!compareLayouts(prevLayout, nextLayout)) {\n          setLayout(nextLayout);\n          eagerValuesRef.current.layout = nextLayout;\n          if (onLayout) {\n            onLayout(nextLayout);\n          }\n          callPanelCallbacks(panelDataArray, nextLayout, panelIdToLastNotifiedSizeMapRef.current);\n        }\n      }\n    }\n  }, []);\n\n  // External APIs are safe to memoize via committed values ref\n  const getPanelSize = useCallback(panelData => {\n    const {\n      layout,\n      panelDataArray\n    } = eagerValuesRef.current;\n    const {\n      panelSize\n    } = panelDataHelper(panelDataArray, panelData, layout);\n    assert(panelSize != null, `Panel size not found for panel \"${panelData.id}\"`);\n    return panelSize;\n  }, []);\n\n  // This API should never read from committedValuesRef\n  const getPanelStyle = useCallback((panelData, defaultSize) => {\n    const {\n      panelDataArray\n    } = eagerValuesRef.current;\n    const panelIndex = findPanelDataIndex(panelDataArray, panelData);\n    return computePanelFlexBoxStyle({\n      defaultSize,\n      dragState,\n      layout,\n      panelData: panelDataArray,\n      panelIndex\n    });\n  }, [dragState, layout]);\n\n  // External APIs are safe to memoize via committed values ref\n  const isPanelCollapsed = useCallback(panelData => {\n    const {\n      layout,\n      panelDataArray\n    } = eagerValuesRef.current;\n    const {\n      collapsedSize = 0,\n      collapsible,\n      panelSize\n    } = panelDataHelper(panelDataArray, panelData, layout);\n    assert(panelSize != null, `Panel size not found for panel \"${panelData.id}\"`);\n    return collapsible === true && fuzzyNumbersEqual$1(panelSize, collapsedSize);\n  }, []);\n\n  // External APIs are safe to memoize via committed values ref\n  const isPanelExpanded = useCallback(panelData => {\n    const {\n      layout,\n      panelDataArray\n    } = eagerValuesRef.current;\n    const {\n      collapsedSize = 0,\n      collapsible,\n      panelSize\n    } = panelDataHelper(panelDataArray, panelData, layout);\n    assert(panelSize != null, `Panel size not found for panel \"${panelData.id}\"`);\n    return !collapsible || fuzzyCompareNumbers(panelSize, collapsedSize) > 0;\n  }, []);\n  const registerPanel = useCallback(panelData => {\n    const {\n      panelDataArray\n    } = eagerValuesRef.current;\n    panelDataArray.push(panelData);\n    panelDataArray.sort((panelA, panelB) => {\n      const orderA = panelA.order;\n      const orderB = panelB.order;\n      if (orderA == null && orderB == null) {\n        return 0;\n      } else if (orderA == null) {\n        return -1;\n      } else if (orderB == null) {\n        return 1;\n      } else {\n        return orderA - orderB;\n      }\n    });\n    eagerValuesRef.current.panelDataArrayChanged = true;\n    forceUpdate();\n  }, [forceUpdate]);\n\n  // (Re)calculate group layout whenever panels are registered or unregistered.\n  // eslint-disable-next-line react-hooks/exhaustive-deps\n  useIsomorphicLayoutEffect(() => {\n    if (eagerValuesRef.current.panelDataArrayChanged) {\n      eagerValuesRef.current.panelDataArrayChanged = false;\n      const {\n        autoSaveId,\n        onLayout,\n        storage\n      } = committedValuesRef.current;\n      const {\n        layout: prevLayout,\n        panelDataArray\n      } = eagerValuesRef.current;\n\n      // If this panel has been configured to persist sizing information,\n      // default size should be restored from local storage if possible.\n      let unsafeLayout = null;\n      if (autoSaveId) {\n        const state = loadPanelGroupState(autoSaveId, panelDataArray, storage);\n        if (state) {\n          panelSizeBeforeCollapseRef.current = new Map(Object.entries(state.expandToSizes));\n          unsafeLayout = state.layout;\n        }\n      }\n      if (unsafeLayout == null) {\n        unsafeLayout = calculateUnsafeDefaultLayout({\n          panelDataArray\n        });\n      }\n\n      // Validate even saved layouts in case something has changed since last render\n      // e.g. for pixel groups, this could be the size of the window\n      const nextLayout = validatePanelGroupLayout({\n        layout: unsafeLayout,\n        panelConstraints: panelDataArray.map(panelData => panelData.constraints)\n      });\n      if (!areEqual(prevLayout, nextLayout)) {\n        setLayout(nextLayout);\n        eagerValuesRef.current.layout = nextLayout;\n        if (onLayout) {\n          onLayout(nextLayout);\n        }\n        callPanelCallbacks(panelDataArray, nextLayout, panelIdToLastNotifiedSizeMapRef.current);\n      }\n    }\n  });\n\n  // Reset the cached layout if hidden by the Activity/Offscreen API\n  useIsomorphicLayoutEffect(() => {\n    const eagerValues = eagerValuesRef.current;\n    return () => {\n      eagerValues.layout = [];\n    };\n  }, []);\n  const registerResizeHandle = useCallback(dragHandleId => {\n    let isRTL = false;\n    const panelGroupElement = panelGroupElementRef.current;\n    if (panelGroupElement) {\n      const style = window.getComputedStyle(panelGroupElement, null);\n      if (style.getPropertyValue(\"direction\") === \"rtl\") {\n        isRTL = true;\n      }\n    }\n    return function resizeHandler(event) {\n      event.preventDefault();\n      const panelGroupElement = panelGroupElementRef.current;\n      if (!panelGroupElement) {\n        return () => null;\n      }\n      const {\n        direction,\n        dragState,\n        id: groupId,\n        keyboardResizeBy,\n        onLayout\n      } = committedValuesRef.current;\n      const {\n        layout: prevLayout,\n        panelDataArray\n      } = eagerValuesRef.current;\n      const {\n        initialLayout\n      } = dragState !== null && dragState !== void 0 ? dragState : {};\n      const pivotIndices = determinePivotIndices(groupId, dragHandleId, panelGroupElement);\n      let delta = calculateDeltaPercentage(event, dragHandleId, direction, dragState, keyboardResizeBy, panelGroupElement);\n      const isHorizontal = direction === \"horizontal\";\n      if (isHorizontal && isRTL) {\n        delta = -delta;\n      }\n      const panelConstraints = panelDataArray.map(panelData => panelData.constraints);\n      const nextLayout = adjustLayoutByDelta({\n        delta,\n        initialLayout: initialLayout !== null && initialLayout !== void 0 ? initialLayout : prevLayout,\n        panelConstraints,\n        pivotIndices,\n        prevLayout,\n        trigger: isKeyDown(event) ? \"keyboard\" : \"mouse-or-touch\"\n      });\n      const layoutChanged = !compareLayouts(prevLayout, nextLayout);\n\n      // Only update the cursor for layout changes triggered by touch/mouse events (not keyboard)\n      // Update the cursor even if the layout hasn't changed (we may need to show an invalid cursor state)\n      if (isPointerEvent(event) || isMouseEvent(event)) {\n        // Watch for multiple subsequent deltas; this might occur for tiny cursor movements.\n        // In this case, Panel sizes might not change–\n        // but updating cursor in this scenario would cause a flicker.\n        if (prevDeltaRef.current != delta) {\n          prevDeltaRef.current = delta;\n          if (!layoutChanged && delta !== 0) {\n            // If the pointer has moved too far to resize the panel any further, note this so we can update the cursor.\n            // This mimics VS Code behavior.\n            if (isHorizontal) {\n              reportConstraintsViolation(dragHandleId, delta < 0 ? EXCEEDED_HORIZONTAL_MIN : EXCEEDED_HORIZONTAL_MAX);\n            } else {\n              reportConstraintsViolation(dragHandleId, delta < 0 ? EXCEEDED_VERTICAL_MIN : EXCEEDED_VERTICAL_MAX);\n            }\n          } else {\n            reportConstraintsViolation(dragHandleId, 0);\n          }\n        }\n      }\n      if (layoutChanged) {\n        setLayout(nextLayout);\n        eagerValuesRef.current.layout = nextLayout;\n        if (onLayout) {\n          onLayout(nextLayout);\n        }\n        callPanelCallbacks(panelDataArray, nextLayout, panelIdToLastNotifiedSizeMapRef.current);\n      }\n    };\n  }, []);\n\n  // External APIs are safe to memoize via committed values ref\n  const resizePanel = useCallback((panelData, unsafePanelSize) => {\n    const {\n      onLayout\n    } = committedValuesRef.current;\n    const {\n      layout: prevLayout,\n      panelDataArray\n    } = eagerValuesRef.current;\n    const panelConstraintsArray = panelDataArray.map(panelData => panelData.constraints);\n    const {\n      panelSize,\n      pivotIndices\n    } = panelDataHelper(panelDataArray, panelData, prevLayout);\n    assert(panelSize != null, `Panel size not found for panel \"${panelData.id}\"`);\n    const isLastPanel = findPanelDataIndex(panelDataArray, panelData) === panelDataArray.length - 1;\n    const delta = isLastPanel ? panelSize - unsafePanelSize : unsafePanelSize - panelSize;\n    const nextLayout = adjustLayoutByDelta({\n      delta,\n      initialLayout: prevLayout,\n      panelConstraints: panelConstraintsArray,\n      pivotIndices,\n      prevLayout,\n      trigger: \"imperative-api\"\n    });\n    if (!compareLayouts(prevLayout, nextLayout)) {\n      setLayout(nextLayout);\n      eagerValuesRef.current.layout = nextLayout;\n      if (onLayout) {\n        onLayout(nextLayout);\n      }\n      callPanelCallbacks(panelDataArray, nextLayout, panelIdToLastNotifiedSizeMapRef.current);\n    }\n  }, []);\n  const reevaluatePanelConstraints = useCallback((panelData, prevConstraints) => {\n    const {\n      layout,\n      panelDataArray\n    } = eagerValuesRef.current;\n    const {\n      collapsedSize: prevCollapsedSize = 0,\n      collapsible: prevCollapsible\n    } = prevConstraints;\n    const {\n      collapsedSize: nextCollapsedSize = 0,\n      collapsible: nextCollapsible,\n      maxSize: nextMaxSize = 100,\n      minSize: nextMinSize = 0\n    } = panelData.constraints;\n    const {\n      panelSize: prevPanelSize\n    } = panelDataHelper(panelDataArray, panelData, layout);\n    if (prevPanelSize == null) {\n      // It's possible that the panels in this group have changed since the last render\n      return;\n    }\n    if (prevCollapsible && nextCollapsible && fuzzyNumbersEqual$1(prevPanelSize, prevCollapsedSize)) {\n      if (!fuzzyNumbersEqual$1(prevCollapsedSize, nextCollapsedSize)) {\n        resizePanel(panelData, nextCollapsedSize);\n      }\n    } else if (prevPanelSize < nextMinSize) {\n      resizePanel(panelData, nextMinSize);\n    } else if (prevPanelSize > nextMaxSize) {\n      resizePanel(panelData, nextMaxSize);\n    }\n  }, [resizePanel]);\n\n  // TODO Multiple drag handles can be active at the same time so this API is a bit awkward now\n  const startDragging = useCallback((dragHandleId, event) => {\n    const {\n      direction\n    } = committedValuesRef.current;\n    const {\n      layout\n    } = eagerValuesRef.current;\n    if (!panelGroupElementRef.current) {\n      return;\n    }\n    const handleElement = getResizeHandleElement(dragHandleId, panelGroupElementRef.current);\n    assert(handleElement, `Drag handle element not found for id \"${dragHandleId}\"`);\n    const initialCursorPosition = getResizeEventCursorPosition(direction, event);\n    setDragState({\n      dragHandleId,\n      dragHandleRect: handleElement.getBoundingClientRect(),\n      initialCursorPosition,\n      initialLayout: layout\n    });\n  }, []);\n  const stopDragging = useCallback(() => {\n    setDragState(null);\n  }, []);\n  const unregisterPanel = useCallback(panelData => {\n    const {\n      panelDataArray\n    } = eagerValuesRef.current;\n    const index = findPanelDataIndex(panelDataArray, panelData);\n    if (index >= 0) {\n      panelDataArray.splice(index, 1);\n\n      // TRICKY\n      // When a panel is removed from the group, we should delete the most recent prev-size entry for it.\n      // If we don't do this, then a conditionally rendered panel might not call onResize when it's re-mounted.\n      // Strict effects mode makes this tricky though because all panels will be registered, unregistered, then re-registered on mount.\n      delete panelIdToLastNotifiedSizeMapRef.current[panelData.id];\n      eagerValuesRef.current.panelDataArrayChanged = true;\n      forceUpdate();\n    }\n  }, [forceUpdate]);\n  const context = useMemo(() => ({\n    collapsePanel,\n    direction,\n    dragState,\n    expandPanel,\n    getPanelSize,\n    getPanelStyle,\n    groupId,\n    isPanelCollapsed,\n    isPanelExpanded,\n    reevaluatePanelConstraints,\n    registerPanel,\n    registerResizeHandle,\n    resizePanel,\n    startDragging,\n    stopDragging,\n    unregisterPanel,\n    panelGroupElement: panelGroupElementRef.current\n  }), [collapsePanel, dragState, direction, expandPanel, getPanelSize, getPanelStyle, groupId, isPanelCollapsed, isPanelExpanded, reevaluatePanelConstraints, registerPanel, registerResizeHandle, resizePanel, startDragging, stopDragging, unregisterPanel]);\n  const style = {\n    display: \"flex\",\n    flexDirection: direction === \"horizontal\" ? \"row\" : \"column\",\n    height: \"100%\",\n    overflow: \"hidden\",\n    width: \"100%\"\n  };\n  return createElement(PanelGroupContext.Provider, {\n    value: context\n  }, createElement(Type, {\n    ...rest,\n    children,\n    className: classNameFromProps,\n    id: idFromProps,\n    ref: panelGroupElementRef,\n    style: {\n      ...style,\n      ...styleFromProps\n    },\n    // CSS selectors\n    [DATA_ATTRIBUTES.group]: \"\",\n    [DATA_ATTRIBUTES.groupDirection]: direction,\n    [DATA_ATTRIBUTES.groupId]: groupId\n  }));\n}\nconst PanelGroup = forwardRef((props, ref) => createElement(PanelGroupWithForwardedRef, {\n  ...props,\n  forwardedRef: ref\n}));\nPanelGroupWithForwardedRef.displayName = \"PanelGroup\";\nPanelGroup.displayName = \"forwardRef(PanelGroup)\";\nfunction findPanelDataIndex(panelDataArray, panelData) {\n  return panelDataArray.findIndex(prevPanelData => prevPanelData === panelData || prevPanelData.id === panelData.id);\n}\nfunction panelDataHelper(panelDataArray, panelData, layout) {\n  const panelIndex = findPanelDataIndex(panelDataArray, panelData);\n  const isLastPanel = panelIndex === panelDataArray.length - 1;\n  const pivotIndices = isLastPanel ? [panelIndex - 1, panelIndex] : [panelIndex, panelIndex + 1];\n  const panelSize = layout[panelIndex];\n  return {\n    ...panelData.constraints,\n    panelSize,\n    pivotIndices\n  };\n}\n\n// https://www.w3.org/WAI/ARIA/apg/patterns/windowsplitter/\n\nfunction useWindowSplitterResizeHandlerBehavior({\n  disabled,\n  handleId,\n  resizeHandler,\n  panelGroupElement\n}) {\n  useEffect(() => {\n    if (disabled || resizeHandler == null || panelGroupElement == null) {\n      return;\n    }\n    const handleElement = getResizeHandleElement(handleId, panelGroupElement);\n    if (handleElement == null) {\n      return;\n    }\n    const onKeyDown = event => {\n      if (event.defaultPrevented) {\n        return;\n      }\n      switch (event.key) {\n        case \"ArrowDown\":\n        case \"ArrowLeft\":\n        case \"ArrowRight\":\n        case \"ArrowUp\":\n        case \"End\":\n        case \"Home\":\n          {\n            event.preventDefault();\n            resizeHandler(event);\n            break;\n          }\n        case \"F6\":\n          {\n            event.preventDefault();\n            const groupId = handleElement.getAttribute(DATA_ATTRIBUTES.groupId);\n            assert(groupId, `No group element found for id \"${groupId}\"`);\n            const handles = getResizeHandleElementsForGroup(groupId, panelGroupElement);\n            const index = getResizeHandleElementIndex(groupId, handleId, panelGroupElement);\n            assert(index !== null, `No resize element found for id \"${handleId}\"`);\n            const nextIndex = event.shiftKey ? index > 0 ? index - 1 : handles.length - 1 : index + 1 < handles.length ? index + 1 : 0;\n            const nextHandle = handles[nextIndex];\n            nextHandle.focus();\n            break;\n          }\n      }\n    };\n    handleElement.addEventListener(\"keydown\", onKeyDown);\n    return () => {\n      handleElement.removeEventListener(\"keydown\", onKeyDown);\n    };\n  }, [panelGroupElement, disabled, handleId, resizeHandler]);\n}\n\nfunction PanelResizeHandle({\n  children = null,\n  className: classNameFromProps = \"\",\n  disabled = false,\n  hitAreaMargins,\n  id: idFromProps,\n  onBlur,\n  onClick,\n  onDragging,\n  onFocus,\n  onPointerDown,\n  onPointerUp,\n  style: styleFromProps = {},\n  tabIndex = 0,\n  tagName: Type = \"div\",\n  ...rest\n}) {\n  var _hitAreaMargins$coars, _hitAreaMargins$fine;\n  const elementRef = useRef(null);\n\n  // Use a ref to guard against users passing inline props\n  const callbacksRef = useRef({\n    onClick,\n    onDragging,\n    onPointerDown,\n    onPointerUp\n  });\n  useEffect(() => {\n    callbacksRef.current.onClick = onClick;\n    callbacksRef.current.onDragging = onDragging;\n    callbacksRef.current.onPointerDown = onPointerDown;\n    callbacksRef.current.onPointerUp = onPointerUp;\n  });\n  const panelGroupContext = useContext(PanelGroupContext);\n  if (panelGroupContext === null) {\n    throw Error(`PanelResizeHandle components must be rendered within a PanelGroup container`);\n  }\n  const {\n    direction,\n    groupId,\n    registerResizeHandle: registerResizeHandleWithParentGroup,\n    startDragging,\n    stopDragging,\n    panelGroupElement\n  } = panelGroupContext;\n  const resizeHandleId = useUniqueId(idFromProps);\n  const [state, setState] = useState(\"inactive\");\n  const [isFocused, setIsFocused] = useState(false);\n  const [resizeHandler, setResizeHandler] = useState(null);\n  const committedValuesRef = useRef({\n    state\n  });\n  useIsomorphicLayoutEffect(() => {\n    committedValuesRef.current.state = state;\n  });\n  useEffect(() => {\n    if (disabled) {\n      setResizeHandler(null);\n    } else {\n      const resizeHandler = registerResizeHandleWithParentGroup(resizeHandleId);\n      setResizeHandler(() => resizeHandler);\n    }\n  }, [disabled, resizeHandleId, registerResizeHandleWithParentGroup]);\n\n  // Extract hit area margins before passing them to the effect's dependency array\n  // so that inline object values won't trigger re-renders\n  const coarseHitAreaMargins = (_hitAreaMargins$coars = hitAreaMargins === null || hitAreaMargins === void 0 ? void 0 : hitAreaMargins.coarse) !== null && _hitAreaMargins$coars !== void 0 ? _hitAreaMargins$coars : 15;\n  const fineHitAreaMargins = (_hitAreaMargins$fine = hitAreaMargins === null || hitAreaMargins === void 0 ? void 0 : hitAreaMargins.fine) !== null && _hitAreaMargins$fine !== void 0 ? _hitAreaMargins$fine : 5;\n  useEffect(() => {\n    if (disabled || resizeHandler == null) {\n      return;\n    }\n    const element = elementRef.current;\n    assert(element, \"Element ref not attached\");\n    let didMove = false;\n    const setResizeHandlerState = (action, isActive, event) => {\n      if (!isActive) {\n        setState(\"inactive\");\n        return;\n      }\n      switch (action) {\n        case \"down\":\n          {\n            setState(\"drag\");\n            didMove = false;\n            assert(event, 'Expected event to be defined for \"down\" action');\n            startDragging(resizeHandleId, event);\n            const {\n              onDragging,\n              onPointerDown\n            } = callbacksRef.current;\n            onDragging === null || onDragging === void 0 ? void 0 : onDragging(true);\n            onPointerDown === null || onPointerDown === void 0 ? void 0 : onPointerDown();\n            break;\n          }\n        case \"move\":\n          {\n            const {\n              state\n            } = committedValuesRef.current;\n            didMove = true;\n            if (state !== \"drag\") {\n              setState(\"hover\");\n            }\n            assert(event, 'Expected event to be defined for \"move\" action');\n            resizeHandler(event);\n            break;\n          }\n        case \"up\":\n          {\n            setState(\"hover\");\n            stopDragging();\n            const {\n              onClick,\n              onDragging,\n              onPointerUp\n            } = callbacksRef.current;\n            onDragging === null || onDragging === void 0 ? void 0 : onDragging(false);\n            onPointerUp === null || onPointerUp === void 0 ? void 0 : onPointerUp();\n            if (!didMove) {\n              onClick === null || onClick === void 0 ? void 0 : onClick();\n            }\n            break;\n          }\n      }\n    };\n    return registerResizeHandle(resizeHandleId, element, direction, {\n      coarse: coarseHitAreaMargins,\n      fine: fineHitAreaMargins\n    }, setResizeHandlerState);\n  }, [coarseHitAreaMargins, direction, disabled, fineHitAreaMargins, registerResizeHandleWithParentGroup, resizeHandleId, resizeHandler, startDragging, stopDragging]);\n  useWindowSplitterResizeHandlerBehavior({\n    disabled,\n    handleId: resizeHandleId,\n    resizeHandler,\n    panelGroupElement\n  });\n  const style = {\n    touchAction: \"none\",\n    userSelect: \"none\"\n  };\n  return createElement(Type, {\n    ...rest,\n    children,\n    className: classNameFromProps,\n    id: idFromProps,\n    onBlur: () => {\n      setIsFocused(false);\n      onBlur === null || onBlur === void 0 ? void 0 : onBlur();\n    },\n    onFocus: () => {\n      setIsFocused(true);\n      onFocus === null || onFocus === void 0 ? void 0 : onFocus();\n    },\n    ref: elementRef,\n    role: \"separator\",\n    style: {\n      ...style,\n      ...styleFromProps\n    },\n    tabIndex,\n    // CSS selectors\n    [DATA_ATTRIBUTES.groupDirection]: direction,\n    [DATA_ATTRIBUTES.groupId]: groupId,\n    [DATA_ATTRIBUTES.resizeHandle]: \"\",\n    [DATA_ATTRIBUTES.resizeHandleActive]: state === \"drag\" ? \"pointer\" : isFocused ? \"keyboard\" : undefined,\n    [DATA_ATTRIBUTES.resizeHandleEnabled]: !disabled,\n    [DATA_ATTRIBUTES.resizeHandleId]: resizeHandleId,\n    [DATA_ATTRIBUTES.resizeHandleState]: state\n  });\n}\nPanelResizeHandle.displayName = \"PanelResizeHandle\";\n\nfunction usePanelGroupContext() {\n  const context = useContext(PanelGroupContext);\n  return {\n    direction: context === null || context === void 0 ? void 0 : context.direction,\n    groupId: context === null || context === void 0 ? void 0 : context.groupId\n  };\n}\n\nfunction getPanelElement(id, scope = document) {\n  const element = scope.querySelector(`[data-panel-id=\"${id}\"]`);\n  if (element) {\n    return element;\n  }\n  return null;\n}\n\nfunction getPanelElementsForGroup(groupId, scope = document) {\n  return Array.from(scope.querySelectorAll(`[data-panel][data-panel-group-id=\"${groupId}\"]`));\n}\n\nfunction getIntersectingRectangle(rectOne, rectTwo, strict) {\n  if (!intersects(rectOne, rectTwo, strict)) {\n    return {\n      x: 0,\n      y: 0,\n      width: 0,\n      height: 0\n    };\n  }\n  return {\n    x: Math.max(rectOne.x, rectTwo.x),\n    y: Math.max(rectOne.y, rectTwo.y),\n    width: Math.min(rectOne.x + rectOne.width, rectTwo.x + rectTwo.width) - Math.max(rectOne.x, rectTwo.x),\n    height: Math.min(rectOne.y + rectOne.height, rectTwo.y + rectTwo.height) - Math.max(rectOne.y, rectTwo.y)\n  };\n}\n\nexport { DATA_ATTRIBUTES, Panel, PanelGroup, PanelResizeHandle, assert, disableGlobalCursorStyles, enableGlobalCursorStyles, getIntersectingRectangle, getPanelElement, getPanelElementsForGroup, getPanelGroupElement, getResizeHandleElement, getResizeHandleElementIndex, getResizeHandleElementsForGroup, getResizeHandlePanelIds, intersects, setNonce, usePanelGroupContext };\n"], "names": [], "sourceRoot": ""}