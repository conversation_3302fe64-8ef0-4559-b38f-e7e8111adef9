{"version": 3, "file": "vendors-node_modules_pnpm_radix-ui_react-accordion_1_51304fa0823e9b2a065feab26851e0ad_node_mo-83f716.iframe.bundle.js", "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAaA;AACA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC/TA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AASA;AACA;;;;;;;;;;;;;;;;;;;AC1JA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;;;;;;AC1CA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;;AC7BA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;;;;ACpBA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;;;;;;ACbA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "sources": ["webpack://storybook/../../node_modules/.pnpm/@radix-ui+react-accordion@1_51304fa0823e9b2a065feab26851e0ad/node_modules/@radix-ui/react-accordion/dist/index.mjs", "webpack://storybook/../../node_modules/.pnpm/@radix-ui+react-collapsible_cecf39ac1375c4a4148ed47a207c8f40/node_modules/@radix-ui/react-collapsible/dist/index.mjs", "webpack://storybook/../../node_modules/.pnpm/lucide-react@0.511.0_react@19.1.0/node_modules/lucide-react/dist/esm/Icon.js", "webpack://storybook/../../node_modules/.pnpm/lucide-react@0.511.0_react@19.1.0/node_modules/lucide-react/dist/esm/createLucideIcon.js", "webpack://storybook/../../node_modules/.pnpm/lucide-react@0.511.0_react@19.1.0/node_modules/lucide-react/dist/esm/defaultAttributes.js", "webpack://storybook/../../node_modules/.pnpm/lucide-react@0.511.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/chevron-down.js", "webpack://storybook/../../node_modules/.pnpm/lucide-react@0.511.0_react@19.1.0/node_modules/lucide-react/dist/esm/shared/src/utils.js"], "sourcesContent": ["\"use client\";\n\n// src/accordion.tsx\nimport React from \"react\";\nimport { createContextScope } from \"@radix-ui/react-context\";\nimport { createCollection } from \"@radix-ui/react-collection\";\nimport { useComposedRefs } from \"@radix-ui/react-compose-refs\";\nimport { composeEventHandlers } from \"@radix-ui/primitive\";\nimport { useControllableState } from \"@radix-ui/react-use-controllable-state\";\nimport { Primitive } from \"@radix-ui/react-primitive\";\nimport * as CollapsiblePrimitive from \"@radix-ui/react-collapsible\";\nimport { createCollapsibleScope } from \"@radix-ui/react-collapsible\";\nimport { useId } from \"@radix-ui/react-id\";\nimport { useDirection } from \"@radix-ui/react-direction\";\nimport { jsx } from \"react/jsx-runtime\";\nvar ACCORDION_NAME = \"Accordion\";\nvar ACCORDION_KEYS = [\"Home\", \"End\", \"ArrowDown\", \"ArrowUp\", \"ArrowLeft\", \"ArrowRight\"];\nvar [Collection, useCollection, createCollectionScope] = createCollection(ACCORDION_NAME);\nvar [createAccordionContext, createAccordionScope] = createContextScope(ACCORDION_NAME, [\n  createCollectionScope,\n  createCollapsibleScope\n]);\nvar useCollapsibleScope = createCollapsibleScope();\nvar Accordion = React.forwardRef(\n  (props, forwardedRef) => {\n    const { type, ...accordionProps } = props;\n    const singleProps = accordionProps;\n    const multipleProps = accordionProps;\n    return /* @__PURE__ */ jsx(Collection.Provider, { scope: props.__scopeAccordion, children: type === \"multiple\" ? /* @__PURE__ */ jsx(AccordionImplMultiple, { ...multipleProps, ref: forwardedRef }) : /* @__PURE__ */ jsx(AccordionImplSingle, { ...singleProps, ref: forwardedRef }) });\n  }\n);\nAccordion.displayName = ACCORDION_NAME;\nvar [AccordionValueProvider, useAccordionValueContext] = createAccordionContext(ACCORDION_NAME);\nvar [AccordionCollapsibleProvider, useAccordionCollapsibleContext] = createAccordionContext(\n  ACCORDION_NAME,\n  { collapsible: false }\n);\nvar AccordionImplSingle = React.forwardRef(\n  (props, forwardedRef) => {\n    const {\n      value: valueProp,\n      defaultValue,\n      onValueChange = () => {\n      },\n      collapsible = false,\n      ...accordionSingleProps\n    } = props;\n    const [value, setValue] = useControllableState({\n      prop: valueProp,\n      defaultProp: defaultValue ?? \"\",\n      onChange: onValueChange,\n      caller: ACCORDION_NAME\n    });\n    return /* @__PURE__ */ jsx(\n      AccordionValueProvider,\n      {\n        scope: props.__scopeAccordion,\n        value: React.useMemo(() => value ? [value] : [], [value]),\n        onItemOpen: setValue,\n        onItemClose: React.useCallback(() => collapsible && setValue(\"\"), [collapsible, setValue]),\n        children: /* @__PURE__ */ jsx(AccordionCollapsibleProvider, { scope: props.__scopeAccordion, collapsible, children: /* @__PURE__ */ jsx(AccordionImpl, { ...accordionSingleProps, ref: forwardedRef }) })\n      }\n    );\n  }\n);\nvar AccordionImplMultiple = React.forwardRef((props, forwardedRef) => {\n  const {\n    value: valueProp,\n    defaultValue,\n    onValueChange = () => {\n    },\n    ...accordionMultipleProps\n  } = props;\n  const [value, setValue] = useControllableState({\n    prop: valueProp,\n    defaultProp: defaultValue ?? [],\n    onChange: onValueChange,\n    caller: ACCORDION_NAME\n  });\n  const handleItemOpen = React.useCallback(\n    (itemValue) => setValue((prevValue = []) => [...prevValue, itemValue]),\n    [setValue]\n  );\n  const handleItemClose = React.useCallback(\n    (itemValue) => setValue((prevValue = []) => prevValue.filter((value2) => value2 !== itemValue)),\n    [setValue]\n  );\n  return /* @__PURE__ */ jsx(\n    AccordionValueProvider,\n    {\n      scope: props.__scopeAccordion,\n      value,\n      onItemOpen: handleItemOpen,\n      onItemClose: handleItemClose,\n      children: /* @__PURE__ */ jsx(AccordionCollapsibleProvider, { scope: props.__scopeAccordion, collapsible: true, children: /* @__PURE__ */ jsx(AccordionImpl, { ...accordionMultipleProps, ref: forwardedRef }) })\n    }\n  );\n});\nvar [AccordionImplProvider, useAccordionContext] = createAccordionContext(ACCORDION_NAME);\nvar AccordionImpl = React.forwardRef(\n  (props, forwardedRef) => {\n    const { __scopeAccordion, disabled, dir, orientation = \"vertical\", ...accordionProps } = props;\n    const accordionRef = React.useRef(null);\n    const composedRefs = useComposedRefs(accordionRef, forwardedRef);\n    const getItems = useCollection(__scopeAccordion);\n    const direction = useDirection(dir);\n    const isDirectionLTR = direction === \"ltr\";\n    const handleKeyDown = composeEventHandlers(props.onKeyDown, (event) => {\n      if (!ACCORDION_KEYS.includes(event.key)) return;\n      const target = event.target;\n      const triggerCollection = getItems().filter((item) => !item.ref.current?.disabled);\n      const triggerIndex = triggerCollection.findIndex((item) => item.ref.current === target);\n      const triggerCount = triggerCollection.length;\n      if (triggerIndex === -1) return;\n      event.preventDefault();\n      let nextIndex = triggerIndex;\n      const homeIndex = 0;\n      const endIndex = triggerCount - 1;\n      const moveNext = () => {\n        nextIndex = triggerIndex + 1;\n        if (nextIndex > endIndex) {\n          nextIndex = homeIndex;\n        }\n      };\n      const movePrev = () => {\n        nextIndex = triggerIndex - 1;\n        if (nextIndex < homeIndex) {\n          nextIndex = endIndex;\n        }\n      };\n      switch (event.key) {\n        case \"Home\":\n          nextIndex = homeIndex;\n          break;\n        case \"End\":\n          nextIndex = endIndex;\n          break;\n        case \"ArrowRight\":\n          if (orientation === \"horizontal\") {\n            if (isDirectionLTR) {\n              moveNext();\n            } else {\n              movePrev();\n            }\n          }\n          break;\n        case \"ArrowDown\":\n          if (orientation === \"vertical\") {\n            moveNext();\n          }\n          break;\n        case \"ArrowLeft\":\n          if (orientation === \"horizontal\") {\n            if (isDirectionLTR) {\n              movePrev();\n            } else {\n              moveNext();\n            }\n          }\n          break;\n        case \"ArrowUp\":\n          if (orientation === \"vertical\") {\n            movePrev();\n          }\n          break;\n      }\n      const clampedIndex = nextIndex % triggerCount;\n      triggerCollection[clampedIndex].ref.current?.focus();\n    });\n    return /* @__PURE__ */ jsx(\n      AccordionImplProvider,\n      {\n        scope: __scopeAccordion,\n        disabled,\n        direction: dir,\n        orientation,\n        children: /* @__PURE__ */ jsx(Collection.Slot, { scope: __scopeAccordion, children: /* @__PURE__ */ jsx(\n          Primitive.div,\n          {\n            ...accordionProps,\n            \"data-orientation\": orientation,\n            ref: composedRefs,\n            onKeyDown: disabled ? void 0 : handleKeyDown\n          }\n        ) })\n      }\n    );\n  }\n);\nvar ITEM_NAME = \"AccordionItem\";\nvar [AccordionItemProvider, useAccordionItemContext] = createAccordionContext(ITEM_NAME);\nvar AccordionItem = React.forwardRef(\n  (props, forwardedRef) => {\n    const { __scopeAccordion, value, ...accordionItemProps } = props;\n    const accordionContext = useAccordionContext(ITEM_NAME, __scopeAccordion);\n    const valueContext = useAccordionValueContext(ITEM_NAME, __scopeAccordion);\n    const collapsibleScope = useCollapsibleScope(__scopeAccordion);\n    const triggerId = useId();\n    const open = value && valueContext.value.includes(value) || false;\n    const disabled = accordionContext.disabled || props.disabled;\n    return /* @__PURE__ */ jsx(\n      AccordionItemProvider,\n      {\n        scope: __scopeAccordion,\n        open,\n        disabled,\n        triggerId,\n        children: /* @__PURE__ */ jsx(\n          CollapsiblePrimitive.Root,\n          {\n            \"data-orientation\": accordionContext.orientation,\n            \"data-state\": getState(open),\n            ...collapsibleScope,\n            ...accordionItemProps,\n            ref: forwardedRef,\n            disabled,\n            open,\n            onOpenChange: (open2) => {\n              if (open2) {\n                valueContext.onItemOpen(value);\n              } else {\n                valueContext.onItemClose(value);\n              }\n            }\n          }\n        )\n      }\n    );\n  }\n);\nAccordionItem.displayName = ITEM_NAME;\nvar HEADER_NAME = \"AccordionHeader\";\nvar AccordionHeader = React.forwardRef(\n  (props, forwardedRef) => {\n    const { __scopeAccordion, ...headerProps } = props;\n    const accordionContext = useAccordionContext(ACCORDION_NAME, __scopeAccordion);\n    const itemContext = useAccordionItemContext(HEADER_NAME, __scopeAccordion);\n    return /* @__PURE__ */ jsx(\n      Primitive.h3,\n      {\n        \"data-orientation\": accordionContext.orientation,\n        \"data-state\": getState(itemContext.open),\n        \"data-disabled\": itemContext.disabled ? \"\" : void 0,\n        ...headerProps,\n        ref: forwardedRef\n      }\n    );\n  }\n);\nAccordionHeader.displayName = HEADER_NAME;\nvar TRIGGER_NAME = \"AccordionTrigger\";\nvar AccordionTrigger = React.forwardRef(\n  (props, forwardedRef) => {\n    const { __scopeAccordion, ...triggerProps } = props;\n    const accordionContext = useAccordionContext(ACCORDION_NAME, __scopeAccordion);\n    const itemContext = useAccordionItemContext(TRIGGER_NAME, __scopeAccordion);\n    const collapsibleContext = useAccordionCollapsibleContext(TRIGGER_NAME, __scopeAccordion);\n    const collapsibleScope = useCollapsibleScope(__scopeAccordion);\n    return /* @__PURE__ */ jsx(Collection.ItemSlot, { scope: __scopeAccordion, children: /* @__PURE__ */ jsx(\n      CollapsiblePrimitive.Trigger,\n      {\n        \"aria-disabled\": itemContext.open && !collapsibleContext.collapsible || void 0,\n        \"data-orientation\": accordionContext.orientation,\n        id: itemContext.triggerId,\n        ...collapsibleScope,\n        ...triggerProps,\n        ref: forwardedRef\n      }\n    ) });\n  }\n);\nAccordionTrigger.displayName = TRIGGER_NAME;\nvar CONTENT_NAME = \"AccordionContent\";\nvar AccordionContent = React.forwardRef(\n  (props, forwardedRef) => {\n    const { __scopeAccordion, ...contentProps } = props;\n    const accordionContext = useAccordionContext(ACCORDION_NAME, __scopeAccordion);\n    const itemContext = useAccordionItemContext(CONTENT_NAME, __scopeAccordion);\n    const collapsibleScope = useCollapsibleScope(__scopeAccordion);\n    return /* @__PURE__ */ jsx(\n      CollapsiblePrimitive.Content,\n      {\n        role: \"region\",\n        \"aria-labelledby\": itemContext.triggerId,\n        \"data-orientation\": accordionContext.orientation,\n        ...collapsibleScope,\n        ...contentProps,\n        ref: forwardedRef,\n        style: {\n          [\"--radix-accordion-content-height\"]: \"var(--radix-collapsible-content-height)\",\n          [\"--radix-accordion-content-width\"]: \"var(--radix-collapsible-content-width)\",\n          ...props.style\n        }\n      }\n    );\n  }\n);\nAccordionContent.displayName = CONTENT_NAME;\nfunction getState(open) {\n  return open ? \"open\" : \"closed\";\n}\nvar Root2 = Accordion;\nvar Item = AccordionItem;\nvar Header = AccordionHeader;\nvar Trigger2 = AccordionTrigger;\nvar Content2 = AccordionContent;\nexport {\n  Accordion,\n  AccordionContent,\n  AccordionHeader,\n  AccordionItem,\n  AccordionTrigger,\n  Content2 as Content,\n  Header,\n  Item,\n  Root2 as Root,\n  Trigger2 as Trigger,\n  createAccordionScope\n};\n//# sourceMappingURL=index.mjs.map\n", "\"use client\";\n\n// src/collapsible.tsx\nimport * as React from \"react\";\nimport { composeEventHandlers } from \"@radix-ui/primitive\";\nimport { createContextScope } from \"@radix-ui/react-context\";\nimport { useControllableState } from \"@radix-ui/react-use-controllable-state\";\nimport { useLayoutEffect } from \"@radix-ui/react-use-layout-effect\";\nimport { useComposedRefs } from \"@radix-ui/react-compose-refs\";\nimport { Primitive } from \"@radix-ui/react-primitive\";\nimport { Presence } from \"@radix-ui/react-presence\";\nimport { useId } from \"@radix-ui/react-id\";\nimport { jsx } from \"react/jsx-runtime\";\nvar COLLAPSIBLE_NAME = \"Collapsible\";\nvar [createCollapsibleContext, createCollapsibleScope] = createContextScope(COLLAPSIBLE_NAME);\nvar [CollapsibleProvider, useCollapsibleContext] = createCollapsibleContext(COLLAPSIBLE_NAME);\nvar Collapsible = React.forwardRef(\n  (props, forwardedRef) => {\n    const {\n      __scopeCollapsible,\n      open: openProp,\n      defaultOpen,\n      disabled,\n      onOpenChange,\n      ...collapsibleProps\n    } = props;\n    const [open, setOpen] = useControllableState({\n      prop: openProp,\n      defaultProp: defaultOpen ?? false,\n      onChange: onOpenChange,\n      caller: COLLAPSIBLE_NAME\n    });\n    return /* @__PURE__ */ jsx(\n      CollapsibleProvider,\n      {\n        scope: __scopeCollapsible,\n        disabled,\n        contentId: useId(),\n        open,\n        onOpenToggle: React.useCallback(() => setOpen((prevOpen) => !prevOpen), [setOpen]),\n        children: /* @__PURE__ */ jsx(\n          Primitive.div,\n          {\n            \"data-state\": getState(open),\n            \"data-disabled\": disabled ? \"\" : void 0,\n            ...collapsibleProps,\n            ref: forwardedRef\n          }\n        )\n      }\n    );\n  }\n);\nCollapsible.displayName = COLLAPSIBLE_NAME;\nvar TRIGGER_NAME = \"CollapsibleTrigger\";\nvar CollapsibleTrigger = React.forwardRef(\n  (props, forwardedRef) => {\n    const { __scopeCollapsible, ...triggerProps } = props;\n    const context = useCollapsibleContext(TRIGGER_NAME, __scopeCollapsible);\n    return /* @__PURE__ */ jsx(\n      Primitive.button,\n      {\n        type: \"button\",\n        \"aria-controls\": context.contentId,\n        \"aria-expanded\": context.open || false,\n        \"data-state\": getState(context.open),\n        \"data-disabled\": context.disabled ? \"\" : void 0,\n        disabled: context.disabled,\n        ...triggerProps,\n        ref: forwardedRef,\n        onClick: composeEventHandlers(props.onClick, context.onOpenToggle)\n      }\n    );\n  }\n);\nCollapsibleTrigger.displayName = TRIGGER_NAME;\nvar CONTENT_NAME = \"CollapsibleContent\";\nvar CollapsibleContent = React.forwardRef(\n  (props, forwardedRef) => {\n    const { forceMount, ...contentProps } = props;\n    const context = useCollapsibleContext(CONTENT_NAME, props.__scopeCollapsible);\n    return /* @__PURE__ */ jsx(Presence, { present: forceMount || context.open, children: ({ present }) => /* @__PURE__ */ jsx(CollapsibleContentImpl, { ...contentProps, ref: forwardedRef, present }) });\n  }\n);\nCollapsibleContent.displayName = CONTENT_NAME;\nvar CollapsibleContentImpl = React.forwardRef((props, forwardedRef) => {\n  const { __scopeCollapsible, present, children, ...contentProps } = props;\n  const context = useCollapsibleContext(CONTENT_NAME, __scopeCollapsible);\n  const [isPresent, setIsPresent] = React.useState(present);\n  const ref = React.useRef(null);\n  const composedRefs = useComposedRefs(forwardedRef, ref);\n  const heightRef = React.useRef(0);\n  const height = heightRef.current;\n  const widthRef = React.useRef(0);\n  const width = widthRef.current;\n  const isOpen = context.open || isPresent;\n  const isMountAnimationPreventedRef = React.useRef(isOpen);\n  const originalStylesRef = React.useRef(void 0);\n  React.useEffect(() => {\n    const rAF = requestAnimationFrame(() => isMountAnimationPreventedRef.current = false);\n    return () => cancelAnimationFrame(rAF);\n  }, []);\n  useLayoutEffect(() => {\n    const node = ref.current;\n    if (node) {\n      originalStylesRef.current = originalStylesRef.current || {\n        transitionDuration: node.style.transitionDuration,\n        animationName: node.style.animationName\n      };\n      node.style.transitionDuration = \"0s\";\n      node.style.animationName = \"none\";\n      const rect = node.getBoundingClientRect();\n      heightRef.current = rect.height;\n      widthRef.current = rect.width;\n      if (!isMountAnimationPreventedRef.current) {\n        node.style.transitionDuration = originalStylesRef.current.transitionDuration;\n        node.style.animationName = originalStylesRef.current.animationName;\n      }\n      setIsPresent(present);\n    }\n  }, [context.open, present]);\n  return /* @__PURE__ */ jsx(\n    Primitive.div,\n    {\n      \"data-state\": getState(context.open),\n      \"data-disabled\": context.disabled ? \"\" : void 0,\n      id: context.contentId,\n      hidden: !isOpen,\n      ...contentProps,\n      ref: composedRefs,\n      style: {\n        [`--radix-collapsible-content-height`]: height ? `${height}px` : void 0,\n        [`--radix-collapsible-content-width`]: width ? `${width}px` : void 0,\n        ...props.style\n      },\n      children: isOpen && children\n    }\n  );\n});\nfunction getState(open) {\n  return open ? \"open\" : \"closed\";\n}\nvar Root = Collapsible;\nvar Trigger = CollapsibleTrigger;\nvar Content = CollapsibleContent;\nexport {\n  Collapsible,\n  CollapsibleContent,\n  CollapsibleTrigger,\n  Content,\n  Root,\n  Trigger,\n  createCollapsibleScope\n};\n//# sourceMappingURL=index.mjs.map\n", "/**\n * @license lucide-react v0.511.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport { forwardRef, createElement } from 'react';\nimport defaultAttributes from './defaultAttributes.js';\nimport { mergeClasses, hasA11yProp } from './shared/src/utils.js';\n\nconst Icon = forwardRef(\n  ({\n    color = \"currentColor\",\n    size = 24,\n    strokeWidth = 2,\n    absoluteStrokeWidth,\n    className = \"\",\n    children,\n    iconNode,\n    ...rest\n  }, ref) => createElement(\n    \"svg\",\n    {\n      ref,\n      ...defaultAttributes,\n      width: size,\n      height: size,\n      stroke: color,\n      strokeWidth: absoluteStrokeWidth ? Number(strokeWidth) * 24 / Number(size) : strokeWidth,\n      className: mergeClasses(\"lucide\", className),\n      ...!children && !hasA11yProp(rest) && { \"aria-hidden\": \"true\" },\n      ...rest\n    },\n    [\n      ...iconNode.map(([tag, attrs]) => createElement(tag, attrs)),\n      ...Array.isArray(children) ? children : [children]\n    ]\n  )\n);\n\nexport { Icon as default };\n//# sourceMappingURL=Icon.js.map\n", "/**\n * @license lucide-react v0.511.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport { forwardRef, createElement } from 'react';\nimport { mergeClasses, toKebabCase, toPascalCase } from './shared/src/utils.js';\nimport Icon from './Icon.js';\n\nconst createLucideIcon = (iconName, iconNode) => {\n  const Component = forwardRef(\n    ({ className, ...props }, ref) => createElement(Icon, {\n      ref,\n      iconNode,\n      className: mergeClasses(\n        `lucide-${toKebabCase(toPascalCase(iconName))}`,\n        `lucide-${iconName}`,\n        className\n      ),\n      ...props\n    })\n  );\n  Component.displayName = toPascalCase(iconName);\n  return Component;\n};\n\nexport { createLucideIcon as default };\n//# sourceMappingURL=createLucideIcon.js.map\n", "/**\n * @license lucide-react v0.511.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nvar defaultAttributes = {\n  xmlns: \"http://www.w3.org/2000/svg\",\n  width: 24,\n  height: 24,\n  viewBox: \"0 0 24 24\",\n  fill: \"none\",\n  stroke: \"currentColor\",\n  strokeWidth: 2,\n  strokeLinecap: \"round\",\n  strokeLinejoin: \"round\"\n};\n\nexport { defaultAttributes as default };\n//# sourceMappingURL=defaultAttributes.js.map\n", "/**\n * @license lucide-react v0.511.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\n\nconst __iconNode = [[\"path\", { d: \"m6 9 6 6 6-6\", key: \"qrunsl\" }]];\nconst ChevronDown = createLucideIcon(\"chevron-down\", __iconNode);\n\nexport { __iconNode, ChevronDown as default };\n//# sourceMappingURL=chevron-down.js.map\n", "/**\n * @license lucide-react v0.511.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nconst toKebabCase = (string) => string.replace(/([a-z0-9])([A-Z])/g, \"$1-$2\").toLowerCase();\nconst toCamelCase = (string) => string.replace(\n  /^([A-Z])|[\\s-_]+(\\w)/g,\n  (match, p1, p2) => p2 ? p2.toUpperCase() : p1.toLowerCase()\n);\nconst toPascalCase = (string) => {\n  const camelCase = toCamelCase(string);\n  return camelCase.charAt(0).toUpperCase() + camelCase.slice(1);\n};\nconst mergeClasses = (...classes) => classes.filter((className, index, array) => {\n  return Boolean(className) && className.trim() !== \"\" && array.indexOf(className) === index;\n}).join(\" \").trim();\nconst hasA11yProp = (props) => {\n  for (const prop in props) {\n    if (prop.startsWith(\"aria-\") || prop === \"role\" || prop === \"title\") {\n      return true;\n    }\n  }\n};\n\nexport { hasA11yProp, mergeClasses, toCamelCase, toKebabCase, toPascalCase };\n//# sourceMappingURL=utils.js.map\n"], "names": [], "sourceRoot": ""}