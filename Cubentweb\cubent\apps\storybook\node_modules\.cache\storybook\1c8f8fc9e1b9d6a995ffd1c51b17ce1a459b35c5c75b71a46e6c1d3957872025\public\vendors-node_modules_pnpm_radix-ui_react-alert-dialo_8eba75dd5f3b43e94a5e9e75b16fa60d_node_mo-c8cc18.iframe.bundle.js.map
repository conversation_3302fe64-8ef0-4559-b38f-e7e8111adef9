{"version": 3, "file": "vendors-node_modules_pnpm_radix-ui_react-alert-dialo_8eba75dd5f3b43e94a5e9e75b16fa60d_node_mo-c8cc18.iframe.bundle.js", "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAqBA;AACA;;;;;;;;;;;;;;;;;ACzKA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "sources": ["webpack://storybook/../../node_modules/.pnpm/@radix-ui+react-alert-dialo_8eba75dd5f3b43e94a5e9e75b16fa60d/node_modules/@radix-ui/react-alert-dialog/dist/index.mjs", "webpack://storybook/../../node_modules/.pnpm/class-variance-authority@0.7.1/node_modules/class-variance-authority/dist/index.mjs"], "sourcesContent": ["\"use client\";\n\n// src/alert-dialog.tsx\nimport * as React from \"react\";\nimport { createContextScope } from \"@radix-ui/react-context\";\nimport { useComposedRefs } from \"@radix-ui/react-compose-refs\";\nimport * as DialogPrimitive from \"@radix-ui/react-dialog\";\nimport { createDialogScope } from \"@radix-ui/react-dialog\";\nimport { composeEventHandlers } from \"@radix-ui/primitive\";\nimport { createSlottable } from \"@radix-ui/react-slot\";\nimport { jsx, jsxs } from \"react/jsx-runtime\";\nvar ROOT_NAME = \"AlertDialog\";\nvar [createAlertDialogContext, createAlertDialogScope] = createContextScope(ROOT_NAME, [\n  createDialogScope\n]);\nvar useDialogScope = createDialogScope();\nvar AlertDialog = (props) => {\n  const { __scopeAlertDialog, ...alertDialogProps } = props;\n  const dialogScope = useDialogScope(__scopeAlertDialog);\n  return /* @__PURE__ */ jsx(DialogPrimitive.Root, { ...dialogScope, ...alertDialogProps, modal: true });\n};\nAlertDialog.displayName = ROOT_NAME;\nvar TRIGGER_NAME = \"AlertDialogTrigger\";\nvar AlertDialogTrigger = React.forwardRef(\n  (props, forwardedRef) => {\n    const { __scopeAlertDialog, ...triggerProps } = props;\n    const dialogScope = useDialogScope(__scopeAlertDialog);\n    return /* @__PURE__ */ jsx(DialogPrimitive.Trigger, { ...dialogScope, ...triggerProps, ref: forwardedRef });\n  }\n);\nAlertDialogTrigger.displayName = TRIGGER_NAME;\nvar PORTAL_NAME = \"AlertDialogPortal\";\nvar AlertDialogPortal = (props) => {\n  const { __scopeAlertDialog, ...portalProps } = props;\n  const dialogScope = useDialogScope(__scopeAlertDialog);\n  return /* @__PURE__ */ jsx(DialogPrimitive.Portal, { ...dialogScope, ...portalProps });\n};\nAlertDialogPortal.displayName = PORTAL_NAME;\nvar OVERLAY_NAME = \"AlertDialogOverlay\";\nvar AlertDialogOverlay = React.forwardRef(\n  (props, forwardedRef) => {\n    const { __scopeAlertDialog, ...overlayProps } = props;\n    const dialogScope = useDialogScope(__scopeAlertDialog);\n    return /* @__PURE__ */ jsx(DialogPrimitive.Overlay, { ...dialogScope, ...overlayProps, ref: forwardedRef });\n  }\n);\nAlertDialogOverlay.displayName = OVERLAY_NAME;\nvar CONTENT_NAME = \"AlertDialogContent\";\nvar [AlertDialogContentProvider, useAlertDialogContentContext] = createAlertDialogContext(CONTENT_NAME);\nvar Slottable = createSlottable(\"AlertDialogContent\");\nvar AlertDialogContent = React.forwardRef(\n  (props, forwardedRef) => {\n    const { __scopeAlertDialog, children, ...contentProps } = props;\n    const dialogScope = useDialogScope(__scopeAlertDialog);\n    const contentRef = React.useRef(null);\n    const composedRefs = useComposedRefs(forwardedRef, contentRef);\n    const cancelRef = React.useRef(null);\n    return /* @__PURE__ */ jsx(\n      DialogPrimitive.WarningProvider,\n      {\n        contentName: CONTENT_NAME,\n        titleName: TITLE_NAME,\n        docsSlug: \"alert-dialog\",\n        children: /* @__PURE__ */ jsx(AlertDialogContentProvider, { scope: __scopeAlertDialog, cancelRef, children: /* @__PURE__ */ jsxs(\n          DialogPrimitive.Content,\n          {\n            role: \"alertdialog\",\n            ...dialogScope,\n            ...contentProps,\n            ref: composedRefs,\n            onOpenAutoFocus: composeEventHandlers(contentProps.onOpenAutoFocus, (event) => {\n              event.preventDefault();\n              cancelRef.current?.focus({ preventScroll: true });\n            }),\n            onPointerDownOutside: (event) => event.preventDefault(),\n            onInteractOutside: (event) => event.preventDefault(),\n            children: [\n              /* @__PURE__ */ jsx(Slottable, { children }),\n              /* @__PURE__ */ jsx(DescriptionWarning, { contentRef })\n            ]\n          }\n        ) })\n      }\n    );\n  }\n);\nAlertDialogContent.displayName = CONTENT_NAME;\nvar TITLE_NAME = \"AlertDialogTitle\";\nvar AlertDialogTitle = React.forwardRef(\n  (props, forwardedRef) => {\n    const { __scopeAlertDialog, ...titleProps } = props;\n    const dialogScope = useDialogScope(__scopeAlertDialog);\n    return /* @__PURE__ */ jsx(DialogPrimitive.Title, { ...dialogScope, ...titleProps, ref: forwardedRef });\n  }\n);\nAlertDialogTitle.displayName = TITLE_NAME;\nvar DESCRIPTION_NAME = \"AlertDialogDescription\";\nvar AlertDialogDescription = React.forwardRef((props, forwardedRef) => {\n  const { __scopeAlertDialog, ...descriptionProps } = props;\n  const dialogScope = useDialogScope(__scopeAlertDialog);\n  return /* @__PURE__ */ jsx(DialogPrimitive.Description, { ...dialogScope, ...descriptionProps, ref: forwardedRef });\n});\nAlertDialogDescription.displayName = DESCRIPTION_NAME;\nvar ACTION_NAME = \"AlertDialogAction\";\nvar AlertDialogAction = React.forwardRef(\n  (props, forwardedRef) => {\n    const { __scopeAlertDialog, ...actionProps } = props;\n    const dialogScope = useDialogScope(__scopeAlertDialog);\n    return /* @__PURE__ */ jsx(DialogPrimitive.Close, { ...dialogScope, ...actionProps, ref: forwardedRef });\n  }\n);\nAlertDialogAction.displayName = ACTION_NAME;\nvar CANCEL_NAME = \"AlertDialogCancel\";\nvar AlertDialogCancel = React.forwardRef(\n  (props, forwardedRef) => {\n    const { __scopeAlertDialog, ...cancelProps } = props;\n    const { cancelRef } = useAlertDialogContentContext(CANCEL_NAME, __scopeAlertDialog);\n    const dialogScope = useDialogScope(__scopeAlertDialog);\n    const ref = useComposedRefs(forwardedRef, cancelRef);\n    return /* @__PURE__ */ jsx(DialogPrimitive.Close, { ...dialogScope, ...cancelProps, ref });\n  }\n);\nAlertDialogCancel.displayName = CANCEL_NAME;\nvar DescriptionWarning = ({ contentRef }) => {\n  const MESSAGE = `\\`${CONTENT_NAME}\\` requires a description for the component to be accessible for screen reader users.\n\nYou can add a description to the \\`${CONTENT_NAME}\\` by passing a \\`${DESCRIPTION_NAME}\\` component as a child, which also benefits sighted users by adding visible context to the dialog.\n\nAlternatively, you can use your own component as a description by assigning it an \\`id\\` and passing the same value to the \\`aria-describedby\\` prop in \\`${CONTENT_NAME}\\`. If the description is confusing or duplicative for sighted users, you can use the \\`@radix-ui/react-visually-hidden\\` primitive as a wrapper around your description component.\n\nFor more information, see https://radix-ui.com/primitives/docs/components/alert-dialog`;\n  React.useEffect(() => {\n    const hasDescription = document.getElementById(\n      contentRef.current?.getAttribute(\"aria-describedby\")\n    );\n    if (!hasDescription) console.warn(MESSAGE);\n  }, [MESSAGE, contentRef]);\n  return null;\n};\nvar Root2 = AlertDialog;\nvar Trigger2 = AlertDialogTrigger;\nvar Portal2 = AlertDialogPortal;\nvar Overlay2 = AlertDialogOverlay;\nvar Content2 = AlertDialogContent;\nvar Action = AlertDialogAction;\nvar Cancel = AlertDialogCancel;\nvar Title2 = AlertDialogTitle;\nvar Description2 = AlertDialogDescription;\nexport {\n  Action,\n  AlertDialog,\n  AlertDialogAction,\n  AlertDialogCancel,\n  AlertDialogContent,\n  AlertDialogDescription,\n  AlertDialogOverlay,\n  AlertDialogPortal,\n  AlertDialogTitle,\n  AlertDialogTrigger,\n  Cancel,\n  Content2 as Content,\n  Description2 as Description,\n  Overlay2 as Overlay,\n  Portal2 as Portal,\n  Root2 as Root,\n  Title2 as Title,\n  Trigger2 as Trigger,\n  createAlertDialogScope\n};\n//# sourceMappingURL=index.mjs.map\n", "/**\n * Copyright 2022 Joe Bell. All rights reserved.\n *\n * This file is licensed to you under the Apache License, Version 2.0\n * (the \"License\"); you may not use this file except in compliance with the\n * License. You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS, WITHOUT\n * WARRANTIES OR REPRESENTATIONS OF ANY KIND, either express or implied. See the\n * License for the specific language governing permissions and limitations under\n * the License.\n */ import { clsx } from \"clsx\";\nconst falsyToString = (value)=>typeof value === \"boolean\" ? `${value}` : value === 0 ? \"0\" : value;\nexport const cx = clsx;\nexport const cva = (base, config)=>(props)=>{\n        var _config_compoundVariants;\n        if ((config === null || config === void 0 ? void 0 : config.variants) == null) return cx(base, props === null || props === void 0 ? void 0 : props.class, props === null || props === void 0 ? void 0 : props.className);\n        const { variants, defaultVariants } = config;\n        const getVariantClassNames = Object.keys(variants).map((variant)=>{\n            const variantProp = props === null || props === void 0 ? void 0 : props[variant];\n            const defaultVariantProp = defaultVariants === null || defaultVariants === void 0 ? void 0 : defaultVariants[variant];\n            if (variantProp === null) return null;\n            const variantKey = falsyToString(variantProp) || falsyToString(defaultVariantProp);\n            return variants[variant][variantKey];\n        });\n        const propsWithoutUndefined = props && Object.entries(props).reduce((acc, param)=>{\n            let [key, value] = param;\n            if (value === undefined) {\n                return acc;\n            }\n            acc[key] = value;\n            return acc;\n        }, {});\n        const getCompoundVariantClassNames = config === null || config === void 0 ? void 0 : (_config_compoundVariants = config.compoundVariants) === null || _config_compoundVariants === void 0 ? void 0 : _config_compoundVariants.reduce((acc, param)=>{\n            let { class: cvClass, className: cvClassName, ...compoundVariantOptions } = param;\n            return Object.entries(compoundVariantOptions).every((param)=>{\n                let [key, value] = param;\n                return Array.isArray(value) ? value.includes({\n                    ...defaultVariants,\n                    ...propsWithoutUndefined\n                }[key]) : ({\n                    ...defaultVariants,\n                    ...propsWithoutUndefined\n                })[key] === value;\n            }) ? [\n                ...acc,\n                cvClass,\n                cvClassName\n            ] : acc;\n        }, []);\n        return cx(base, getVariantClassNames, getCompoundVariantClassNames, props === null || props === void 0 ? void 0 : props.class, props === null || props === void 0 ? void 0 : props.className);\n    };\n\n"], "names": [], "sourceRoot": ""}