{"version": 3, "file": "vendors-node_modules_pnpm_radix-ui_react-context-men_72c6958fcc1befd6afed1fc8a44148fd_node_mo-c73e08.iframe.bundle.js", "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAmCA;AACA", "sources": ["webpack://storybook/../../node_modules/.pnpm/@radix-ui+react-context-men_72c6958fcc1befd6afed1fc8a44148fd/node_modules/@radix-ui/react-context-menu/dist/index.mjs"], "sourcesContent": ["\"use client\";\n\n// src/context-menu.tsx\nimport * as React from \"react\";\nimport { composeEventHandlers } from \"@radix-ui/primitive\";\nimport { createContextScope } from \"@radix-ui/react-context\";\nimport { Primitive } from \"@radix-ui/react-primitive\";\nimport * as MenuPrimitive from \"@radix-ui/react-menu\";\nimport { createMenuScope } from \"@radix-ui/react-menu\";\nimport { useCallbackRef } from \"@radix-ui/react-use-callback-ref\";\nimport { useControllableState } from \"@radix-ui/react-use-controllable-state\";\nimport { Fragment, jsx, jsxs } from \"react/jsx-runtime\";\nvar CONTEXT_MENU_NAME = \"ContextMenu\";\nvar [createContextMenuContext, createContextMenuScope] = createContextScope(CONTEXT_MENU_NAME, [\n  createMenuScope\n]);\nvar useMenuScope = createMenuScope();\nvar [ContextMenuProvider, useContextMenuContext] = createContextMenuContext(CONTEXT_MENU_NAME);\nvar ContextMenu = (props) => {\n  const { __scopeContextMenu, children, onOpenChange, dir, modal = true } = props;\n  const [open, setOpen] = React.useState(false);\n  const menuScope = useMenuScope(__scopeContextMenu);\n  const handleOpenChangeProp = useCallbackRef(onOpenChange);\n  const handleOpenChange = React.useCallback(\n    (open2) => {\n      setOpen(open2);\n      handleOpenChangeProp(open2);\n    },\n    [handleOpenChangeProp]\n  );\n  return /* @__PURE__ */ jsx(\n    ContextMenuProvider,\n    {\n      scope: __scopeContextMenu,\n      open,\n      onOpenChange: handleOpenChange,\n      modal,\n      children: /* @__PURE__ */ jsx(\n        MenuPrimitive.Root,\n        {\n          ...menuScope,\n          dir,\n          open,\n          onOpenChange: handleOpenChange,\n          modal,\n          children\n        }\n      )\n    }\n  );\n};\nContextMenu.displayName = CONTEXT_MENU_NAME;\nvar TRIGGER_NAME = \"ContextMenuTrigger\";\nvar ContextMenuTrigger = React.forwardRef(\n  (props, forwardedRef) => {\n    const { __scopeContextMenu, disabled = false, ...triggerProps } = props;\n    const context = useContextMenuContext(TRIGGER_NAME, __scopeContextMenu);\n    const menuScope = useMenuScope(__scopeContextMenu);\n    const pointRef = React.useRef({ x: 0, y: 0 });\n    const virtualRef = React.useRef({\n      getBoundingClientRect: () => DOMRect.fromRect({ width: 0, height: 0, ...pointRef.current })\n    });\n    const longPressTimerRef = React.useRef(0);\n    const clearLongPress = React.useCallback(\n      () => window.clearTimeout(longPressTimerRef.current),\n      []\n    );\n    const handleOpen = (event) => {\n      pointRef.current = { x: event.clientX, y: event.clientY };\n      context.onOpenChange(true);\n    };\n    React.useEffect(() => clearLongPress, [clearLongPress]);\n    React.useEffect(() => void (disabled && clearLongPress()), [disabled, clearLongPress]);\n    return /* @__PURE__ */ jsxs(Fragment, { children: [\n      /* @__PURE__ */ jsx(MenuPrimitive.Anchor, { ...menuScope, virtualRef }),\n      /* @__PURE__ */ jsx(\n        Primitive.span,\n        {\n          \"data-state\": context.open ? \"open\" : \"closed\",\n          \"data-disabled\": disabled ? \"\" : void 0,\n          ...triggerProps,\n          ref: forwardedRef,\n          style: { WebkitTouchCallout: \"none\", ...props.style },\n          onContextMenu: disabled ? props.onContextMenu : composeEventHandlers(props.onContextMenu, (event) => {\n            clearLongPress();\n            handleOpen(event);\n            event.preventDefault();\n          }),\n          onPointerDown: disabled ? props.onPointerDown : composeEventHandlers(\n            props.onPointerDown,\n            whenTouchOrPen((event) => {\n              clearLongPress();\n              longPressTimerRef.current = window.setTimeout(() => handleOpen(event), 700);\n            })\n          ),\n          onPointerMove: disabled ? props.onPointerMove : composeEventHandlers(props.onPointerMove, whenTouchOrPen(clearLongPress)),\n          onPointerCancel: disabled ? props.onPointerCancel : composeEventHandlers(props.onPointerCancel, whenTouchOrPen(clearLongPress)),\n          onPointerUp: disabled ? props.onPointerUp : composeEventHandlers(props.onPointerUp, whenTouchOrPen(clearLongPress))\n        }\n      )\n    ] });\n  }\n);\nContextMenuTrigger.displayName = TRIGGER_NAME;\nvar PORTAL_NAME = \"ContextMenuPortal\";\nvar ContextMenuPortal = (props) => {\n  const { __scopeContextMenu, ...portalProps } = props;\n  const menuScope = useMenuScope(__scopeContextMenu);\n  return /* @__PURE__ */ jsx(MenuPrimitive.Portal, { ...menuScope, ...portalProps });\n};\nContextMenuPortal.displayName = PORTAL_NAME;\nvar CONTENT_NAME = \"ContextMenuContent\";\nvar ContextMenuContent = React.forwardRef(\n  (props, forwardedRef) => {\n    const { __scopeContextMenu, ...contentProps } = props;\n    const context = useContextMenuContext(CONTENT_NAME, __scopeContextMenu);\n    const menuScope = useMenuScope(__scopeContextMenu);\n    const hasInteractedOutsideRef = React.useRef(false);\n    return /* @__PURE__ */ jsx(\n      MenuPrimitive.Content,\n      {\n        ...menuScope,\n        ...contentProps,\n        ref: forwardedRef,\n        side: \"right\",\n        sideOffset: 2,\n        align: \"start\",\n        onCloseAutoFocus: (event) => {\n          props.onCloseAutoFocus?.(event);\n          if (!event.defaultPrevented && hasInteractedOutsideRef.current) {\n            event.preventDefault();\n          }\n          hasInteractedOutsideRef.current = false;\n        },\n        onInteractOutside: (event) => {\n          props.onInteractOutside?.(event);\n          if (!event.defaultPrevented && !context.modal) hasInteractedOutsideRef.current = true;\n        },\n        style: {\n          ...props.style,\n          // re-namespace exposed content custom properties\n          ...{\n            \"--radix-context-menu-content-transform-origin\": \"var(--radix-popper-transform-origin)\",\n            \"--radix-context-menu-content-available-width\": \"var(--radix-popper-available-width)\",\n            \"--radix-context-menu-content-available-height\": \"var(--radix-popper-available-height)\",\n            \"--radix-context-menu-trigger-width\": \"var(--radix-popper-anchor-width)\",\n            \"--radix-context-menu-trigger-height\": \"var(--radix-popper-anchor-height)\"\n          }\n        }\n      }\n    );\n  }\n);\nContextMenuContent.displayName = CONTENT_NAME;\nvar GROUP_NAME = \"ContextMenuGroup\";\nvar ContextMenuGroup = React.forwardRef(\n  (props, forwardedRef) => {\n    const { __scopeContextMenu, ...groupProps } = props;\n    const menuScope = useMenuScope(__scopeContextMenu);\n    return /* @__PURE__ */ jsx(MenuPrimitive.Group, { ...menuScope, ...groupProps, ref: forwardedRef });\n  }\n);\nContextMenuGroup.displayName = GROUP_NAME;\nvar LABEL_NAME = \"ContextMenuLabel\";\nvar ContextMenuLabel = React.forwardRef(\n  (props, forwardedRef) => {\n    const { __scopeContextMenu, ...labelProps } = props;\n    const menuScope = useMenuScope(__scopeContextMenu);\n    return /* @__PURE__ */ jsx(MenuPrimitive.Label, { ...menuScope, ...labelProps, ref: forwardedRef });\n  }\n);\nContextMenuLabel.displayName = LABEL_NAME;\nvar ITEM_NAME = \"ContextMenuItem\";\nvar ContextMenuItem = React.forwardRef(\n  (props, forwardedRef) => {\n    const { __scopeContextMenu, ...itemProps } = props;\n    const menuScope = useMenuScope(__scopeContextMenu);\n    return /* @__PURE__ */ jsx(MenuPrimitive.Item, { ...menuScope, ...itemProps, ref: forwardedRef });\n  }\n);\nContextMenuItem.displayName = ITEM_NAME;\nvar CHECKBOX_ITEM_NAME = \"ContextMenuCheckboxItem\";\nvar ContextMenuCheckboxItem = React.forwardRef((props, forwardedRef) => {\n  const { __scopeContextMenu, ...checkboxItemProps } = props;\n  const menuScope = useMenuScope(__scopeContextMenu);\n  return /* @__PURE__ */ jsx(MenuPrimitive.CheckboxItem, { ...menuScope, ...checkboxItemProps, ref: forwardedRef });\n});\nContextMenuCheckboxItem.displayName = CHECKBOX_ITEM_NAME;\nvar RADIO_GROUP_NAME = \"ContextMenuRadioGroup\";\nvar ContextMenuRadioGroup = React.forwardRef((props, forwardedRef) => {\n  const { __scopeContextMenu, ...radioGroupProps } = props;\n  const menuScope = useMenuScope(__scopeContextMenu);\n  return /* @__PURE__ */ jsx(MenuPrimitive.RadioGroup, { ...menuScope, ...radioGroupProps, ref: forwardedRef });\n});\nContextMenuRadioGroup.displayName = RADIO_GROUP_NAME;\nvar RADIO_ITEM_NAME = \"ContextMenuRadioItem\";\nvar ContextMenuRadioItem = React.forwardRef((props, forwardedRef) => {\n  const { __scopeContextMenu, ...radioItemProps } = props;\n  const menuScope = useMenuScope(__scopeContextMenu);\n  return /* @__PURE__ */ jsx(MenuPrimitive.RadioItem, { ...menuScope, ...radioItemProps, ref: forwardedRef });\n});\nContextMenuRadioItem.displayName = RADIO_ITEM_NAME;\nvar INDICATOR_NAME = \"ContextMenuItemIndicator\";\nvar ContextMenuItemIndicator = React.forwardRef((props, forwardedRef) => {\n  const { __scopeContextMenu, ...itemIndicatorProps } = props;\n  const menuScope = useMenuScope(__scopeContextMenu);\n  return /* @__PURE__ */ jsx(MenuPrimitive.ItemIndicator, { ...menuScope, ...itemIndicatorProps, ref: forwardedRef });\n});\nContextMenuItemIndicator.displayName = INDICATOR_NAME;\nvar SEPARATOR_NAME = \"ContextMenuSeparator\";\nvar ContextMenuSeparator = React.forwardRef((props, forwardedRef) => {\n  const { __scopeContextMenu, ...separatorProps } = props;\n  const menuScope = useMenuScope(__scopeContextMenu);\n  return /* @__PURE__ */ jsx(MenuPrimitive.Separator, { ...menuScope, ...separatorProps, ref: forwardedRef });\n});\nContextMenuSeparator.displayName = SEPARATOR_NAME;\nvar ARROW_NAME = \"ContextMenuArrow\";\nvar ContextMenuArrow = React.forwardRef(\n  (props, forwardedRef) => {\n    const { __scopeContextMenu, ...arrowProps } = props;\n    const menuScope = useMenuScope(__scopeContextMenu);\n    return /* @__PURE__ */ jsx(MenuPrimitive.Arrow, { ...menuScope, ...arrowProps, ref: forwardedRef });\n  }\n);\nContextMenuArrow.displayName = ARROW_NAME;\nvar SUB_NAME = \"ContextMenuSub\";\nvar ContextMenuSub = (props) => {\n  const { __scopeContextMenu, children, onOpenChange, open: openProp, defaultOpen } = props;\n  const menuScope = useMenuScope(__scopeContextMenu);\n  const [open, setOpen] = useControllableState({\n    prop: openProp,\n    defaultProp: defaultOpen ?? false,\n    onChange: onOpenChange,\n    caller: SUB_NAME\n  });\n  return /* @__PURE__ */ jsx(MenuPrimitive.Sub, { ...menuScope, open, onOpenChange: setOpen, children });\n};\nContextMenuSub.displayName = SUB_NAME;\nvar SUB_TRIGGER_NAME = \"ContextMenuSubTrigger\";\nvar ContextMenuSubTrigger = React.forwardRef((props, forwardedRef) => {\n  const { __scopeContextMenu, ...triggerItemProps } = props;\n  const menuScope = useMenuScope(__scopeContextMenu);\n  return /* @__PURE__ */ jsx(MenuPrimitive.SubTrigger, { ...menuScope, ...triggerItemProps, ref: forwardedRef });\n});\nContextMenuSubTrigger.displayName = SUB_TRIGGER_NAME;\nvar SUB_CONTENT_NAME = \"ContextMenuSubContent\";\nvar ContextMenuSubContent = React.forwardRef((props, forwardedRef) => {\n  const { __scopeContextMenu, ...subContentProps } = props;\n  const menuScope = useMenuScope(__scopeContextMenu);\n  return /* @__PURE__ */ jsx(\n    MenuPrimitive.SubContent,\n    {\n      ...menuScope,\n      ...subContentProps,\n      ref: forwardedRef,\n      style: {\n        ...props.style,\n        // re-namespace exposed content custom properties\n        ...{\n          \"--radix-context-menu-content-transform-origin\": \"var(--radix-popper-transform-origin)\",\n          \"--radix-context-menu-content-available-width\": \"var(--radix-popper-available-width)\",\n          \"--radix-context-menu-content-available-height\": \"var(--radix-popper-available-height)\",\n          \"--radix-context-menu-trigger-width\": \"var(--radix-popper-anchor-width)\",\n          \"--radix-context-menu-trigger-height\": \"var(--radix-popper-anchor-height)\"\n        }\n      }\n    }\n  );\n});\nContextMenuSubContent.displayName = SUB_CONTENT_NAME;\nfunction whenTouchOrPen(handler) {\n  return (event) => event.pointerType !== \"mouse\" ? handler(event) : void 0;\n}\nvar Root2 = ContextMenu;\nvar Trigger = ContextMenuTrigger;\nvar Portal2 = ContextMenuPortal;\nvar Content2 = ContextMenuContent;\nvar Group2 = ContextMenuGroup;\nvar Label2 = ContextMenuLabel;\nvar Item2 = ContextMenuItem;\nvar CheckboxItem2 = ContextMenuCheckboxItem;\nvar RadioGroup2 = ContextMenuRadioGroup;\nvar RadioItem2 = ContextMenuRadioItem;\nvar ItemIndicator2 = ContextMenuItemIndicator;\nvar Separator2 = ContextMenuSeparator;\nvar Arrow2 = ContextMenuArrow;\nvar Sub2 = ContextMenuSub;\nvar SubTrigger2 = ContextMenuSubTrigger;\nvar SubContent2 = ContextMenuSubContent;\nexport {\n  Arrow2 as Arrow,\n  CheckboxItem2 as CheckboxItem,\n  Content2 as Content,\n  ContextMenu,\n  ContextMenuArrow,\n  ContextMenuCheckboxItem,\n  ContextMenuContent,\n  ContextMenuGroup,\n  ContextMenuItem,\n  ContextMenuItemIndicator,\n  ContextMenuLabel,\n  ContextMenuPortal,\n  ContextMenuRadioGroup,\n  ContextMenuRadioItem,\n  ContextMenuSeparator,\n  ContextMenuSub,\n  ContextMenuSubContent,\n  ContextMenuSubTrigger,\n  ContextMenuTrigger,\n  Group2 as Group,\n  Item2 as Item,\n  ItemIndicator2 as ItemIndicator,\n  Label2 as Label,\n  Portal2 as Portal,\n  RadioGroup2 as RadioGroup,\n  RadioItem2 as RadioItem,\n  Root2 as Root,\n  Separator2 as Separator,\n  Sub2 as Sub,\n  SubContent2 as SubContent,\n  SubTrigger2 as SubTrigger,\n  Trigger,\n  createContextMenuScope\n};\n//# sourceMappingURL=index.mjs.map\n"], "names": [], "sourceRoot": ""}