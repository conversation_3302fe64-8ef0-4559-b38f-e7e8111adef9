module.exports = {

"[project]/node_modules/.pnpm/@clerk+nextjs@6.20.0_next@1_c77f473bcb010f4557dab79e25c1da72/node_modules/@clerk/nextjs/dist/esm/app-router/data:495d35 [app-ssr] (ecmascript) <text/javascript>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/* __next_internal_action_entry_do_not_use__ [{"7ffbe169be525c902a5f18139949fecd0cba96b111":"createOrReadKeylessAction"},"node_modules/.pnpm/@clerk+nextjs@6.20.0_next@1_c77f473bcb010f4557dab79e25c1da72/node_modules/@clerk/nextjs/dist/esm/app-router/keyless-actions.js",""] */ __turbopack_context__.s({
    "createOrReadKeylessAction": (()=>createOrReadKeylessAction)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$client$2d$wrapper$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/next@15.3.2_@babel+core@7.2_09efc9edc30971f6ad8d5c21a0ab8f1f/node_modules/next/dist/build/webpack/loaders/next-flight-loader/action-client-wrapper.js [app-ssr] (ecmascript)");
"use turbopack no side effects";
;
var createOrReadKeylessAction = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$client$2d$wrapper$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["createServerReference"])("7ffbe169be525c902a5f18139949fecd0cba96b111", __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$client$2d$wrapper$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["callServer"], void 0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$client$2d$wrapper$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["findSourceMapURL"], "createOrReadKeylessAction"); //# sourceMappingURL=data:application/json;base64,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
}}),
"[project]/node_modules/.pnpm/@clerk+nextjs@6.20.0_next@1_c77f473bcb010f4557dab79e25c1da72/node_modules/@clerk/nextjs/dist/esm/app-router/client/keyless-creator-reader.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "KeylessCreatorOrReader": (()=>KeylessCreatorOrReader)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/next@15.3.2_@babel+core@7.2_09efc9edc30971f6ad8d5c21a0ab8f1f/node_modules/next/navigation.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/next@15.3.2_@babel+core@7.2_09efc9edc30971f6ad8d5c21a0ab8f1f/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$clerk$2b$nextjs$40$6$2e$20$2e$0_next$40$1_c77f473bcb010f4557dab79e25c1da72$2f$node_modules$2f40$clerk$2f$nextjs$2f$dist$2f$esm$2f$app$2d$router$2f$data$3a$495d35__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$text$2f$javascript$3e$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@clerk+nextjs@6.20.0_next@1_c77f473bcb010f4557dab79e25c1da72/node_modules/@clerk/nextjs/dist/esm/app-router/data:495d35 [app-ssr] (ecmascript) <text/javascript>");
;
;
;
;
const KeylessCreatorOrReader = (props)=>{
    var _a;
    const { children } = props;
    const segments = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useSelectedLayoutSegments"])();
    const isNotFoundRoute = ((_a = segments[0]) == null ? void 0 : _a.startsWith("/_not-found")) || false;
    const [state, fetchKeys] = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].useActionState(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$clerk$2b$nextjs$40$6$2e$20$2e$0_next$40$1_c77f473bcb010f4557dab79e25c1da72$2f$node_modules$2f40$clerk$2f$nextjs$2f$dist$2f$esm$2f$app$2d$router$2f$data$3a$495d35__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$text$2f$javascript$3e$__["createOrReadKeylessAction"], null);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        if (isNotFoundRoute) {
            return;
        }
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].startTransition(()=>{
            fetchKeys();
        });
    }, [
        isNotFoundRoute
    ]);
    if (!__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].isValidElement(children)) {
        return children;
    }
    return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_09efc9edc30971f6ad8d5c21a0ab8f1f$2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].cloneElement(children, {
        key: state == null ? void 0 : state.publishableKey,
        publishableKey: state == null ? void 0 : state.publishableKey,
        __internal_keyless_claimKeylessApplicationUrl: state == null ? void 0 : state.claimUrl,
        __internal_keyless_copyInstanceKeysUrl: state == null ? void 0 : state.apiKeysUrl,
        __internal_bypassMissingPublishableKey: true
    });
};
;
 //# sourceMappingURL=keyless-creator-reader.js.map
}}),

};

//# sourceMappingURL=25c57_%40clerk_nextjs_dist_esm_app-router_5b0bd8bf._.js.map