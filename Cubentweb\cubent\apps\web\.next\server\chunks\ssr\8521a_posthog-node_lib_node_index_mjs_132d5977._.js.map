{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "file": "index.mjs", "sources": ["file:///C:/Users/<USER>/Documents/Cubentweb/cubent/node_modules/.pnpm/posthog-node%404.17.2/node_modules/posthog-node/src/extensions/sentry-integration.ts", "file:///C:/Users/<USER>/Documents/Cubentweb/cubent/node_modules/.pnpm/posthog-node%404.17.2/node_modules/posthog-core/src/vendor/uuidv7.ts", "file:///C:/Users/<USER>/Documents/Cubentweb/cubent/node_modules/.pnpm/posthog-node%404.17.2/node_modules/posthog-node/src/extensions/error-tracking/autocapture.ts", "file:///C:/Users/<USER>/Documents/Cubentweb/cubent/node_modules/.pnpm/posthog-node%404.17.2/node_modules/posthog-node/src/extensions/error-tracking/type-checking.ts", "file:///C:/Users/<USER>/Documents/Cubentweb/cubent/node_modules/.pnpm/posthog-node%404.17.2/node_modules/posthog-node/src/extensions/error-tracking/error-conversion.ts", "file:///C:/Users/<USER>/Documents/Cubentweb/cubent/node_modules/.pnpm/posthog-node%404.17.2/node_modules/posthog-node/src/extensions/error-tracking/index.ts", "file:///C:/Users/<USER>/Documents/Cubentweb/cubent/node_modules/.pnpm/posthog-node%404.17.2/node_modules/posthog-node/src/extensions/express.ts", "file:///C:/Users/<USER>/Documents/Cubentweb/cubent/node_modules/.pnpm/posthog-node%404.17.2/node_modules/posthog-node/src/extensions/error-tracking/get-module.node.ts", "file:///C:/Users/<USER>/Documents/Cubentweb/cubent/node_modules/.pnpm/posthog-node%404.17.2/node_modules/posthog-node/src/extensions/error-tracking/reduceable-cache.ts", "file:///C:/Users/<USER>/Documents/Cubentweb/cubent/node_modules/.pnpm/posthog-node%404.17.2/node_modules/posthog-node/src/extensions/error-tracking/context-lines.node.ts", "file:///C:/Users/<USER>/Documents/Cubentweb/cubent/node_modules/.pnpm/posthog-node%404.17.2/node_modules/posthog-core/src/types.ts", "file:///C:/Users/<USER>/Documents/Cubentweb/cubent/node_modules/.pnpm/posthog-node%404.17.2/node_modules/posthog-core/src/featureFlagUtils.ts", "file:///C:/Users/<USER>/Documents/Cubentweb/cubent/node_modules/.pnpm/posthog-node%404.17.2/node_modules/posthog-core/src/utils.ts", "file:///C:/Users/<USER>/Documents/Cubentweb/cubent/node_modules/.pnpm/posthog-node%404.17.2/node_modules/posthog-core/src/lz-string.ts", "file:///C:/Users/<USER>/Documents/Cubentweb/cubent/node_modules/.pnpm/posthog-node%404.17.2/node_modules/posthog-core/src/eventemitter.ts", "file:///C:/Users/<USER>/Documents/Cubentweb/cubent/node_modules/.pnpm/posthog-node%404.17.2/node_modules/posthog-core/src/index.ts", "file:///C:/Users/<USER>/Documents/Cubentweb/cubent/node_modules/.pnpm/posthog-node%404.17.2/node_modules/posthog-node/src/fetch.ts", "file:///C:/Users/<USER>/Documents/Cubentweb/cubent/node_modules/.pnpm/posthog-node%404.17.2/node_modules/posthog-node/src/extensions/feature-flags/lazy.ts", "file:///C:/Users/<USER>/Documents/Cubentweb/cubent/node_modules/.pnpm/posthog-node%404.17.2/node_modules/posthog-node/src/extensions/feature-flags/crypto-helpers.ts", "file:///C:/Users/<USER>/Documents/Cubentweb/cubent/node_modules/.pnpm/posthog-node%404.17.2/node_modules/posthog-node/src/extensions/feature-flags/crypto.ts", "file:///C:/Users/<USER>/Documents/Cubentweb/cubent/node_modules/.pnpm/posthog-node%404.17.2/node_modules/posthog-node/src/extensions/feature-flags/feature-flags.ts", "file:///C:/Users/<USER>/Documents/Cubentweb/cubent/node_modules/.pnpm/posthog-node%404.17.2/node_modules/posthog-node/src/storage-memory.ts", "file:///C:/Users/<USER>/Documents/Cubentweb/cubent/node_modules/.pnpm/posthog-node%404.17.2/node_modules/posthog-node/src/client.ts", "file:///C:/Users/<USER>/Documents/Cubentweb/cubent/node_modules/.pnpm/posthog-node%404.17.2/node_modules/posthog-node/src/extensions/error-tracking/stack-parser.ts", "file:///C:/Users/<USER>/Documents/Cubentweb/cubent/node_modules/.pnpm/posthog-node%404.17.2/node_modules/posthog-node/src/entrypoints/index.node.ts"], "sourcesContent": ["/**\n * @file Adapted from [posthog-js](https://github.com/PostHog/posthog-js/blob/8157df935a4d0e71d2fefef7127aa85ee51c82d1/src/extensions/sentry-integration.ts) with modifications for the Node SDK.\n */\n/**\n * Integrate Sentry with PostHog. This will add a direct link to the person in Sentry, and an $exception event in PostHog.\n *\n * ### Usage\n *\n *     Sentry.init({\n *          dsn: 'https://example',\n *          integrations: [\n *              new PostHogSentryIntegration(posthog)\n *          ]\n *     })\n *\n *     Sentry.setTag(PostHogSentryIntegration.POSTHOG_ID_TAG, 'some distinct id');\n *\n * @param {Object} [posthog] The posthog object\n * @param {string} [organization] Optional: The Sentry organization, used to send a direct link from PostHog to Sentry\n * @param {Number} [projectId] Optional: The Sentry project id, used to send a direct link from PostHog to Sentry\n * @param {string} [prefix] Optional: Url of a self-hosted sentry instance (default: https://sentry.io/organizations/)\n * @param {SeverityLevel[] | '*'} [severityAllowList] Optional: send events matching the provided levels. Use '*' to send all events (default: ['error'])\n */\n\nimport { SeverityLevel } from './error-tracking/types'\nimport { type PostHogBackendClient } from '../client'\n\n// NOTE - we can't import from @sentry/types because it changes frequently and causes clashes\n// We only use a small subset of the types, so we can just define the integration overall and use any for the rest\n\n// import {\n//     Event as _SentryEvent,\n//     EventProcessor as _SentryEventProcessor,\n//     Exception as _SentryException,\n//     Hub as _SentryHub,\n//     Primitive as _SentryPrimitive,\n//     Integration as _SentryIntegration,\n//     IntegrationClass as _SentryIntegrationClass,\n// } from '@sentry/types'\n\n// Uncomment the above and comment the below to get type checking for development\n\ntype _SentryEvent = any\ntype _SentryEventProcessor = any\ntype _SentryException = any\ntype _SentryHub = any\ntype _SentryPrimitive = any\n\ninterface _SentryIntegration {\n  name: string\n  processEvent(event: _SentryEvent): _SentryEvent\n}\n\ninterface _SentryIntegrationClass {\n  name: string\n  setupOnce(addGlobalEventProcessor: (callback: _SentryEventProcessor) => void, getCurrentHub: () => _SentryHub): void\n}\n\ninterface SentryExceptionProperties {\n  $sentry_event_id?: string\n  $sentry_exception?: { values?: _SentryException[] }\n  $sentry_exception_message?: string\n  $sentry_exception_type?: string\n  $sentry_tags: { [key: string]: _SentryPrimitive }\n  $sentry_url?: string\n}\n\nexport type SentryIntegrationOptions = {\n  organization?: string\n  projectId?: number\n  prefix?: string\n  severityAllowList?: SeverityLevel[] | '*'\n}\n\nconst NAME = 'posthog-node'\n\nexport function createEventProcessor(\n  _posthog: PostHogBackendClient,\n  { organization, projectId, prefix, severityAllowList = ['error'] }: SentryIntegrationOptions = {}\n): (event: _SentryEvent) => _SentryEvent {\n  return (event) => {\n    const shouldProcessLevel = severityAllowList === '*' || severityAllowList.includes(event.level)\n    if (!shouldProcessLevel) {\n      return event\n    }\n    if (!event.tags) {\n      event.tags = {}\n    }\n\n    // Get the PostHog user ID from a specific tag, which users can set on their Sentry scope as they need.\n    const userId = event.tags[PostHogSentryIntegration.POSTHOG_ID_TAG]\n    if (userId === undefined) {\n      // If we can't find a user ID, don't bother linking the event. We won't be able to send anything meaningful to PostHog without it.\n      return event\n    }\n\n    const uiHost = _posthog.options.host ?? 'https://us.i.posthog.com'\n    const personUrl = new URL(`/project/${_posthog.apiKey}/person/${userId}`, uiHost).toString()\n\n    event.tags['PostHog Person URL'] = personUrl\n\n    const exceptions: _SentryException[] = event.exception?.values || []\n\n    const exceptionList = exceptions.map((exception) => ({\n      ...exception,\n      stacktrace: exception.stacktrace\n        ? {\n            ...exception.stacktrace,\n            type: 'raw',\n            frames: (exception.stacktrace.frames || []).map((frame: any) => {\n              return { ...frame, platform: 'node:javascript' }\n            }),\n          }\n        : undefined,\n    }))\n\n    const properties: SentryExceptionProperties & {\n      // two properties added to match any exception auto-capture\n      // added manually to avoid any dependency on the lazily loaded content\n      $exception_message: any\n      $exception_type: any\n      $exception_list: any\n      $exception_personURL: string\n      $exception_level: SeverityLevel\n    } = {\n      // PostHog Exception Properties,\n      $exception_message: exceptions[0]?.value || event.message,\n      $exception_type: exceptions[0]?.type,\n      $exception_personURL: personUrl,\n      $exception_level: event.level,\n      $exception_list: exceptionList,\n      // Sentry Exception Properties\n      $sentry_event_id: event.event_id,\n      $sentry_exception: event.exception,\n      $sentry_exception_message: exceptions[0]?.value || event.message,\n      $sentry_exception_type: exceptions[0]?.type,\n      $sentry_tags: event.tags,\n    }\n\n    if (organization && projectId) {\n      properties['$sentry_url'] =\n        (prefix || 'https://sentry.io/organizations/') +\n        organization +\n        '/issues/?project=' +\n        projectId +\n        '&query=' +\n        event.event_id\n    }\n\n    _posthog.capture({ event: '$exception', distinctId: userId, properties })\n\n    return event\n  }\n}\n\n// V8 integration - function based\nexport function sentryIntegration(\n  _posthog: PostHogBackendClient,\n  options?: SentryIntegrationOptions\n): _SentryIntegration {\n  const processor = createEventProcessor(_posthog, options)\n  return {\n    name: NAME,\n    processEvent(event) {\n      return processor(event)\n    },\n  }\n}\n\n// V7 integration - class based\nexport class PostHogSentryIntegration implements _SentryIntegrationClass {\n  public readonly name = NAME\n\n  public static readonly POSTHOG_ID_TAG = 'posthog_distinct_id'\n\n  public setupOnce: (\n    addGlobalEventProcessor: (callback: _SentryEventProcessor) => void,\n    getCurrentHub: () => _SentryHub\n  ) => void\n\n  constructor(\n    _posthog: PostHogBackendClient,\n    organization?: string,\n    prefix?: string,\n    severityAllowList?: SeverityLevel[] | '*'\n  ) {\n    // setupOnce gets called by Sentry when it intializes the plugin\n    this.name = NAME\n    this.setupOnce = function (\n      addGlobalEventProcessor: (callback: _SentryEventProcessor) => void,\n      getCurrentHub: () => _SentryHub\n    ) {\n      const projectId = getCurrentHub()?.getClient()?.getDsn()?.projectId\n      addGlobalEventProcessor(\n        createEventProcessor(_posthog, {\n          organization,\n          projectId,\n          prefix,\n          severityAllowList,\n        })\n      )\n    }\n  }\n}\n", "// vendor from: https://github.com/LiosK/uuidv7/blob/f30b7a7faff73afbce0b27a46c638310f96912ba/src/index.ts\n// https://github.com/LiosK/uuidv7#license\n\n/**\n * uuidv7: An experimental implementation of the proposed UUID Version 7\n *\n * @license Apache-2.0\n * @copyright 2021-2023 LiosK\n * @packageDocumentation\n */\n\nconst DIGITS = \"0123456789abcdef\";\n\n/** Represents a UUID as a 16-byte byte array. */\nexport class UUID {\n  /** @param bytes - The 16-byte byte array representation. */\n  private constructor(readonly bytes: Readonly<Uint8Array>) {}\n\n  /**\n   * Creates an object from the internal representation, a 16-byte byte array\n   * containing the binary UUID representation in the big-endian byte order.\n   *\n   * This method does NOT shallow-copy the argument, and thus the created object\n   * holds the reference to the underlying buffer.\n   *\n   * @throws TypeError if the length of the argument is not 16.\n   */\n  static ofInner(bytes: Readonly<Uint8Array>): UUID {\n    if (bytes.length !== 16) {\n      throw new TypeError(\"not 128-bit length\");\n    } else {\n      return new UUID(bytes);\n    }\n  }\n\n  /**\n   * Builds a byte array from UUIDv7 field values.\n   *\n   * @param unixTsMs - A 48-bit `unix_ts_ms` field value.\n   * @param randA - A 12-bit `rand_a` field value.\n   * @param randBHi - The higher 30 bits of 62-bit `rand_b` field value.\n   * @param randBLo - The lower 32 bits of 62-bit `rand_b` field value.\n   * @throws RangeError if any field value is out of the specified range.\n   */\n  static fromFieldsV7(\n    unixTsMs: number,\n    randA: number,\n    randBHi: number,\n    randBLo: number,\n  ): UUID {\n    if (\n      !Number.isInteger(unixTsMs) ||\n      !Number.isInteger(randA) ||\n      !Number.isInteger(randBHi) ||\n      !Number.isInteger(randBLo) ||\n      unixTsMs < 0 ||\n      randA < 0 ||\n      randBHi < 0 ||\n      randBLo < 0 ||\n      unixTsMs > 0xffff_ffff_ffff ||\n      randA > 0xfff ||\n      randBHi > 0x3fff_ffff ||\n      randBLo > 0xffff_ffff\n    ) {\n      throw new RangeError(\"invalid field value\");\n    }\n\n    const bytes = new Uint8Array(16);\n    bytes[0] = unixTsMs / 2 ** 40;\n    bytes[1] = unixTsMs / 2 ** 32;\n    bytes[2] = unixTsMs / 2 ** 24;\n    bytes[3] = unixTsMs / 2 ** 16;\n    bytes[4] = unixTsMs / 2 ** 8;\n    bytes[5] = unixTsMs;\n    bytes[6] = 0x70 | (randA >>> 8);\n    bytes[7] = randA;\n    bytes[8] = 0x80 | (randBHi >>> 24);\n    bytes[9] = randBHi >>> 16;\n    bytes[10] = randBHi >>> 8;\n    bytes[11] = randBHi;\n    bytes[12] = randBLo >>> 24;\n    bytes[13] = randBLo >>> 16;\n    bytes[14] = randBLo >>> 8;\n    bytes[15] = randBLo;\n    return new UUID(bytes);\n  }\n\n  /**\n   * Builds a byte array from a string representation.\n   *\n   * This method accepts the following formats:\n   *\n   * - 32-digit hexadecimal format without hyphens: `0189dcd553117d408db09496a2eef37b`\n   * - 8-4-4-4-12 hyphenated format: `0189dcd5-5311-7d40-8db0-9496a2eef37b`\n   * - Hyphenated format with surrounding braces: `{0189dcd5-5311-7d40-8db0-9496a2eef37b}`\n   * - RFC 4122 URN format: `urn:uuid:0189dcd5-5311-7d40-8db0-9496a2eef37b`\n   *\n   * Leading and trailing whitespaces represents an error.\n   *\n   * @throws SyntaxError if the argument could not parse as a valid UUID string.\n   */\n  static parse(uuid: string): UUID {\n    let hex: string | undefined = undefined;\n    switch (uuid.length) {\n      case 32:\n        hex = /^[0-9a-f]{32}$/i.exec(uuid)?.[0];\n        break;\n      case 36:\n        hex =\n          /^([0-9a-f]{8})-([0-9a-f]{4})-([0-9a-f]{4})-([0-9a-f]{4})-([0-9a-f]{12})$/i\n            .exec(uuid)\n            ?.slice(1, 6)\n            .join(\"\");\n        break;\n      case 38:\n        hex =\n          /^\\{([0-9a-f]{8})-([0-9a-f]{4})-([0-9a-f]{4})-([0-9a-f]{4})-([0-9a-f]{12})\\}$/i\n            .exec(uuid)\n            ?.slice(1, 6)\n            .join(\"\");\n        break;\n      case 45:\n        hex =\n          /^urn:uuid:([0-9a-f]{8})-([0-9a-f]{4})-([0-9a-f]{4})-([0-9a-f]{4})-([0-9a-f]{12})$/i\n            .exec(uuid)\n            ?.slice(1, 6)\n            .join(\"\");\n        break;\n      default:\n        break;\n    }\n\n    if (hex) {\n      const inner = new Uint8Array(16);\n      for (let i = 0; i < 16; i += 4) {\n        const n = parseInt(hex.substring(2 * i, 2 * i + 8), 16);\n        inner[i + 0] = n >>> 24;\n        inner[i + 1] = n >>> 16;\n        inner[i + 2] = n >>> 8;\n        inner[i + 3] = n;\n      }\n      return new UUID(inner);\n    } else {\n      throw new SyntaxError(\"could not parse UUID string\");\n    }\n  }\n\n  /**\n   * @returns The 8-4-4-4-12 canonical hexadecimal string representation\n   * (`0189dcd5-5311-7d40-8db0-9496a2eef37b`).\n   */\n  toString(): string {\n    let text = \"\";\n    for (let i = 0; i < this.bytes.length; i++) {\n      text += DIGITS.charAt(this.bytes[i] >>> 4);\n      text += DIGITS.charAt(this.bytes[i] & 0xf);\n      if (i === 3 || i === 5 || i === 7 || i === 9) {\n        text += \"-\";\n      }\n    }\n    return text;\n  }\n\n  /**\n   * @returns The 32-digit hexadecimal representation without hyphens\n   * (`0189dcd553117d408db09496a2eef37b`).\n   */\n  toHex(): string {\n    let text = \"\";\n    for (let i = 0; i < this.bytes.length; i++) {\n      text += DIGITS.charAt(this.bytes[i] >>> 4);\n      text += DIGITS.charAt(this.bytes[i] & 0xf);\n    }\n    return text;\n  }\n\n  /** @returns The 8-4-4-4-12 canonical hexadecimal string representation. */\n  toJSON(): string {\n    return this.toString();\n  }\n\n  /**\n   * Reports the variant field value of the UUID or, if appropriate, \"NIL\" or\n   * \"MAX\".\n   *\n   * For convenience, this method reports \"NIL\" or \"MAX\" if `this` represents\n   * the Nil or Max UUID, although the Nil and Max UUIDs are technically\n   * subsumed under the variants `0b0` and `0b111`, respectively.\n   */\n  getVariant():\n    | \"VAR_0\"\n    | \"VAR_10\"\n    | \"VAR_110\"\n    | \"VAR_RESERVED\"\n    | \"NIL\"\n    | \"MAX\" {\n    const n = this.bytes[8] >>> 4;\n    if (n < 0) {\n      throw new Error(\"unreachable\");\n    } else if (n <= 0b0111) {\n      return this.bytes.every((e) => e === 0) ? \"NIL\" : \"VAR_0\";\n    } else if (n <= 0b1011) {\n      return \"VAR_10\";\n    } else if (n <= 0b1101) {\n      return \"VAR_110\";\n    } else if (n <= 0b1111) {\n      return this.bytes.every((e) => e === 0xff) ? \"MAX\" : \"VAR_RESERVED\";\n    } else {\n      throw new Error(\"unreachable\");\n    }\n  }\n\n  /**\n   * Returns the version field value of the UUID or `undefined` if the UUID does\n   * not have the variant field value of `0b10`.\n   */\n  getVersion(): number | undefined {\n    return this.getVariant() === \"VAR_10\" ? this.bytes[6] >>> 4 : undefined;\n  }\n\n  /** Creates an object from `this`. */\n  clone(): UUID {\n    return new UUID(this.bytes.slice(0));\n  }\n\n  /** Returns true if `this` is equivalent to `other`. */\n  equals(other: UUID): boolean {\n    return this.compareTo(other) === 0;\n  }\n\n  /**\n   * Returns a negative integer, zero, or positive integer if `this` is less\n   * than, equal to, or greater than `other`, respectively.\n   */\n  compareTo(other: UUID): number {\n    for (let i = 0; i < 16; i++) {\n      const diff = this.bytes[i] - other.bytes[i];\n      if (diff !== 0) {\n        return Math.sign(diff);\n      }\n    }\n    return 0;\n  }\n}\n\n/**\n * Encapsulates the monotonic counter state.\n *\n * This class provides APIs to utilize a separate counter state from that of the\n * global generator used by {@link uuidv7} and {@link uuidv7obj}. In addition to\n * the default {@link generate} method, this class has {@link generateOrAbort}\n * that is useful to absolutely guarantee the monotonically increasing order of\n * generated UUIDs. See their respective documentation for details.\n */\nexport class V7Generator {\n  private timestamp = 0;\n  private counter = 0;\n\n  /** The random number generator used by the generator. */\n  private readonly random: { nextUint32(): number };\n\n  /**\n   * Creates a generator object with the default random number generator, or\n   * with the specified one if passed as an argument. The specified random\n   * number generator should be cryptographically strong and securely seeded.\n   */\n  constructor(randomNumberGenerator?: {\n    /** Returns a 32-bit random unsigned integer. */\n    nextUint32(): number;\n  }) {\n    this.random = randomNumberGenerator ?? getDefaultRandom();\n  }\n\n  /**\n   * Generates a new UUIDv7 object from the current timestamp, or resets the\n   * generator upon significant timestamp rollback.\n   *\n   * This method returns a monotonically increasing UUID by reusing the previous\n   * timestamp even if the up-to-date timestamp is smaller than the immediately\n   * preceding UUID's. However, when such a clock rollback is considered\n   * significant (i.e., by more than ten seconds), this method resets the\n   * generator and returns a new UUID based on the given timestamp, breaking the\n   * increasing order of UUIDs.\n   *\n   * See {@link generateOrAbort} for the other mode of generation and\n   * {@link generateOrResetCore} for the low-level primitive.\n   */\n  generate(): UUID {\n    return this.generateOrResetCore(Date.now(), 10_000);\n  }\n\n  /**\n   * Generates a new UUIDv7 object from the current timestamp, or returns\n   * `undefined` upon significant timestamp rollback.\n   *\n   * This method returns a monotonically increasing UUID by reusing the previous\n   * timestamp even if the up-to-date timestamp is smaller than the immediately\n   * preceding UUID's. However, when such a clock rollback is considered\n   * significant (i.e., by more than ten seconds), this method aborts and\n   * returns `undefined` immediately.\n   *\n   * See {@link generate} for the other mode of generation and\n   * {@link generateOrAbortCore} for the low-level primitive.\n   */\n  generateOrAbort(): UUID | undefined {\n    return this.generateOrAbortCore(Date.now(), 10_000);\n  }\n\n  /**\n   * Generates a new UUIDv7 object from the `unixTsMs` passed, or resets the\n   * generator upon significant timestamp rollback.\n   *\n   * This method is equivalent to {@link generate} except that it takes a custom\n   * timestamp and clock rollback allowance.\n   *\n   * @param rollbackAllowance - The amount of `unixTsMs` rollback that is\n   * considered significant. A suggested value is `10_000` (milliseconds).\n   * @throws RangeError if `unixTsMs` is not a 48-bit positive integer.\n   */\n  generateOrResetCore(unixTsMs: number, rollbackAllowance: number): UUID {\n    let value = this.generateOrAbortCore(unixTsMs, rollbackAllowance);\n    if (value === undefined) {\n      // reset state and resume\n      this.timestamp = 0;\n      value = this.generateOrAbortCore(unixTsMs, rollbackAllowance)!;\n    }\n    return value;\n  }\n\n  /**\n   * Generates a new UUIDv7 object from the `unixTsMs` passed, or returns\n   * `undefined` upon significant timestamp rollback.\n   *\n   * This method is equivalent to {@link generateOrAbort} except that it takes a\n   * custom timestamp and clock rollback allowance.\n   *\n   * @param rollbackAllowance - The amount of `unixTsMs` rollback that is\n   * considered significant. A suggested value is `10_000` (milliseconds).\n   * @throws RangeError if `unixTsMs` is not a 48-bit positive integer.\n   */\n  generateOrAbortCore(\n    unixTsMs: number,\n    rollbackAllowance: number,\n  ): UUID | undefined {\n    const MAX_COUNTER = 0x3ff_ffff_ffff;\n\n    if (\n      !Number.isInteger(unixTsMs) ||\n      unixTsMs < 1 ||\n      unixTsMs > 0xffff_ffff_ffff\n    ) {\n      throw new RangeError(\"`unixTsMs` must be a 48-bit positive integer\");\n    } else if (rollbackAllowance < 0 || rollbackAllowance > 0xffff_ffff_ffff) {\n      throw new RangeError(\"`rollbackAllowance` out of reasonable range\");\n    }\n\n    if (unixTsMs > this.timestamp) {\n      this.timestamp = unixTsMs;\n      this.resetCounter();\n    } else if (unixTsMs + rollbackAllowance >= this.timestamp) {\n      // go on with previous timestamp if new one is not much smaller\n      this.counter++;\n      if (this.counter > MAX_COUNTER) {\n        // increment timestamp at counter overflow\n        this.timestamp++;\n        this.resetCounter();\n      }\n    } else {\n      // abort if clock went backwards to unbearable extent\n      return undefined;\n    }\n\n    return UUID.fromFieldsV7(\n      this.timestamp,\n      Math.trunc(this.counter / 2 ** 30),\n      this.counter & (2 ** 30 - 1),\n      this.random.nextUint32(),\n    );\n  }\n\n  /** Initializes the counter at a 42-bit random integer. */\n  private resetCounter(): void {\n    this.counter =\n      this.random.nextUint32() * 0x400 + (this.random.nextUint32() & 0x3ff);\n  }\n\n  /**\n   * Generates a new UUIDv4 object utilizing the random number generator inside.\n   *\n   * @internal\n   */\n  generateV4(): UUID {\n    const bytes = new Uint8Array(\n      Uint32Array.of(\n        this.random.nextUint32(),\n        this.random.nextUint32(),\n        this.random.nextUint32(),\n        this.random.nextUint32(),\n      ).buffer,\n    );\n    bytes[6] = 0x40 | (bytes[6] >>> 4);\n    bytes[8] = 0x80 | (bytes[8] >>> 2);\n    return UUID.ofInner(bytes);\n  }\n}\n\n/** A global flag to force use of cryptographically strong RNG. */\n// declare const UUIDV7_DENY_WEAK_RNG: boolean;\n\n/** Returns the default random number generator available in the environment. */\nconst getDefaultRandom = (): { nextUint32(): number } => {\n// fix: crypto isn't available in react-native, always use Math.random\n\n//   // detect Web Crypto API\n//   if (\n//     typeof crypto !== \"undefined\" &&\n//     typeof crypto.getRandomValues !== \"undefined\"\n//   ) {\n//     return new BufferedCryptoRandom();\n//   } else {\n//     // fall back on Math.random() unless the flag is set to true\n//     if (typeof UUIDV7_DENY_WEAK_RNG !== \"undefined\" && UUIDV7_DENY_WEAK_RNG) {\n//       throw new Error(\"no cryptographically strong RNG available\");\n//     }\n//     return {\n//       nextUint32: (): number =>\n//         Math.trunc(Math.random() * 0x1_0000) * 0x1_0000 +\n//         Math.trunc(Math.random() * 0x1_0000),\n//     };\n//   }\n  return {\n    nextUint32: (): number =>\n      Math.trunc(Math.random() * 0x1_0000) * 0x1_0000 +\n      Math.trunc(Math.random() * 0x1_0000),\n  };\n};\n\n// /**\n//  * Wraps `crypto.getRandomValues()` to enable buffering; this uses a small\n//  * buffer by default to avoid both unbearable throughput decline in some\n//  * environments and the waste of time and space for unused values.\n//  */\n// class BufferedCryptoRandom {\n//   private readonly buffer = new Uint32Array(8);\n//   private cursor = 0xffff;\n//   nextUint32(): number {\n//     if (this.cursor >= this.buffer.length) {\n//       crypto.getRandomValues(this.buffer);\n//       this.cursor = 0;\n//     }\n//     return this.buffer[this.cursor++];\n//   }\n// }\n\nlet defaultGenerator: V7Generator | undefined;\n\n/**\n * Generates a UUIDv7 string.\n *\n * @returns The 8-4-4-4-12 canonical hexadecimal string representation\n * (\"xxxxxxxx-xxxx-xxxx-xxxx-xxxxxxxxxxxx\").\n */\nexport const uuidv7 = (): string => uuidv7obj().toString();\n\n/** Generates a UUIDv7 object. */\nexport const uuidv7obj = (): UUID =>\n  (defaultGenerator || (defaultGenerator = new V7Generator())).generate();\n\n/**\n * Generates a UUIDv4 string.\n *\n * @returns The 8-4-4-4-12 canonical hexadecimal string representation\n * (\"xxxxxxxx-xxxx-xxxx-xxxx-xxxxxxxxxxxx\").\n */\nexport const uuidv4 = (): string => uuidv4obj().toString();\n\n/** Generates a UUIDv4 object. */\nexport const uuidv4obj = (): UUID =>\n  (defaultGenerator || (defaultGenerator = new V7Generator())).generateV4();\n", "// Portions of this file are derived from getsentry/sentry-javascript by Software, Inc. dba Sentry\n// Licensed under the MIT License\n\nimport { EventHint } from './types'\n\ntype ErrorHandler = { _posthogErrorHandler: boolean } & ((error: Error) => void)\n\nfunction makeUncaughtExceptionHandler(\n  captureFn: (exception: Error, hint: EventHint) => void,\n  onFatalFn: () => void\n): ErrorHandler {\n  let calledFatalError: boolean = false\n\n  return Object.assign(\n    (error: Error): void => {\n      // Attaching a listener to `uncaughtException` will prevent the node process from exiting. We generally do not\n      // want to alter this behaviour so we check for other listeners that users may have attached themselves and adjust\n      // exit behaviour of the SDK accordingly:\n      // - If other listeners are attached, do not exit.\n      // - If the only listener attached is ours, exit.\n      const userProvidedListenersCount = global.process.listeners('uncaughtException').filter((listener) => {\n        // There are 2 listeners we ignore:\n        return (\n          // as soon as we're using domains this listener is attached by node itself\n          listener.name !== 'domainUncaughtExceptionClear' &&\n          // the handler we register in this integration\n          (listener as <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>)._posthogErrorHandler !== true\n        )\n      }).length\n\n      const processWouldExit = userProvidedListenersCount === 0\n\n      captureFn(error, {\n        mechanism: {\n          type: 'onuncaughtexception',\n          handled: false,\n        },\n      })\n\n      if (!calledFatalError && processWouldExit) {\n        calledFatalError = true\n        onFatalFn()\n      }\n    },\n    { _posthogErrorHandler: true }\n  )\n}\n\nexport function addUncaughtExceptionListener(\n  captureFn: (exception: Error, hint: EventHint) => void,\n  onFatalFn: () => void\n): void {\n  global.process.on('uncaughtException', makeUncaughtExceptionHandler(captureFn, onFatalFn))\n}\n\nexport function addUnhandledRejectionListener(captureFn: (exception: unknown, hint: EventHint) => void): void {\n  global.process.on('unhandledRejection', (reason: unknown) => {\n    captureFn(reason, {\n      mechanism: {\n        type: 'onunhandledrejection',\n        handled: false,\n      },\n    })\n  })\n}\n", "// Portions of this file are derived from getsentry/sentry-javascript by Software, Inc. dba Sentry\n// Licensed under the MIT License\n\nimport { PolymorphicEvent } from './types'\n\nexport function isEvent(candidate: unknown): candidate is PolymorphicEvent {\n  return typeof Event !== 'undefined' && isInstanceOf(candidate, Event)\n}\n\nexport function isPlainObject(candidate: unknown): candidate is Record<string, unknown> {\n  return isBuiltin(candidate, 'Object')\n}\n\nexport function isError(candidate: unknown): candidate is Error {\n  switch (Object.prototype.toString.call(candidate)) {\n    case '[object Error]':\n    case '[object Exception]':\n    case '[object DOMException]':\n    case '[object WebAssembly.Exception]':\n      return true\n    default:\n      return isInstanceOf(candidate, Error)\n  }\n}\n\nexport function isInstanceOf(candidate: unknown, base: any): boolean {\n  try {\n    return candidate instanceof base\n  } catch {\n    return false\n  }\n}\n\nexport function isErrorEvent(event: unknown): boolean {\n  return isBuiltin(event, 'ErrorEvent')\n}\n\nexport function isBuiltin(candidate: unknown, className: string): boolean {\n  return Object.prototype.toString.call(candidate) === `[object ${className}]`\n}\n", "// Portions of this file are derived from getsentry/sentry-javascript by Software, Inc. dba Sentry\n// Licensed under the MIT License\n\nimport { isError, isErrorEvent, isEvent, isPlainObject } from './type-checking'\nimport {\n  ErrorProperties,\n  EventHint,\n  Exception,\n  Mechanism,\n  StackFrame,\n  StackFrameModifierFn,\n  StackParser,\n} from './types'\n\nexport async function propertiesFromUnknownInput(\n  stackParser: StackParser,\n  frameModifiers: StackFrameModifierFn[],\n  input: unknown,\n  hint?: EventHint\n): Promise<ErrorProperties> {\n  const providedMechanism = hint && hint.mechanism\n  const mechanism = providedMechanism || {\n    handled: true,\n    type: 'generic',\n  }\n\n  const errorList = getErrorList(mechanism, input, hint)\n  const exceptionList = await Promise.all(\n    errorList.map(async (error) => {\n      const exception = await exceptionFromError(stackParser, frameModifiers, error)\n      exception.value = exception.value || ''\n      exception.type = exception.type || 'Error'\n      exception.mechanism = mechanism\n      return exception\n    })\n  )\n\n  const properties = { $exception_list: exceptionList }\n  return properties\n}\n\n// Flatten error causes into a list of errors\n// See: https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Error/cause\nfunction getErrorList(mechanism: Mechanism, input: unknown, hint?: EventHint): Error[] {\n  const error = getError(mechanism, input, hint)\n  if (error.cause) {\n    return [error, ...getErrorList(mechanism, error.cause, hint)]\n  }\n  return [error]\n}\n\nfunction getError(mechanism: Mechanism, exception: unknown, hint?: EventHint): Error {\n  if (isError(exception)) {\n    return exception\n  }\n\n  mechanism.synthetic = true\n\n  if (isPlainObject(exception)) {\n    const errorFromProp = getErrorPropertyFromObject(exception)\n    if (errorFromProp) {\n      return errorFromProp\n    }\n\n    const message = getMessageForObject(exception)\n    const ex = hint?.syntheticException || new Error(message)\n    ex.message = message\n\n    return ex\n  }\n\n  // This handles when someone does: `throw \"something awesome\";`\n  // We use synthesized Error here so we can extract a (rough) stack trace.\n  const ex = hint?.syntheticException || new Error(exception as string)\n  ex.message = `${exception}`\n\n  return ex\n}\n\n/** If a plain object has a property that is an `Error`, return this error. */\nfunction getErrorPropertyFromObject(obj: Record<string, unknown>): Error | undefined {\n  for (const prop in obj) {\n    if (Object.prototype.hasOwnProperty.call(obj, prop)) {\n      const value = obj[prop]\n      if (isError(value)) {\n        return value\n      }\n    }\n  }\n\n  return undefined\n}\n\nfunction getMessageForObject(exception: Record<string, unknown>): string {\n  if ('name' in exception && typeof exception.name === 'string') {\n    let message = `'${exception.name}' captured as exception`\n\n    if ('message' in exception && typeof exception.message === 'string') {\n      message += ` with message '${exception.message}'`\n    }\n\n    return message\n  } else if ('message' in exception && typeof exception.message === 'string') {\n    return exception.message\n  }\n\n  const keys = extractExceptionKeysForMessage(exception)\n\n  // Some ErrorEvent instances do not have an `error` property, which is why they are not handled before\n  // We still want to try to get a decent message for these cases\n  if (isErrorEvent(exception)) {\n    return `Event \\`ErrorEvent\\` captured as exception with message \\`${exception.message}\\``\n  }\n\n  const className = getObjectClassName(exception)\n\n  return `${className && className !== 'Object' ? `'${className}'` : 'Object'} captured as exception with keys: ${keys}`\n}\n\nfunction getObjectClassName(obj: unknown): string | undefined | void {\n  try {\n    const prototype: unknown | null = Object.getPrototypeOf(obj)\n    return prototype ? prototype.constructor.name : undefined\n  } catch (e) {\n    // ignore errors here\n  }\n}\n\n/**\n * Given any captured exception, extract its keys and create a sorted\n * and truncated list that will be used inside the event message.\n * eg. `Non-error exception captured with keys: foo, bar, baz`\n */\nfunction extractExceptionKeysForMessage(exception: Record<string, unknown>, maxLength: number = 40): string {\n  const keys = Object.keys(convertToPlainObject(exception))\n  keys.sort()\n\n  const firstKey = keys[0]\n\n  if (!firstKey) {\n    return '[object has no keys]'\n  }\n\n  if (firstKey.length >= maxLength) {\n    return truncate(firstKey, maxLength)\n  }\n\n  for (let includedKeys = keys.length; includedKeys > 0; includedKeys--) {\n    const serialized = keys.slice(0, includedKeys).join(', ')\n    if (serialized.length > maxLength) {\n      continue\n    }\n    if (includedKeys === keys.length) {\n      return serialized\n    }\n    return truncate(serialized, maxLength)\n  }\n\n  return ''\n}\n\nfunction truncate(str: string, max: number = 0): string {\n  if (typeof str !== 'string' || max === 0) {\n    return str\n  }\n  return str.length <= max ? str : `${str.slice(0, max)}...`\n}\n\n/**\n * Transforms any `Error` or `Event` into a plain object with all of their enumerable properties, and some of their\n * non-enumerable properties attached.\n *\n * @param value Initial source that we have to transform in order for it to be usable by the serializer\n * @returns An Event or Error turned into an object - or the value argument itself, when value is neither an Event nor\n *  an Error.\n */\nfunction convertToPlainObject<V>(value: V):\n  | {\n      [ownProps: string]: unknown\n      type: string\n      target: string\n      currentTarget: string\n      detail?: unknown\n    }\n  | {\n      [ownProps: string]: unknown\n      message: string\n      name: string\n      stack?: string\n    }\n  | V {\n  if (isError(value)) {\n    return {\n      message: value.message,\n      name: value.name,\n      stack: value.stack,\n      ...getOwnProperties(value),\n    }\n  } else if (isEvent(value)) {\n    const newObj: {\n      [ownProps: string]: unknown\n      type: string\n      target: string\n      currentTarget: string\n      detail?: unknown\n    } = {\n      type: value.type,\n      target: serializeEventTarget(value.target),\n      currentTarget: serializeEventTarget(value.currentTarget),\n      ...getOwnProperties(value),\n    }\n\n    // TODO: figure out why this fails typing (I think CustomEvent is only supported in Node 19 onwards)\n    // if (typeof CustomEvent !== 'undefined' && isInstanceOf(value, CustomEvent)) {\n    //   newObj.detail = (value as unknown as CustomEvent).detail\n    // }\n\n    return newObj\n  } else {\n    return value\n  }\n}\n\n/** Filters out all but an object's own properties */\nfunction getOwnProperties(obj: unknown): { [key: string]: unknown } {\n  if (typeof obj === 'object' && obj !== null) {\n    const extractedProps: { [key: string]: unknown } = {}\n    for (const property in obj) {\n      if (Object.prototype.hasOwnProperty.call(obj, property)) {\n        extractedProps[property] = (obj as Record<string, unknown>)[property]\n      }\n    }\n    return extractedProps\n  } else {\n    return {}\n  }\n}\n\n/** Creates a string representation of the target of an `Event` object */\nfunction serializeEventTarget(target: unknown): string {\n  try {\n    return Object.prototype.toString.call(target)\n  } catch (_oO) {\n    return '<unknown>'\n  }\n}\n\n/**\n * Extracts stack frames from the error and builds an Exception\n */\nasync function exceptionFromError(\n  stackParser: StackParser,\n  frameModifiers: StackFrameModifierFn[],\n  error: Error\n): Promise<Exception> {\n  const exception: Exception = {\n    type: error.name || error.constructor.name,\n    value: error.message,\n  }\n\n  let frames = parseStackFrames(stackParser, error)\n\n  for (const modifier of frameModifiers) {\n    frames = await modifier(frames)\n  }\n\n  if (frames.length) {\n    exception.stacktrace = { frames, type: 'raw' }\n  }\n\n  return exception\n}\n\n/**\n * Extracts stack frames from the error.stack string\n */\nfunction parseStackFrames(stackParser: StackParser, error: Error): StackFrame[] {\n  return stackParser(error.stack || '', 1)\n}\n", "import { EventHint, StackFrameModifierFn, StackParser } from './types'\nimport { addUncaughtExceptionListener, addUnhandledRejectionListener } from './autocapture'\nimport { PostHogBackendClient } from '../../client'\nimport { uuidv7 } from 'posthog-core/src/vendor/uuidv7'\nimport { propertiesFromUnknownInput } from './error-conversion'\nimport { EventMessage, PostHogOptions } from '../../types'\n\nconst SHUTDOWN_TIMEOUT = 2000\n\nexport default class ErrorTracking {\n  private client: PostHogBackendClient\n  private _exceptionAutocaptureEnabled: boolean\n\n  static stackParser: StackParser\n  static frameModifiers: StackFrameModifierFn[]\n\n  static async captureException(\n    client: PostHogBackendClient,\n    error: unknown,\n    hint: EventHint,\n    distinctId?: string,\n    additionalProperties?: Record<string | number, any>\n  ): Promise<void> {\n    const properties: EventMessage['properties'] = { ...additionalProperties }\n\n    // Given stateless nature of Node SDK we capture exceptions using personless processing when no\n    // user can be determined because a distinct_id is not provided e.g. exception autocapture\n    if (!distinctId) {\n      properties.$process_person_profile = false\n    }\n\n    const exceptionProperties = await propertiesFromUnknownInput(this.stackParser, this.frameModifiers, error, hint)\n\n    client.capture({\n      event: '$exception',\n      distinctId: distinctId || uuidv7(),\n      properties: {\n        ...exceptionProperties,\n        ...properties,\n      },\n    })\n  }\n\n  constructor(client: PostHogBackendClient, options: PostHogOptions) {\n    this.client = client\n    this._exceptionAutocaptureEnabled = options.enableExceptionAutocapture || false\n\n    this.startAutocaptureIfEnabled()\n  }\n\n  private startAutocaptureIfEnabled(): void {\n    if (this.isEnabled()) {\n      addUncaughtExceptionListener(this.onException.bind(this), this.onFatalError.bind(this))\n      addUnhandledRejectionListener(this.onException.bind(this))\n    }\n  }\n\n  private onException(exception: unknown, hint: EventHint): void {\n    ErrorTracking.captureException(this.client, exception, hint)\n  }\n\n  private async onFatalError(): Promise<void> {\n    await this.client.shutdown(SHUTDOWN_TIMEOUT)\n  }\n\n  isEnabled(): boolean {\n    return !this.client.isDisabled && this._exceptionAutocaptureEnabled\n  }\n}\n", "import type * as http from 'node:http'\nimport { uuidv7 } from 'posthog-core/src/vendor/uuidv7'\nimport ErrorTracking from './error-tracking'\nimport { PostHogBackendClient } from '../client'\n\ntype ExpressMiddleware = (req: http.IncomingMessage, res: http.ServerResponse, next: () => void) => void\n\ntype ExpressErrorMiddleware = (\n  error: MiddlewareError,\n  req: http.IncomingMessage,\n  res: http.ServerResponse,\n  next: (error: MiddlewareError) => void\n) => void\n\ninterface MiddlewareError extends Error {\n  status?: number | string\n  statusCode?: number | string\n  status_code?: number | string\n  output?: {\n    statusCode?: number | string\n  }\n}\n\nexport function setupExpressErrorHandler(\n  _posthog: PostHogBackendClient,\n  app: {\n    use: (middleware: ExpressMiddleware | ExpressErrorMiddleware) => unknown\n  }\n): void {\n  app.use((error: MiddlewareError, _, __, next: (error: MiddlewareError) => void): void => {\n    const hint = { mechanism: { type: 'middleware', handled: false } }\n    // Given stateless nature of Node SDK we capture exceptions using personless processing\n    // when no user can be determined e.g. in the case of exception autocapture\n    ErrorTracking.captureException(_posthog, error, hint, uuidv7(), { $process_person_profile: false })\n    next(error)\n  })\n}\n", "// Portions of this file are derived from getsentry/sentry-javascript by Software, Inc. dba Sentry\n// Licensed under the MIT License\n\nimport { posix, sep, dirname } from 'path'\n\n/** Creates a function that gets the module name from a filename */\nexport function createGetModuleFromFilename(\n  basePath: string = process.argv[1] ? dirname(process.argv[1]) : process.cwd(),\n  isWindows: boolean = sep === '\\\\'\n): (filename: string | undefined) => string | undefined {\n  const normalizedBase = isWindows ? normalizeWindowsPath(basePath) : basePath\n\n  return (filename: string | undefined) => {\n    if (!filename) {\n      return\n    }\n\n    const normalizedFilename = isWindows ? normalizeWindowsPath(filename) : filename\n\n    // eslint-disable-next-line prefer-const\n    let { dir, base: file, ext } = posix.parse(normalizedFilename)\n\n    if (ext === '.js' || ext === '.mjs' || ext === '.cjs') {\n      file = file.slice(0, ext.length * -1)\n    }\n\n    // The file name might be URI-encoded which we want to decode to\n    // the original file name.\n    const decodedFile = decodeURIComponent(file)\n\n    if (!dir) {\n      // No dirname whatsoever\n      dir = '.'\n    }\n\n    const n = dir.lastIndexOf('/node_modules')\n    if (n > -1) {\n      return `${dir.slice(n + 14).replace(/\\//g, '.')}:${decodedFile}`\n    }\n\n    // Let's see if it's a part of the main module\n    // To be a part of main module, it has to share the same base\n    if (dir.startsWith(normalizedBase)) {\n      const moduleName = dir.slice(normalizedBase.length + 1).replace(/\\//g, '.')\n      return moduleName ? `${moduleName}:${decodedFile}` : decodedFile\n    }\n\n    return decodedFile\n  }\n}\n\n/** normalizes Windows paths */\nfunction normalizeWindowsPath(path: string): string {\n  return path\n    .replace(/^[A-Z]:/, '') // remove Windows-style prefix\n    .replace(/\\\\/g, '/') // replace all `\\` instances with `/`\n}\n", "// Portions of this file are derived from getsentry/sentry-javascript by Software, Inc. dba Sentry\n// Licensed under the MIT License\n\n/** A simple Least Recently Used map */\nexport class ReduceableCache<K, V> {\n  private readonly _cache: Map<K, V>\n\n  public constructor(private readonly _maxSize: number) {\n    this._cache = new Map<K, V>()\n  }\n\n  /** Get an entry or undefined if it was not in the cache. Re-inserts to update the recently used order */\n  public get(key: K): V | undefined {\n    const value = this._cache.get(key)\n    if (value === undefined) {\n      return undefined\n    }\n    // Remove and re-insert to update the order\n    this._cache.delete(key)\n    this._cache.set(key, value)\n    return value\n  }\n\n  /** Insert an entry and evict an older entry if we've reached maxSize */\n  public set(key: K, value: V): void {\n    this._cache.set(key, value)\n  }\n\n  /** Remove an entry and return the entry if it was in the cache */\n  public reduce(): void {\n    while (this._cache.size >= this._maxSize) {\n      const value = this._cache.keys().next().value\n      if (value) {\n        // keys() returns an iterator in insertion order so keys().next() gives us the oldest key\n        this._cache.delete(value)\n      }\n    }\n  }\n}\n", "// Portions of this file are derived from getsentry/sentry-javascript by Software, Inc. dba Sentry\n// Licensed under the MIT License\n\nimport { StackFrame } from './types'\nimport { ReduceableCache } from './reduceable-cache'\nimport { createReadStream } from 'node:fs'\nimport { createInterface } from 'node:readline'\n\nconst LRU_FILE_CONTENTS_CACHE = new ReduceableCache<string, Record<number, string>>(25)\nconst LRU_FILE_CONTENTS_FS_READ_FAILED = new ReduceableCache<string, 1>(20)\nconst DEFAULT_LINES_OF_CONTEXT = 7\n// Determines the upper bound of lineno/colno that we will attempt to read. Large colno values are likely to be\n// minified code while large lineno values are likely to be bundled code.\n// Exported for testing purposes.\nexport const MAX_CONTEXTLINES_COLNO: number = 1000\nexport const MAX_CONTEXTLINES_LINENO: number = 10000\n\ntype ReadlineRange = [start: number, end: number]\n\nexport async function addSourceContext(frames: StackFrame[]): Promise<StackFrame[]> {\n  // keep a lookup map of which files we've already enqueued to read,\n  // so we don't enqueue the same file multiple times which would cause multiple i/o reads\n  const filesToLines: Record<string, number[]> = {}\n\n  // Maps preserve insertion order, so we iterate in reverse, starting at the\n  // outermost frame and closer to where the exception has occurred (poor mans priority)\n  for (let i = frames.length - 1; i >= 0; i--) {\n    const frame: StackFrame | undefined = frames[i]\n    const filename = frame?.filename\n\n    if (\n      !frame ||\n      typeof filename !== 'string' ||\n      typeof frame.lineno !== 'number' ||\n      shouldSkipContextLinesForFile(filename) ||\n      shouldSkipContextLinesForFrame(frame)\n    ) {\n      continue\n    }\n\n    const filesToLinesOutput = filesToLines[filename]\n    if (!filesToLinesOutput) {\n      filesToLines[filename] = []\n    }\n    filesToLines[filename].push(frame.lineno)\n  }\n\n  const files = Object.keys(filesToLines)\n  if (files.length == 0) {\n    return frames\n  }\n\n  const readlinePromises: Promise<void>[] = []\n  for (const file of files) {\n    // If we failed to read this before, dont try reading it again.\n    if (LRU_FILE_CONTENTS_FS_READ_FAILED.get(file)) {\n      continue\n    }\n\n    const filesToLineRanges = filesToLines[file]\n    if (!filesToLineRanges) {\n      continue\n    }\n\n    // Sort ranges so that they are sorted by line increasing order and match how the file is read.\n    filesToLineRanges.sort((a, b) => a - b)\n    // Check if the contents are already in the cache and if we can avoid reading the file again.\n    const ranges = makeLineReaderRanges(filesToLineRanges)\n    if (ranges.every((r) => rangeExistsInContentCache(file, r))) {\n      continue\n    }\n\n    const cache = emplace(LRU_FILE_CONTENTS_CACHE, file, {})\n    readlinePromises.push(getContextLinesFromFile(file, ranges, cache))\n  }\n\n  // The promise rejections are caught in order to prevent them from short circuiting Promise.all\n  await Promise.all(readlinePromises).catch(() => {})\n\n  // Perform the same loop as above, but this time we can assume all files are in the cache\n  // and attempt to add source context to frames.\n  if (frames && frames.length > 0) {\n    addSourceContextToFrames(frames, LRU_FILE_CONTENTS_CACHE)\n  }\n\n  // Once we're finished processing an exception reduce the files held in the cache\n  // so that we don't indefinetly increase the size of this map\n  LRU_FILE_CONTENTS_CACHE.reduce()\n\n  return frames\n}\n\n/**\n * Extracts lines from a file and stores them in a cache.\n */\nfunction getContextLinesFromFile(path: string, ranges: ReadlineRange[], output: Record<number, string>): Promise<void> {\n  return new Promise((resolve) => {\n    // It is important *not* to have any async code between createInterface and the 'line' event listener\n    // as it will cause the 'line' event to\n    // be emitted before the listener is attached.\n    const stream = createReadStream(path)\n    const lineReaded = createInterface({\n      input: stream,\n    })\n\n    // We need to explicitly destroy the stream to prevent memory leaks,\n    // removing the listeners on the readline interface is not enough.\n    // See: https://github.com/nodejs/node/issues/9002 and https://github.com/getsentry/sentry-javascript/issues/14892\n    function destroyStreamAndResolve(): void {\n      stream.destroy()\n      resolve()\n    }\n\n    // Init at zero and increment at the start of the loop because lines are 1 indexed.\n    let lineNumber = 0\n    let currentRangeIndex = 0\n    const range = ranges[currentRangeIndex]\n    if (range === undefined) {\n      // We should never reach this point, but if we do, we should resolve the promise to prevent it from hanging.\n      destroyStreamAndResolve()\n      return\n    }\n    let rangeStart = range[0]\n    let rangeEnd = range[1]\n\n    // We use this inside Promise.all, so we need to resolve the promise even if there is an error\n    // to prevent Promise.all from short circuiting the rest.\n    function onStreamError(): void {\n      // Mark file path as failed to read and prevent multiple read attempts.\n      LRU_FILE_CONTENTS_FS_READ_FAILED.set(path, 1)\n      lineReaded.close()\n      lineReaded.removeAllListeners()\n      destroyStreamAndResolve()\n    }\n\n    // We need to handle the error event to prevent the process from crashing in < Node 16\n    // https://github.com/nodejs/node/pull/31603\n    stream.on('error', onStreamError)\n    lineReaded.on('error', onStreamError)\n    lineReaded.on('close', destroyStreamAndResolve)\n\n    lineReaded.on('line', (line) => {\n      lineNumber++\n      if (lineNumber < rangeStart) {\n        return\n      }\n\n      // !Warning: This mutates the cache by storing the snipped line into the cache.\n      output[lineNumber] = snipLine(line, 0)\n\n      if (lineNumber >= rangeEnd) {\n        if (currentRangeIndex === ranges.length - 1) {\n          // We need to close the file stream and remove listeners, else the reader will continue to run our listener;\n          lineReaded.close()\n          lineReaded.removeAllListeners()\n          return\n        }\n        currentRangeIndex++\n        const range = ranges[currentRangeIndex]\n        if (range === undefined) {\n          // This should never happen as it means we have a bug in the context.\n          lineReaded.close()\n          lineReaded.removeAllListeners()\n          return\n        }\n        rangeStart = range[0]\n        rangeEnd = range[1]\n      }\n    })\n  })\n}\n\n/** Adds context lines to frames */\nfunction addSourceContextToFrames(frames: StackFrame[], cache: ReduceableCache<string, Record<number, string>>): void {\n  for (const frame of frames) {\n    // Only add context if we have a filename and it hasn't already been added\n    if (frame.filename && frame.context_line === undefined && typeof frame.lineno === 'number') {\n      const contents = cache.get(frame.filename)\n      if (contents === undefined) {\n        continue\n      }\n\n      addContextToFrame(frame.lineno, frame, contents)\n    }\n  }\n}\n\n/**\n * Resolves context lines before and after the given line number and appends them to the frame;\n */\nfunction addContextToFrame(lineno: number, frame: StackFrame, contents: Record<number, string> | undefined): void {\n  // When there is no line number in the frame, attaching context is nonsensical and will even break grouping.\n  // We already check for lineno before calling this, but since StackFrame lineno is optional, we check it again.\n  if (frame.lineno === undefined || contents === undefined) {\n    return\n  }\n\n  frame.pre_context = []\n  for (let i = makeRangeStart(lineno); i < lineno; i++) {\n    // We always expect the start context as line numbers cannot be negative. If we dont find a line, then\n    // something went wrong somewhere. Clear the context and return without adding any linecontext.\n    const line = contents[i]\n    if (line === undefined) {\n      clearLineContext(frame)\n      return\n    }\n\n    frame.pre_context.push(line)\n  }\n\n  // We should always have the context line. If we dont, something went wrong, so we clear the context and return\n  // without adding any linecontext.\n  if (contents[lineno] === undefined) {\n    clearLineContext(frame)\n    return\n  }\n\n  frame.context_line = contents[lineno]\n\n  const end = makeRangeEnd(lineno)\n  frame.post_context = []\n  for (let i = lineno + 1; i <= end; i++) {\n    // Since we dont track when the file ends, we cant clear the context if we dont find a line as it could\n    // just be that we reached the end of the file.\n    const line = contents[i]\n    if (line === undefined) {\n      break\n    }\n    frame.post_context.push(line)\n  }\n}\n\n/**\n * Clears the context lines from a frame, used to reset a frame to its original state\n * if we fail to resolve all context lines for it.\n */\nfunction clearLineContext(frame: StackFrame): void {\n  delete frame.pre_context\n  delete frame.context_line\n  delete frame.post_context\n}\n\n/**\n * Determines if context lines should be skipped for a file.\n * - .min.(mjs|cjs|js) files are and not useful since they dont point to the original source\n * - node: prefixed modules are part of the runtime and cannot be resolved to a file\n * - data: skip json, wasm and inline js https://nodejs.org/api/esm.html#data-imports\n */\nfunction shouldSkipContextLinesForFile(path: string): boolean {\n  // Test the most common prefix and extension first. These are the ones we\n  // are most likely to see in user applications and are the ones we can break out of first.\n  return (\n    path.startsWith('node:') ||\n    path.endsWith('.min.js') ||\n    path.endsWith('.min.cjs') ||\n    path.endsWith('.min.mjs') ||\n    path.startsWith('data:')\n  )\n}\n\n/**\n * Determines if we should skip contextlines based off the max lineno and colno values.\n */\nfunction shouldSkipContextLinesForFrame(frame: StackFrame): boolean {\n  if (frame.lineno !== undefined && frame.lineno > MAX_CONTEXTLINES_LINENO) {\n    return true\n  }\n  if (frame.colno !== undefined && frame.colno > MAX_CONTEXTLINES_COLNO) {\n    return true\n  }\n  return false\n}\n\n/**\n * Checks if we have all the contents that we need in the cache.\n */\nfunction rangeExistsInContentCache(file: string, range: ReadlineRange): boolean {\n  const contents = LRU_FILE_CONTENTS_CACHE.get(file)\n  if (contents === undefined) {\n    return false\n  }\n\n  for (let i = range[0]; i <= range[1]; i++) {\n    if (contents[i] === undefined) {\n      return false\n    }\n  }\n\n  return true\n}\n\n/**\n * Creates contiguous ranges of lines to read from a file. In the case where context lines overlap,\n * the ranges are merged to create a single range.\n */\nfunction makeLineReaderRanges(lines: number[]): ReadlineRange[] {\n  if (!lines.length) {\n    return []\n  }\n\n  let i = 0\n  const line = lines[0]\n\n  if (typeof line !== 'number') {\n    return []\n  }\n\n  let current = makeContextRange(line)\n  const out: ReadlineRange[] = []\n  while (true) {\n    if (i === lines.length - 1) {\n      out.push(current)\n      break\n    }\n\n    // If the next line falls into the current range, extend the current range to lineno + linecontext.\n    const next = lines[i + 1]\n    if (typeof next !== 'number') {\n      break\n    }\n    if (next <= current[1]) {\n      current[1] = next + DEFAULT_LINES_OF_CONTEXT\n    } else {\n      out.push(current)\n      current = makeContextRange(next)\n    }\n\n    i++\n  }\n\n  return out\n}\n// Determine start and end indices for context range (inclusive);\nfunction makeContextRange(line: number): [start: number, end: number] {\n  return [makeRangeStart(line), makeRangeEnd(line)]\n}\n// Compute inclusive end context range\nfunction makeRangeStart(line: number): number {\n  return Math.max(1, line - DEFAULT_LINES_OF_CONTEXT)\n}\n// Compute inclusive start context range\nfunction makeRangeEnd(line: number): number {\n  return line + DEFAULT_LINES_OF_CONTEXT\n}\n\n/**\n * Get or init map value\n */\nfunction emplace<T extends ReduceableCache<K, V>, K extends string, V>(map: T, key: K, contents: V): V {\n  const value = map.get(key)\n\n  if (value === undefined) {\n    map.set(key, contents)\n    return contents\n  }\n\n  return value\n}\n\nfunction snipLine(line: string, colno: number): string {\n  let newLine = line\n  const lineLength = newLine.length\n  if (lineLength <= 150) {\n    return newLine\n  }\n  if (colno > lineLength) {\n    colno = lineLength\n  }\n\n  let start = Math.max(colno - 60, 0)\n  if (start < 5) {\n    start = 0\n  }\n\n  let end = Math.min(start + 140, lineLength)\n  if (end > lineLength - 5) {\n    end = lineLength\n  }\n  if (end === lineLength) {\n    start = Math.max(end - 140, 0)\n  }\n\n  newLine = newLine.slice(start, end)\n  if (start > 0) {\n    newLine = `...${newLine}`\n  }\n  if (end < lineLength) {\n    newLine += '...'\n  }\n\n  return newLine\n}\n", "export type PostHogCoreOptions = {\n  /** PostHog API host, usually 'https://us.i.posthog.com' or 'https://eu.i.posthog.com' */\n  host?: string\n  /** The number of events to queue before sending to PostHog (flushing) */\n  flushAt?: number\n  /** The interval in milliseconds between periodic flushes */\n  flushInterval?: number\n  /** The maximum number of queued messages to be flushed as part of a single batch (must be higher than `flushAt`) */\n  maxBatchSize?: number\n  /** The maximum number of cached messages either in memory or on the local storage.\n   * Defaults to 1000, (must be higher than `flushAt`)\n   */\n  maxQueueSize?: number\n  /** If set to true the SDK is essentially disabled (useful for local environments where you don't want to track anything) */\n  disabled?: boolean\n  /** If set to false the SDK will not track until the `optIn` function is called. */\n  defaultOptIn?: boolean\n  /** Whether to track that `getFeatureFlag` was called (used by Experiments) */\n  sendFeatureFlagEvent?: boolean\n  /** Whether to load feature flags when initialized or not */\n  preloadFeatureFlags?: boolean\n  /**\n   * Whether to load remote config when initialized or not\n   * Experimental support\n   * Default: false - Remote config is loaded by default\n   */\n  disableRemoteConfig?: boolean\n  /**\n   * Whether to load surveys when initialized or not\n   * Experimental support\n   * Default: false - Surveys are loaded by default, but requires the `PostHogSurveyProvider` to be used\n   */\n  disableSurveys?: boolean\n  /** Option to bootstrap the library with given distinctId and feature flags */\n  bootstrap?: {\n    distinctId?: string\n    isIdentifiedId?: boolean\n    featureFlags?: Record<string, FeatureFlagValue>\n    featureFlagPayloads?: Record<string, JsonType>\n  }\n  /** How many times we will retry HTTP requests. Defaults to 3. */\n  fetchRetryCount?: number\n  /** The delay between HTTP request retries, Defaults to 3 seconds. */\n  fetchRetryDelay?: number\n  /** Timeout in milliseconds for any calls. Defaults to 10 seconds. */\n  requestTimeout?: number\n  /** Timeout in milliseconds for feature flag calls. Defaults to 10 seconds for stateful clients, and 3 seconds for stateless. */\n  featureFlagsRequestTimeoutMs?: number\n  /** Timeout in milliseconds for remote config calls. Defaults to 3 seconds. */\n  remoteConfigRequestTimeoutMs?: number\n  /** For Session Analysis how long before we expire a session (defaults to 30 mins) */\n  sessionExpirationTimeSeconds?: number\n  /** Whether to post events to PostHog in JSON or compressed format. Defaults to 'json' */\n  captureMode?: 'json' | 'form'\n  disableGeoip?: boolean\n  /** Special flag to indicate ingested data is for a historical migration. */\n  historicalMigration?: boolean\n}\n\nexport enum PostHogPersistedProperty {\n  AnonymousId = 'anonymous_id',\n  DistinctId = 'distinct_id',\n  Props = 'props',\n  FeatureFlagDetails = 'feature_flag_details',\n  FeatureFlags = 'feature_flags',\n  FeatureFlagPayloads = 'feature_flag_payloads',\n  BootstrapFeatureFlagDetails = 'bootstrap_feature_flag_details',\n  BootstrapFeatureFlags = 'bootstrap_feature_flags',\n  BootstrapFeatureFlagPayloads = 'bootstrap_feature_flag_payloads',\n  OverrideFeatureFlags = 'override_feature_flags',\n  Queue = 'queue',\n  OptedOut = 'opted_out',\n  SessionId = 'session_id',\n  SessionLastTimestamp = 'session_timestamp',\n  PersonProperties = 'person_properties',\n  GroupProperties = 'group_properties',\n  InstalledAppBuild = 'installed_app_build', // only used by posthog-react-native\n  InstalledAppVersion = 'installed_app_version', // only used by posthog-react-native\n  SessionReplay = 'session_replay', // only used by posthog-react-native\n  DecideEndpointWasHit = 'decide_endpoint_was_hit', // only used by posthog-react-native\n  SurveyLastSeenDate = 'survey_last_seen_date', // only used by posthog-react-native\n  SurveysSeen = 'surveys_seen', // only used by posthog-react-native\n  Surveys = 'surveys', // only used by posthog-react-native\n  RemoteConfig = 'remote_config',\n}\n\nexport type PostHogFetchOptions = {\n  method: 'GET' | 'POST' | 'PUT' | 'PATCH'\n  mode?: 'no-cors'\n  credentials?: 'omit'\n  headers: { [key: string]: string }\n  body?: string\n  signal?: AbortSignal\n}\n\n// Check out posthog-js for these additional options and try to keep them in sync\nexport type PostHogCaptureOptions = {\n  /** If provided overrides the auto-generated event ID */\n  uuid?: string\n  /** If provided overrides the auto-generated timestamp */\n  timestamp?: Date\n  disableGeoip?: boolean\n}\n\nexport type PostHogFetchResponse = {\n  status: number\n  text: () => Promise<string>\n  json: () => Promise<any>\n}\n\nexport type PostHogQueueItem = {\n  message: any\n  callback?: (err: any) => void\n}\n\nexport type PostHogEventProperties = {\n  [key: string]: JsonType\n}\n\nexport type PostHogGroupProperties = {\n  [type: string]: string | number\n}\n\nexport type PostHogAutocaptureElement = {\n  $el_text?: string\n  tag_name: string\n  href?: string\n  nth_child?: number\n  nth_of_type?: number\n  order?: number\n} & PostHogEventProperties\n// Any key prefixed with `attr__` can be added\n\nexport type PostHogRemoteConfig = {\n  sessionRecording?:\n    | boolean\n    | {\n        [key: string]: JsonType\n      }\n\n  /**\n   * Whether surveys are enabled\n   */\n  surveys?: boolean | Survey[]\n\n  /**\n   * Indicates if the team has any flags enabled (if not we don't need to load them)\n   */\n  hasFeatureFlags?: boolean\n}\n\nexport type FeatureFlagValue = string | boolean\n\nexport type PostHogDecideResponse = Omit<PostHogRemoteConfig, 'surveys' | 'hasFeatureFlags'> & {\n  featureFlags: {\n    [key: string]: FeatureFlagValue\n  }\n  featureFlagPayloads: {\n    [key: string]: JsonType\n  }\n  flags: {\n    [key: string]: FeatureFlagDetail\n  }\n  errorsWhileComputingFlags: boolean\n  sessionRecording?:\n    | boolean\n    | {\n        [key: string]: JsonType\n      }\n  quotaLimited?: string[]\n  requestId?: string\n}\n\nexport type PostHogFeatureFlagsResponse = PartialWithRequired<\n  PostHogDecideResponse,\n  'flags' | 'featureFlags' | 'featureFlagPayloads' | 'requestId'\n>\n\n/**\n * Creates a type with all properties of T, but makes only K properties required while the rest remain optional.\n *\n * @template T - The base type containing all properties\n * @template K - Union type of keys from T that should be required\n *\n * @example\n * interface User {\n *   id: number;\n *   name: string;\n *   email?: string;\n *   age?: number;\n * }\n *\n * // Makes 'id' and 'name' required, but 'email' and 'age' optional\n * type RequiredUser = PartialWithRequired<User, 'id' | 'name'>;\n *\n * const user: RequiredUser = {\n *   id: 1,      // Must be provided\n *   name: \"John\" // Must be provided\n *   // email and age are optional\n * };\n */\nexport type PartialWithRequired<T, K extends keyof T> = {\n  [P in K]: T[P] // Required fields\n} & {\n  [P in Exclude<keyof T, K>]?: T[P] // Optional fields\n}\n\n/**\n * These are the fields we care about from PostHogDecideResponse for feature flags.\n */\nexport type PostHogFeatureFlagDetails = PartialWithRequired<\n  PostHogDecideResponse,\n  'flags' | 'featureFlags' | 'featureFlagPayloads' | 'requestId'\n>\n\n/**\n * Models the response from the v3 `/decide` endpoint.\n */\nexport type PostHogV3DecideResponse = Omit<PostHogDecideResponse, 'flags'>\nexport type PostHogV4DecideResponse = Omit<PostHogDecideResponse, 'featureFlags' | 'featureFlagPayloads'>\n\n/**\n * The format of the flags object in persisted storage\n *\n * When we pull flags from persistence, we can normalize them to PostHogFeatureFlagDetails\n * so that we can support v3 and v4 of the API.\n */\nexport type PostHogFlagsStorageFormat = Pick<PostHogFeatureFlagDetails, 'flags'>\n\n/**\n * Models legacy flags and payloads return type for many public methods.\n */\nexport type PostHogFlagsAndPayloadsResponse = Partial<\n  Pick<PostHogDecideResponse, 'featureFlags' | 'featureFlagPayloads'>\n>\n\nexport type JsonType = string | number | boolean | null | { [key: string]: JsonType } | Array<JsonType> | JsonType[]\n\nexport type FetchLike = (url: string, options: PostHogFetchOptions) => Promise<PostHogFetchResponse>\n\nexport type FeatureFlagDetail = {\n  key: string\n  enabled: boolean\n  variant: string | undefined\n  reason: EvaluationReason | undefined\n  metadata: FeatureFlagMetadata | undefined\n}\n\nexport type FeatureFlagMetadata = {\n  id: number | undefined\n  version: number | undefined\n  description: string | undefined\n  // Payloads in the response are always JSON encoded as a string\n  payload: string | undefined\n}\n\nexport type EvaluationReason = {\n  code: string | undefined\n  condition_index: number | undefined\n  description: string | undefined\n}\n\n// survey types\nexport type SurveyAppearance = {\n  // keep in sync with frontend/src/types.ts -> SurveyAppearance\n  backgroundColor?: string\n  submitButtonColor?: string\n  // deprecate submit button text eventually\n  submitButtonText?: string\n  submitButtonTextColor?: string\n  ratingButtonColor?: string\n  ratingButtonActiveColor?: string\n  autoDisappear?: boolean\n  displayThankYouMessage?: boolean\n  thankYouMessageHeader?: string\n  thankYouMessageDescription?: string\n  thankYouMessageDescriptionContentType?: SurveyQuestionDescriptionContentType\n  thankYouMessageCloseButtonText?: string\n  borderColor?: string\n  position?: SurveyPosition\n  placeholder?: string\n  shuffleQuestions?: boolean\n  surveyPopupDelaySeconds?: number\n  // widget options\n  widgetType?: SurveyWidgetType\n  widgetSelector?: string\n  widgetLabel?: string\n  widgetColor?: string\n}\n\nexport enum SurveyPosition {\n  Left = 'left',\n  Right = 'right',\n  Center = 'center',\n}\n\nexport enum SurveyWidgetType {\n  Button = 'button',\n  Tab = 'tab',\n  Selector = 'selector',\n}\n\nexport enum SurveyType {\n  Popover = 'popover',\n  API = 'api',\n  Widget = 'widget',\n}\n\nexport type SurveyQuestion = BasicSurveyQuestion | LinkSurveyQuestion | RatingSurveyQuestion | MultipleSurveyQuestion\n\nexport enum SurveyQuestionDescriptionContentType {\n  Html = 'html',\n  Text = 'text',\n}\n\ntype SurveyQuestionBase = {\n  question: string\n  id?: string // TODO: use this for the question id\n  description?: string\n  descriptionContentType?: SurveyQuestionDescriptionContentType\n  optional?: boolean\n  buttonText?: string\n  originalQuestionIndex: number\n  branching?: NextQuestionBranching | EndBranching | ResponseBasedBranching | SpecificQuestionBranching\n}\n\nexport type BasicSurveyQuestion = SurveyQuestionBase & {\n  type: SurveyQuestionType.Open\n}\n\nexport type LinkSurveyQuestion = SurveyQuestionBase & {\n  type: SurveyQuestionType.Link\n  link?: string\n}\n\nexport type RatingSurveyQuestion = SurveyQuestionBase & {\n  type: SurveyQuestionType.Rating\n  display: SurveyRatingDisplay\n  scale: 3 | 5 | 7 | 10\n  lowerBoundLabel: string\n  upperBoundLabel: string\n}\n\nexport enum SurveyRatingDisplay {\n  Number = 'number',\n  Emoji = 'emoji',\n}\n\nexport type MultipleSurveyQuestion = SurveyQuestionBase & {\n  type: SurveyQuestionType.SingleChoice | SurveyQuestionType.MultipleChoice\n  choices: string[]\n  hasOpenChoice?: boolean\n  shuffleOptions?: boolean\n}\n\nexport enum SurveyQuestionType {\n  Open = 'open',\n  MultipleChoice = 'multiple_choice',\n  SingleChoice = 'single_choice',\n  Rating = 'rating',\n  Link = 'link',\n}\n\nexport enum SurveyQuestionBranchingType {\n  NextQuestion = 'next_question',\n  End = 'end',\n  ResponseBased = 'response_based',\n  SpecificQuestion = 'specific_question',\n}\n\nexport type NextQuestionBranching = {\n  type: SurveyQuestionBranchingType.NextQuestion\n}\n\nexport type EndBranching = {\n  type: SurveyQuestionBranchingType.End\n}\n\nexport type ResponseBasedBranching = {\n  type: SurveyQuestionBranchingType.ResponseBased\n  responseValues: Record<string, any>\n}\n\nexport type SpecificQuestionBranching = {\n  type: SurveyQuestionBranchingType.SpecificQuestion\n  index: number\n}\n\nexport type SurveyResponse = {\n  surveys: Survey[]\n}\n\nexport type SurveyCallback = (surveys: Survey[]) => void\n\nexport enum SurveyMatchType {\n  Regex = 'regex',\n  NotRegex = 'not_regex',\n  Exact = 'exact',\n  IsNot = 'is_not',\n  Icontains = 'icontains',\n  NotIcontains = 'not_icontains',\n}\n\nexport type SurveyElement = {\n  text?: string\n  $el_text?: string\n  tag_name?: string\n  href?: string\n  attr_id?: string\n  attr_class?: string[]\n  nth_child?: number\n  nth_of_type?: number\n  attributes?: Record<string, any>\n  event_id?: number\n  order?: number\n  group_id?: number\n}\nexport type SurveyRenderReason = {\n  visible: boolean\n  disabledReason?: string\n}\n\nexport type Survey = {\n  // Sync this with the backend's SurveyAPISerializer!\n  id: string\n  name: string\n  description?: string\n  type: SurveyType\n  feature_flag_keys?:\n    | {\n        key: string\n        value?: string\n      }[]\n  linked_flag_key?: string\n  targeting_flag_key?: string\n  internal_targeting_flag_key?: string\n  questions: SurveyQuestion[]\n  appearance?: SurveyAppearance\n  conditions?: {\n    url?: string\n    selector?: string\n    seenSurveyWaitPeriodInDays?: number\n    urlMatchType?: SurveyMatchType\n    events?: {\n      repeatedActivation?: boolean\n      values?: {\n        name: string\n      }[]\n    }\n    actions?: {\n      values: SurveyActionType[]\n    }\n    deviceTypes?: string[]\n    deviceTypesMatchType?: SurveyMatchType\n  }\n  start_date?: string\n  end_date?: string\n  current_iteration?: number\n  current_iteration_start_date?: string\n}\n\nexport type SurveyActionType = {\n  id: number\n  name?: string\n  steps?: ActionStepType[]\n}\n\n/** Sync with plugin-server/src/types.ts */\nexport enum ActionStepStringMatching {\n  Contains = 'contains',\n  Exact = 'exact',\n  Regex = 'regex',\n}\n\nexport type ActionStepType = {\n  event?: string\n  selector?: string\n  /** @deprecated Only `selector` should be used now. */\n  tag_name?: string\n  text?: string\n  /** @default StringMatching.Exact */\n  text_matching?: ActionStepStringMatching\n  href?: string\n  /** @default ActionStepStringMatching.Exact */\n  href_matching?: ActionStepStringMatching\n  url?: string\n  /** @default StringMatching.Contains */\n  url_matching?: ActionStepStringMatching\n}\n", "import {\n  FeatureFlagDetail,\n  FeatureFlagValue,\n  JsonType,\n  PostHogDecideResponse,\n  PostHogV3DecideResponse,\n  PostHogV4DecideResponse,\n  PostHogFlagsAndPayloadsResponse,\n  PartialWithRequired,\n  PostHogFeatureFlagsResponse,\n} from './types'\n\nexport const normalizeDecideResponse = (\n  decideResponse:\n    | PartialWithRequired<PostHogV4DecideResponse, 'flags'>\n    | PartialWithRequired<PostHogV3DecideResponse, 'featureFlags' | 'featureFlagPayloads'>\n): PostHogFeatureFlagsResponse => {\n  if ('flags' in decideResponse) {\n    // Convert v4 format to v3 format\n    const featureFlags = getFlagValuesFromFlags(decideResponse.flags)\n    const featureFlagPayloads = getPayloadsFromFlags(decideResponse.flags)\n\n    return {\n      ...decideResponse,\n      featureFlags,\n      featureFlagPayloads,\n    }\n  } else {\n    // Convert v3 format to v4 format\n    const featureFlags = decideResponse.featureFlags ?? {}\n    const featureFlagPayloads = Object.fromEntries(\n      Object.entries(decideResponse.featureFlagPayloads || {}).map(([k, v]) => [k, parsePayload(v)])\n    )\n\n    const flags = Object.fromEntries(\n      Object.entries(featureFlags).map(([key, value]) => [\n        key,\n        getFlagDetailFromFlagAndPayload(key, value, featureFlagPayloads[key]),\n      ])\n    )\n\n    return {\n      ...decideResponse,\n      featureFlags,\n      featureFlagPayloads,\n      flags,\n    }\n  }\n}\n\nfunction getFlagDetailFromFlagAndPayload(\n  key: string,\n  value: FeatureFlagValue,\n  payload: JsonType | undefined\n): FeatureFlagDetail {\n  return {\n    key: key,\n    enabled: typeof value === 'string' ? true : value,\n    variant: typeof value === 'string' ? value : undefined,\n    reason: undefined,\n    metadata: {\n      id: undefined,\n      version: undefined,\n      payload: payload ? JSON.stringify(payload) : undefined,\n      description: undefined,\n    },\n  }\n}\n\n/**\n * Get the flag values from the flags v4 response.\n * @param flags - The flags\n * @returns The flag values\n */\nexport const getFlagValuesFromFlags = (\n  flags: PostHogDecideResponse['flags']\n): PostHogDecideResponse['featureFlags'] => {\n  return Object.fromEntries(\n    Object.entries(flags ?? {})\n      .map(([key, detail]) => [key, getFeatureFlagValue(detail)])\n      .filter(([, value]): boolean => value !== undefined)\n  )\n}\n\n/**\n * Get the payloads from the flags v4 response.\n * @param flags - The flags\n * @returns The payloads\n */\nexport const getPayloadsFromFlags = (\n  flags: PostHogDecideResponse['flags']\n): PostHogDecideResponse['featureFlagPayloads'] => {\n  const safeFlags = flags ?? {}\n  return Object.fromEntries(\n    Object.keys(safeFlags)\n      .filter((flag) => {\n        const details = safeFlags[flag]\n        return details.enabled && details.metadata && details.metadata.payload !== undefined\n      })\n      .map((flag) => {\n        const payload = safeFlags[flag].metadata?.payload as string\n        return [flag, payload ? parsePayload(payload) : undefined]\n      })\n  )\n}\n\n/**\n * Get the flag details from the legacy v3 flags and payloads. As such, it will lack the reason, id, version, and description.\n * @param decideResponse - The decide response\n * @returns The flag details\n */\nexport const getFlagDetailsFromFlagsAndPayloads = (\n  decideResponse: PostHogFeatureFlagsResponse\n): PostHogDecideResponse['flags'] => {\n  const flags = decideResponse.featureFlags ?? {}\n  const payloads = decideResponse.featureFlagPayloads ?? {}\n  return Object.fromEntries(\n    Object.entries(flags).map(([key, value]) => [\n      key,\n      {\n        key: key,\n        enabled: typeof value === 'string' ? true : value,\n        variant: typeof value === 'string' ? value : undefined,\n        reason: undefined,\n        metadata: {\n          id: undefined,\n          version: undefined,\n          payload: payloads?.[key] ? JSON.stringify(payloads[key]) : undefined,\n          description: undefined,\n        },\n      },\n    ])\n  )\n}\n\nexport const getFeatureFlagValue = (detail: FeatureFlagDetail | undefined): FeatureFlagValue | undefined => {\n  return detail === undefined ? undefined : detail.variant ?? detail.enabled\n}\n\nexport const parsePayload = (response: any): any => {\n  if (typeof response !== 'string') {\n    return response\n  }\n\n  try {\n    return JSON.parse(response)\n  } catch {\n    return response\n  }\n}\n\n/**\n * Get the normalized flag details from the flags and payloads.\n * This is used to convert things like boostrap and stored feature flags and payloads to the v4 format.\n * This helps us ensure backwards compatibility.\n * If a key exists in the featureFlagPayloads that is not in the featureFlags, we treat it as a true feature flag.\n *\n * @param featureFlags - The feature flags\n * @param featureFlagPayloads - The feature flag payloads\n * @returns The normalized flag details\n */\nexport const createDecideResponseFromFlagsAndPayloads = (\n  featureFlags: PostHogV3DecideResponse['featureFlags'],\n  featureFlagPayloads: PostHogV3DecideResponse['featureFlagPayloads']\n): PostHogFeatureFlagsResponse => {\n  // If a feature flag payload key is not in the feature flags, we treat it as true feature flag.\n  const allKeys = [...new Set([...Object.keys(featureFlags ?? {}), ...Object.keys(featureFlagPayloads ?? {})])]\n  const enabledFlags = allKeys\n    .filter((flag) => !!featureFlags[flag] || !!featureFlagPayloads[flag])\n    .reduce((res: Record<string, FeatureFlagValue>, key) => ((res[key] = featureFlags[key] ?? true), res), {})\n\n  const flagDetails: PostHogFlagsAndPayloadsResponse = {\n    featureFlags: enabledFlags,\n    featureFlagPayloads: featureFlagPayloads ?? {},\n  }\n\n  return normalizeDecideResponse(flagDetails as PostHogV3DecideResponse)\n}\n\nexport const updateFlagValue = (flag: FeatureFlagDetail, value: FeatureFlagValue): FeatureFlagDetail => {\n  return {\n    ...flag,\n    enabled: getEnabledFromValue(value),\n    variant: getVariantFromValue(value),\n  }\n}\n\nfunction getEnabledFromValue(value: FeatureFlagValue): boolean {\n  return typeof value === 'string' ? true : value\n}\n\nfunction getVariantFromValue(value: FeatureFlagValue): string | undefined {\n  return typeof value === 'string' ? value : undefined\n}\n", "import { FetchLike } from './types'\n\n// Rollout constants\nexport const NEW_FLAGS_ROLLOUT_PERCENTAGE = 1\n// The fnv1a hashes of the tokens that are explicitly excluded from the rollout\n// see https://github.com/PostHog/posthog-js-lite/blob/main/posthog-core/src/utils.ts#L84\n// are hashed API tokens from our top 10 for each category supported by this SDK.\nexport const NEW_FLAGS_EXCLUDED_HASHES = new Set([\n  // Node\n  '61be3dd8',\n  '96f6df5f',\n  '8cfdba9b',\n  'bf027177',\n  'e59430a8',\n  '7fa5500b',\n  '569798e9',\n  '04809ff7',\n  '0ebc61a5',\n  '32de7f98',\n  '3beeb69a',\n  '12d34ad9',\n  '733853ec',\n  '0645bb64',\n  '5dcbee21',\n  'b1f95fa3',\n  '2189e408',\n  '82b460c2',\n  '3a8cc979',\n  '29ef8843',\n  '2cdbf767',\n  '38084b54',\n  // React Native\n  '50f9f8de',\n  '41d0df91',\n  '5c236689',\n  'c11aedd3',\n  'ada46672',\n  'f4331ee1',\n  '42fed62a',\n  'c957462c',\n  'd62f705a',\n  // Web (lots of teams per org, hence lots of API tokens)\n  'e0162666',\n  '01b3e5cf',\n  '441cef7f',\n  'bb9cafee',\n  '8f348eb0',\n  'b2553f3a',\n  '97469d7d',\n  '39f21a76',\n  '03706dcc',\n  '27d50569',\n  '307584a7',\n  '6433e92e',\n  '150c7fbb',\n  '49f57f22',\n  '3772f65b',\n  '01eb8256',\n  '3c9e9234',\n  'f853c7f7',\n  'c0ac4b67',\n  'cd609d40',\n  '10ca9b1a',\n  '8a87f11b',\n  '8e8e5216',\n  '1f6b63b3',\n  'db7943dd',\n  '79b7164c',\n  '07f78e33',\n  '2d21b6fd',\n  '952db5ee',\n  'a7d3b43f',\n  '1924dd9c',\n  '84e1b8f6',\n  'dff631b6',\n  'c5aa8a79',\n  'fa133a95',\n  '498a4508',\n  '24748755',\n  '98f3d658',\n  '21bbda67',\n  '7dbfed69',\n  'be3ec24c',\n  'fc80b8e2',\n  '75cc0998',\n])\n\nexport const STRING_FORMAT = 'utf8'\n\nexport function assert(truthyValue: any, message: string): void {\n  if (!truthyValue || typeof truthyValue !== 'string' || isEmpty(truthyValue)) {\n    throw new Error(message)\n  }\n}\n\nfunction isEmpty(truthyValue: string): boolean {\n  if (truthyValue.trim().length === 0) {\n    return true\n  }\n  return false\n}\n\nexport function removeTrailingSlash(url: string): string {\n  return url?.replace(/\\/+$/, '')\n}\n\nexport interface RetriableOptions {\n  retryCount: number\n  retryDelay: number\n  retryCheck: (err: unknown) => boolean\n}\n\nexport async function retriable<T>(fn: () => Promise<T>, props: RetriableOptions): Promise<T> {\n  let lastError = null\n\n  for (let i = 0; i < props.retryCount + 1; i++) {\n    if (i > 0) {\n      // don't wait when it's the last try\n      await new Promise<void>((r) => setTimeout(r, props.retryDelay))\n    }\n\n    try {\n      const res = await fn()\n      return res\n    } catch (e) {\n      lastError = e\n      if (!props.retryCheck(e)) {\n        throw e\n      }\n    }\n  }\n\n  throw lastError\n}\n\nexport function currentTimestamp(): number {\n  return new Date().getTime()\n}\n\nexport function currentISOTime(): string {\n  return new Date().toISOString()\n}\n\nexport function safeSetTimeout(fn: () => void, timeout: number): any {\n  // NOTE: we use this so rarely that it is totally fine to do `safeSetTimeout(fn, 0)``\n  // rather than setImmediate.\n  const t = setTimeout(fn, timeout) as any\n  // We unref if available to prevent Node.js hanging on exit\n  t?.unref && t?.unref()\n  return t\n}\n\n// NOTE: We opt for this slightly imperfect check as the global \"Promise\" object can get mutated in certain environments\nexport const isPromise = (obj: any): obj is Promise<any> => {\n  return obj && typeof obj.then === 'function'\n}\n\nexport const isError = (x: unknown): x is Error => {\n  return x instanceof Error\n}\n\nexport function getFetch(): FetchLike | undefined {\n  return typeof fetch !== 'undefined' ? fetch : typeof globalThis.fetch !== 'undefined' ? globalThis.fetch : undefined\n}\n\n// FNV-1a hash function\n// https://en.wikipedia.org/wiki/Fowler%E2%80%93Noll%E2%80%93Vo_hash_function\n// I know, I know, I'm rolling my own hash function, but I didn't want to take on\n// a crypto dependency and this is just temporary anyway\nfunction fnv1a(str: string): string {\n  let hash = 0x811c9dc5 // FNV offset basis\n  for (let i = 0; i < str.length; i++) {\n    hash ^= str.charCodeAt(i)\n    hash += (hash << 1) + (hash << 4) + (hash << 7) + (hash << 8) + (hash << 24)\n  }\n  // Convert to hex string, padding to 8 chars\n  return (hash >>> 0).toString(16).padStart(8, '0')\n}\n\nexport function isTokenInRollout(token: string, percentage: number = 0, excludedHashes?: Set<string>): boolean {\n  const tokenHash = fnv1a(token)\n  // Check excluded hashes (we're explicitly including these tokens from the rollout)\n  if (excludedHashes?.has(tokenHash)) {\n    return false\n  }\n\n  // Convert hash to int and divide by max value to get number between 0-1\n  const hashInt = parseInt(tokenHash, 16)\n  const hashFloat = hashInt / 0xffffffff\n\n  return hashFloat < percentage\n}\n\nexport function allSettled<T>(\n  promises: (Promise<T> | null | undefined)[]\n): Promise<({ status: 'fulfilled'; value: T } | { status: 'rejected'; reason: any })[]> {\n  return Promise.all(\n    promises.map((p) =>\n      (p ?? Promise.resolve()).then(\n        (value: any) => ({ status: 'fulfilled' as const, value }),\n        (reason: any) => ({ status: 'rejected' as const, reason })\n      )\n    )\n  )\n}\n", "// Copyright (c) 2013 Pieroxy <<EMAIL>>\n// This work is free. You can redistribute it and/or modify it\n// under the terms of the WTFPL, Version 2\n// For more information see LICENSE.txt or http://www.wtfpl.net/\n//\n// For more information, the home page:\n// http://pieroxy.net/blog/pages/lz-string/testing.html\n//\n// LZ-based compression algorithm, version 1.4.4\n\n// private property\nconst f = String.fromCharCode\nconst keyStrBase64 = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/='\nconst baseReverseDic: any = {}\n\nfunction getBaseValue(alphabet: any, character: any): any {\n  if (!baseReverseDic[alphabet]) {\n    baseReverseDic[alphabet] = {}\n    for (let i = 0; i < alphabet.length; i++) {\n      baseReverseDic[alphabet][alphabet.charAt(i)] = i\n    }\n  }\n  return baseReverseDic[alphabet][character]\n}\n\nexport const LZString = {\n  compressToBase64: function (input: any): string {\n    if (input == null) {\n      return ''\n    }\n    const res = LZString._compress(input, 6, function (a: any) {\n      return keyStrBase64.charAt(a)\n    })\n    switch (\n      res.length % 4 // To produce valid Base64\n    ) {\n      default: // When could this happen ?\n      case 0:\n        return res\n      case 1:\n        return res + '==='\n      case 2:\n        return res + '=='\n      case 3:\n        return res + '='\n    }\n  },\n\n  decompressFromBase64: function (input: any): any {\n    if (input == null) {\n      return ''\n    }\n    if (input == '') {\n      return null\n    }\n    return LZString._decompress(input.length, 32, function (index: any) {\n      return getBaseValue(keyStrBase64, input.charAt(index))\n    })\n  },\n\n  compress: function (uncompressed: any): any {\n    return LZString._compress(uncompressed, 16, function (a: any) {\n      return f(a)\n    })\n  },\n  _compress: function (uncompressed: any, bitsPerChar: any, getCharFromInt: any): any {\n    if (uncompressed == null) {\n      return ''\n    }\n    const context_dictionary: any = {},\n      context_dictionaryToCreate: any = {},\n      context_data = []\n\n    let i,\n      value,\n      context_c = '',\n      context_wc = '',\n      context_w = '',\n      context_enlargeIn = 2, // Compensate for the first entry which should not count\n      context_dictSize = 3,\n      context_numBits = 2,\n      context_data_val = 0,\n      context_data_position = 0,\n      ii\n\n    for (ii = 0; ii < uncompressed.length; ii += 1) {\n      context_c = uncompressed.charAt(ii)\n      if (!Object.prototype.hasOwnProperty.call(context_dictionary, context_c)) {\n        context_dictionary[context_c] = context_dictSize++\n        context_dictionaryToCreate[context_c] = true\n      }\n\n      context_wc = context_w + context_c\n      if (Object.prototype.hasOwnProperty.call(context_dictionary, context_wc)) {\n        context_w = context_wc\n      } else {\n        if (Object.prototype.hasOwnProperty.call(context_dictionaryToCreate, context_w)) {\n          if (context_w.charCodeAt(0) < 256) {\n            for (i = 0; i < context_numBits; i++) {\n              context_data_val = context_data_val << 1\n              if (context_data_position == bitsPerChar - 1) {\n                context_data_position = 0\n                context_data.push(getCharFromInt(context_data_val))\n                context_data_val = 0\n              } else {\n                context_data_position++\n              }\n            }\n            value = context_w.charCodeAt(0)\n            for (i = 0; i < 8; i++) {\n              context_data_val = (context_data_val << 1) | (value & 1)\n              if (context_data_position == bitsPerChar - 1) {\n                context_data_position = 0\n                context_data.push(getCharFromInt(context_data_val))\n                context_data_val = 0\n              } else {\n                context_data_position++\n              }\n              value = value >> 1\n            }\n          } else {\n            value = 1\n            for (i = 0; i < context_numBits; i++) {\n              context_data_val = (context_data_val << 1) | value\n              if (context_data_position == bitsPerChar - 1) {\n                context_data_position = 0\n                context_data.push(getCharFromInt(context_data_val))\n                context_data_val = 0\n              } else {\n                context_data_position++\n              }\n              value = 0\n            }\n            value = context_w.charCodeAt(0)\n            for (i = 0; i < 16; i++) {\n              context_data_val = (context_data_val << 1) | (value & 1)\n              if (context_data_position == bitsPerChar - 1) {\n                context_data_position = 0\n                context_data.push(getCharFromInt(context_data_val))\n                context_data_val = 0\n              } else {\n                context_data_position++\n              }\n              value = value >> 1\n            }\n          }\n          context_enlargeIn--\n          if (context_enlargeIn == 0) {\n            context_enlargeIn = Math.pow(2, context_numBits)\n            context_numBits++\n          }\n          delete context_dictionaryToCreate[context_w]\n        } else {\n          value = context_dictionary[context_w]\n          for (i = 0; i < context_numBits; i++) {\n            context_data_val = (context_data_val << 1) | (value & 1)\n            if (context_data_position == bitsPerChar - 1) {\n              context_data_position = 0\n              context_data.push(getCharFromInt(context_data_val))\n              context_data_val = 0\n            } else {\n              context_data_position++\n            }\n            value = value >> 1\n          }\n        }\n        context_enlargeIn--\n        if (context_enlargeIn == 0) {\n          context_enlargeIn = Math.pow(2, context_numBits)\n          context_numBits++\n        }\n        // Add wc to the dictionary.\n        context_dictionary[context_wc] = context_dictSize++\n        context_w = String(context_c)\n      }\n    }\n\n    // Output the code for w.\n    if (context_w !== '') {\n      if (Object.prototype.hasOwnProperty.call(context_dictionaryToCreate, context_w)) {\n        if (context_w.charCodeAt(0) < 256) {\n          for (i = 0; i < context_numBits; i++) {\n            context_data_val = context_data_val << 1\n            if (context_data_position == bitsPerChar - 1) {\n              context_data_position = 0\n              context_data.push(getCharFromInt(context_data_val))\n              context_data_val = 0\n            } else {\n              context_data_position++\n            }\n          }\n          value = context_w.charCodeAt(0)\n          for (i = 0; i < 8; i++) {\n            context_data_val = (context_data_val << 1) | (value & 1)\n            if (context_data_position == bitsPerChar - 1) {\n              context_data_position = 0\n              context_data.push(getCharFromInt(context_data_val))\n              context_data_val = 0\n            } else {\n              context_data_position++\n            }\n            value = value >> 1\n          }\n        } else {\n          value = 1\n          for (i = 0; i < context_numBits; i++) {\n            context_data_val = (context_data_val << 1) | value\n            if (context_data_position == bitsPerChar - 1) {\n              context_data_position = 0\n              context_data.push(getCharFromInt(context_data_val))\n              context_data_val = 0\n            } else {\n              context_data_position++\n            }\n            value = 0\n          }\n          value = context_w.charCodeAt(0)\n          for (i = 0; i < 16; i++) {\n            context_data_val = (context_data_val << 1) | (value & 1)\n            if (context_data_position == bitsPerChar - 1) {\n              context_data_position = 0\n              context_data.push(getCharFromInt(context_data_val))\n              context_data_val = 0\n            } else {\n              context_data_position++\n            }\n            value = value >> 1\n          }\n        }\n        context_enlargeIn--\n        if (context_enlargeIn == 0) {\n          context_enlargeIn = Math.pow(2, context_numBits)\n          context_numBits++\n        }\n        delete context_dictionaryToCreate[context_w]\n      } else {\n        value = context_dictionary[context_w]\n        for (i = 0; i < context_numBits; i++) {\n          context_data_val = (context_data_val << 1) | (value & 1)\n          if (context_data_position == bitsPerChar - 1) {\n            context_data_position = 0\n            context_data.push(getCharFromInt(context_data_val))\n            context_data_val = 0\n          } else {\n            context_data_position++\n          }\n          value = value >> 1\n        }\n      }\n      context_enlargeIn--\n      if (context_enlargeIn == 0) {\n        context_enlargeIn = Math.pow(2, context_numBits)\n        context_numBits++\n      }\n    }\n\n    // Mark the end of the stream\n    value = 2\n    for (i = 0; i < context_numBits; i++) {\n      context_data_val = (context_data_val << 1) | (value & 1)\n      if (context_data_position == bitsPerChar - 1) {\n        context_data_position = 0\n        context_data.push(getCharFromInt(context_data_val))\n        context_data_val = 0\n      } else {\n        context_data_position++\n      }\n      value = value >> 1\n    }\n\n    // Flush the last char\n    while (true) {\n      context_data_val = context_data_val << 1\n      if (context_data_position == bitsPerChar - 1) {\n        context_data.push(getCharFromInt(context_data_val))\n        break\n      } else {\n        context_data_position++\n      }\n    }\n    return context_data.join('')\n  },\n\n  decompress: function (compressed: any): any {\n    if (compressed == null) {\n      return ''\n    }\n    if (compressed == '') {\n      return null\n    }\n    return LZString._decompress(compressed.length, 32768, function (index: any) {\n      return compressed.charCodeAt(index)\n    })\n  },\n\n  _decompress: function (length: any, resetValue: any, getNextValue: any): any {\n    const dictionary = [],\n      result = [],\n      data = { val: getNextValue(0), position: resetValue, index: 1 }\n\n    let next,\n      enlargeIn = 4,\n      dictSize = 4,\n      numBits = 3,\n      entry: any = '',\n      i,\n      w,\n      bits,\n      resb,\n      maxpower,\n      power,\n      c\n\n    for (i = 0; i < 3; i += 1) {\n      dictionary[i] = i\n    }\n\n    bits = 0\n    maxpower = Math.pow(2, 2)\n    power = 1\n    while (power != maxpower) {\n      resb = data.val & data.position\n      data.position >>= 1\n      if (data.position == 0) {\n        data.position = resetValue\n        data.val = getNextValue(data.index++)\n      }\n      bits |= (resb > 0 ? 1 : 0) * power\n      power <<= 1\n    }\n\n    // eslint-disable-next-line @typescript-eslint/no-unused-vars\n    switch ((next = bits)) {\n      case 0:\n        bits = 0\n        maxpower = Math.pow(2, 8)\n        power = 1\n        while (power != maxpower) {\n          resb = data.val & data.position\n          data.position >>= 1\n          if (data.position == 0) {\n            data.position = resetValue\n            data.val = getNextValue(data.index++)\n          }\n          bits |= (resb > 0 ? 1 : 0) * power\n          power <<= 1\n        }\n        c = f(bits)\n        break\n      case 1:\n        bits = 0\n        maxpower = Math.pow(2, 16)\n        power = 1\n        while (power != maxpower) {\n          resb = data.val & data.position\n          data.position >>= 1\n          if (data.position == 0) {\n            data.position = resetValue\n            data.val = getNextValue(data.index++)\n          }\n          bits |= (resb > 0 ? 1 : 0) * power\n          power <<= 1\n        }\n        c = f(bits)\n        break\n      case 2:\n        return ''\n    }\n    dictionary[3] = c\n    w = c\n    result.push(c)\n    while (true) {\n      if (data.index > length) {\n        return ''\n      }\n\n      bits = 0\n      maxpower = Math.pow(2, numBits)\n      power = 1\n      while (power != maxpower) {\n        resb = data.val & data.position\n        data.position >>= 1\n        if (data.position == 0) {\n          data.position = resetValue\n          data.val = getNextValue(data.index++)\n        }\n        bits |= (resb > 0 ? 1 : 0) * power\n        power <<= 1\n      }\n\n      switch ((c = bits)) {\n        case 0:\n          bits = 0\n          maxpower = Math.pow(2, 8)\n          power = 1\n          while (power != maxpower) {\n            resb = data.val & data.position\n            data.position >>= 1\n            if (data.position == 0) {\n              data.position = resetValue\n              data.val = getNextValue(data.index++)\n            }\n            bits |= (resb > 0 ? 1 : 0) * power\n            power <<= 1\n          }\n\n          dictionary[dictSize++] = f(bits)\n          c = dictSize - 1\n          enlargeIn--\n          break\n        case 1:\n          bits = 0\n          maxpower = Math.pow(2, 16)\n          power = 1\n          while (power != maxpower) {\n            resb = data.val & data.position\n            data.position >>= 1\n            if (data.position == 0) {\n              data.position = resetValue\n              data.val = getNextValue(data.index++)\n            }\n            bits |= (resb > 0 ? 1 : 0) * power\n            power <<= 1\n          }\n          dictionary[dictSize++] = f(bits)\n          c = dictSize - 1\n          enlargeIn--\n          break\n        case 2:\n          return result.join('')\n      }\n\n      if (enlargeIn == 0) {\n        enlargeIn = Math.pow(2, numBits)\n        numBits++\n      }\n\n      if (dictionary[c]) {\n        entry = dictionary[c]\n      } else {\n        if (c === dictSize) {\n          entry = w + w.charAt(0)\n        } else {\n          return null\n        }\n      }\n      result.push(entry)\n\n      // Add w+entry[0] to the dictionary.\n      dictionary[dictSize++] = w + entry.charAt(0)\n      enlargeIn--\n\n      w = entry\n\n      if (enlargeIn == 0) {\n        enlargeIn = Math.pow(2, numBits)\n        numBits++\n      }\n    }\n  },\n}\n", "export class SimpleEventEmitter {\n  events: { [key: string]: ((...args: any[]) => void)[] } = {}\n\n  constructor() {\n    this.events = {}\n  }\n\n  on(event: string, listener: (...args: any[]) => void): () => void {\n    if (!this.events[event]) {\n      this.events[event] = []\n    }\n    this.events[event].push(listener)\n\n    return () => {\n      this.events[event] = this.events[event].filter((x) => x !== listener)\n    }\n  }\n\n  emit(event: string, payload: any): void {\n    for (const listener of this.events[event] || []) {\n      listener(payload)\n    }\n    for (const listener of this.events['*'] || []) {\n      listener(event, payload)\n    }\n  }\n}\n", "import {\n  PostHogFetchOptions,\n  PostHogFetchResponse,\n  PostHogQueueItem,\n  PostHogAutocaptureElement,\n  PostHogDecideResponse,\n  PostHogCoreOptions,\n  PostHogEventProperties,\n  PostHogPersistedProperty,\n  PostHogCaptureOptions,\n  JsonType,\n  PostHogRemoteConfig,\n  FeatureFlagValue,\n  PostHogV4DecideResponse,\n  PostHogV3DecideResponse,\n  PostHogFeatureFlagDetails,\n  PostHogFlagsStorageFormat,\n  FeatureFlagDetail,\n  Survey,\n  SurveyResponse,\n  PostHogGroupProperties,\n} from './types'\nimport {\n  createDecideResponseFromFlagsAndPayloads,\n  getFeatureFlagValue,\n  getFlagValuesFromFlags,\n  getPayloadsFromFlags,\n  normalizeDecideResponse,\n  updateFlagValue,\n} from './featureFlagUtils'\nimport {\n  allSettled,\n  assert,\n  currentISOTime,\n  currentTimestamp,\n  isError,\n  isTokenInRollout,\n  NEW_FLAGS_EXCLUDED_HASHES,\n  NEW_FLAGS_ROLLOUT_PERCENTAGE,\n  removeTrailingSlash,\n  retriable,\n  RetriableOptions,\n  safeSetTimeout,\n  STRING_FORMAT,\n} from './utils'\nimport { LZString } from './lz-string'\nimport { SimpleEventEmitter } from './eventemitter'\nimport { uuidv7 } from './vendor/uuidv7'\n\nexport { safeSetTimeout } from './utils'\nexport { getFetch } from './utils'\nexport { getFeatureFlagValue } from './featureFlagUtils'\nexport * as utils from './utils'\n\nclass PostHogFetchHttpError extends Error {\n  name = 'PostHogFetchHttpError'\n\n  constructor(public response: PostHogFetchResponse, public reqByteLength: number) {\n    super('HTTP error while fetching PostHog: status=' + response.status + ', reqByteLength=' + reqByteLength)\n  }\n\n  get status(): number {\n    return this.response.status\n  }\n\n  get text(): Promise<string> {\n    return this.response.text()\n  }\n\n  get json(): Promise<any> {\n    return this.response.json()\n  }\n}\n\nclass PostHogFetchNetworkError extends Error {\n  name = 'PostHogFetchNetworkError'\n\n  constructor(public error: unknown) {\n    // TRICKY: \"cause\" is a newer property but is just ignored otherwise. Cast to any to ignore the type issue.\n    // eslint-disable-next-line @typescript-eslint/prefer-ts-expect-error\n    // @ts-ignore\n    super('Network error while fetching PostHog', error instanceof Error ? { cause: error } : {})\n  }\n}\n\nexport const maybeAdd = (key: string, value: JsonType | undefined): Record<string, JsonType> =>\n  value !== undefined ? { [key]: value } : {}\n\nexport async function logFlushError(err: any): Promise<void> {\n  if (err instanceof PostHogFetchHttpError) {\n    let text = ''\n    try {\n      text = await err.text\n    } catch {}\n\n    console.error(`Error while flushing PostHog: message=${err.message}, response body=${text}`, err)\n  } else {\n    console.error('Error while flushing PostHog', err)\n  }\n  return Promise.resolve()\n}\n\nfunction isPostHogFetchError(err: unknown): err is PostHogFetchHttpError | PostHogFetchNetworkError {\n  return typeof err === 'object' && (err instanceof PostHogFetchHttpError || err instanceof PostHogFetchNetworkError)\n}\n\nfunction isPostHogFetchContentTooLargeError(err: unknown): err is PostHogFetchHttpError & { status: 413 } {\n  return typeof err === 'object' && err instanceof PostHogFetchHttpError && err.status === 413\n}\n\nenum QuotaLimitedFeature {\n  FeatureFlags = 'feature_flags',\n  Recordings = 'recordings',\n}\n\nexport abstract class PostHogCoreStateless {\n  // options\n  readonly apiKey: string\n  readonly host: string\n  readonly flushAt: number\n  readonly preloadFeatureFlags: boolean\n  readonly disableSurveys: boolean\n  private maxBatchSize: number\n  private maxQueueSize: number\n  private flushInterval: number\n  private flushPromise: Promise<any> | null = null\n  private shutdownPromise: Promise<void> | null = null\n  private requestTimeout: number\n  private featureFlagsRequestTimeoutMs: number\n  private remoteConfigRequestTimeoutMs: number\n  private captureMode: 'form' | 'json'\n  private removeDebugCallback?: () => void\n  private disableGeoip: boolean\n  private historicalMigration: boolean\n  protected disabled\n\n  private defaultOptIn: boolean\n  private pendingPromises: Record<string, Promise<any>> = {}\n\n  // internal\n  protected _events = new SimpleEventEmitter()\n  protected _flushTimer?: any\n  protected _retryOptions: RetriableOptions\n  protected _initPromise: Promise<void>\n  protected _isInitialized: boolean = false\n  protected _remoteConfigResponsePromise?: Promise<PostHogRemoteConfig | undefined>\n\n  // Abstract methods to be overridden by implementations\n  abstract fetch(url: string, options: PostHogFetchOptions): Promise<PostHogFetchResponse>\n  abstract getLibraryId(): string\n  abstract getLibraryVersion(): string\n  abstract getCustomUserAgent(): string | void\n\n  // This is our abstracted storage. Each implementation should handle its own\n  abstract getPersistedProperty<T>(key: PostHogPersistedProperty): T | undefined\n  abstract setPersistedProperty<T>(key: PostHogPersistedProperty, value: T | null): void\n\n  constructor(apiKey: string, options?: PostHogCoreOptions) {\n    assert(apiKey, \"You must pass your PostHog project's api key.\")\n\n    this.apiKey = apiKey\n    this.host = removeTrailingSlash(options?.host || 'https://us.i.posthog.com')\n    this.flushAt = options?.flushAt ? Math.max(options?.flushAt, 1) : 20\n    this.maxBatchSize = Math.max(this.flushAt, options?.maxBatchSize ?? 100)\n    this.maxQueueSize = Math.max(this.flushAt, options?.maxQueueSize ?? 1000)\n    this.flushInterval = options?.flushInterval ?? 10000\n    this.captureMode = options?.captureMode || 'json'\n    this.preloadFeatureFlags = options?.preloadFeatureFlags ?? true\n    // If enable is explicitly set to false we override the optout\n    this.defaultOptIn = options?.defaultOptIn ?? true\n    this.disableSurveys = options?.disableSurveys ?? false\n\n    this._retryOptions = {\n      retryCount: options?.fetchRetryCount ?? 3,\n      retryDelay: options?.fetchRetryDelay ?? 3000, // 3 seconds\n      retryCheck: isPostHogFetchError,\n    }\n    this.requestTimeout = options?.requestTimeout ?? 10000 // 10 seconds\n    this.featureFlagsRequestTimeoutMs = options?.featureFlagsRequestTimeoutMs ?? 3000 // 3 seconds\n    this.remoteConfigRequestTimeoutMs = options?.remoteConfigRequestTimeoutMs ?? 3000 // 3 seconds\n    this.disableGeoip = options?.disableGeoip ?? true\n    this.disabled = options?.disabled ?? false\n    this.historicalMigration = options?.historicalMigration ?? false\n    // Init promise allows the derived class to block calls until it is ready\n    this._initPromise = Promise.resolve()\n    this._isInitialized = true\n  }\n\n  protected logMsgIfDebug(fn: () => void): void {\n    if (this.isDebug) {\n      fn()\n    }\n  }\n\n  protected wrap(fn: () => void): void {\n    if (this.disabled) {\n      this.logMsgIfDebug(() => console.warn('[PostHog] The client is disabled'))\n      return\n    }\n\n    if (this._isInitialized) {\n      // NOTE: We could also check for the \"opt in\" status here...\n      return fn()\n    }\n\n    this._initPromise.then(() => fn())\n  }\n\n  protected getCommonEventProperties(): PostHogEventProperties {\n    return {\n      $lib: this.getLibraryId(),\n      $lib_version: this.getLibraryVersion(),\n    }\n  }\n\n  public get optedOut(): boolean {\n    return this.getPersistedProperty(PostHogPersistedProperty.OptedOut) ?? !this.defaultOptIn\n  }\n\n  async optIn(): Promise<void> {\n    this.wrap(() => {\n      this.setPersistedProperty(PostHogPersistedProperty.OptedOut, false)\n    })\n  }\n\n  async optOut(): Promise<void> {\n    this.wrap(() => {\n      this.setPersistedProperty(PostHogPersistedProperty.OptedOut, true)\n    })\n  }\n\n  on(event: string, cb: (...args: any[]) => void): () => void {\n    return this._events.on(event, cb)\n  }\n\n  debug(enabled: boolean = true): void {\n    this.removeDebugCallback?.()\n\n    if (enabled) {\n      const removeDebugCallback = this.on('*', (event, payload) => console.log('PostHog Debug', event, payload))\n      this.removeDebugCallback = () => {\n        removeDebugCallback()\n        this.removeDebugCallback = undefined\n      }\n    }\n  }\n\n  get isDebug(): boolean {\n    return !!this.removeDebugCallback\n  }\n\n  get isDisabled(): boolean {\n    return this.disabled\n  }\n\n  private buildPayload(payload: {\n    distinct_id: string\n    event: string\n    properties?: PostHogEventProperties\n  }): PostHogEventProperties {\n    return {\n      distinct_id: payload.distinct_id,\n      event: payload.event,\n      properties: {\n        ...(payload.properties || {}),\n        ...this.getCommonEventProperties(), // Common PH props\n      },\n    }\n  }\n\n  protected addPendingPromise<T>(promise: Promise<T>): Promise<T> {\n    const promiseUUID = uuidv7()\n    this.pendingPromises[promiseUUID] = promise\n    promise\n      .catch(() => {})\n      .finally(() => {\n        delete this.pendingPromises[promiseUUID]\n      })\n\n    return promise\n  }\n\n  /***\n   *** TRACKING\n   ***/\n  protected identifyStateless(\n    distinctId: string,\n    properties?: PostHogEventProperties,\n    options?: PostHogCaptureOptions\n  ): void {\n    this.wrap(() => {\n      // The properties passed to identifyStateless are event properties.\n      // To add person properties, pass in all person properties to the `$set` and `$set_once` keys.\n\n      const payload = {\n        ...this.buildPayload({\n          distinct_id: distinctId,\n          event: '$identify',\n          properties,\n        }),\n      }\n\n      this.enqueue('identify', payload, options)\n    })\n  }\n\n  protected async identifyStatelessImmediate(\n    distinctId: string,\n    properties?: PostHogEventProperties,\n    options?: PostHogCaptureOptions\n  ): Promise<void> {\n    const payload = {\n      ...this.buildPayload({\n        distinct_id: distinctId,\n        event: '$identify',\n        properties,\n      }),\n    }\n\n    await this.sendImmediate('identify', payload, options)\n  }\n\n  protected captureStateless(\n    distinctId: string,\n    event: string,\n    properties?: PostHogEventProperties,\n    options?: PostHogCaptureOptions\n  ): void {\n    this.wrap(() => {\n      const payload = this.buildPayload({ distinct_id: distinctId, event, properties })\n      this.enqueue('capture', payload, options)\n    })\n  }\n\n  protected async captureStatelessImmediate(\n    distinctId: string,\n    event: string,\n    properties?: PostHogEventProperties,\n    options?: PostHogCaptureOptions\n  ): Promise<void> {\n    const payload = this.buildPayload({ distinct_id: distinctId, event, properties })\n    await this.sendImmediate('capture', payload, options)\n  }\n\n  protected aliasStateless(\n    alias: string,\n    distinctId: string,\n    properties?: PostHogEventProperties,\n    options?: PostHogCaptureOptions\n  ): void {\n    this.wrap(() => {\n      const payload = this.buildPayload({\n        event: '$create_alias',\n        distinct_id: distinctId,\n        properties: {\n          ...(properties || {}),\n          distinct_id: distinctId,\n          alias,\n        },\n      })\n\n      this.enqueue('alias', payload, options)\n    })\n  }\n\n  protected async aliasStatelessImmediate(\n    alias: string,\n    distinctId: string,\n    properties?: PostHogEventProperties,\n    options?: PostHogCaptureOptions\n  ): Promise<void> {\n    const payload = this.buildPayload({\n      event: '$create_alias',\n      distinct_id: distinctId,\n      properties: {\n        ...(properties || {}),\n        distinct_id: distinctId,\n        alias,\n      },\n    })\n\n    await this.sendImmediate('alias', payload, options)\n  }\n\n  /***\n   *** GROUPS\n   ***/\n  protected groupIdentifyStateless(\n    groupType: string,\n    groupKey: string | number,\n    groupProperties?: PostHogEventProperties,\n    options?: PostHogCaptureOptions,\n    distinctId?: string,\n    eventProperties?: PostHogEventProperties\n  ): void {\n    this.wrap(() => {\n      const payload = this.buildPayload({\n        distinct_id: distinctId || `$${groupType}_${groupKey}`,\n        event: '$groupidentify',\n        properties: {\n          $group_type: groupType,\n          $group_key: groupKey,\n          $group_set: groupProperties || {},\n          ...(eventProperties || {}),\n        },\n      })\n\n      this.enqueue('capture', payload, options)\n    })\n  }\n\n  protected async getRemoteConfig(): Promise<PostHogRemoteConfig | undefined> {\n    await this._initPromise\n\n    let host = this.host\n\n    if (host === 'https://us.i.posthog.com') {\n      host = 'https://us-assets.i.posthog.com'\n    } else if (host === 'https://eu.i.posthog.com') {\n      host = 'https://eu-assets.i.posthog.com'\n    }\n\n    const url = `${host}/array/${this.apiKey}/config`\n    const fetchOptions: PostHogFetchOptions = {\n      method: 'GET',\n      headers: { ...this.getCustomHeaders(), 'Content-Type': 'application/json' },\n    }\n    // Don't retry remote config API calls\n    return this.fetchWithRetry(url, fetchOptions, { retryCount: 0 }, this.remoteConfigRequestTimeoutMs)\n      .then((response) => response.json() as Promise<PostHogRemoteConfig>)\n      .catch((error) => {\n        this.logMsgIfDebug(() => console.error('Remote config could not be loaded', error))\n        this._events.emit('error', error)\n        return undefined\n      })\n  }\n\n  /***\n   *** FEATURE FLAGS\n   ***/\n\n  protected async getDecide(\n    distinctId: string,\n    groups: Record<string, string | number> = {},\n    personProperties: Record<string, string> = {},\n    groupProperties: Record<string, Record<string, string>> = {},\n    extraPayload: Record<string, any> = {}\n  ): Promise<PostHogDecideResponse | undefined> {\n    await this._initPromise\n\n    // Check if the API token is in the new flags rollout\n    // This is a temporary measure to ensure that we can still use the old flags API\n    // while we migrate to the new flags API\n    const useFlags = isTokenInRollout(this.apiKey, NEW_FLAGS_ROLLOUT_PERCENTAGE, NEW_FLAGS_EXCLUDED_HASHES)\n\n    const url = useFlags ? `${this.host}/flags/?v=2` : `${this.host}/decide/?v=4`\n    const fetchOptions: PostHogFetchOptions = {\n      method: 'POST',\n      headers: { ...this.getCustomHeaders(), 'Content-Type': 'application/json' },\n      body: JSON.stringify({\n        token: this.apiKey,\n        distinct_id: distinctId,\n        groups,\n        person_properties: personProperties,\n        group_properties: groupProperties,\n        ...extraPayload,\n      }),\n    }\n\n    this.logMsgIfDebug(() => console.log('PostHog Debug', 'Decide URL', url))\n\n    // Don't retry /decide API calls\n    return this.fetchWithRetry(url, fetchOptions, { retryCount: 0 }, this.featureFlagsRequestTimeoutMs)\n      .then((response) => response.json() as Promise<PostHogV3DecideResponse | PostHogV4DecideResponse>)\n      .then((response) => normalizeDecideResponse(response))\n      .catch((error) => {\n        this._events.emit('error', error)\n        return undefined\n      }) as Promise<PostHogDecideResponse | undefined>\n  }\n\n  protected async getFeatureFlagStateless(\n    key: string,\n    distinctId: string,\n    groups: Record<string, string> = {},\n    personProperties: Record<string, string> = {},\n    groupProperties: Record<string, Record<string, string>> = {},\n    disableGeoip?: boolean\n  ): Promise<{\n    response: FeatureFlagValue | undefined\n    requestId: string | undefined\n  }> {\n    await this._initPromise\n\n    const flagDetailResponse = await this.getFeatureFlagDetailStateless(\n      key,\n      distinctId,\n      groups,\n      personProperties,\n      groupProperties,\n      disableGeoip\n    )\n\n    if (flagDetailResponse === undefined) {\n      // If we haven't loaded flags yet, or errored out, we respond with undefined\n      return {\n        response: undefined,\n        requestId: undefined,\n      }\n    }\n\n    let response = getFeatureFlagValue(flagDetailResponse.response)\n\n    if (response === undefined) {\n      // For cases where the flag is unknown, return false\n      response = false\n    }\n\n    // If we have flags we either return the value (true or string) or false\n    return {\n      response,\n      requestId: flagDetailResponse.requestId,\n    }\n  }\n\n  protected async getFeatureFlagDetailStateless(\n    key: string,\n    distinctId: string,\n    groups: Record<string, string> = {},\n    personProperties: Record<string, string> = {},\n    groupProperties: Record<string, Record<string, string>> = {},\n    disableGeoip?: boolean\n  ): Promise<\n    | {\n        response: FeatureFlagDetail | undefined\n        requestId: string | undefined\n      }\n    | undefined\n  > {\n    await this._initPromise\n\n    const decideResponse = await this.getFeatureFlagDetailsStateless(\n      distinctId,\n      groups,\n      personProperties,\n      groupProperties,\n      disableGeoip,\n      [key]\n    )\n\n    if (decideResponse === undefined) {\n      return undefined\n    }\n\n    const featureFlags = decideResponse.flags\n\n    const flagDetail = featureFlags[key]\n\n    return {\n      response: flagDetail,\n      requestId: decideResponse.requestId,\n    }\n  }\n\n  protected async getFeatureFlagPayloadStateless(\n    key: string,\n    distinctId: string,\n    groups: Record<string, string> = {},\n    personProperties: Record<string, string> = {},\n    groupProperties: Record<string, Record<string, string>> = {},\n    disableGeoip?: boolean\n  ): Promise<JsonType | undefined> {\n    await this._initPromise\n\n    const payloads = await this.getFeatureFlagPayloadsStateless(\n      distinctId,\n      groups,\n      personProperties,\n      groupProperties,\n      disableGeoip,\n      [key]\n    )\n\n    if (!payloads) {\n      return undefined\n    }\n\n    const response = payloads[key]\n\n    // Undefined means a loading or missing data issue. Null means evaluation happened and there was no match\n    if (response === undefined) {\n      return null\n    }\n\n    return response\n  }\n\n  protected async getFeatureFlagPayloadsStateless(\n    distinctId: string,\n    groups: Record<string, string> = {},\n    personProperties: Record<string, string> = {},\n    groupProperties: Record<string, Record<string, string>> = {},\n    disableGeoip?: boolean,\n    flagKeysToEvaluate?: string[]\n  ): Promise<PostHogDecideResponse['featureFlagPayloads'] | undefined> {\n    await this._initPromise\n\n    const payloads = (\n      await this.getFeatureFlagsAndPayloadsStateless(\n        distinctId,\n        groups,\n        personProperties,\n        groupProperties,\n        disableGeoip,\n        flagKeysToEvaluate\n      )\n    ).payloads\n\n    return payloads\n  }\n\n  protected async getFeatureFlagsStateless(\n    distinctId: string,\n    groups: Record<string, string | number> = {},\n    personProperties: Record<string, string> = {},\n    groupProperties: Record<string, Record<string, string>> = {},\n    disableGeoip?: boolean,\n    flagKeysToEvaluate?: string[]\n  ): Promise<{\n    flags: PostHogDecideResponse['featureFlags'] | undefined\n    payloads: PostHogDecideResponse['featureFlagPayloads'] | undefined\n    requestId: PostHogDecideResponse['requestId'] | undefined\n  }> {\n    await this._initPromise\n\n    return await this.getFeatureFlagsAndPayloadsStateless(\n      distinctId,\n      groups,\n      personProperties,\n      groupProperties,\n      disableGeoip,\n      flagKeysToEvaluate\n    )\n  }\n\n  protected async getFeatureFlagsAndPayloadsStateless(\n    distinctId: string,\n    groups: Record<string, string | number> = {},\n    personProperties: Record<string, string> = {},\n    groupProperties: Record<string, Record<string, string>> = {},\n    disableGeoip?: boolean,\n    flagKeysToEvaluate?: string[]\n  ): Promise<{\n    flags: PostHogDecideResponse['featureFlags'] | undefined\n    payloads: PostHogDecideResponse['featureFlagPayloads'] | undefined\n    requestId: PostHogDecideResponse['requestId'] | undefined\n  }> {\n    await this._initPromise\n\n    const featureFlagDetails = await this.getFeatureFlagDetailsStateless(\n      distinctId,\n      groups,\n      personProperties,\n      groupProperties,\n      disableGeoip,\n      flagKeysToEvaluate\n    )\n\n    if (!featureFlagDetails) {\n      return {\n        flags: undefined,\n        payloads: undefined,\n        requestId: undefined,\n      }\n    }\n\n    return {\n      flags: featureFlagDetails.featureFlags,\n      payloads: featureFlagDetails.featureFlagPayloads,\n      requestId: featureFlagDetails.requestId,\n    }\n  }\n\n  protected async getFeatureFlagDetailsStateless(\n    distinctId: string,\n    groups: Record<string, string | number> = {},\n    personProperties: Record<string, string> = {},\n    groupProperties: Record<string, Record<string, string>> = {},\n    disableGeoip?: boolean,\n    flagKeysToEvaluate?: string[]\n  ): Promise<PostHogFeatureFlagDetails | undefined> {\n    await this._initPromise\n\n    const extraPayload: Record<string, any> = {}\n    if (disableGeoip ?? this.disableGeoip) {\n      extraPayload['geoip_disable'] = true\n    }\n    if (flagKeysToEvaluate) {\n      extraPayload['flag_keys_to_evaluate'] = flagKeysToEvaluate\n    }\n    const decideResponse = await this.getDecide(distinctId, groups, personProperties, groupProperties, extraPayload)\n\n    if (decideResponse === undefined) {\n      // We probably errored out, so return undefined\n      return undefined\n    }\n\n    // if there's an error on the decideResponse, log a console error, but don't throw an error\n    if (decideResponse.errorsWhileComputingFlags) {\n      console.error(\n        '[FEATURE FLAGS] Error while computing feature flags, some flags may be missing or incorrect. Learn more at https://posthog.com/docs/feature-flags/best-practices'\n      )\n    }\n\n    // Add check for quota limitation on feature flags\n    if (decideResponse.quotaLimited?.includes(QuotaLimitedFeature.FeatureFlags)) {\n      console.warn(\n        '[FEATURE FLAGS] Feature flags quota limit exceeded - feature flags unavailable. Learn more about billing limits at https://posthog.com/docs/billing/limits-alerts'\n      )\n      return {\n        flags: {},\n        featureFlags: {},\n        featureFlagPayloads: {},\n        requestId: decideResponse?.requestId,\n      }\n    }\n\n    return decideResponse\n  }\n\n  /***\n   *** SURVEYS\n   ***/\n\n  public async getSurveysStateless(): Promise<SurveyResponse['surveys']> {\n    await this._initPromise\n\n    if (this.disableSurveys === true) {\n      this.logMsgIfDebug(() => console.log('PostHog Debug', 'Loading surveys is disabled.'))\n      return []\n    }\n\n    const url = `${this.host}/api/surveys/?token=${this.apiKey}`\n    const fetchOptions: PostHogFetchOptions = {\n      method: 'GET',\n      headers: { ...this.getCustomHeaders(), 'Content-Type': 'application/json' },\n    }\n\n    const response = await this.fetchWithRetry(url, fetchOptions)\n      .then((response) => {\n        if (response.status !== 200 || !response.json) {\n          const msg = `Surveys API could not be loaded: ${response.status}`\n          const error = new Error(msg)\n          this.logMsgIfDebug(() => console.error(error))\n\n          this._events.emit('error', new Error(msg))\n          return undefined\n        }\n\n        return response.json() as Promise<SurveyResponse>\n      })\n      .catch((error) => {\n        this.logMsgIfDebug(() => console.error('Surveys API could not be loaded', error))\n\n        this._events.emit('error', error)\n        return undefined\n      })\n\n    const newSurveys = response?.surveys\n\n    if (newSurveys) {\n      this.logMsgIfDebug(() => console.log('PostHog Debug', 'Surveys fetched from API: ', JSON.stringify(newSurveys)))\n    }\n\n    return newSurveys ?? []\n  }\n\n  /***\n   *** SUPER PROPERTIES\n   ***/\n  private _props: PostHogEventProperties | undefined\n\n  protected get props(): PostHogEventProperties {\n    if (!this._props) {\n      this._props = this.getPersistedProperty<PostHogEventProperties>(PostHogPersistedProperty.Props)\n    }\n    return this._props || {}\n  }\n\n  protected set props(val: PostHogEventProperties | undefined) {\n    this._props = val\n  }\n\n  async register(properties: PostHogEventProperties): Promise<void> {\n    this.wrap(() => {\n      this.props = {\n        ...this.props,\n        ...properties,\n      }\n      this.setPersistedProperty<PostHogEventProperties>(PostHogPersistedProperty.Props, this.props)\n    })\n  }\n\n  async unregister(property: string): Promise<void> {\n    this.wrap(() => {\n      delete this.props[property]\n      this.setPersistedProperty<PostHogEventProperties>(PostHogPersistedProperty.Props, this.props)\n    })\n  }\n\n  /***\n   *** QUEUEING AND FLUSHING\n   ***/\n  protected enqueue(type: string, _message: any, options?: PostHogCaptureOptions): void {\n    this.wrap(() => {\n      if (this.optedOut) {\n        this._events.emit(type, `Library is disabled. Not sending event. To re-enable, call posthog.optIn()`)\n        return\n      }\n\n      const message = this.prepareMessage(type, _message, options)\n\n      const queue = this.getPersistedProperty<PostHogQueueItem[]>(PostHogPersistedProperty.Queue) || []\n\n      if (queue.length >= this.maxQueueSize) {\n        queue.shift()\n        this.logMsgIfDebug(() => console.info('Queue is full, the oldest event is dropped.'))\n      }\n\n      queue.push({ message })\n      this.setPersistedProperty<PostHogQueueItem[]>(PostHogPersistedProperty.Queue, queue)\n\n      this._events.emit(type, message)\n\n      // Flush queued events if we meet the flushAt length\n      if (queue.length >= this.flushAt) {\n        this.flushBackground()\n      }\n\n      if (this.flushInterval && !this._flushTimer) {\n        this._flushTimer = safeSetTimeout(() => this.flushBackground(), this.flushInterval)\n      }\n    })\n  }\n\n  protected async sendImmediate(type: string, _message: any, options?: PostHogCaptureOptions): Promise<void> {\n    if (this.disabled) {\n      this.logMsgIfDebug(() => console.warn('[PostHog] The client is disabled'))\n      return\n    }\n\n    if (!this._isInitialized) {\n      await this._initPromise\n    }\n\n    if (this.optedOut) {\n      this._events.emit(type, `Library is disabled. Not sending event. To re-enable, call posthog.optIn()`)\n      return\n    }\n\n    const data: Record<string, any> = {\n      api_key: this.apiKey,\n      batch: [this.prepareMessage(type, _message, options)],\n      sent_at: currentISOTime(),\n    }\n\n    if (this.historicalMigration) {\n      data.historical_migration = true\n    }\n\n    const payload = JSON.stringify(data)\n\n    const url =\n      this.captureMode === 'form'\n        ? `${this.host}/e/?ip=1&_=${currentTimestamp()}&v=${this.getLibraryVersion()}`\n        : `${this.host}/batch/`\n\n    const fetchOptions: PostHogFetchOptions =\n      this.captureMode === 'form'\n        ? {\n            method: 'POST',\n            mode: 'no-cors',\n            credentials: 'omit',\n            headers: { ...this.getCustomHeaders(), 'Content-Type': 'application/x-www-form-urlencoded' },\n            body: `data=${encodeURIComponent(LZString.compressToBase64(payload))}&compression=lz64`,\n          }\n        : {\n            method: 'POST',\n            headers: { ...this.getCustomHeaders(), 'Content-Type': 'application/json' },\n            body: payload,\n          }\n\n    try {\n      await this.fetchWithRetry(url, fetchOptions)\n    } catch (err) {\n      this._events.emit('error', err)\n    }\n  }\n\n  private prepareMessage(type: string, _message: any, options?: PostHogCaptureOptions): PostHogEventProperties {\n    const message = {\n      ..._message,\n      type: type,\n      library: this.getLibraryId(),\n      library_version: this.getLibraryVersion(),\n      timestamp: options?.timestamp ? options?.timestamp : currentISOTime(),\n      uuid: options?.uuid ? options.uuid : uuidv7(),\n    }\n\n    const addGeoipDisableProperty = options?.disableGeoip ?? this.disableGeoip\n    if (addGeoipDisableProperty) {\n      if (!message.properties) {\n        message.properties = {}\n      }\n      message['properties']['$geoip_disable'] = true\n    }\n\n    if (message.distinctId) {\n      message.distinct_id = message.distinctId\n      delete message.distinctId\n    }\n\n    return message\n  }\n\n  private clearFlushTimer(): void {\n    if (this._flushTimer) {\n      clearTimeout(this._flushTimer)\n      this._flushTimer = undefined\n    }\n  }\n\n  /**\n   * Helper for flushing the queue in the background\n   * Avoids unnecessary promise errors\n   */\n  private flushBackground(): void {\n    void this.flush().catch(async (err) => {\n      await logFlushError(err)\n    })\n  }\n\n  /**\n   * Flushes the queue\n   *\n   * This function will return a promise that will resolve when the flush is complete,\n   * or reject if there was an error (for example if the server or network is down).\n   *\n   * If there is already a flush in progress, this function will wait for that flush to complete.\n   *\n   * It's recommended to do error handling in the callback of the promise.\n   *\n   * @example\n   * posthog.flush().then(() => {\n   *   console.log('Flush complete')\n   * }).catch((err) => {\n   *   console.error('Flush failed', err)\n   * })\n   *\n   *\n   * @throws PostHogFetchHttpError\n   * @throws PostHogFetchNetworkError\n   * @throws Error\n   */\n  async flush(): Promise<void> {\n    // Wait for the current flush operation to finish (regardless of success or failure), then try to flush again.\n    // Use allSettled instead of finally to be defensive around flush throwing errors immediately rather than rejecting.\n    // Use a custom allSettled implementation to avoid issues with patching Promise on RN\n    const nextFlushPromise = allSettled([this.flushPromise]).then(() => {\n      return this._flush()\n    })\n\n    this.flushPromise = nextFlushPromise\n    void this.addPendingPromise(nextFlushPromise)\n\n    allSettled([nextFlushPromise]).then(() => {\n      // If there are no others waiting to flush, clear the promise.\n      // We don't strictly need to do this, but it could make debugging easier\n      if (this.flushPromise === nextFlushPromise) {\n        this.flushPromise = null\n      }\n    })\n\n    return nextFlushPromise\n  }\n\n  protected getCustomHeaders(): { [key: string]: string } {\n    // Don't set the user agent if we're not on a browser. The latest spec allows\n    // the User-Agent header (see https://fetch.spec.whatwg.org/#terminology-headers\n    // and https://developer.mozilla.org/en-US/docs/Web/API/XMLHttpRequest/setRequestHeader),\n    // but browsers such as Chrome and Safari have not caught up.\n    const customUserAgent = this.getCustomUserAgent()\n    const headers: { [key: string]: string } = {}\n    if (customUserAgent && customUserAgent !== '') {\n      headers['User-Agent'] = customUserAgent\n    }\n    return headers\n  }\n\n  private async _flush(): Promise<void> {\n    this.clearFlushTimer()\n    await this._initPromise\n\n    let queue = this.getPersistedProperty<PostHogQueueItem[]>(PostHogPersistedProperty.Queue) || []\n\n    if (!queue.length) {\n      return\n    }\n\n    const sentMessages: any[] = []\n    const originalQueueLength = queue.length\n\n    while (queue.length > 0 && sentMessages.length < originalQueueLength) {\n      const batchItems = queue.slice(0, this.maxBatchSize)\n      const batchMessages = batchItems.map((item) => item.message)\n\n      const persistQueueChange = (): void => {\n        const refreshedQueue = this.getPersistedProperty<PostHogQueueItem[]>(PostHogPersistedProperty.Queue) || []\n        const newQueue = refreshedQueue.slice(batchItems.length)\n        this.setPersistedProperty<PostHogQueueItem[]>(PostHogPersistedProperty.Queue, newQueue)\n        queue = newQueue\n      }\n\n      const data: Record<string, any> = {\n        api_key: this.apiKey,\n        batch: batchMessages,\n        sent_at: currentISOTime(),\n      }\n\n      if (this.historicalMigration) {\n        data.historical_migration = true\n      }\n\n      const payload = JSON.stringify(data)\n\n      const url =\n        this.captureMode === 'form'\n          ? `${this.host}/e/?ip=1&_=${currentTimestamp()}&v=${this.getLibraryVersion()}`\n          : `${this.host}/batch/`\n\n      const fetchOptions: PostHogFetchOptions =\n        this.captureMode === 'form'\n          ? {\n              method: 'POST',\n              mode: 'no-cors',\n              credentials: 'omit',\n              headers: { ...this.getCustomHeaders(), 'Content-Type': 'application/x-www-form-urlencoded' },\n              body: `data=${encodeURIComponent(LZString.compressToBase64(payload))}&compression=lz64`,\n            }\n          : {\n              method: 'POST',\n              headers: { ...this.getCustomHeaders(), 'Content-Type': 'application/json' },\n              body: payload,\n            }\n\n      const retryOptions: Partial<RetriableOptions> = {\n        retryCheck: (err) => {\n          // don't automatically retry on 413 errors, we want to reduce the batch size first\n          if (isPostHogFetchContentTooLargeError(err)) {\n            return false\n          }\n          // otherwise, retry on network errors\n          return isPostHogFetchError(err)\n        },\n      }\n\n      try {\n        await this.fetchWithRetry(url, fetchOptions, retryOptions)\n      } catch (err) {\n        if (isPostHogFetchContentTooLargeError(err) && batchMessages.length > 1) {\n          // if we get a 413 error, we want to reduce the batch size and try again\n          this.maxBatchSize = Math.max(1, Math.floor(batchMessages.length / 2))\n          this.logMsgIfDebug(() =>\n            console.warn(\n              `Received 413 when sending batch of size ${batchMessages.length}, reducing batch size to ${this.maxBatchSize}`\n            )\n          )\n          // do not persist the queue change, we want to retry the same batch\n          continue\n        }\n\n        // depending on the error type, eg a malformed JSON or broken queue, it'll always return an error\n        // and this will be an endless loop, in this case, if the error isn't a network issue, we always remove the items from the queue\n        if (!(err instanceof PostHogFetchNetworkError)) {\n          persistQueueChange()\n        }\n        this._events.emit('error', err)\n\n        throw err\n      }\n\n      persistQueueChange()\n\n      sentMessages.push(...batchMessages)\n    }\n    this._events.emit('flush', sentMessages)\n  }\n\n  private async fetchWithRetry(\n    url: string,\n    options: PostHogFetchOptions,\n    retryOptions?: Partial<RetriableOptions>,\n    requestTimeout?: number\n  ): Promise<PostHogFetchResponse> {\n    ;(AbortSignal as any).timeout ??= function timeout(ms: number) {\n      const ctrl = new AbortController()\n      setTimeout(() => ctrl.abort(), ms)\n      return ctrl.signal\n    }\n\n    const body = options.body ? options.body : ''\n    let reqByteLength = -1\n    try {\n      reqByteLength = Buffer.byteLength(body, STRING_FORMAT)\n    } catch {\n      const encoded = new TextEncoder().encode(body)\n      reqByteLength = encoded.length\n    }\n\n    return await retriable(\n      async () => {\n        let res: PostHogFetchResponse | null = null\n        try {\n          res = await this.fetch(url, {\n            signal: (AbortSignal as any).timeout(requestTimeout ?? this.requestTimeout),\n            ...options,\n          })\n        } catch (e) {\n          // fetch will only throw on network errors or on timeouts\n          throw new PostHogFetchNetworkError(e)\n        }\n        // If we're in no-cors mode, we can't access the response status\n        // We only throw on HTTP errors if we're not in no-cors mode\n        // https://developer.mozilla.org/en-US/docs/Web/API/Request/mode#no-cors\n        const isNoCors = options.mode === 'no-cors'\n        if (!isNoCors && (res.status < 200 || res.status >= 400)) {\n          throw new PostHogFetchHttpError(res, reqByteLength)\n        }\n        return res\n      },\n      { ...this._retryOptions, ...retryOptions }\n    )\n  }\n\n  async _shutdown(shutdownTimeoutMs: number = 30000): Promise<void> {\n    // A little tricky - we want to have a max shutdown time and enforce it, even if that means we have some\n    // dangling promises. We'll keep track of the timeout and resolve/reject based on that.\n\n    await this._initPromise\n    let hasTimedOut = false\n    this.clearFlushTimer()\n\n    const doShutdown = async (): Promise<void> => {\n      try {\n        await Promise.all(Object.values(this.pendingPromises))\n\n        while (true) {\n          const queue = this.getPersistedProperty<PostHogQueueItem[]>(PostHogPersistedProperty.Queue) || []\n\n          if (queue.length === 0) {\n            break\n          }\n\n          // flush again to make sure we send all events, some of which might've been added\n          // while we were waiting for the pending promises to resolve\n          // For example, see sendFeatureFlags in posthog-node/src/posthog-node.ts::capture\n          await this.flush()\n\n          if (hasTimedOut) {\n            break\n          }\n        }\n      } catch (e) {\n        if (!isPostHogFetchError(e)) {\n          throw e\n        }\n\n        await logFlushError(e)\n      }\n    }\n\n    return Promise.race([\n      new Promise<void>((_, reject) => {\n        safeSetTimeout(() => {\n          this.logMsgIfDebug(() => console.error('Timed out while shutting down PostHog'))\n          hasTimedOut = true\n          reject('Timeout while shutting down PostHog. Some events may not have been sent.')\n        }, shutdownTimeoutMs)\n      }),\n      doShutdown(),\n    ])\n  }\n\n  /**\n   *  Call shutdown() once before the node process exits, so ensure that all events have been sent and all promises\n   *  have resolved. Do not use this function if you intend to keep using this PostHog instance after calling it.\n   * @param shutdownTimeoutMs\n   */\n  async shutdown(shutdownTimeoutMs: number = 30000): Promise<void> {\n    if (this.shutdownPromise) {\n      this.logMsgIfDebug(() =>\n        console.warn(\n          'shutdown() called while already shutting down. shutdown() is meant to be called once before process exit - use flush() for per-request cleanup'\n        )\n      )\n    } else {\n      this.shutdownPromise = this._shutdown(shutdownTimeoutMs).finally(() => {\n        this.shutdownPromise = null\n      })\n    }\n    return this.shutdownPromise\n  }\n}\n\nexport abstract class PostHogCore extends PostHogCoreStateless {\n  // options\n  private sendFeatureFlagEvent: boolean\n  private flagCallReported: { [key: string]: boolean } = {}\n\n  // internal\n  protected _decideResponsePromise?: Promise<PostHogDecideResponse | undefined> // TODO: come back to this, fix typing\n  protected _sessionExpirationTimeSeconds: number\n  protected sessionProps: PostHogEventProperties = {}\n\n  constructor(apiKey: string, options?: PostHogCoreOptions) {\n    // Default for stateful mode is to not disable geoip. Only override if explicitly set\n    const disableGeoipOption = options?.disableGeoip ?? false\n\n    // Default for stateful mode is to timeout at 10s. Only override if explicitly set\n    const featureFlagsRequestTimeoutMs = options?.featureFlagsRequestTimeoutMs ?? 10000 // 10 seconds\n\n    super(apiKey, { ...options, disableGeoip: disableGeoipOption, featureFlagsRequestTimeoutMs })\n\n    this.sendFeatureFlagEvent = options?.sendFeatureFlagEvent ?? true\n    this._sessionExpirationTimeSeconds = options?.sessionExpirationTimeSeconds ?? 1800 // 30 minutes\n  }\n\n  protected setupBootstrap(options?: Partial<PostHogCoreOptions>): void {\n    const bootstrap = options?.bootstrap\n    if (!bootstrap) {\n      return\n    }\n\n    // bootstrap options are only set if no persisted values are found\n    // this is to prevent overwriting existing values\n    if (bootstrap.distinctId) {\n      if (bootstrap.isIdentifiedId) {\n        const distinctId = this.getPersistedProperty(PostHogPersistedProperty.DistinctId)\n\n        if (!distinctId) {\n          this.setPersistedProperty(PostHogPersistedProperty.DistinctId, bootstrap.distinctId)\n        }\n      } else {\n        const anonymousId = this.getPersistedProperty(PostHogPersistedProperty.AnonymousId)\n\n        if (!anonymousId) {\n          this.setPersistedProperty(PostHogPersistedProperty.AnonymousId, bootstrap.distinctId)\n        }\n      }\n    }\n\n    const bootstrapFeatureFlags = bootstrap.featureFlags\n    const bootstrapFeatureFlagPayloads = bootstrap.featureFlagPayloads ?? {}\n    if (bootstrapFeatureFlags && Object.keys(bootstrapFeatureFlags).length) {\n      const normalizedBootstrapFeatureFlagDetails = createDecideResponseFromFlagsAndPayloads(\n        bootstrapFeatureFlags,\n        bootstrapFeatureFlagPayloads\n      )\n\n      if (Object.keys(normalizedBootstrapFeatureFlagDetails.flags).length > 0) {\n        this.setBootstrappedFeatureFlagDetails(normalizedBootstrapFeatureFlagDetails)\n\n        const currentFeatureFlagDetails = this.getKnownFeatureFlagDetails() || { flags: {}, requestId: undefined }\n        const newFeatureFlagDetails = {\n          flags: {\n            ...normalizedBootstrapFeatureFlagDetails.flags,\n            ...currentFeatureFlagDetails.flags,\n          },\n          requestId: normalizedBootstrapFeatureFlagDetails.requestId,\n        }\n\n        this.setKnownFeatureFlagDetails(newFeatureFlagDetails)\n      }\n    }\n  }\n\n  private clearProps(): void {\n    this.props = undefined\n    this.sessionProps = {}\n    this.flagCallReported = {}\n  }\n\n  on(event: string, cb: (...args: any[]) => void): () => void {\n    return this._events.on(event, cb)\n  }\n\n  reset(propertiesToKeep?: PostHogPersistedProperty[]): void {\n    this.wrap(() => {\n      const allPropertiesToKeep = [PostHogPersistedProperty.Queue, ...(propertiesToKeep || [])]\n\n      // clean up props\n      this.clearProps()\n\n      for (const key of <(keyof typeof PostHogPersistedProperty)[]>Object.keys(PostHogPersistedProperty)) {\n        if (!allPropertiesToKeep.includes(PostHogPersistedProperty[key])) {\n          this.setPersistedProperty((PostHogPersistedProperty as any)[key], null)\n        }\n      }\n\n      this.reloadFeatureFlags()\n    })\n  }\n\n  protected getCommonEventProperties(): PostHogEventProperties {\n    const featureFlags = this.getFeatureFlags()\n\n    const featureVariantProperties: Record<string, FeatureFlagValue> = {}\n    if (featureFlags) {\n      for (const [feature, variant] of Object.entries(featureFlags)) {\n        featureVariantProperties[`$feature/${feature}`] = variant\n      }\n    }\n    return {\n      ...maybeAdd('$active_feature_flags', featureFlags ? Object.keys(featureFlags) : undefined),\n      ...featureVariantProperties,\n      ...super.getCommonEventProperties(),\n    }\n  }\n\n  private enrichProperties(properties?: PostHogEventProperties): PostHogEventProperties {\n    return {\n      ...this.props, // Persisted properties first\n      ...this.sessionProps, // Followed by session properties\n      ...(properties || {}), // Followed by user specified properties\n      ...this.getCommonEventProperties(), // Followed by FF props\n      $session_id: this.getSessionId(),\n    }\n  }\n\n  /**\n   * * @returns {string} The stored session ID for the current session. This may be an empty string if the client is not yet fully initialized.\n   */\n  getSessionId(): string {\n    if (!this._isInitialized) {\n      return ''\n    }\n\n    let sessionId = this.getPersistedProperty<string>(PostHogPersistedProperty.SessionId)\n    const sessionTimestamp = this.getPersistedProperty<number>(PostHogPersistedProperty.SessionLastTimestamp) || 0\n    if (!sessionId || Date.now() - sessionTimestamp > this._sessionExpirationTimeSeconds * 1000) {\n      sessionId = uuidv7()\n      this.setPersistedProperty(PostHogPersistedProperty.SessionId, sessionId)\n    }\n    this.setPersistedProperty(PostHogPersistedProperty.SessionLastTimestamp, Date.now())\n\n    return sessionId\n  }\n\n  resetSessionId(): void {\n    this.wrap(() => {\n      this.setPersistedProperty(PostHogPersistedProperty.SessionId, null)\n      this.setPersistedProperty(PostHogPersistedProperty.SessionLastTimestamp, null)\n    })\n  }\n\n  /**\n   * * @returns {string} The stored anonymous ID. This may be an empty string if the client is not yet fully initialized.\n   */\n  getAnonymousId(): string {\n    if (!this._isInitialized) {\n      return ''\n    }\n\n    let anonId = this.getPersistedProperty<string>(PostHogPersistedProperty.AnonymousId)\n    if (!anonId) {\n      anonId = uuidv7()\n      this.setPersistedProperty(PostHogPersistedProperty.AnonymousId, anonId)\n    }\n    return anonId\n  }\n\n  /**\n   * * @returns {string} The stored distinct ID. This may be an empty string if the client is not yet fully initialized.\n   */\n  getDistinctId(): string {\n    if (!this._isInitialized) {\n      return ''\n    }\n\n    return this.getPersistedProperty<string>(PostHogPersistedProperty.DistinctId) || this.getAnonymousId()\n  }\n\n  registerForSession(properties: PostHogEventProperties): void {\n    this.sessionProps = {\n      ...this.sessionProps,\n      ...properties,\n    }\n  }\n\n  unregisterForSession(property: string): void {\n    delete this.sessionProps[property]\n  }\n\n  /***\n   *** TRACKING\n   ***/\n  identify(distinctId?: string, properties?: PostHogEventProperties, options?: PostHogCaptureOptions): void {\n    this.wrap(() => {\n      const previousDistinctId = this.getDistinctId()\n      distinctId = distinctId || previousDistinctId\n\n      if (properties?.$groups) {\n        this.groups(properties.$groups as PostHogGroupProperties)\n      }\n\n      // promote $set and $set_once to top level\n      const userPropsOnce = properties?.$set_once\n      delete properties?.$set_once\n\n      // if no $set is provided we assume all properties are $set\n      const userProps = properties?.$set || properties\n\n      const allProperties = this.enrichProperties({\n        $anon_distinct_id: this.getAnonymousId(),\n        ...maybeAdd('$set', userProps),\n        ...maybeAdd('$set_once', userPropsOnce),\n      })\n\n      if (distinctId !== previousDistinctId) {\n        // We keep the AnonymousId to be used by decide calls and identify to link the previousId\n        this.setPersistedProperty(PostHogPersistedProperty.AnonymousId, previousDistinctId)\n        this.setPersistedProperty(PostHogPersistedProperty.DistinctId, distinctId)\n        this.reloadFeatureFlags()\n      }\n\n      super.identifyStateless(distinctId, allProperties, options)\n    })\n  }\n\n  capture(event: string, properties?: PostHogEventProperties, options?: PostHogCaptureOptions): void {\n    this.wrap(() => {\n      const distinctId = this.getDistinctId()\n\n      if (properties?.$groups) {\n        this.groups(properties.$groups as PostHogGroupProperties)\n      }\n\n      const allProperties = this.enrichProperties(properties)\n\n      super.captureStateless(distinctId, event, allProperties, options)\n    })\n  }\n\n  alias(alias: string): void {\n    this.wrap(() => {\n      const distinctId = this.getDistinctId()\n      const allProperties = this.enrichProperties({})\n\n      super.aliasStateless(alias, distinctId, allProperties)\n    })\n  }\n\n  autocapture(\n    eventType: string,\n    elements: PostHogAutocaptureElement[],\n    properties: PostHogEventProperties = {},\n    options?: PostHogCaptureOptions\n  ): void {\n    this.wrap(() => {\n      const distinctId = this.getDistinctId()\n      const payload = {\n        distinct_id: distinctId,\n        event: '$autocapture',\n        properties: {\n          ...this.enrichProperties(properties),\n          $event_type: eventType,\n          $elements: elements,\n        },\n      }\n\n      this.enqueue('autocapture', payload, options)\n    })\n  }\n\n  /***\n   *** GROUPS\n   ***/\n\n  groups(groups: PostHogGroupProperties): void {\n    this.wrap(() => {\n      // Get persisted groups\n      const existingGroups = this.props.$groups || {}\n\n      this.register({\n        $groups: {\n          ...(existingGroups as PostHogGroupProperties),\n          ...groups,\n        },\n      })\n\n      if (Object.keys(groups).find((type) => existingGroups[type as keyof typeof existingGroups] !== groups[type])) {\n        this.reloadFeatureFlags()\n      }\n    })\n  }\n\n  group(\n    groupType: string,\n    groupKey: string | number,\n    groupProperties?: PostHogEventProperties,\n    options?: PostHogCaptureOptions\n  ): void {\n    this.wrap(() => {\n      this.groups({\n        [groupType]: groupKey,\n      })\n\n      if (groupProperties) {\n        this.groupIdentify(groupType, groupKey, groupProperties, options)\n      }\n    })\n  }\n\n  groupIdentify(\n    groupType: string,\n    groupKey: string | number,\n    groupProperties?: PostHogEventProperties,\n    options?: PostHogCaptureOptions\n  ): void {\n    this.wrap(() => {\n      const distinctId = this.getDistinctId()\n      const eventProperties = this.enrichProperties({})\n      super.groupIdentifyStateless(groupType, groupKey, groupProperties, options, distinctId, eventProperties)\n    })\n  }\n\n  /***\n   * PROPERTIES\n   ***/\n  setPersonPropertiesForFlags(properties: { [type: string]: string }): void {\n    this.wrap(() => {\n      // Get persisted person properties\n      const existingProperties =\n        this.getPersistedProperty<Record<string, string>>(PostHogPersistedProperty.PersonProperties) || {}\n\n      this.setPersistedProperty<PostHogEventProperties>(PostHogPersistedProperty.PersonProperties, {\n        ...existingProperties,\n        ...properties,\n      })\n    })\n  }\n\n  resetPersonPropertiesForFlags(): void {\n    this.wrap(() => {\n      this.setPersistedProperty<PostHogEventProperties>(PostHogPersistedProperty.PersonProperties, null)\n    })\n  }\n\n  /** @deprecated - Renamed to setPersonPropertiesForFlags */\n  personProperties(properties: { [type: string]: string }): void {\n    return this.setPersonPropertiesForFlags(properties)\n  }\n\n  setGroupPropertiesForFlags(properties: { [type: string]: Record<string, string> }): void {\n    this.wrap(() => {\n      // Get persisted group properties\n      const existingProperties =\n        this.getPersistedProperty<Record<string, Record<string, string>>>(PostHogPersistedProperty.GroupProperties) ||\n        {}\n\n      if (Object.keys(existingProperties).length !== 0) {\n        Object.keys(existingProperties).forEach((groupType) => {\n          existingProperties[groupType] = {\n            ...existingProperties[groupType],\n            ...properties[groupType],\n          }\n          delete properties[groupType]\n        })\n      }\n\n      this.setPersistedProperty<PostHogEventProperties>(PostHogPersistedProperty.GroupProperties, {\n        ...existingProperties,\n        ...properties,\n      })\n    })\n  }\n\n  resetGroupPropertiesForFlags(): void {\n    this.wrap(() => {\n      this.setPersistedProperty<PostHogEventProperties>(PostHogPersistedProperty.GroupProperties, null)\n    })\n  }\n\n  /** @deprecated - Renamed to setGroupPropertiesForFlags */\n  groupProperties(properties: { [type: string]: Record<string, string> }): void {\n    this.wrap(() => {\n      this.setGroupPropertiesForFlags(properties)\n    })\n  }\n\n  private async remoteConfigAsync(): Promise<PostHogRemoteConfig | undefined> {\n    await this._initPromise\n    if (this._remoteConfigResponsePromise) {\n      return this._remoteConfigResponsePromise\n    }\n    return this._remoteConfigAsync()\n  }\n\n  /***\n   *** FEATURE FLAGS\n   ***/\n  private async decideAsync(sendAnonDistinctId: boolean = true): Promise<PostHogDecideResponse | undefined> {\n    await this._initPromise\n    if (this._decideResponsePromise) {\n      return this._decideResponsePromise\n    }\n    return this._decideAsync(sendAnonDistinctId)\n  }\n\n  private cacheSessionReplay(source: string, response?: PostHogRemoteConfig): void {\n    const sessionReplay = response?.sessionRecording\n    if (sessionReplay) {\n      this.setPersistedProperty(PostHogPersistedProperty.SessionReplay, sessionReplay)\n      this.logMsgIfDebug(() =>\n        console.log('PostHog Debug', `Session replay config from ${source}: `, JSON.stringify(sessionReplay))\n      )\n    } else if (typeof sessionReplay === 'boolean' && sessionReplay === false) {\n      // if session replay is disabled, we don't need to cache it\n      // we need to check for this because the response might be undefined (/flags does not return sessionRecording yet)\n      this.logMsgIfDebug(() => console.info('PostHog Debug', `Session replay config from ${source} disabled.`))\n      this.setPersistedProperty(PostHogPersistedProperty.SessionReplay, null)\n    }\n  }\n\n  private async _remoteConfigAsync(): Promise<PostHogRemoteConfig | undefined> {\n    this._remoteConfigResponsePromise = this._initPromise\n      .then(() => {\n        let remoteConfig = this.getPersistedProperty<Omit<PostHogRemoteConfig, 'surveys'>>(\n          PostHogPersistedProperty.RemoteConfig\n        )\n\n        this.logMsgIfDebug(() => console.log('PostHog Debug', 'Cached remote config: ', JSON.stringify(remoteConfig)))\n\n        return super.getRemoteConfig().then((response) => {\n          if (response) {\n            const remoteConfigWithoutSurveys = { ...response }\n            delete remoteConfigWithoutSurveys.surveys\n\n            this.logMsgIfDebug(() =>\n              console.log('PostHog Debug', 'Fetched remote config: ', JSON.stringify(remoteConfigWithoutSurveys))\n            )\n\n            if (this.disableSurveys === false) {\n              const surveys = response.surveys\n\n              let hasSurveys = true\n\n              if (!Array.isArray(surveys)) {\n                // If surveys is not an array, it means there are no surveys (its a boolean instead)\n                this.logMsgIfDebug(() => console.log('PostHog Debug', 'There are no surveys.'))\n                hasSurveys = false\n              } else {\n                this.logMsgIfDebug(() =>\n                  console.log('PostHog Debug', 'Surveys fetched from remote config: ', JSON.stringify(surveys))\n                )\n              }\n\n              if (hasSurveys) {\n                this.setPersistedProperty<SurveyResponse['surveys']>(\n                  PostHogPersistedProperty.Surveys,\n                  surveys as Survey[]\n                )\n              } else {\n                this.setPersistedProperty<SurveyResponse['surveys']>(PostHogPersistedProperty.Surveys, null)\n              }\n            } else {\n              this.setPersistedProperty<SurveyResponse['surveys']>(PostHogPersistedProperty.Surveys, null)\n            }\n            // we cache the surveys in its own storage key\n            this.setPersistedProperty<Omit<PostHogRemoteConfig, 'surveys'>>(\n              PostHogPersistedProperty.RemoteConfig,\n              remoteConfigWithoutSurveys\n            )\n\n            this.cacheSessionReplay('remote config', response)\n\n            // we only dont load flags if the remote config has no feature flags\n            if (response.hasFeatureFlags === false) {\n              // resetting flags to empty object\n              this.setKnownFeatureFlagDetails({ flags: {} })\n\n              this.logMsgIfDebug(() => console.warn('Remote config has no feature flags, will not load feature flags.'))\n            } else if (this.preloadFeatureFlags !== false) {\n              this.reloadFeatureFlags()\n            }\n\n            remoteConfig = response\n          }\n\n          return remoteConfig\n        })\n      })\n      .finally(() => {\n        this._remoteConfigResponsePromise = undefined\n      })\n    return this._remoteConfigResponsePromise\n  }\n\n  private async _decideAsync(sendAnonDistinctId: boolean = true): Promise<PostHogDecideResponse | undefined> {\n    this._decideResponsePromise = this._initPromise\n      .then(async () => {\n        const distinctId = this.getDistinctId()\n        const groups = this.props.$groups || {}\n        const personProperties =\n          this.getPersistedProperty<Record<string, string>>(PostHogPersistedProperty.PersonProperties) || {}\n        const groupProperties =\n          this.getPersistedProperty<Record<string, Record<string, string>>>(PostHogPersistedProperty.GroupProperties) ||\n          {}\n\n        const extraProperties = {\n          $anon_distinct_id: sendAnonDistinctId ? this.getAnonymousId() : undefined,\n        }\n\n        const res = await super.getDecide(\n          distinctId,\n          groups as PostHogGroupProperties,\n          personProperties,\n          groupProperties,\n          extraProperties\n        )\n\n        // Add check for quota limitation on feature flags\n        if (res?.quotaLimited?.includes(QuotaLimitedFeature.FeatureFlags)) {\n          // Unset all feature flags by setting to null\n          this.setKnownFeatureFlagDetails(null)\n          console.warn(\n            '[FEATURE FLAGS] Feature flags quota limit exceeded - unsetting all flags. Learn more about billing limits at https://posthog.com/docs/billing/limits-alerts'\n          )\n          return res\n        }\n        if (res?.featureFlags) {\n          // clear flag call reported if we have new flags since they might have changed\n          if (this.sendFeatureFlagEvent) {\n            this.flagCallReported = {}\n          }\n\n          let newFeatureFlagDetails = res\n          if (res.errorsWhileComputingFlags) {\n            // if not all flags were computed, we upsert flags instead of replacing them\n            const currentFlagDetails = this.getKnownFeatureFlagDetails()\n            this.logMsgIfDebug(() =>\n              console.log('PostHog Debug', 'Cached feature flags: ', JSON.stringify(currentFlagDetails))\n            )\n\n            newFeatureFlagDetails = {\n              ...res,\n              flags: { ...currentFlagDetails?.flags, ...res.flags },\n            }\n          }\n          this.setKnownFeatureFlagDetails(newFeatureFlagDetails)\n          // Mark that we hit the /decide endpoint so we can capture this in the $feature_flag_called event\n          this.setPersistedProperty(PostHogPersistedProperty.DecideEndpointWasHit, true)\n\n          this.cacheSessionReplay('decide/flags', res)\n        }\n        return res\n      })\n      .finally(() => {\n        this._decideResponsePromise = undefined\n      })\n    return this._decideResponsePromise\n  }\n\n  // We only store the flags and request id in the feature flag details storage key\n  private setKnownFeatureFlagDetails(decideResponse: PostHogFlagsStorageFormat | null): void {\n    this.wrap(() => {\n      this.setPersistedProperty<PostHogFlagsStorageFormat>(PostHogPersistedProperty.FeatureFlagDetails, decideResponse)\n\n      this._events.emit('featureflags', getFlagValuesFromFlags(decideResponse?.flags ?? {}))\n    })\n  }\n\n  private getKnownFeatureFlagDetails(): PostHogFeatureFlagDetails | undefined {\n    const storedDetails = this.getPersistedProperty<PostHogFlagsStorageFormat>(\n      PostHogPersistedProperty.FeatureFlagDetails\n    )\n    if (!storedDetails) {\n      // Rebuild from the stored feature flags and feature flag payloads\n      const featureFlags = this.getPersistedProperty<PostHogDecideResponse['featureFlags']>(\n        PostHogPersistedProperty.FeatureFlags\n      )\n      const featureFlagPayloads = this.getPersistedProperty<PostHogDecideResponse['featureFlagPayloads']>(\n        PostHogPersistedProperty.FeatureFlagPayloads\n      )\n\n      if (featureFlags === undefined && featureFlagPayloads === undefined) {\n        return undefined\n      }\n\n      return createDecideResponseFromFlagsAndPayloads(featureFlags ?? {}, featureFlagPayloads ?? {})\n    }\n\n    return normalizeDecideResponse(\n      storedDetails as PostHogV3DecideResponse | PostHogV4DecideResponse\n    ) as PostHogFeatureFlagDetails\n  }\n\n  protected getKnownFeatureFlags(): PostHogDecideResponse['featureFlags'] | undefined {\n    const featureFlagDetails = this.getKnownFeatureFlagDetails()\n    if (!featureFlagDetails) {\n      return undefined\n    }\n    return getFlagValuesFromFlags(featureFlagDetails.flags)\n  }\n\n  private getKnownFeatureFlagPayloads(): PostHogDecideResponse['featureFlagPayloads'] | undefined {\n    const featureFlagDetails = this.getKnownFeatureFlagDetails()\n    if (!featureFlagDetails) {\n      return undefined\n    }\n    return getPayloadsFromFlags(featureFlagDetails.flags)\n  }\n\n  private getBootstrappedFeatureFlagDetails(): PostHogFeatureFlagDetails | undefined {\n    const details = this.getPersistedProperty<PostHogFeatureFlagDetails>(\n      PostHogPersistedProperty.BootstrapFeatureFlagDetails\n    )\n    if (!details) {\n      return undefined\n    }\n    return details\n  }\n\n  private setBootstrappedFeatureFlagDetails(details: PostHogFeatureFlagDetails): void {\n    this.setPersistedProperty<PostHogFeatureFlagDetails>(PostHogPersistedProperty.BootstrapFeatureFlagDetails, details)\n  }\n\n  private getBootstrappedFeatureFlags(): PostHogDecideResponse['featureFlags'] | undefined {\n    const details = this.getBootstrappedFeatureFlagDetails()\n    if (!details) {\n      return undefined\n    }\n    return getFlagValuesFromFlags(details.flags)\n  }\n\n  private getBootstrappedFeatureFlagPayloads(): PostHogDecideResponse['featureFlagPayloads'] | undefined {\n    const details = this.getBootstrappedFeatureFlagDetails()\n    if (!details) {\n      return undefined\n    }\n    return getPayloadsFromFlags(details.flags)\n  }\n\n  getFeatureFlag(key: string): FeatureFlagValue | undefined {\n    const details = this.getFeatureFlagDetails()\n\n    if (!details) {\n      // If we haven't loaded flags yet, or errored out, we respond with undefined\n      return undefined\n    }\n\n    const featureFlag = details.flags[key]\n\n    let response = getFeatureFlagValue(featureFlag)\n\n    if (response === undefined) {\n      // For cases where the flag is unknown, return false\n      response = false\n    }\n\n    if (this.sendFeatureFlagEvent && !this.flagCallReported[key]) {\n      const bootstrappedResponse = this.getBootstrappedFeatureFlags()?.[key]\n      const bootstrappedPayload = this.getBootstrappedFeatureFlagPayloads()?.[key]\n\n      this.flagCallReported[key] = true\n      this.capture('$feature_flag_called', {\n        $feature_flag: key,\n        $feature_flag_response: response,\n        ...maybeAdd('$feature_flag_id', featureFlag?.metadata?.id),\n        ...maybeAdd('$feature_flag_version', featureFlag?.metadata?.version),\n        ...maybeAdd('$feature_flag_reason', featureFlag?.reason?.description ?? featureFlag?.reason?.code),\n        ...maybeAdd('$feature_flag_bootstrapped_response', bootstrappedResponse),\n        ...maybeAdd('$feature_flag_bootstrapped_payload', bootstrappedPayload),\n        // If we haven't yet received a response from the /decide endpoint, we must have used the bootstrapped value\n        $used_bootstrap_value: !this.getPersistedProperty(PostHogPersistedProperty.DecideEndpointWasHit),\n        ...maybeAdd('$feature_flag_request_id', details.requestId),\n      })\n    }\n\n    // If we have flags we either return the value (true or string) or false\n    return response\n  }\n\n  getFeatureFlagPayload(key: string): JsonType | undefined {\n    const payloads = this.getFeatureFlagPayloads()\n\n    if (!payloads) {\n      return undefined\n    }\n\n    const response = payloads[key]\n\n    // Undefined means a loading or missing data issue. Null means evaluation happened and there was no match\n    if (response === undefined) {\n      return null\n    }\n\n    return response\n  }\n\n  getFeatureFlagPayloads(): PostHogDecideResponse['featureFlagPayloads'] | undefined {\n    return this.getFeatureFlagDetails()?.featureFlagPayloads\n  }\n\n  getFeatureFlags(): PostHogDecideResponse['featureFlags'] | undefined {\n    // NOTE: We don't check for _initPromise here as the function is designed to be\n    // callable before the state being loaded anyways\n    return this.getFeatureFlagDetails()?.featureFlags\n  }\n\n  getFeatureFlagDetails(): PostHogFeatureFlagDetails | undefined {\n    // NOTE: We don't check for _initPromise here as the function is designed to be\n    // callable before the state being loaded anyways\n    let details = this.getKnownFeatureFlagDetails()\n    const overriddenFlags = this.getPersistedProperty<PostHogDecideResponse['featureFlags']>(\n      PostHogPersistedProperty.OverrideFeatureFlags\n    )\n\n    if (!overriddenFlags) {\n      return details\n    }\n\n    details = details ?? { featureFlags: {}, featureFlagPayloads: {}, flags: {} }\n\n    const flags: Record<string, FeatureFlagDetail> = details.flags ?? {}\n\n    for (const key in overriddenFlags) {\n      if (!overriddenFlags[key]) {\n        delete flags[key]\n      } else {\n        flags[key] = updateFlagValue(flags[key], overriddenFlags[key])\n      }\n    }\n\n    const result = {\n      ...details,\n      flags,\n    }\n\n    return normalizeDecideResponse(result) as PostHogFeatureFlagDetails\n  }\n\n  getFeatureFlagsAndPayloads(): {\n    flags: PostHogDecideResponse['featureFlags'] | undefined\n    payloads: PostHogDecideResponse['featureFlagPayloads'] | undefined\n  } {\n    const flags = this.getFeatureFlags()\n    const payloads = this.getFeatureFlagPayloads()\n\n    return {\n      flags,\n      payloads,\n    }\n  }\n\n  isFeatureEnabled(key: string): boolean | undefined {\n    const response = this.getFeatureFlag(key)\n    if (response === undefined) {\n      return undefined\n    }\n    return !!response\n  }\n\n  // Used when we want to trigger the reload but we don't care about the result\n  reloadFeatureFlags(cb?: (err?: Error, flags?: PostHogDecideResponse['featureFlags']) => void): void {\n    this.decideAsync()\n      .then((res) => {\n        cb?.(undefined, res?.featureFlags)\n      })\n      .catch((e) => {\n        cb?.(e, undefined)\n        if (!cb) {\n          this.logMsgIfDebug(() => console.log('PostHog Debug', 'Error reloading feature flags', e))\n        }\n      })\n  }\n\n  async reloadRemoteConfigAsync(): Promise<PostHogRemoteConfig | undefined> {\n    return await this.remoteConfigAsync()\n  }\n\n  async reloadFeatureFlagsAsync(\n    sendAnonDistinctId: boolean = true\n  ): Promise<PostHogDecideResponse['featureFlags'] | undefined> {\n    return (await this.decideAsync(sendAnonDistinctId))?.featureFlags\n  }\n\n  onFeatureFlags(cb: (flags: PostHogDecideResponse['featureFlags']) => void): () => void {\n    return this.on('featureflags', async () => {\n      const flags = this.getFeatureFlags()\n      if (flags) {\n        cb(flags)\n      }\n    })\n  }\n\n  onFeatureFlag(key: string, cb: (value: FeatureFlagValue) => void): () => void {\n    return this.on('featureflags', async () => {\n      const flagResponse = this.getFeatureFlag(key)\n      if (flagResponse !== undefined) {\n        cb(flagResponse)\n      }\n    })\n  }\n\n  async overrideFeatureFlag(flags: PostHogDecideResponse['featureFlags'] | null): Promise<void> {\n    this.wrap(() => {\n      if (flags === null) {\n        return this.setPersistedProperty(PostHogPersistedProperty.OverrideFeatureFlags, null)\n      }\n      return this.setPersistedProperty(PostHogPersistedProperty.OverrideFeatureFlags, flags)\n    })\n  }\n\n  /***\n   *** ERROR TRACKING\n   ***/\n  captureException(error: unknown, additionalProperties?: PostHogEventProperties): void {\n    const properties: { [key: string]: any } = {\n      $exception_level: 'error',\n      $exception_list: [\n        {\n          type: isError(error) ? error.name : 'Error',\n          value: isError(error) ? error.message : error,\n          mechanism: {\n            handled: true,\n            synthetic: false,\n          },\n        },\n      ],\n      ...additionalProperties,\n    }\n\n    properties.$exception_personURL = new URL(\n      `/project/${this.apiKey}/person/${this.getDistinctId()}`,\n      this.host\n    ).toString()\n\n    this.capture('$exception', properties)\n  }\n\n  /**\n   * Capture written user feedback for a LLM trace. Numeric values are converted to strings.\n   * @param traceId The trace ID to capture feedback for.\n   * @param userFeedback The feedback to capture.\n   */\n  captureTraceFeedback(traceId: string | number, userFeedback: string): void {\n    this.capture('$ai_feedback', {\n      $ai_feedback_text: userFeedback,\n      $ai_trace_id: String(traceId),\n    })\n  }\n\n  /**\n   * Capture a metric for a LLM trace. Numeric values are converted to strings.\n   * @param traceId The trace ID to capture the metric for.\n   * @param metricName The name of the metric to capture.\n   * @param metricValue The value of the metric to capture.\n   */\n  captureTraceMetric(traceId: string | number, metricName: string, metricValue: string | number | boolean): void {\n    this.capture('$ai_metric', {\n      $ai_metric_name: metricName,\n      $ai_metric_value: String(metricValue),\n      $ai_trace_id: String(traceId),\n    })\n  }\n}\n\nexport * from './types'\nexport { LZString }\n", "/**\n * Fetch wrapper\n *\n * We want to polyfill fetch when not available with axios but use it when it is.\n * NOTE: The current version of Axios has an issue when in non-node environments like Clouflare Workers.\n * This is currently solved by using the global fetch if available instead.\n * See https://github.com/PostHog/posthog-js-lite/issues/127 for more info\n */\n\nimport { FetchLike, PostHogFetchOptions, PostHogFetchResponse } from 'posthog-core'\nimport { getFetch } from 'posthog-core'\n\nlet _fetch: FetchLike | undefined = getFetch()\n\nif (!_fetch) {\n  // eslint-disable-next-line @typescript-eslint/no-var-requires\n  const axios = require('axios')\n\n  _fetch = async (url: string, options: PostHogFetchOptions): Promise<PostHogFetchResponse> => {\n    const res = await axios.request({\n      url,\n      headers: options.headers,\n      method: options.method.toLowerCase(),\n      data: options.body,\n      signal: options.signal,\n      // fetch only throws on network errors, not on HTTP errors\n      validateStatus: () => true,\n    })\n\n    return {\n      status: res.status,\n      text: async () => res.data,\n      json: async () => res.data,\n    }\n  }\n}\n\n// NOTE: We have to export this as default, even though we prefer named exports as we are relying on detecting \"fetch\" in the global scope\nexport default _fetch as FetchLike\n", "/**\n * A lazy value that is only computed when needed. Inspired by C#'s Lazy<T> class.\n */\nexport class Lazy<T> {\n  private value: T | undefined\n  private factory: () => Promise<T>\n  private initializationPromise: Promise<T> | undefined\n\n  constructor(factory: () => Promise<T>) {\n    this.factory = factory\n  }\n\n  /**\n   * Gets the value, initializing it if necessary.\n   * Multiple concurrent calls will share the same initialization promise.\n   */\n  async getValue(): Promise<T> {\n    if (this.value !== undefined) {\n      return this.value\n    }\n\n    if (this.initializationPromise === undefined) {\n      this.initializationPromise = (async () => {\n        try {\n          const result = await this.factory()\n          this.value = result\n          return result\n        } finally {\n          // Clear the promise so we can retry if needed\n          this.initializationPromise = undefined\n        }\n      })()\n    }\n\n    return this.initializationPromise\n  }\n\n  /**\n   * Returns true if the value has been initialized.\n   */\n  isInitialized(): boolean {\n    return this.value !== undefined\n  }\n\n  /**\n   * Returns a promise that resolves when the value is initialized.\n   * If already initialized, resolves immediately.\n   */\n  async waitForInitialization(): Promise<void> {\n    if (this.isInitialized()) {\n      return\n    }\n    await this.getValue()\n  }\n}\n", "/// <reference lib=\"dom\" />\nimport { Lazy } from './lazy'\n\nconst nodeCrypto = new Lazy(async () => {\n  try {\n    return await import('crypto')\n  } catch {\n    return undefined\n  }\n})\n\nexport async function getNodeCrypto(): Promise<typeof import('crypto') | undefined> {\n  return await nodeCrypto.getValue()\n}\n\nconst webCrypto = new Lazy(async (): Promise<SubtleCrypto | undefined> => {\n  if (typeof globalThis.crypto?.subtle !== 'undefined') {\n    return globalThis.crypto.subtle\n  }\n\n  try {\n    // Node.js: use built-in webcrypto and assign it if needed\n    const crypto = await nodeCrypto.getValue()\n    if (crypto?.webcrypto?.subtle) {\n      return crypto.webcrypto.subtle as SubtleCrypto\n    }\n  } catch {\n    // Ignore if not available\n  }\n\n  return undefined\n})\n\nexport async function getWebCrypto(): Promise<SubtleCrypto | undefined> {\n  return await webCrypto.getValue()\n}\n", "/// <reference lib=\"dom\" />\n\nimport { getNodeCrypto, getWebCrypto } from './crypto-helpers'\n\nexport async function hashSHA1(text: string): Promise<string> {\n  // Try Node.js crypto first\n  const nodeCrypto = await getNodeCrypto()\n  if (nodeCrypto) {\n    return nodeCrypto.createHash('sha1').update(text).digest('hex')\n  }\n\n  const webCrypto = await getWebCrypto()\n\n  // Fall back to Web Crypto API\n  if (webCrypto) {\n    const hashBuffer = await webCrypto.digest('SHA-1', new TextEncoder().encode(text))\n    const hashArray = Array.from(new Uint8Array(hashBuffer))\n    return hashArray.map((byte) => byte.toString(16).padStart(2, '0')).join('')\n  }\n\n  throw new Error('No crypto implementation available. Tried Node Crypto API and Web SubtleCrypto API')\n}\n", "import { FeatureFlagCondition, FlagProperty, PostHogFeatureFlag, PropertyGroup } from '../../types'\nimport type { FeatureFlagValue, JsonType, PostHogFetchOptions, PostHogFetchResponse } from 'posthog-core'\nimport { safeSetTimeout } from 'posthog-core'\nimport fetch from '../../fetch'\nimport { hashSHA1 } from './crypto'\n\nconst SIXTY_SECONDS = 60 * 1000\n\n// eslint-disable-next-line\nconst LONG_SCALE = 0xfffffffffffffff\n\nconst NULL_VALUES_ALLOWED_OPERATORS = ['is_not']\nclass ClientError extends Error {\n  constructor(message: string) {\n    super()\n    Error.captureStackTrace(this, this.constructor)\n    this.name = 'ClientError'\n    this.message = message\n    Object.setPrototypeOf(this, ClientError.prototype)\n  }\n}\n\nclass InconclusiveMatchError extends Error {\n  constructor(message: string) {\n    super(message)\n    this.name = this.constructor.name\n    Error.captureStackTrace(this, this.constructor)\n    // instanceof doesn't work in ES3 or ES5\n    // https://www.dannyguo.com/blog/how-to-fix-instanceof-not-working-for-custom-errors-in-typescript/\n    // this is the workaround\n    Object.setPrototypeOf(this, InconclusiveMatchError.prototype)\n  }\n}\n\ntype FeatureFlagsPollerOptions = {\n  personalApiKey: string\n  projectApiKey: string\n  host: string\n  pollingInterval: number\n  timeout?: number\n  fetch?: (url: string, options: PostHogFetchOptions) => Promise<PostHogFetchResponse>\n  onError?: (error: Error) => void\n  onLoad?: (count: number) => void\n  customHeaders?: { [key: string]: string }\n}\n\nclass FeatureFlagsPoller {\n  pollingInterval: number\n  personalApiKey: string\n  projectApiKey: string\n  featureFlags: Array<PostHogFeatureFlag>\n  featureFlagsByKey: Record<string, PostHogFeatureFlag>\n  groupTypeMapping: Record<string, string>\n  cohorts: Record<string, PropertyGroup>\n  loadedSuccessfullyOnce: boolean\n  timeout?: number\n  host: FeatureFlagsPollerOptions['host']\n  poller?: NodeJS.Timeout\n  fetch: (url: string, options: PostHogFetchOptions) => Promise<PostHogFetchResponse>\n  debugMode: boolean = false\n  onError?: (error: Error) => void\n  customHeaders?: { [key: string]: string }\n  shouldBeginExponentialBackoff: boolean = false\n  backOffCount: number = 0\n  onLoad?: (count: number) => void\n\n  constructor({\n    pollingInterval,\n    personalApiKey,\n    projectApiKey,\n    timeout,\n    host,\n    customHeaders,\n    ...options\n  }: FeatureFlagsPollerOptions) {\n    this.pollingInterval = pollingInterval\n    this.personalApiKey = personalApiKey\n    this.featureFlags = []\n    this.featureFlagsByKey = {}\n    this.groupTypeMapping = {}\n    this.cohorts = {}\n    this.loadedSuccessfullyOnce = false\n    this.timeout = timeout\n    this.projectApiKey = projectApiKey\n    this.host = host\n    this.poller = undefined\n    this.fetch = options.fetch || fetch\n    this.onError = options.onError\n    this.customHeaders = customHeaders\n    this.onLoad = options.onLoad\n    void this.loadFeatureFlags()\n  }\n\n  debug(enabled: boolean = true): void {\n    this.debugMode = enabled\n  }\n\n  private logMsgIfDebug(fn: () => void): void {\n    if (this.debugMode) {\n      fn()\n    }\n  }\n\n  async getFeatureFlag(\n    key: string,\n    distinctId: string,\n    groups: Record<string, string> = {},\n    personProperties: Record<string, string> = {},\n    groupProperties: Record<string, Record<string, string>> = {}\n  ): Promise<FeatureFlagValue | undefined> {\n    await this.loadFeatureFlags()\n\n    let response: FeatureFlagValue | undefined = undefined\n    let featureFlag = undefined\n\n    if (!this.loadedSuccessfullyOnce) {\n      return response\n    }\n\n    for (const flag of this.featureFlags) {\n      if (key === flag.key) {\n        featureFlag = flag\n        break\n      }\n    }\n\n    if (featureFlag !== undefined) {\n      try {\n        response = await this.computeFlagLocally(featureFlag, distinctId, groups, personProperties, groupProperties)\n        this.logMsgIfDebug(() => console.debug(`Successfully computed flag locally: ${key} -> ${response}`))\n      } catch (e) {\n        if (e instanceof InconclusiveMatchError) {\n          this.logMsgIfDebug(() => console.debug(`InconclusiveMatchError when computing flag locally: ${key}: ${e}`))\n        } else if (e instanceof Error) {\n          this.onError?.(new Error(`Error computing flag locally: ${key}: ${e}`))\n        }\n      }\n    }\n\n    return response\n  }\n\n  async computeFeatureFlagPayloadLocally(key: string, matchValue: FeatureFlagValue): Promise<JsonType | undefined> {\n    await this.loadFeatureFlags()\n\n    let response = undefined\n\n    if (!this.loadedSuccessfullyOnce) {\n      return undefined\n    }\n\n    if (typeof matchValue == 'boolean') {\n      response = this.featureFlagsByKey?.[key]?.filters?.payloads?.[matchValue.toString()]\n    } else if (typeof matchValue == 'string') {\n      response = this.featureFlagsByKey?.[key]?.filters?.payloads?.[matchValue]\n    }\n\n    // Undefined means a loading or missing data issue. Null means evaluation happened and there was no match\n    if (response === undefined || response === null) {\n      return null\n    }\n\n    try {\n      return JSON.parse(response)\n    } catch {\n      return response\n    }\n  }\n\n  async getAllFlagsAndPayloads(\n    distinctId: string,\n    groups: Record<string, string> = {},\n    personProperties: Record<string, string> = {},\n    groupProperties: Record<string, Record<string, string>> = {}\n  ): Promise<{\n    response: Record<string, FeatureFlagValue>\n    payloads: Record<string, JsonType>\n    fallbackToDecide: boolean\n  }> {\n    await this.loadFeatureFlags()\n\n    const response: Record<string, FeatureFlagValue> = {}\n    const payloads: Record<string, JsonType> = {}\n    let fallbackToDecide = this.featureFlags.length == 0\n\n    await Promise.all(\n      this.featureFlags.map(async (flag) => {\n        try {\n          const matchValue = await this.computeFlagLocally(flag, distinctId, groups, personProperties, groupProperties)\n          response[flag.key] = matchValue\n          const matchPayload = await this.computeFeatureFlagPayloadLocally(flag.key, matchValue)\n          if (matchPayload) {\n            payloads[flag.key] = matchPayload\n          }\n        } catch (e) {\n          if (e instanceof InconclusiveMatchError) {\n            // do nothing\n          } else if (e instanceof Error) {\n            this.onError?.(new Error(`Error computing flag locally: ${flag.key}: ${e}`))\n          }\n          fallbackToDecide = true\n        }\n      })\n    )\n\n    return { response, payloads, fallbackToDecide }\n  }\n\n  async computeFlagLocally(\n    flag: PostHogFeatureFlag,\n    distinctId: string,\n    groups: Record<string, string> = {},\n    personProperties: Record<string, string> = {},\n    groupProperties: Record<string, Record<string, string>> = {}\n  ): Promise<FeatureFlagValue> {\n    if (flag.ensure_experience_continuity) {\n      throw new InconclusiveMatchError('Flag has experience continuity enabled')\n    }\n\n    if (!flag.active) {\n      return false\n    }\n\n    const flagFilters = flag.filters || {}\n    const aggregation_group_type_index = flagFilters.aggregation_group_type_index\n\n    if (aggregation_group_type_index != undefined) {\n      const groupName = this.groupTypeMapping[String(aggregation_group_type_index)]\n\n      if (!groupName) {\n        this.logMsgIfDebug(() =>\n          console.warn(\n            `[FEATURE FLAGS] Unknown group type index ${aggregation_group_type_index} for feature flag ${flag.key}`\n          )\n        )\n        throw new InconclusiveMatchError('Flag has unknown group type index')\n      }\n\n      if (!(groupName in groups)) {\n        this.logMsgIfDebug(() =>\n          console.warn(`[FEATURE FLAGS] Can't compute group feature flag: ${flag.key} without group names passed in`)\n        )\n        return false\n      }\n\n      const focusedGroupProperties = groupProperties[groupName]\n      return await this.matchFeatureFlagProperties(flag, groups[groupName], focusedGroupProperties)\n    } else {\n      return await this.matchFeatureFlagProperties(flag, distinctId, personProperties)\n    }\n  }\n\n  async matchFeatureFlagProperties(\n    flag: PostHogFeatureFlag,\n    distinctId: string,\n    properties: Record<string, string>\n  ): Promise<FeatureFlagValue> {\n    const flagFilters = flag.filters || {}\n    const flagConditions = flagFilters.groups || []\n    let isInconclusive = false\n    let result = undefined\n\n    // # Stable sort conditions with variant overrides to the top. This ensures that if overrides are present, they are\n    // # evaluated first, and the variant override is applied to the first matching condition.\n    const sortedFlagConditions = [...flagConditions].sort((conditionA, conditionB) => {\n      const AHasVariantOverride = !!conditionA.variant\n      const BHasVariantOverride = !!conditionB.variant\n\n      if (AHasVariantOverride && BHasVariantOverride) {\n        return 0\n      } else if (AHasVariantOverride) {\n        return -1\n      } else if (BHasVariantOverride) {\n        return 1\n      } else {\n        return 0\n      }\n    })\n\n    for (const condition of sortedFlagConditions) {\n      try {\n        if (await this.isConditionMatch(flag, distinctId, condition, properties)) {\n          const variantOverride = condition.variant\n          const flagVariants = flagFilters.multivariate?.variants || []\n          if (variantOverride && flagVariants.some((variant) => variant.key === variantOverride)) {\n            result = variantOverride\n          } else {\n            result = (await this.getMatchingVariant(flag, distinctId)) || true\n          }\n          break\n        }\n      } catch (e) {\n        if (e instanceof InconclusiveMatchError) {\n          isInconclusive = true\n        } else {\n          throw e\n        }\n      }\n    }\n\n    if (result !== undefined) {\n      return result\n    } else if (isInconclusive) {\n      throw new InconclusiveMatchError(\"Can't determine if feature flag is enabled or not with given properties\")\n    }\n\n    // We can only return False when all conditions are False\n    return false\n  }\n\n  async isConditionMatch(\n    flag: PostHogFeatureFlag,\n    distinctId: string,\n    condition: FeatureFlagCondition,\n    properties: Record<string, string>\n  ): Promise<boolean> {\n    const rolloutPercentage = condition.rollout_percentage\n    const warnFunction = (msg: string): void => {\n      this.logMsgIfDebug(() => console.warn(msg))\n    }\n    if ((condition.properties || []).length > 0) {\n      for (const prop of condition.properties) {\n        const propertyType = prop.type\n        let matches = false\n\n        if (propertyType === 'cohort') {\n          matches = matchCohort(prop, properties, this.cohorts, this.debugMode)\n        } else {\n          matches = matchProperty(prop, properties, warnFunction)\n        }\n\n        if (!matches) {\n          return false\n        }\n      }\n\n      if (rolloutPercentage == undefined) {\n        return true\n      }\n    }\n\n    if (rolloutPercentage != undefined && (await _hash(flag.key, distinctId)) > rolloutPercentage / 100.0) {\n      return false\n    }\n\n    return true\n  }\n\n  async getMatchingVariant(flag: PostHogFeatureFlag, distinctId: string): Promise<FeatureFlagValue | undefined> {\n    const hashValue = await _hash(flag.key, distinctId, 'variant')\n    const matchingVariant = this.variantLookupTable(flag).find((variant) => {\n      return hashValue >= variant.valueMin && hashValue < variant.valueMax\n    })\n\n    if (matchingVariant) {\n      return matchingVariant.key\n    }\n    return undefined\n  }\n\n  variantLookupTable(flag: PostHogFeatureFlag): { valueMin: number; valueMax: number; key: string }[] {\n    const lookupTable: { valueMin: number; valueMax: number; key: string }[] = []\n    let valueMin = 0\n    let valueMax = 0\n    const flagFilters = flag.filters || {}\n    const multivariates: {\n      key: string\n      rollout_percentage: number\n    }[] = flagFilters.multivariate?.variants || []\n\n    multivariates.forEach((variant) => {\n      valueMax = valueMin + variant.rollout_percentage / 100.0\n      lookupTable.push({ valueMin, valueMax, key: variant.key })\n      valueMin = valueMax\n    })\n    return lookupTable\n  }\n\n  async loadFeatureFlags(forceReload = false): Promise<void> {\n    if (!this.loadedSuccessfullyOnce || forceReload) {\n      await this._loadFeatureFlags()\n    }\n  }\n\n  /**\n   * Returns true if the feature flags poller has loaded successfully at least once and has more than 0 feature flags.\n   * This is useful to check if local evaluation is ready before calling getFeatureFlag.\n   */\n  isLocalEvaluationReady(): boolean {\n    return (this.loadedSuccessfullyOnce ?? false) && (this.featureFlags?.length ?? 0) > 0\n  }\n\n  /**\n   * If a client is misconfigured with an invalid or improper API key, the polling interval is doubled each time\n   * until a successful request is made, up to a maximum of 60 seconds.\n   *\n   * @returns The polling interval to use for the next request.\n   */\n  private getPollingInterval(): number {\n    if (!this.shouldBeginExponentialBackoff) {\n      return this.pollingInterval\n    }\n\n    return Math.min(SIXTY_SECONDS, this.pollingInterval * 2 ** this.backOffCount)\n  }\n\n  async _loadFeatureFlags(): Promise<void> {\n    if (this.poller) {\n      clearTimeout(this.poller)\n      this.poller = undefined\n    }\n\n    this.poller = setTimeout(() => this._loadFeatureFlags(), this.getPollingInterval())\n\n    try {\n      const res = await this._requestFeatureFlagDefinitions()\n\n      // Handle undefined res case, this shouldn't happen, but it doesn't hurt to handle it anyway\n      if (!res) {\n        // Don't override existing flags when something goes wrong\n        return\n      }\n\n      // NB ON ERROR HANDLING & `loadedSuccessfullyOnce`:\n      //\n      // `loadedSuccessfullyOnce` indicates we've successfully loaded a valid set of flags at least once.\n      // If we set it to `true` in an error scenario (e.g. 402 Over Quota, 401 Invalid Key, etc.),\n      // any manual call to `loadFeatureFlags()` (without forceReload) will skip refetching entirely,\n      // leaving us stuck with zero or outdated flags. The poller does keep running, but we also want\n      // manual reloads to be possible as soon as the error condition is resolved.\n      //\n      // Therefore, on error statuses, we do *not* set `loadedSuccessfullyOnce = true`, ensuring that\n      // both the background poller and any subsequent manual calls can keep trying to load flags\n      // once the issue (quota, permission, rate limit, etc.) is resolved.\n      switch (res.status) {\n        case 401:\n          // Invalid API key\n          this.shouldBeginExponentialBackoff = true\n          this.backOffCount += 1\n          throw new ClientError(\n            `Your project key or personal API key is invalid. Setting next polling interval to ${this.getPollingInterval()}ms. More information: https://posthog.com/docs/api#rate-limiting`\n          )\n\n        case 402:\n          // Quota exceeded - clear all flags\n          console.warn(\n            '[FEATURE FLAGS] Feature flags quota limit exceeded - unsetting all local flags. Learn more about billing limits at https://posthog.com/docs/billing/limits-alerts'\n          )\n          this.featureFlags = []\n          this.featureFlagsByKey = {}\n          this.groupTypeMapping = {}\n          this.cohorts = {}\n          return\n\n        case 403:\n          // Permissions issue\n          this.shouldBeginExponentialBackoff = true\n          this.backOffCount += 1\n          throw new ClientError(\n            `Your personal API key does not have permission to fetch feature flag definitions for local evaluation. Setting next polling interval to ${this.getPollingInterval()}ms. Are you sure you're using the correct personal and Project API key pair? More information: https://posthog.com/docs/api/overview`\n          )\n\n        case 429:\n          // Rate limited\n          this.shouldBeginExponentialBackoff = true\n          this.backOffCount += 1\n          throw new ClientError(\n            `You are being rate limited. Setting next polling interval to ${this.getPollingInterval()}ms. More information: https://posthog.com/docs/api#rate-limiting`\n          )\n\n        case 200: {\n          // Process successful response\n          const responseJson = ((await res.json()) as { [key: string]: any }) ?? {}\n          if (!('flags' in responseJson)) {\n            this.onError?.(new Error(`Invalid response when getting feature flags: ${JSON.stringify(responseJson)}`))\n            return\n          }\n\n          this.featureFlags = (responseJson.flags as PostHogFeatureFlag[]) ?? []\n          this.featureFlagsByKey = this.featureFlags.reduce(\n            (acc, curr) => ((acc[curr.key] = curr), acc),\n            <Record<string, PostHogFeatureFlag>>{}\n          )\n          this.groupTypeMapping = (responseJson.group_type_mapping as Record<string, string>) || {}\n          this.cohorts = (responseJson.cohorts as Record<string, PropertyGroup>) || {}\n          this.loadedSuccessfullyOnce = true\n          this.shouldBeginExponentialBackoff = false\n          this.backOffCount = 0\n          this.onLoad?.(this.featureFlags.length)\n          break\n        }\n\n        default:\n          // Something else went wrong, or the server is down.\n          // In this case, don't override existing flags\n          return\n      }\n    } catch (err) {\n      if (err instanceof ClientError) {\n        this.onError?.(err)\n      }\n    }\n  }\n\n  private getPersonalApiKeyRequestOptions(method: 'GET' | 'POST' | 'PUT' | 'PATCH' = 'GET'): PostHogFetchOptions {\n    return {\n      method,\n      headers: {\n        ...this.customHeaders,\n        'Content-Type': 'application/json',\n        Authorization: `Bearer ${this.personalApiKey}`,\n      },\n    }\n  }\n\n  async _requestFeatureFlagDefinitions(): Promise<PostHogFetchResponse> {\n    const url = `${this.host}/api/feature_flag/local_evaluation?token=${this.projectApiKey}&send_cohorts`\n\n    const options = this.getPersonalApiKeyRequestOptions()\n\n    let abortTimeout = null\n\n    if (this.timeout && typeof this.timeout === 'number') {\n      const controller = new AbortController()\n      abortTimeout = safeSetTimeout(() => {\n        controller.abort()\n      }, this.timeout)\n      options.signal = controller.signal\n    }\n\n    try {\n      return await this.fetch(url, options)\n    } finally {\n      clearTimeout(abortTimeout)\n    }\n  }\n\n  stopPoller(): void {\n    clearTimeout(this.poller)\n  }\n\n  _requestRemoteConfigPayload(flagKey: string): Promise<PostHogFetchResponse> {\n    const url = `${this.host}/api/projects/@current/feature_flags/${flagKey}/remote_config/`\n\n    const options = this.getPersonalApiKeyRequestOptions()\n\n    let abortTimeout = null\n    if (this.timeout && typeof this.timeout === 'number') {\n      const controller = new AbortController()\n      abortTimeout = safeSetTimeout(() => {\n        controller.abort()\n      }, this.timeout)\n      options.signal = controller.signal\n    }\n    try {\n      return this.fetch(url, options)\n    } finally {\n      clearTimeout(abortTimeout)\n    }\n  }\n}\n\n// # This function takes a distinct_id and a feature flag key and returns a float between 0 and 1.\n// # Given the same distinct_id and key, it'll always return the same float. These floats are\n// # uniformly distributed between 0 and 1, so if we want to show this feature to 20% of traffic\n// # we can do _hash(key, distinct_id) < 0.2\nasync function _hash(key: string, distinctId: string, salt: string = ''): Promise<number> {\n  const hashString = await hashSHA1(`${key}.${distinctId}${salt}`)\n  return parseInt(hashString.slice(0, 15), 16) / LONG_SCALE\n}\n\nfunction matchProperty(\n  property: FeatureFlagCondition['properties'][number],\n  propertyValues: Record<string, any>,\n  warnFunction?: (msg: string) => void\n): boolean {\n  const key = property.key\n  const value = property.value\n  const operator = property.operator || 'exact'\n\n  if (!(key in propertyValues)) {\n    throw new InconclusiveMatchError(`Property ${key} not found in propertyValues`)\n  } else if (operator === 'is_not_set') {\n    throw new InconclusiveMatchError(`Operator is_not_set is not supported`)\n  }\n\n  const overrideValue = propertyValues[key]\n  if (overrideValue == null && !NULL_VALUES_ALLOWED_OPERATORS.includes(operator)) {\n    // if the value is null, just fail the feature flag comparison\n    // this isn't an InconclusiveMatchError because the property value was provided.\n    if (warnFunction) {\n      warnFunction(`Property ${key} cannot have a value of null/undefined with the ${operator} operator`)\n    }\n\n    return false\n  }\n\n  function computeExactMatch(value: any, overrideValue: any): boolean {\n    if (Array.isArray(value)) {\n      return value.map((val) => String(val).toLowerCase()).includes(String(overrideValue).toLowerCase())\n    }\n    return String(value).toLowerCase() === String(overrideValue).toLowerCase()\n  }\n\n  function compare(lhs: any, rhs: any, operator: string): boolean {\n    if (operator === 'gt') {\n      return lhs > rhs\n    } else if (operator === 'gte') {\n      return lhs >= rhs\n    } else if (operator === 'lt') {\n      return lhs < rhs\n    } else if (operator === 'lte') {\n      return lhs <= rhs\n    } else {\n      throw new Error(`Invalid operator: ${operator}`)\n    }\n  }\n\n  switch (operator) {\n    case 'exact':\n      return computeExactMatch(value, overrideValue)\n    case 'is_not':\n      return !computeExactMatch(value, overrideValue)\n    case 'is_set':\n      return key in propertyValues\n    case 'icontains':\n      return String(overrideValue).toLowerCase().includes(String(value).toLowerCase())\n    case 'not_icontains':\n      return !String(overrideValue).toLowerCase().includes(String(value).toLowerCase())\n    case 'regex':\n      return isValidRegex(String(value)) && String(overrideValue).match(String(value)) !== null\n    case 'not_regex':\n      return isValidRegex(String(value)) && String(overrideValue).match(String(value)) === null\n    case 'gt':\n    case 'gte':\n    case 'lt':\n    case 'lte': {\n      // :TRICKY: We adjust comparison based on the override value passed in,\n      // to make sure we handle both numeric and string comparisons appropriately.\n      let parsedValue = typeof value === 'number' ? value : null\n\n      if (typeof value === 'string') {\n        try {\n          parsedValue = parseFloat(value)\n        } catch (err) {\n          // pass\n        }\n      }\n\n      if (parsedValue != null && overrideValue != null) {\n        // check both null and undefined\n        if (typeof overrideValue === 'string') {\n          return compare(overrideValue, String(value), operator)\n        } else {\n          return compare(overrideValue, parsedValue, operator)\n        }\n      } else {\n        return compare(String(overrideValue), String(value), operator)\n      }\n    }\n    case 'is_date_after':\n    case 'is_date_before': {\n      let parsedDate = relativeDateParseForFeatureFlagMatching(String(value))\n      if (parsedDate == null) {\n        parsedDate = convertToDateTime(value)\n      }\n\n      if (parsedDate == null) {\n        throw new InconclusiveMatchError(`Invalid date: ${value}`)\n      }\n      const overrideDate = convertToDateTime(overrideValue)\n      if (['is_date_before'].includes(operator)) {\n        return overrideDate < parsedDate\n      }\n      return overrideDate > parsedDate\n    }\n    default:\n      throw new InconclusiveMatchError(`Unknown operator: ${operator}`)\n  }\n}\n\nfunction matchCohort(\n  property: FeatureFlagCondition['properties'][number],\n  propertyValues: Record<string, any>,\n  cohortProperties: FeatureFlagsPoller['cohorts'],\n  debugMode: boolean = false\n): boolean {\n  const cohortId = String(property.value)\n  if (!(cohortId in cohortProperties)) {\n    throw new InconclusiveMatchError(\"can't match cohort without a given cohort property value\")\n  }\n\n  const propertyGroup = cohortProperties[cohortId]\n  return matchPropertyGroup(propertyGroup, propertyValues, cohortProperties, debugMode)\n}\n\nfunction matchPropertyGroup(\n  propertyGroup: PropertyGroup,\n  propertyValues: Record<string, any>,\n  cohortProperties: FeatureFlagsPoller['cohorts'],\n  debugMode: boolean = false\n): boolean {\n  if (!propertyGroup) {\n    return true\n  }\n\n  const propertyGroupType = propertyGroup.type\n  const properties = propertyGroup.values\n\n  if (!properties || properties.length === 0) {\n    // empty groups are no-ops, always match\n    return true\n  }\n\n  let errorMatchingLocally = false\n\n  if ('values' in properties[0]) {\n    // a nested property group\n    for (const prop of properties as PropertyGroup[]) {\n      try {\n        const matches = matchPropertyGroup(prop, propertyValues, cohortProperties, debugMode)\n        if (propertyGroupType === 'AND') {\n          if (!matches) {\n            return false\n          }\n        } else {\n          // OR group\n          if (matches) {\n            return true\n          }\n        }\n      } catch (err) {\n        if (err instanceof InconclusiveMatchError) {\n          if (debugMode) {\n            console.debug(`Failed to compute property ${prop} locally: ${err}`)\n          }\n          errorMatchingLocally = true\n        } else {\n          throw err\n        }\n      }\n    }\n\n    if (errorMatchingLocally) {\n      throw new InconclusiveMatchError(\"Can't match cohort without a given cohort property value\")\n    }\n    // if we get here, all matched in AND case, or none matched in OR case\n    return propertyGroupType === 'AND'\n  } else {\n    for (const prop of properties as FlagProperty[]) {\n      try {\n        let matches: boolean\n        if (prop.type === 'cohort') {\n          matches = matchCohort(prop, propertyValues, cohortProperties, debugMode)\n        } else {\n          matches = matchProperty(prop, propertyValues)\n        }\n\n        const negation = prop.negation || false\n\n        if (propertyGroupType === 'AND') {\n          // if negated property, do the inverse\n          if (!matches && !negation) {\n            return false\n          }\n          if (matches && negation) {\n            return false\n          }\n        } else {\n          // OR group\n          if (matches && !negation) {\n            return true\n          }\n          if (!matches && negation) {\n            return true\n          }\n        }\n      } catch (err) {\n        if (err instanceof InconclusiveMatchError) {\n          if (debugMode) {\n            console.debug(`Failed to compute property ${prop} locally: ${err}`)\n          }\n          errorMatchingLocally = true\n        } else {\n          throw err\n        }\n      }\n    }\n\n    if (errorMatchingLocally) {\n      throw new InconclusiveMatchError(\"can't match cohort without a given cohort property value\")\n    }\n\n    // if we get here, all matched in AND case, or none matched in OR case\n    return propertyGroupType === 'AND'\n  }\n}\n\nfunction isValidRegex(regex: string): boolean {\n  try {\n    new RegExp(regex)\n    return true\n  } catch (err) {\n    return false\n  }\n}\n\nfunction convertToDateTime(value: string | number | (string | number)[] | Date): Date {\n  if (value instanceof Date) {\n    return value\n  } else if (typeof value === 'string' || typeof value === 'number') {\n    const date = new Date(value)\n    if (!isNaN(date.valueOf())) {\n      return date\n    }\n    throw new InconclusiveMatchError(`${value} is in an invalid date format`)\n  } else {\n    throw new InconclusiveMatchError(`The date provided ${value} must be a string, number, or date object`)\n  }\n}\n\nfunction relativeDateParseForFeatureFlagMatching(value: string): Date | null {\n  const regex = /^-?(?<number>[0-9]+)(?<interval>[a-z])$/\n  const match = value.match(regex)\n  const parsedDt = new Date(new Date().toISOString())\n\n  if (match) {\n    if (!match.groups) {\n      return null\n    }\n\n    const number = parseInt(match.groups['number'])\n\n    if (number >= 10000) {\n      // Guard against overflow, disallow numbers greater than 10_000\n      return null\n    }\n    const interval = match.groups['interval']\n    if (interval == 'h') {\n      parsedDt.setUTCHours(parsedDt.getUTCHours() - number)\n    } else if (interval == 'd') {\n      parsedDt.setUTCDate(parsedDt.getUTCDate() - number)\n    } else if (interval == 'w') {\n      parsedDt.setUTCDate(parsedDt.getUTCDate() - number * 7)\n    } else if (interval == 'm') {\n      parsedDt.setUTCMonth(parsedDt.getUTCMonth() - number)\n    } else if (interval == 'y') {\n      parsedDt.setUTCFullYear(parsedDt.getUTCFullYear() - number)\n    } else {\n      return null\n    }\n\n    return parsedDt\n  } else {\n    return null\n  }\n}\n\nexport {\n  FeatureFlagsPoller,\n  matchProperty,\n  relativeDateParseForFeatureFlagMatching,\n  InconclusiveMatchError,\n  ClientError,\n}\n", "import { PostHogPersistedProperty } from 'posthog-core'\n\nexport class PostHogMemoryStorage {\n  private _memoryStorage: { [key: string]: any | undefined } = {}\n\n  getProperty(key: PostHogPersistedProperty): any | undefined {\n    return this._memoryStorage[key]\n  }\n\n  setProperty(key: PostHogPersistedProperty, value: any | null): void {\n    this._memoryStorage[key] = value !== null ? value : undefined\n  }\n}\n", "import { version } from '../package.json'\n\nimport {\n  JsonType,\n  PostHogCoreStateless,\n  PostHogDecideResponse,\n  PostHogFetchOptions,\n  PostHogFetchResponse,\n  PostHogFlagsAndPayloadsResponse,\n  PostHogPersistedProperty,\n} from 'posthog-core'\nimport { EventMessage, GroupIdentifyMessage, IdentifyMessage, IPostHog, PostHogOptions } from './types'\nimport { FeatureFlagDetail, FeatureFlagValue } from 'posthog-core'\nimport { FeatureFlagsPoller } from './extensions/feature-flags/feature-flags'\nimport fetch from './fetch'\nimport ErrorTracking from './extensions/error-tracking'\nimport { getFeatureFlagValue } from 'posthog-core'\nimport { PostHogMemoryStorage } from './storage-memory'\n\n// Standard local evaluation rate limit is 600 per minute (10 per second),\n// so the fastest a poller should ever be set is 100ms.\nconst MINIMUM_POLLING_INTERVAL = 100\nconst THIRTY_SECONDS = 30 * 1000\nconst MAX_CACHE_SIZE = 50 * 1000\n\n// The actual exported Nodejs API.\nexport abstract class PostHogBackendClient extends PostHogCoreStateless implements IPostHog {\n  private _memoryStorage = new PostHogMemoryStorage()\n\n  private featureFlagsPoller?: FeatureFlagsPoller\n  protected errorTracking: ErrorTracking\n  private maxCacheSize: number\n  public readonly options: PostHogOptions\n\n  distinctIdHasSentFlagCalls: Record<string, string[]>\n\n  constructor(apiKey: string, options: PostHogOptions = {}) {\n    super(apiKey, options)\n\n    this.options = options\n\n    this.options.featureFlagsPollingInterval =\n      typeof options.featureFlagsPollingInterval === 'number'\n        ? Math.max(options.featureFlagsPollingInterval, MINIMUM_POLLING_INTERVAL)\n        : THIRTY_SECONDS\n\n    if (options.personalApiKey) {\n      if (options.personalApiKey.includes('phc_')) {\n        throw new Error(\n          'Your Personal API key is invalid. These keys are prefixed with \"phx_\" and can be created in PostHog project settings.'\n        )\n      }\n\n      this.featureFlagsPoller = new FeatureFlagsPoller({\n        pollingInterval: this.options.featureFlagsPollingInterval,\n        personalApiKey: options.personalApiKey,\n        projectApiKey: apiKey,\n        timeout: options.requestTimeout ?? 10000, // 10 seconds\n        host: this.host,\n        fetch: options.fetch,\n        onError: (err: Error) => {\n          this._events.emit('error', err)\n        },\n        onLoad: (count: number) => {\n          this._events.emit('localEvaluationFlagsLoaded', count)\n        },\n        customHeaders: this.getCustomHeaders(),\n      })\n    }\n    this.errorTracking = new ErrorTracking(this, options)\n    this.distinctIdHasSentFlagCalls = {}\n    this.maxCacheSize = options.maxCacheSize || MAX_CACHE_SIZE\n  }\n\n  getPersistedProperty(key: PostHogPersistedProperty): any | undefined {\n    return this._memoryStorage.getProperty(key)\n  }\n\n  setPersistedProperty(key: PostHogPersistedProperty, value: any | null): void {\n    return this._memoryStorage.setProperty(key, value)\n  }\n\n  fetch(url: string, options: PostHogFetchOptions): Promise<PostHogFetchResponse> {\n    return this.options.fetch ? this.options.fetch(url, options) : fetch(url, options)\n  }\n  getLibraryVersion(): string {\n    return version\n  }\n  getCustomUserAgent(): string {\n    return `${this.getLibraryId()}/${this.getLibraryVersion()}`\n  }\n\n  enable(): Promise<void> {\n    return super.optIn()\n  }\n\n  disable(): Promise<void> {\n    return super.optOut()\n  }\n\n  debug(enabled: boolean = true): void {\n    super.debug(enabled)\n    this.featureFlagsPoller?.debug(enabled)\n  }\n\n  capture(props: EventMessage): void {\n    if (typeof props === 'string') {\n      this.logMsgIfDebug(() =>\n        console.warn('Called capture() with a string as the first argument when an object was expected.')\n      )\n    }\n    const { distinctId, event, properties, groups, sendFeatureFlags, timestamp, disableGeoip, uuid }: EventMessage =\n      props\n    const _capture = (props: EventMessage['properties']): void => {\n      super.captureStateless(distinctId, event, props, { timestamp, disableGeoip, uuid })\n    }\n\n    const _getFlags = async (\n      distinctId: EventMessage['distinctId'],\n      groups: EventMessage['groups'],\n      disableGeoip: EventMessage['disableGeoip']\n    ): Promise<PostHogDecideResponse['featureFlags'] | undefined> => {\n      return (await super.getFeatureFlagsStateless(distinctId, groups, undefined, undefined, disableGeoip)).flags\n    }\n\n    // :TRICKY: If we flush, or need to shut down, to not lose events we want this promise to resolve before we flush\n    const capturePromise = Promise.resolve()\n      .then(async () => {\n        if (sendFeatureFlags) {\n          // If we are sending feature flags, we need to make sure we have the latest flags\n          // return await super.getFeatureFlagsStateless(distinctId, groups, undefined, undefined, disableGeoip)\n          return await _getFlags(distinctId, groups, disableGeoip)\n        }\n\n        if (event === '$feature_flag_called') {\n          // If we're capturing a $feature_flag_called event, we don't want to enrich the event with cached flags that may be out of date.\n          return {}\n        }\n\n        if ((this.featureFlagsPoller?.featureFlags?.length || 0) > 0) {\n          // Otherwise we may as well check for the flags locally and include them if they are already loaded\n          const groupsWithStringValues: Record<string, string> = {}\n          for (const [key, value] of Object.entries(groups || {})) {\n            groupsWithStringValues[key] = String(value)\n          }\n\n          return await this.getAllFlags(distinctId, {\n            groups: groupsWithStringValues,\n            disableGeoip,\n            onlyEvaluateLocally: true,\n          })\n        }\n        return {}\n      })\n      .then((flags) => {\n        // Derive the relevant flag properties to add\n        const additionalProperties: Record<string, any> = {}\n        if (flags) {\n          for (const [feature, variant] of Object.entries(flags)) {\n            additionalProperties[`$feature/${feature}`] = variant\n          }\n        }\n        const activeFlags = Object.keys(flags || {})\n          .filter((flag) => flags?.[flag] !== false)\n          .sort()\n        if (activeFlags.length > 0) {\n          additionalProperties['$active_feature_flags'] = activeFlags\n        }\n\n        return additionalProperties\n      })\n      .catch(() => {\n        // Something went wrong getting the flag info - we should capture the event anyways\n        return {}\n      })\n      .then((additionalProperties) => {\n        // No matter what - capture the event\n        _capture({ ...additionalProperties, ...properties, $groups: groups })\n      })\n\n    this.addPendingPromise(capturePromise)\n  }\n\n  async captureImmediate(props: EventMessage): Promise<void> {\n    if (typeof props === 'string') {\n      this.logMsgIfDebug(() =>\n        console.warn('Called capture() with a string as the first argument when an object was expected.')\n      )\n    }\n    const { distinctId, event, properties, groups, sendFeatureFlags, timestamp, disableGeoip, uuid }: EventMessage =\n      props\n\n    const _capture = (props: EventMessage['properties']): Promise<void> => {\n      return super.captureStatelessImmediate(distinctId, event, props, { timestamp, disableGeoip, uuid })\n    }\n\n    const _getFlags = async (\n      distinctId: EventMessage['distinctId'],\n      groups: EventMessage['groups'],\n      disableGeoip: EventMessage['disableGeoip']\n    ): Promise<PostHogDecideResponse['featureFlags'] | undefined> => {\n      return (await super.getFeatureFlagsStateless(distinctId, groups, undefined, undefined, disableGeoip)).flags\n    }\n\n    const capturePromise = Promise.resolve()\n      .then(async () => {\n        if (sendFeatureFlags) {\n          // If we are sending feature flags, we need to make sure we have the latest flags\n          // return await super.getFeatureFlagsStateless(distinctId, groups, undefined, undefined, disableGeoip)\n          return await _getFlags(distinctId, groups, disableGeoip)\n        }\n\n        if (event === '$feature_flag_called') {\n          // If we're capturing a $feature_flag_called event, we don't want to enrich the event with cached flags that may be out of date.\n          return {}\n        }\n\n        if ((this.featureFlagsPoller?.featureFlags?.length || 0) > 0) {\n          // Otherwise we may as well check for the flags locally and include them if they are already loaded\n          const groupsWithStringValues: Record<string, string> = {}\n          for (const [key, value] of Object.entries(groups || {})) {\n            groupsWithStringValues[key] = String(value)\n          }\n\n          return await this.getAllFlags(distinctId, {\n            groups: groupsWithStringValues,\n            disableGeoip,\n            onlyEvaluateLocally: true,\n          })\n        }\n        return {}\n      })\n      .then((flags) => {\n        // Derive the relevant flag properties to add\n        const additionalProperties: Record<string, any> = {}\n        if (flags) {\n          for (const [feature, variant] of Object.entries(flags)) {\n            additionalProperties[`$feature/${feature}`] = variant\n          }\n        }\n        const activeFlags = Object.keys(flags || {})\n          .filter((flag) => flags?.[flag] !== false)\n          .sort()\n        if (activeFlags.length > 0) {\n          additionalProperties['$active_feature_flags'] = activeFlags\n        }\n\n        return additionalProperties\n      })\n      .catch(() => {\n        // Something went wrong getting the flag info - we should capture the event anyways\n        return {}\n      })\n      .then((additionalProperties) => {\n        // No matter what - capture the event\n        _capture({ ...additionalProperties, ...properties, $groups: groups })\n      })\n\n    await capturePromise\n  }\n\n  identify({ distinctId, properties, disableGeoip }: IdentifyMessage): void {\n    // Catch properties passed as $set and move them to the top level\n\n    // promote $set and $set_once to top level\n    const userPropsOnce = properties?.$set_once\n    delete properties?.$set_once\n\n    // if no $set is provided we assume all properties are $set\n    const userProps = properties?.$set || properties\n\n    super.identifyStateless(\n      distinctId,\n      {\n        $set: userProps,\n        $set_once: userPropsOnce,\n      },\n      { disableGeoip }\n    )\n  }\n\n  async identifyImmediate({ distinctId, properties, disableGeoip }: IdentifyMessage): Promise<void> {\n    // promote $set and $set_once to top level\n    const userPropsOnce = properties?.$set_once\n    delete properties?.$set_once\n\n    // if no $set is provided we assume all properties are $set\n    const userProps = properties?.$set || properties\n\n    await super.identifyStatelessImmediate(\n      distinctId,\n      {\n        $set: userProps,\n        $set_once: userPropsOnce,\n      },\n      { disableGeoip }\n    )\n  }\n\n  alias(data: { distinctId: string; alias: string; disableGeoip?: boolean }): void {\n    super.aliasStateless(data.alias, data.distinctId, undefined, { disableGeoip: data.disableGeoip })\n  }\n\n  async aliasImmediate(data: { distinctId: string; alias: string; disableGeoip?: boolean }): Promise<void> {\n    await super.aliasStatelessImmediate(data.alias, data.distinctId, undefined, { disableGeoip: data.disableGeoip })\n  }\n\n  isLocalEvaluationReady(): boolean {\n    return this.featureFlagsPoller?.isLocalEvaluationReady() ?? false\n  }\n\n  async waitForLocalEvaluationReady(timeoutMs: number = THIRTY_SECONDS): Promise<boolean> {\n    if (this.isLocalEvaluationReady()) {\n      return true\n    }\n\n    if (this.featureFlagsPoller === undefined) {\n      return false\n    }\n\n    return new Promise((resolve) => {\n      const timeout = setTimeout(() => {\n        cleanup()\n        resolve(false)\n      }, timeoutMs)\n\n      const cleanup = this._events.on('localEvaluationFlagsLoaded', (count: number) => {\n        clearTimeout(timeout)\n        cleanup()\n        resolve(count > 0)\n      })\n    })\n  }\n\n  async getFeatureFlag(\n    key: string,\n    distinctId: string,\n    options?: {\n      groups?: Record<string, string>\n      personProperties?: Record<string, string>\n      groupProperties?: Record<string, Record<string, string>>\n      onlyEvaluateLocally?: boolean\n      sendFeatureFlagEvents?: boolean\n      disableGeoip?: boolean\n    }\n  ): Promise<FeatureFlagValue | undefined> {\n    const { groups, disableGeoip } = options || {}\n    let { onlyEvaluateLocally, sendFeatureFlagEvents, personProperties, groupProperties } = options || {}\n\n    const adjustedProperties = this.addLocalPersonAndGroupProperties(\n      distinctId,\n      groups,\n      personProperties,\n      groupProperties\n    )\n\n    personProperties = adjustedProperties.allPersonProperties\n    groupProperties = adjustedProperties.allGroupProperties\n\n    // set defaults\n    if (onlyEvaluateLocally == undefined) {\n      onlyEvaluateLocally = false\n    }\n    if (sendFeatureFlagEvents == undefined) {\n      sendFeatureFlagEvents = true\n    }\n\n    let response = await this.featureFlagsPoller?.getFeatureFlag(\n      key,\n      distinctId,\n      groups,\n      personProperties,\n      groupProperties\n    )\n\n    const flagWasLocallyEvaluated = response !== undefined\n    let requestId = undefined\n    let flagDetail: FeatureFlagDetail | undefined = undefined\n    if (!flagWasLocallyEvaluated && !onlyEvaluateLocally) {\n      const remoteResponse = await super.getFeatureFlagDetailStateless(\n        key,\n        distinctId,\n        groups,\n        personProperties,\n        groupProperties,\n        disableGeoip\n      )\n\n      if (remoteResponse === undefined) {\n        return undefined\n      }\n\n      flagDetail = remoteResponse.response\n      response = getFeatureFlagValue(flagDetail)\n      requestId = remoteResponse?.requestId\n    }\n\n    const featureFlagReportedKey = `${key}_${response}`\n\n    if (\n      sendFeatureFlagEvents &&\n      (!(distinctId in this.distinctIdHasSentFlagCalls) ||\n        !this.distinctIdHasSentFlagCalls[distinctId].includes(featureFlagReportedKey))\n    ) {\n      if (Object.keys(this.distinctIdHasSentFlagCalls).length >= this.maxCacheSize) {\n        this.distinctIdHasSentFlagCalls = {}\n      }\n      if (Array.isArray(this.distinctIdHasSentFlagCalls[distinctId])) {\n        this.distinctIdHasSentFlagCalls[distinctId].push(featureFlagReportedKey)\n      } else {\n        this.distinctIdHasSentFlagCalls[distinctId] = [featureFlagReportedKey]\n      }\n      this.capture({\n        distinctId,\n        event: '$feature_flag_called',\n        properties: {\n          $feature_flag: key,\n          $feature_flag_response: response,\n          $feature_flag_id: flagDetail?.metadata?.id,\n          $feature_flag_version: flagDetail?.metadata?.version,\n          $feature_flag_reason: flagDetail?.reason?.description ?? flagDetail?.reason?.code,\n          locally_evaluated: flagWasLocallyEvaluated,\n          [`$feature/${key}`]: response,\n          $feature_flag_request_id: requestId,\n        },\n        groups,\n        disableGeoip,\n      })\n    }\n    return response\n  }\n\n  async getFeatureFlagPayload(\n    key: string,\n    distinctId: string,\n    matchValue?: FeatureFlagValue,\n    options?: {\n      groups?: Record<string, string>\n      personProperties?: Record<string, string>\n      groupProperties?: Record<string, Record<string, string>>\n      onlyEvaluateLocally?: boolean\n      sendFeatureFlagEvents?: boolean\n      disableGeoip?: boolean\n    }\n  ): Promise<JsonType | undefined> {\n    const { groups, disableGeoip } = options || {}\n    let { onlyEvaluateLocally, sendFeatureFlagEvents, personProperties, groupProperties } = options || {}\n\n    const adjustedProperties = this.addLocalPersonAndGroupProperties(\n      distinctId,\n      groups,\n      personProperties,\n      groupProperties\n    )\n\n    personProperties = adjustedProperties.allPersonProperties\n    groupProperties = adjustedProperties.allGroupProperties\n\n    let response = undefined\n\n    const localEvaluationEnabled = this.featureFlagsPoller !== undefined\n    if (localEvaluationEnabled) {\n      // Try to get match value locally if not provided\n      if (!matchValue) {\n        matchValue = await this.getFeatureFlag(key, distinctId, {\n          ...options,\n          onlyEvaluateLocally: true,\n          sendFeatureFlagEvents: false,\n        })\n      }\n\n      if (matchValue) {\n        response = await this.featureFlagsPoller?.computeFeatureFlagPayloadLocally(key, matchValue)\n      }\n    }\n    //}\n\n    // set defaults\n    if (onlyEvaluateLocally == undefined) {\n      onlyEvaluateLocally = false\n    }\n    if (sendFeatureFlagEvents == undefined) {\n      sendFeatureFlagEvents = true\n    }\n\n    // set defaults\n    if (onlyEvaluateLocally == undefined) {\n      onlyEvaluateLocally = false\n    }\n\n    const payloadWasLocallyEvaluated = response !== undefined\n\n    if (!payloadWasLocallyEvaluated && !onlyEvaluateLocally) {\n      response = await super.getFeatureFlagPayloadStateless(\n        key,\n        distinctId,\n        groups,\n        personProperties,\n        groupProperties,\n        disableGeoip\n      )\n    }\n    return response\n  }\n\n  async getRemoteConfigPayload(flagKey: string): Promise<JsonType | undefined> {\n    return (await this.featureFlagsPoller?._requestRemoteConfigPayload(flagKey))?.json()\n  }\n\n  async isFeatureEnabled(\n    key: string,\n    distinctId: string,\n    options?: {\n      groups?: Record<string, string>\n      personProperties?: Record<string, string>\n      groupProperties?: Record<string, Record<string, string>>\n      onlyEvaluateLocally?: boolean\n      sendFeatureFlagEvents?: boolean\n      disableGeoip?: boolean\n    }\n  ): Promise<boolean | undefined> {\n    const feat = await this.getFeatureFlag(key, distinctId, options)\n    if (feat === undefined) {\n      return undefined\n    }\n    return !!feat || false\n  }\n\n  async getAllFlags(\n    distinctId: string,\n    options?: {\n      groups?: Record<string, string>\n      personProperties?: Record<string, string>\n      groupProperties?: Record<string, Record<string, string>>\n      onlyEvaluateLocally?: boolean\n      disableGeoip?: boolean\n    }\n  ): Promise<Record<string, FeatureFlagValue>> {\n    const response = await this.getAllFlagsAndPayloads(distinctId, options)\n    return response.featureFlags || {}\n  }\n\n  async getAllFlagsAndPayloads(\n    distinctId: string,\n    options?: {\n      groups?: Record<string, string>\n      personProperties?: Record<string, string>\n      groupProperties?: Record<string, Record<string, string>>\n      onlyEvaluateLocally?: boolean\n      disableGeoip?: boolean\n    }\n  ): Promise<PostHogFlagsAndPayloadsResponse> {\n    const { groups, disableGeoip } = options || {}\n    let { onlyEvaluateLocally, personProperties, groupProperties } = options || {}\n\n    const adjustedProperties = this.addLocalPersonAndGroupProperties(\n      distinctId,\n      groups,\n      personProperties,\n      groupProperties\n    )\n\n    personProperties = adjustedProperties.allPersonProperties\n    groupProperties = adjustedProperties.allGroupProperties\n\n    // set defaults\n    if (onlyEvaluateLocally == undefined) {\n      onlyEvaluateLocally = false\n    }\n\n    const localEvaluationResult = await this.featureFlagsPoller?.getAllFlagsAndPayloads(\n      distinctId,\n      groups,\n      personProperties,\n      groupProperties\n    )\n\n    let featureFlags = {}\n    let featureFlagPayloads = {}\n    let fallbackToDecide = true\n    if (localEvaluationResult) {\n      featureFlags = localEvaluationResult.response\n      featureFlagPayloads = localEvaluationResult.payloads\n      fallbackToDecide = localEvaluationResult.fallbackToDecide\n    }\n\n    if (fallbackToDecide && !onlyEvaluateLocally) {\n      const remoteEvaluationResult = await super.getFeatureFlagsAndPayloadsStateless(\n        distinctId,\n        groups,\n        personProperties,\n        groupProperties,\n        disableGeoip\n      )\n      featureFlags = {\n        ...featureFlags,\n        ...(remoteEvaluationResult.flags || {}),\n      }\n      featureFlagPayloads = {\n        ...featureFlagPayloads,\n        ...(remoteEvaluationResult.payloads || {}),\n      }\n    }\n\n    return { featureFlags, featureFlagPayloads }\n  }\n\n  groupIdentify({ groupType, groupKey, properties, distinctId, disableGeoip }: GroupIdentifyMessage): void {\n    super.groupIdentifyStateless(groupType, groupKey, properties, { disableGeoip }, distinctId)\n  }\n\n  /**\n   * Reloads the feature flag definitions from the server for local evaluation.\n   * This is useful to call if you want to ensure that the feature flags are up to date before calling getFeatureFlag.\n   */\n  async reloadFeatureFlags(): Promise<void> {\n    await this.featureFlagsPoller?.loadFeatureFlags(true)\n  }\n\n  async _shutdown(shutdownTimeoutMs?: number): Promise<void> {\n    this.featureFlagsPoller?.stopPoller()\n    return super._shutdown(shutdownTimeoutMs)\n  }\n\n  private addLocalPersonAndGroupProperties(\n    distinctId: string,\n    groups?: Record<string, string>,\n    personProperties?: Record<string, string>,\n    groupProperties?: Record<string, Record<string, string>>\n  ): { allPersonProperties: Record<string, string>; allGroupProperties: Record<string, Record<string, string>> } {\n    const allPersonProperties = { distinct_id: distinctId, ...(personProperties || {}) }\n\n    const allGroupProperties: Record<string, Record<string, string>> = {}\n    if (groups) {\n      for (const groupName of Object.keys(groups)) {\n        allGroupProperties[groupName] = {\n          $group_key: groups[groupName],\n          ...(groupProperties?.[groupName] || {}),\n        }\n      }\n    }\n\n    return { allPersonProperties, allGroupProperties }\n  }\n\n  captureException(error: unknown, distinctId?: string, additionalProperties?: Record<string | number, any>): void {\n    const syntheticException = new Error('PostHog syntheticException')\n    ErrorTracking.captureException(this, error, { syntheticException }, distinctId, additionalProperties)\n  }\n}\n", "// Portions of this file are derived from getsentry/sentry-javascript by Software, Inc. dba Sentry\n// Licensed under the MIT License\n\nimport { GetModuleFn, StackFrame, StackLineParser, StackLineParserFn, StackParser } from './types'\n\n// This was originally forked from https://github.com/csnover/TraceKit, and was largely\n// re-written as part of raven - js.\n//\n// This code was later copied to the JavaScript mono - repo and further modified and\n// refactored over the years.\n\n// Copyright (c) 2013 Onur <NAME_EMAIL> and all TraceKit contributors.\n//\n// Permission is hereby granted, free of charge, to any person obtaining a copy of this\n// software and associated documentation files(the 'Software'), to deal in the Software\n// without restriction, including without limitation the rights to use, copy, modify,\n// merge, publish, distribute, sublicense, and / or sell copies of the Software, and to\n// permit persons to whom the Software is furnished to do so, subject to the following\n// conditions:\n//\n// The above copyright notice and this permission notice shall be included in all copies\n// or substantial portions of the Software.\n//\n// THE SOFTWARE IS PROVIDED 'AS IS', WITHOUT WARRANTY OF ANY KIND, EXPRESS OR IMPLIED,\n// INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY, FITNESS FOR A\n// PARTICULAR PURPOSE AND NONINFRINGEMENT.IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT\n// HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF\n// CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE\n// OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n\nconst WEBPACK_ERROR_REGEXP = /\\(error: (.*)\\)/\nconst STACKTRACE_FRAME_LIMIT = 50\n\nconst UNKNOWN_FUNCTION = '?'\n\n/** Node Stack line parser */\nfunction node(getModule?: GetModuleFn): StackLineParserFn {\n  const FILENAME_MATCH = /^\\s*[-]{4,}$/\n  const FULL_MATCH = /at (?:async )?(?:(.+?)\\s+\\()?(?:(.+):(\\d+):(\\d+)?|([^)]+))\\)?/\n\n  return (line: string) => {\n    const lineMatch = line.match(FULL_MATCH)\n\n    if (lineMatch) {\n      let object: string | undefined\n      let method: string | undefined\n      let functionName: string | undefined\n      let typeName: string | undefined\n      let methodName: string | undefined\n\n      if (lineMatch[1]) {\n        functionName = lineMatch[1]\n\n        let methodStart = functionName.lastIndexOf('.')\n        if (functionName[methodStart - 1] === '.') {\n          methodStart--\n        }\n\n        if (methodStart > 0) {\n          object = functionName.slice(0, methodStart)\n          method = functionName.slice(methodStart + 1)\n          const objectEnd = object.indexOf('.Module')\n          if (objectEnd > 0) {\n            functionName = functionName.slice(objectEnd + 1)\n            object = object.slice(0, objectEnd)\n          }\n        }\n        typeName = undefined\n      }\n\n      if (method) {\n        typeName = object\n        methodName = method\n      }\n\n      if (method === '<anonymous>') {\n        methodName = undefined\n        functionName = undefined\n      }\n\n      if (functionName === undefined) {\n        methodName = methodName || UNKNOWN_FUNCTION\n        functionName = typeName ? `${typeName}.${methodName}` : methodName\n      }\n\n      let filename = lineMatch[2]?.startsWith('file://') ? lineMatch[2].slice(7) : lineMatch[2]\n      const isNative = lineMatch[5] === 'native'\n\n      // If it's a Windows path, trim the leading slash so that `/C:/foo` becomes `C:/foo`\n      if (filename?.match(/\\/[A-Z]:/)) {\n        filename = filename.slice(1)\n      }\n\n      if (!filename && lineMatch[5] && !isNative) {\n        filename = lineMatch[5]\n      }\n\n      return {\n        filename: filename ? decodeURI(filename) : undefined,\n        module: getModule ? getModule(filename) : undefined,\n        function: functionName,\n        lineno: _parseIntOrUndefined(lineMatch[3]),\n        colno: _parseIntOrUndefined(lineMatch[4]),\n        in_app: filenameIsInApp(filename || '', isNative),\n        platform: 'node:javascript',\n      }\n    }\n\n    if (line.match(FILENAME_MATCH)) {\n      return {\n        filename: line,\n        platform: 'node:javascript',\n      }\n    }\n\n    return undefined\n  }\n}\n\n/**\n * Does this filename look like it's part of the app code?\n */\nfunction filenameIsInApp(filename: string, isNative: boolean = false): boolean {\n  const isInternal =\n    isNative ||\n    (filename &&\n      // It's not internal if it's an absolute linux path\n      !filename.startsWith('/') &&\n      // It's not internal if it's an absolute windows path\n      !filename.match(/^[A-Z]:/) &&\n      // It's not internal if the path is starting with a dot\n      !filename.startsWith('.') &&\n      // It's not internal if the frame has a protocol. In node, this is usually the case if the file got pre-processed with a bundler like webpack\n      !filename.match(/^[a-zA-Z]([a-zA-Z0-9.\\-+])*:\\/\\//)) // Schema from: https://stackoverflow.com/a/3641782\n\n  // in_app is all that's not an internal Node function or a module within node_modules\n  // note that isNative appears to return true even for node core libraries\n  // see https://github.com/getsentry/raven-node/issues/176\n\n  return !isInternal && filename !== undefined && !filename.includes('node_modules/')\n}\n\nfunction _parseIntOrUndefined(input: string | undefined): number | undefined {\n  return parseInt(input || '', 10) || undefined\n}\n\nfunction nodeStackLineParser(getModule?: GetModuleFn): StackLineParser {\n  return [90, node(getModule)]\n}\n\nexport function createStackParser(getModule?: GetModuleFn): StackParser {\n  const parsers = [nodeStackLineParser(getModule)]\n  const sortedParsers = parsers.sort((a, b) => a[0] - b[0]).map((p) => p[1])\n\n  return (stack: string, skipFirstLines: number = 0): StackFrame[] => {\n    const frames: StackFrame[] = []\n    const lines = stack.split('\\n')\n\n    for (let i = skipFirstLines; i < lines.length; i++) {\n      const line = lines[i] as string\n      // Ignore lines over 1kb as they are unlikely to be stack frames.\n      if (line.length > 1024) {\n        continue\n      }\n\n      // https://github.com/getsentry/sentry-javascript/issues/5459\n      // Remove webpack (error: *) wrappers\n      const cleanedLine = WEBPACK_ERROR_REGEXP.test(line) ? line.replace(WEBPACK_ERROR_REGEXP, '$1') : line\n\n      // https://github.com/getsentry/sentry-javascript/issues/7813\n      // Skip Error: lines\n      if (cleanedLine.match(/\\S*Error: /)) {\n        continue\n      }\n\n      for (const parser of sortedParsers) {\n        const frame = parser(cleanedLine)\n\n        if (frame) {\n          frames.push(frame)\n          break\n        }\n      }\n\n      if (frames.length >= STACKTRACE_FRAME_LIMIT) {\n        break\n      }\n    }\n\n    return reverseAndStripFrames(frames)\n  }\n}\n\nfunction reverseAndStripFrames(stack: ReadonlyArray<StackFrame>): StackFrame[] {\n  if (!stack.length) {\n    return []\n  }\n\n  const localStack = Array.from(stack)\n\n  localStack.reverse()\n\n  return localStack.slice(0, STACKTRACE_FRAME_LIMIT).map((frame) => ({\n    ...frame,\n    filename: frame.filename || getLastStackFrame(localStack).filename,\n    function: frame.function || UNKNOWN_FUNCTION,\n  }))\n}\n\nfunction getLastStackFrame(arr: StackFrame[]): StackFrame {\n  return arr[arr.length - 1] || {}\n}\n", "export * from '../exports'\n\nimport { createGetModuleFromFilename } from '../extensions/error-tracking/get-module.node'\nimport { addSourceContext } from '../extensions/error-tracking/context-lines.node'\nimport ErrorTracking from '../extensions/error-tracking'\n\nimport { PostHogBackendClient } from '../client'\nimport { createStackParser } from '../extensions/error-tracking/stack-parser'\n\nErrorTracking.stackParser = createStackParser(createGetModuleFromFilename())\nErrorTracking.frameModifiers = [addSourceContext]\n\nexport class PostHog extends PostHogBackendClient {\n  getLibraryId(): string {\n    return 'posthog-node'\n  }\n}\n"], "names": ["NAME", "createEventProcessor", "_posthog", "organization", "projectId", "prefix", "severityAllowList", "event", "shouldProcessLevel", "includes", "level", "tags", "userId", "PostHogSentryIntegration", "POSTHOG_ID_TAG", "undefined", "uiHost", "options", "host", "personUrl", "URL", "<PERSON><PERSON><PERSON><PERSON>", "toString", "exceptions", "exception", "values", "exceptionList", "map", "stacktrace", "type", "frames", "frame", "platform", "properties", "$exception_message", "value", "message", "$exception_type", "$exception_personURL", "$exception_level", "$exception_list", "$sentry_event_id", "event_id", "$sentry_exception", "$sentry_exception_message", "$sentry_exception_type", "$sentry_tags", "capture", "distinctId", "sentryIntegration", "processor", "name", "processEvent", "constructor", "setupOnce", "addGlobalEventProcessor", "getCurrentHub", "getClient", "getDsn", "makeUncaughtExceptionHandler", "captureFn", "onFatalFn", "calledFatalError", "Object", "assign", "error", "userProvidedListenersCount", "global", "process", "listeners", "filter", "listener", "_posthog<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "length", "processWouldExit", "mechanism", "handled", "addUncaughtExceptionListener", "on", "addUnhandledRejectionListener", "reason", "isEvent", "candidate", "Event", "isInstanceOf", "isPlainObject", "isBuiltin", "isError", "prototype", "call", "Error", "base", "isErrorEvent", "className", "propertiesFromUnknownInput", "stack<PERSON>arser", "frameModifiers", "input", "hint", "providedMechanism", "errorList", "getErrorList", "Promise", "all", "exceptionFromError", "getError", "cause", "synthetic", "errorFromProp", "getErrorPropertyFromObject", "getMessageForObject", "ex", "syntheticException", "obj", "prop", "hasOwnProperty", "keys", "extractExceptionKeysForMessage", "getObjectClassName", "getPrototypeOf", "e", "max<PERSON><PERSON><PERSON>", "convertToPlainObject", "sort", "firstKey", "truncate", "<PERSON><PERSON><PERSON><PERSON>", "serialized", "slice", "join", "str", "max", "stack", "getOwnProperties", "newObj", "target", "serializeEventTarget", "currentTarget", "extractedProps", "property", "_oO", "parseStackFrames", "modifier", "SHUTDOWN_TIMEOUT", "ErrorTracking", "captureException", "client", "additionalProperties", "$process_person_profile", "exceptionProperties", "uuidv7", "_exceptionAutocaptureEnabled", "enableExceptionAutocapture", "startAutocaptureIfEnabled", "isEnabled", "onException", "bind", "onFatalError", "shutdown", "isDisabled", "setupExpressErrorHandler", "app", "use", "_", "__", "next", "createGetModuleFromFilename", "basePath", "argv", "dirname", "cwd", "isWindows", "sep", "normalizedBase", "normalizeWindowsPath", "filename", "normalizedFilename", "dir", "file", "ext", "posix", "parse", "decodedFile", "decodeURIComponent", "n", "lastIndexOf", "replace", "startsWith", "moduleName", "path", "ReduceableCache", "_maxSize", "_cache", "Map", "get", "key", "delete", "set", "reduce", "size", "LRU_FILE_CONTENTS_CACHE", "LRU_FILE_CONTENTS_FS_READ_FAILED", "DEFAULT_LINES_OF_CONTEXT", "MAX_CONTEXTLINES_COLNO", "MAX_CONTEXTLINES_LINENO", "addSourceContext", "filesToLines", "i", "lineno", "shouldSkipContextLinesForFile", "shouldSkipContextLinesForFrame", "filesToLinesOutput", "push", "files", "readlinePromises", "filesToLineRanges", "a", "b", "ranges", "makeLineReaderRanges", "every", "r", "rangeExistsInContentCache", "cache", "emplace", "getContextLinesFromFile", "catch", "addSourceContextToFrames", "output", "resolve", "stream", "createReadStream", "lineReaded", "createInterface", "destroyStreamAndResolve", "destroy", "lineNumber", "currentRangeIndex", "range", "rangeStart", "rangeEnd", "onStreamError", "close", "removeAllListeners", "line", "snipLine", "context_line", "contents", "addContextToFrame", "pre_context", "makeRangeStart", "clearLineContext", "end", "makeRangeEnd", "post_context", "endsWith", "colno", "lines", "current", "makeContextRange", "out", "Math", "newLine", "lineLength", "start", "min", "_fetch", "getFetch", "axios", "require", "url", "res", "request", "headers", "method", "toLowerCase", "data", "body", "signal", "validateStatus", "status", "text", "json", "Lazy", "factory", "getValue", "initializationPromise", "result", "isInitialized", "waitForInitialization", "nodeCrypto", "getNodeCrypto", "webCrypto", "globalThis", "crypto", "subtle", "webcrypto", "getWebCrypto", "hashSHA1", "createHash", "update", "digest", "hash<PERSON><PERSON><PERSON>", "TextEncoder", "encode", "hashArray", "Array", "from", "Uint8Array", "byte", "padStart", "SIXTY_SECONDS", "LONG_SCALE", "NULL_VALUES_ALLOWED_OPERATORS", "ClientError", "captureStackTrace", "setPrototypeOf", "InconclusiveMatchError", "FeatureFlagsPoller", "pollingInterval", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "projectApiKey", "timeout", "customHeaders", "debugMode", "shouldBeginExponentialBackoff", "backOffCount", "featureFlags", "featureFlagsByKey", "groupTypeMapping", "cohorts", "loadedSuccessfullyOnce", "poller", "fetch", "onError", "onLoad", "loadFeatureFlags", "debug", "enabled", "logMsgIfDebug", "fn", "getFeatureFlag", "groups", "personProperties", "groupProperties", "response", "featureFlag", "flag", "computeFlagLocally", "console", "computeFeatureFlagPayloadLocally", "matchValue", "filters", "payloads", "JSON", "getAllFlagsAndPayloads", "fallbackToDecide", "matchPayload", "ensure_experience_continuity", "active", "flagFilters", "aggregation_group_type_index", "groupName", "String", "warn", "focusedGroupProperties", "matchFeatureFlagProperties", "flagConditions", "isInconclusive", "sortedFlagConditions", "conditionA", "conditionB", "AHasVariantOverride", "variant", "BHasVariantOverride", "condition", "isConditionMatch", "variantOverride", "flagVariants", "multivariate", "variants", "some", "getMatchingVariant", "rolloutPercentage", "rollout_percentage", "warnFunction", "msg", "propertyType", "matches", "matchCohort", "matchProperty", "_hash", "hashValue", "matching<PERSON><PERSON><PERSON>", "variantLookupTable", "find", "valueMin", "valueMax", "lookupTable", "multivariates", "for<PERSON>ach", "forceReload", "_loadFeatureFlags", "isLocalEvaluationReady", "getPollingInterval", "clearTimeout", "setTimeout", "_requestFeatureFlagDefinitions", "responseJson", "stringify", "flags", "acc", "curr", "group_type_mapping", "err", "getPersonalApiKeyRequestOptions", "Authorization", "abortTimeout", "controller", "AbortController", "safeSetTimeout", "abort", "<PERSON><PERSON><PERSON><PERSON>", "_requestRemoteConfigPayload", "<PERSON><PERSON><PERSON>", "salt", "hashString", "parseInt", "propertyValues", "operator", "overrideValue", "computeExactMatch", "isArray", "val", "compare", "lhs", "rhs", "isValidRegex", "match", "parsedValue", "parseFloat", "parsedDate", "relativeDateParseForFeatureFlagMatching", "convertToDateTime", "overrideDate", "cohortProperties", "cohortId", "propertyGroup", "matchPropertyGroup", "propertyGroupType", "errorMatchingLocally", "negation", "regex", "RegExp", "Date", "date", "isNaN", "valueOf", "parsedDt", "toISOString", "number", "interval", "setUTCHours", "getUTCHours", "setUTCDate", "getUTCDate", "setUTCMonth", "getUTCMonth", "setUTCFullYear", "getUTCFullYear", "PostHogMemoryStorage", "_memoryStorage", "getProperty", "setProperty", "MINIMUM_POLLING_INTERVAL", "THIRTY_SECONDS", "MAX_CACHE_SIZE", "PostHogBackendClient", "PostHogCoreStateless", "featureFlagsPollingInterval", "featureFlagsPoller", "requestTimeout", "_events", "emit", "count", "getCustomHeaders", "errorTracking", "distinctIdHasSentFlagCalls", "maxCacheSize", "getPersistedProperty", "setPersistedProperty", "getLibraryVersion", "version", "getCustomUserAgent", "getLibraryId", "enable", "optIn", "disable", "optOut", "props", "sendFeatureFlags", "timestamp", "disable<PERSON><PERSON><PERSON>", "uuid", "_capture", "captureStateless", "_getFlags", "getFeatureFlagsStateless", "capture<PERSON>romise", "then", "groupsWithStringValues", "entries", "getAllFlags", "onlyEvaluateLocally", "feature", "activeFlags", "$groups", "addPendingPromise", "captureImmediate", "captureStatelessImmediate", "identify", "userPropsOnce", "$set_once", "userProps", "$set", "identifyStateless", "identifyImmediate", "identifyStatelessImmediate", "alias", "aliasStateless", "aliasImmediate", "aliasStatelessImmediate", "waitForLocalEvaluationReady", "timeoutMs", "cleanup", "sendFeatureFlagEvents", "adjustedProperties", "addLocalPersonAndGroupProperties", "allPersonProperties", "allGroupProperties", "flagWasLocallyEvaluated", "requestId", "flagDetail", "remoteResponse", "getFeatureFlagDetailStateless", "getFeatureFlagValue", "featureFlagReportedKey", "$feature_flag", "$feature_flag_response", "$feature_flag_id", "metadata", "id", "$feature_flag_version", "$feature_flag_reason", "description", "code", "locally_evaluated", "$feature_flag_request_id", "getFeatureFlagPayload", "localEvaluationEnabled", "payloadWasLocallyEvaluated", "getFeatureFlagPayloadStateless", "getRemoteConfigPayload", "isFeatureEnabled", "feat", "localEvaluationResult", "featureFlagPayloads", "remoteEvaluationResult", "getFeatureFlagsAndPayloadsStateless", "groupIdentify", "groupType", "groupKey", "groupIdentifyStateless", "reloadFeatureFlags", "_shutdown", "shutdownTimeoutMs", "distinct_id", "$group_key", "WEBPACK_ERROR_REGEXP", "STACKTRACE_FRAME_LIMIT", "UNKNOWN_FUNCTION", "node", "getModule", "FILENAME_MATCH", "FULL_MATCH", "lineMatch", "object", "functionName", "typeName", "methodName", "methodStart", "objectEnd", "indexOf", "isNative", "decodeURI", "module", "function", "_parseIntOrUndefined", "in_app", "filenameIsInApp", "isInternal", "nodeStackLineParser", "createStackParser", "parsers", "sortedParsers", "p", "skipFirstLines", "split", "cleanedLine", "test", "parser", "reverseAndStripFrames", "localStack", "reverse", "getLastStackFrame", "arr", "PostHog"], "mappings": ";;;;;;;;;;;;;AAAA;;CAEG,GACH;;;;;;;;;;;;;;;;;;;CAmBG,GAoDH,MAAMA,IAAI,GAAG,cAAc,CAAA;SAEXC,oBAAoBA,CAClCC,QAA8B,EAC9B,EAAEC,YAAY,EAAEC,SAAS,EAAEC,MAAM,EAAEC,iBAAiB,GAAG;IAAC,OAAO;CAAA,KAAgC,CAAA,CAAE,EAAA;IAEjG,QAAQC,KAAK,IAAI;QACf,MAAMC,kBAAkB,GAAGF,iBAAiB,KAAK,GAAG,IAAIA,iBAAiB,CAACG,QAAQ,CAACF,KAAK,CAACG,KAAK,CAAC,CAAA;QAC/F,IAAI,CAACF,kBAAkB,EAAE;YACvB,OAAOD,KAAK,CAAA;QACb,CAAA;QACD,IAAI,CAACA,KAAK,CAACI,IAAI,EAAE;YACfJ,KAAK,CAACI,IAAI,GAAG,CAAA,CAAE,CAAA;QAChB,CAAA;QAED,uGAAA;QACA,MAAMC,MAAM,GAAGL,KAAK,CAACI,IAAI,CAACE,wBAAwB,CAACC,cAAc,CAAC,CAAA;QAClE,IAAIF,MAAM,KAAKG,SAAS,EAAE;YACxB,kIAAA;YACA,OAAOR,KAAK,CAAA;QACb,CAAA;QAED,MAAMS,MAAM,GAAGd,QAAQ,CAACe,OAAO,CAACC,IAAI,IAAI,0BAA0B,CAAA;QAClE,MAAMC,SAAS,GAAG,IAAIC,GAAG,CAAC,CAAA,SAAA,EAAYlB,QAAQ,CAACmB,MAAM,CAAWT,QAAAA,EAAAA,MAAM,EAAE,EAAEI,MAAM,CAAC,CAACM,QAAQ,EAAE,CAAA;QAE5Ff,KAAK,CAACI,IAAI,CAAC,oBAAoB,CAAC,GAAGQ,SAAS,CAAA;QAE5C,MAAMI,UAAU,GAAuBhB,KAAK,CAACiB,SAAS,EAAEC,MAAM,IAAI,EAAE,CAAA;QAEpE,MAAMC,aAAa,GAAGH,UAAU,CAACI,GAAG,CAAEH,SAAS,IAAA,CAAM;gBACnD,GAAGA,SAAS;gBACZI,UAAU,EAAEJ,SAAS,CAACI,UAAU,GAC5B;oBACE,GAAGJ,SAAS,CAACI,UAAU;oBACvBC,IAAI,EAAE,KAAK;oBACXC,MAAM,EAAE,CAACN,SAAS,CAACI,UAAU,CAACE,MAAM,IAAI,EAAE,EAAEH,GAAG,EAAEI,KAAU,IAAI;wBAC7D,OAAO;4BAAE,GAAGA,KAAK;4BAAEC,QAAQ,EAAE,iBAAA;yBAAmB,CAAA;qBACjD,CAAA;gBACF,CAAA,GACDjB,SAAAA;YACL,CAAA,CAAC,CAAC,CAAA;QAEH,MAAMkB,UAAU,GAQZ;YACF,gCAAA;YACAC,kBAAkB,EAAEX,UAAU,CAAC,CAAC,CAAC,EAAEY,KAAK,IAAI5B,KAAK,CAAC6B,OAAO;YACzDC,eAAe,EAAEd,UAAU,CAAC,CAAC,CAAC,EAAEM,IAAI;YACpCS,oBAAoB,EAAEnB,SAAS;YAC/BoB,gBAAgB,EAAEhC,KAAK,CAACG,KAAK;YAC7B8B,eAAe,EAAEd,aAAa;YAC9B,8BAAA;YACAe,gBAAgB,EAAElC,KAAK,CAACmC,QAAQ;YAChCC,iBAAiB,EAAEpC,KAAK,CAACiB,SAAS;YAClCoB,yBAAyB,EAAErB,UAAU,CAAC,CAAC,CAAC,EAAEY,KAAK,IAAI5B,KAAK,CAAC6B,OAAO;YAChES,sBAAsB,EAAEtB,UAAU,CAAC,CAAC,CAAC,EAAEM,IAAI;YAC3CiB,YAAY,EAAEvC,KAAK,CAACI,IAAAA;SACrB,CAAA;QAED,IAAIR,YAAY,IAAIC,SAAS,EAAE;YAC7B6B,UAAU,CAAC,aAAa,CAAC,GACvB,CAAC5B,MAAM,IAAI,kCAAkC,IAC7CF,YAAY,GACZ,mBAAmB,GACnBC,SAAS,GACT,SAAS,GACTG,KAAK,CAACmC,QAAQ,CAAA;QACjB,CAAA;QAEDxC,QAAQ,CAAC6C,OAAO,CAAC;YAAExC,KAAK,EAAE,YAAY;YAAEyC,UAAU,EAAEpC,MAAM;YAAEqB,UAAAA;QAAU,CAAE,CAAC,CAAA;QAEzE,OAAO1B,KAAK,CAAA;KACb,CAAA;AACH,CAAA;AAEA,kCAAA;AACgB,SAAA0C,iBAAiBA,CAC/B/C,QAA8B,EAC9Be,OAAkC,EAAA;IAElC,MAAMiC,SAAS,GAAGjD,oBAAoB,CAACC,QAAQ,EAAEe,OAAO,CAAC,CAAA;IACzD,OAAO;QACLkC,IAAI,EAAEnD,IAAI;QACVoD,YAAYA,EAAC7C,KAAK,EAAA;YAChB,OAAO2C,SAAS,CAAC3C,KAAK,CAAC,CAAA;QACzB,CAAA;KACD,CAAA;AACH,CAAA;AAEA,+BAAA;MACaM,wBAAwB,CAAA;IAUnCwC,WAAAA,CACEnD,QAA8B,EAC9BC,YAAqB,EACrBE,MAAe,EACfC,iBAAyC,CAAA;QAb3B,IAAI,CAAA6C,IAAA,GAAGnD,IAAI,CAAA;QAezB,gEAAA;QACA,IAAI,CAACmD,IAAI,GAAGnD,IAAI,CAAA;QAChB,IAAI,CAACsD,SAAS,GAAG,SACfC,uBAAkE,EAClEC,aAA+B,EAAA;YAE/B,MAAMpD,SAAS,GAAGoD,aAAa,EAAE,EAAEC,SAAS,EAAE,EAAEC,MAAM,EAAE,EAAEtD,SAAS,CAAA;YACnEmD,uBAAuB,CACrBtD,oBAAoB,CAACC,QAAQ,EAAE;gBAC7BC,YAAY;gBACZC,SAAS;gBACTC,MAAM;gBACNC,iBAAAA;YACD,CAAA,CAAC,CACH,CAAA;SACF,CAAA;IACH,CAAA;;AA7BuBO,wBAAc,CAAAC,cAAA,GAAG,qBAAqB;AC7K/D,0GAAA;AACA,0CAAA;AAEA;;;;;;CAMG,GAEH,MAAM,MAAM,GAAG,kBAAkB,CAAC;AAElC,+CAAA,SACa,IAAI,CAAA;iEAEf,WAAA,CAA6B,KAA2B,CAAA;QAA3B,IAAK,CAAA,KAAA,GAAL,KAAK,CAAsB;KAAI;IAE5D;;;;;;;;KAQG,GACH,OAAO,OAAO,CAAC,KAA2B,EAAA;QACxC,IAAI,KAAK,CAAC,MAAM,KAAK,EAAE,EAAE;YACvB,MAAM,IAAI,SAAS,CAAC,oBAAoB,CAAC,CAAC;QAC3C,CAAA,MAAM;YACL,OAAO,IAAI,IAAI,CAAC,KAAK,CAAC,CAAC;QACxB,CAAA;KACF;IAED;;;;;;;;KAQG,GACH,OAAO,YAAY,CACjB,QAAgB,EAChB,KAAa,EACb,OAAe,EACf,OAAe,EAAA;QAEf,IACE,CAAC,MAAM,CAAC,SAAS,CAAC,QAAQ,CAAC,IAC3B,CAAC,MAAM,CAAC,SAAS,CAAC,KAAK,CAAC,IACxB,CAAC,MAAM,CAAC,SAAS,CAAC,OAAO,CAAC,IAC1B,CAAC,MAAM,CAAC,SAAS,CAAC,OAAO,CAAC,IAC1B,QAAQ,GAAG,CAAC,IACZ,KAAK,GAAG,CAAC,IACT,OAAO,GAAG,CAAC,IACX,OAAO,GAAG,CAAC,IACX,QAAQ,GAAG,eAAgB,IAC3B,KAAK,GAAG,KAAK,IACb,OAAO,GAAG,UAAW,IACrB,OAAO,GAAG,UAAW,EACrB;YACA,MAAM,IAAI,UAAU,CAAC,qBAAqB,CAAC,CAAC;QAC7C,CAAA;QAED,MAAM,KAAK,GAAG,IAAI,UAAU,CAAC,EAAE,CAAC,CAAC;QACjC,KAAK,CAAC,CAAC,CAAC,GAAG,QAAQ,GAAG,CAAC,IAAI,EAAE,CAAC;QAC9B,KAAK,CAAC,CAAC,CAAC,GAAG,QAAQ,GAAG,CAAC,IAAI,EAAE,CAAC;QAC9B,KAAK,CAAC,CAAC,CAAC,GAAG,QAAQ,GAAG,CAAC,IAAI,EAAE,CAAC;QAC9B,KAAK,CAAC,CAAC,CAAC,GAAG,QAAQ,GAAG,CAAC,IAAI,EAAE,CAAC;QAC9B,KAAK,CAAC,CAAC,CAAC,GAAG,QAAQ,GAAG,CAAC,IAAI,CAAC,CAAC;QAC7B,KAAK,CAAC,CAAC,CAAC,GAAG,QAAQ,CAAC;QACpB,KAAK,CAAC,CAAC,CAAC,GAAG,IAAI,GAAI,KAAK,KAAK,CAAC,CAAC,CAAC;QAChC,KAAK,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC;QACjB,KAAK,CAAC,CAAC,CAAC,GAAG,IAAI,GAAI,OAAO,KAAK,EAAE,CAAC,CAAC;QACnC,KAAK,CAAC,CAAC,CAAC,GAAG,OAAO,KAAK,EAAE,CAAC;QAC1B,KAAK,CAAC,EAAE,CAAC,GAAG,OAAO,KAAK,CAAC,CAAC;QAC1B,KAAK,CAAC,EAAE,CAAC,GAAG,OAAO,CAAC;QACpB,KAAK,CAAC,EAAE,CAAC,GAAG,OAAO,KAAK,EAAE,CAAC;QAC3B,KAAK,CAAC,EAAE,CAAC,GAAG,OAAO,KAAK,EAAE,CAAC;QAC3B,KAAK,CAAC,EAAE,CAAC,GAAG,OAAO,KAAK,CAAC,CAAC;QAC1B,KAAK,CAAC,EAAE,CAAC,GAAG,OAAO,CAAC;QACpB,OAAO,IAAI,IAAI,CAAC,KAAK,CAAC,CAAC;KACxB;IAED;;;;;;;;;;;;;KAaG,GACH,OAAO,KAAK,CAAC,IAAY,EAAA;QACvB,IAAI,GAAG,GAAuB,SAAS,CAAC;QACxC,OAAQ,IAAI,CAAC,MAAM;YACjB,KAAK,EAAE;gBACL,GAAG,GAAG,iBAAiB,CAAC,IAAI,CAAC,IAAI,CAAC,EAAA,CAAG,CAAC,CAAC,CAAC;gBACxC,MAAM;YACR,KAAK,EAAE;gBACL,GAAG,GACD,2EAA2E,CACxE,IAAI,CAAC,IAAI,CAAC,EACT,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CACZ,IAAI,CAAC,EAAE,CAAC,CAAC;gBACd,MAAM;YACR,KAAK,EAAE;gBACL,GAAG,GACD,+EAA+E,CAC5E,IAAI,CAAC,IAAI,CAAC,EACT,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CACZ,IAAI,CAAC,EAAE,CAAC,CAAC;gBACd,MAAM;YACR,KAAK,EAAE;gBACL,GAAG,GACD,oFAAoF,CACjF,IAAI,CAAC,IAAI,CAAC,EACT,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CACZ,IAAI,CAAC,EAAE,CAAC,CAAC;gBACd,MAAM;QAGT,CAAA;QAED,IAAI,GAAG,EAAE;YACP,MAAM,KAAK,GAAG,IAAI,UAAU,CAAC,EAAE,CAAC,CAAC;YACjC,IAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,EAAE,EAAE,CAAC,IAAI,CAAC,CAAE;gBAC9B,MAAM,CAAC,GAAG,QAAQ,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;gBACxD,KAAK,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,KAAK,EAAE,CAAC;gBACxB,KAAK,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,KAAK,EAAE,CAAC;gBACxB,KAAK,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;gBACvB,KAAK,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC;YAClB,CAAA;YACD,OAAO,IAAI,IAAI,CAAC,KAAK,CAAC,CAAC;QACxB,CAAA,MAAM;YACL,MAAM,IAAI,WAAW,CAAC,6BAA6B,CAAC,CAAC;QACtD,CAAA;KACF;IAED;;;KAGG,GACH,QAAQ,GAAA;QACN,IAAI,IAAI,GAAG,EAAE,CAAC;QACd,IAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE,CAAC,EAAE,CAAE;YAC1C,IAAI,IAAI,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC;YAC3C,IAAI,IAAI,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC;YAC3C,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE;gBAC5C,IAAI,IAAI,GAAG,CAAC;YACb,CAAA;QACF,CAAA;QACD,OAAO,IAAI,CAAC;KACb;IAED;;;KAGG,GACH,KAAK,GAAA;QACH,IAAI,IAAI,GAAG,EAAE,CAAC;QACd,IAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE,CAAC,EAAE,CAAE;YAC1C,IAAI,IAAI,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC;YAC3C,IAAI,IAAI,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC;QAC5C,CAAA;QACD,OAAO,IAAI,CAAC;KACb;gFAGD,MAAM,GAAA;QACJ,OAAO,IAAI,CAAC,QAAQ,EAAE,CAAC;KACxB;IAED;;;;;;;KAOG,GACH,UAAU,GAAA;QAOR,MAAM,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC;QAC9B,IAAI,CAAC,GAAG,CAAC,EAAE;YACT,MAAM,IAAI,KAAK,CAAC,aAAa,CAAC,CAAC;QAChC,CAAA,MAAM,IAAI,CAAC,IAAI,MAAM,EAAE;YACtB,OAAO,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,GAAK,CAAC,KAAK,CAAC,CAAC,GAAG,KAAK,GAAG,OAAO,CAAC;QAC3D,CAAA,MAAM,IAAI,CAAC,IAAI,MAAM,EAAE;YACtB,OAAO,QAAQ,CAAC;QACjB,CAAA,MAAM,IAAI,CAAC,IAAI,MAAM,EAAE;YACtB,OAAO,SAAS,CAAC;QAClB,CAAA,MAAM,IAAI,CAAC,IAAI,MAAM,EAAE;YACtB,OAAO,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,GAAK,CAAC,KAAK,IAAI,CAAC,GAAG,KAAK,GAAG,cAAc,CAAC;QACrE,CAAA,MAAM;YACL,MAAM,IAAI,KAAK,CAAC,aAAa,CAAC,CAAC;QAChC,CAAA;KACF;IAED;;;KAGG,GACH,UAAU,GAAA;QACR,OAAO,IAAI,CAAC,UAAU,EAAE,KAAK,QAAQ,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,GAAG,SAAS,CAAC;KACzE;0CAGD,KAAK,GAAA;QACH,OAAO,IAAI,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;KACtC;4DAGD,MAAM,CAAC,KAAW,EAAA;QAChB,OAAO,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;KACpC;IAED;;;KAGG,GACH,SAAS,CAAC,KAAW,EAAA;QACnB,IAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,EAAE,EAAE,CAAC,EAAE,CAAE;YAC3B,MAAM,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;YAC5C,IAAI,IAAI,KAAK,CAAC,EAAE;gBACd,OAAO,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACxB,CAAA;QACF,CAAA;QACD,OAAO,CAAC,CAAC;KACV;AACF,CAAA;AAED;;;;;;;;CAQG,SACU,WAAW,CAAA;IAOtB;;;;KAIG,GACH,WAAA,CAAY,qBAGX,CAAA;QAdO,IAAS,CAAA,SAAA,GAAG,CAAC,CAAC;QACd,IAAO,CAAA,OAAA,GAAG,CAAC,CAAC;QAclB,IAAI,CAAC,MAAM,GAAG,qBAAqB,IAAI,gBAAgB,EAAE,CAAC;KAC3D;IAED;;;;;;;;;;;;;KAaG,GACH,QAAQ,GAAA;QACN,OAAO,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,GAAG,EAAE,EAAE,KAAM,CAAC,CAAC;KACrD;IAED;;;;;;;;;;;;KAYG,GACH,eAAe,GAAA;QACb,OAAO,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,GAAG,EAAE,EAAE,KAAM,CAAC,CAAC;KACrD;IAED;;;;;;;;;;KAUG,GACH,mBAAmB,CAAC,QAAgB,EAAE,iBAAyB,EAAA;QAC7D,IAAI,KAAK,GAAG,IAAI,CAAC,mBAAmB,CAAC,QAAQ,EAAE,iBAAiB,CAAC,CAAC;QAClE,IAAI,KAAK,KAAK,SAAS,EAAE;;YAEvB,IAAI,CAAC,SAAS,GAAG,CAAC,CAAC;YACnB,KAAK,GAAG,IAAI,CAAC,mBAAmB,CAAC,QAAQ,EAAE,iBAAiB,CAAE,CAAC;QAChE,CAAA;QACD,OAAO,KAAK,CAAC;KACd;IAED;;;;;;;;;;KAUG,GACH,mBAAmB,CACjB,QAAgB,EAChB,iBAAyB,EAAA;QAEzB,MAAM,WAAW,GAAG,aAAe,CAAC;QAEpC,IACE,CAAC,MAAM,CAAC,SAAS,CAAC,QAAQ,CAAC,IAC3B,QAAQ,GAAG,CAAC,IACZ,QAAQ,GAAG,eAAgB,EAC3B;YACA,MAAM,IAAI,UAAU,CAAC,8CAA8C,CAAC,CAAC;QACtE,CAAA,MAAM,IAAI,iBAAiB,GAAG,CAAC,IAAI,iBAAiB,GAAG,eAAgB,EAAE;YACxE,MAAM,IAAI,UAAU,CAAC,6CAA6C,CAAC,CAAC;QACrE,CAAA;QAED,IAAI,QAAQ,GAAG,IAAI,CAAC,SAAS,EAAE;YAC7B,IAAI,CAAC,SAAS,GAAG,QAAQ,CAAC;YAC1B,IAAI,CAAC,YAAY,EAAE,CAAC;QACrB,CAAA,MAAM,IAAI,QAAQ,GAAG,iBAAiB,IAAI,IAAI,CAAC,SAAS,EAAE;;YAEzD,IAAI,CAAC,OAAO,EAAE,CAAC;YACf,IAAI,IAAI,CAAC,OAAO,GAAG,WAAW,EAAE;;gBAE9B,IAAI,CAAC,SAAS,EAAE,CAAC;gBACjB,IAAI,CAAC,YAAY,EAAE,CAAC;YACrB,CAAA;QACF,CAAA,MAAM;;YAEL,OAAO,SAAS,CAAC;QAClB,CAAA;QAED,OAAO,IAAI,CAAC,YAAY,CACtB,IAAI,CAAC,SAAS,EACd,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,OAAO,GAAG,CAAC,IAAI,EAAE,CAAC,EAClC,IAAI,CAAC,OAAO,GAAI,CAAC,IAAI,EAAE,GAAG,CAAC,CAAC,CAC5B,IAAI,CAAC,MAAM,CAAC,UAAU,EAAE,CACzB,CAAC;KACH;+DAGO,YAAY,GAAA;QAClB,IAAI,CAAC,OAAO,GACV,IAAI,CAAC,MAAM,CAAC,UAAU,EAAE,GAAG,KAAK,GAAA,CAAI,IAAI,CAAC,MAAM,CAAC,UAAU,EAAE,GAAG,KAAK,CAAC,CAAC;KACzE;IAED;;;;KAIG,GACH,UAAU,GAAA;QACR,MAAM,KAAK,GAAG,IAAI,UAAU,CAC1B,WAAW,CAAC,EAAE,CACZ,IAAI,CAAC,MAAM,CAAC,UAAU,EAAE,EACxB,IAAI,CAAC,MAAM,CAAC,UAAU,EAAE,EACxB,IAAI,CAAC,MAAM,CAAC,UAAU,EAAE,EACxB,IAAI,CAAC,MAAM,CAAC,UAAU,EAAE,CACzB,CAAC,MAAM,CACT,CAAC;QACF,KAAK,CAAC,CAAC,CAAC,GAAG,IAAI,GAAI,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC;QACnC,KAAK,CAAC,CAAC,CAAC,GAAG,IAAI,GAAI,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC;QACnC,OAAO,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;KAC5B;AACF,CAAA;AAED,gEAAA,GACA,+CAAA;AAEA,8EAAA,GACA,MAAM,gBAAgB,GAAG,MAA+B;;;;;;;;;;;;;;;;;;;IAoBtD,OAAO;QACL,UAAU,EAAE,IACV,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,KAAQ,CAAC,GAAG,KAAQ,GAC/C,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,KAAQ,CAAC;KACvC,CAAC;AACJ,CAAC,CAAC;AAEF,MAAA;AACA,6EAAA;AACA,2EAAA;AACA,qEAAA;AACA,MAAA;AACA,+BAAA;AACA,kDAAA;AACA,6BAAA;AACA,2BAAA;AACA,+CAAA;AACA,6CAAA;AACA,yBAAA;AACA,QAAA;AACA,yCAAA;AACA,MAAA;AACA,IAAA;AAEA,IAAI,gBAAyC,CAAC;AAE9C;;;;;CAKG,GACI,MAAM,MAAM,GAAG,IAAc,SAAS,EAAE,CAAC,QAAQ,EAAE,CAAC;AAE3D,+BAAA,GACO,MAAM,SAAS,GAAG,IACvB,CAAC,gBAAgB,IAAA,CAAK,gBAAgB,GAAG,IAAI,WAAW,EAAE,CAAC,EAAE,QAAQ,EAAE;ACldzE,kGAAA;AACA,iCAAA;AAMA,SAAS6C,4BAA4BA,CACnCC,SAAsD,EACtDC,SAAqB,EAAA;IAErB,IAAIC,gBAAgB,GAAY,KAAK,CAAA;IAErC,OAAOC,MAAM,CAACC,MAAM,EACjBC,KAAY,IAAU;QACrB,8GAAA;QACA,kHAAA;QACA,yCAAA;QACA,kDAAA;QACA,iDAAA;QACA,MAAMC,0BAA0B,GAAGC,MAAM,CAACC,OAAO,CAACC,SAAS,CAAC,mBAAmB,CAAC,CAACC,MAAM,EAAEC,QAAQ,IAAI;YACnG,mCAAA;YACA,OACE,0EAAA;YACAA,QAAQ,CAACpB,IAAI,KAAK,8BAA8B,IAChD,8CAAA;YACCoB,QAAyB,CAACC,oBAAoB,KAAK,IAAA;SAEvD,CAAC,CAACC,MAAM,CAAA;QAET,MAAMC,gBAAgB,GAAGR,0BAA0B,KAAK,CAAC,CAAA;QAEzDN,SAAS,CAACK,KAAK,EAAE;YACfU,SAAS,EAAE;gBACT9C,IAAI,EAAE,qBAAqB;gBAC3B+C,OAAO,EAAE,KAAA;YACV,CAAA;QACF,CAAA,CAAC,CAAA;QAEF,IAAI,CAACd,gBAAgB,IAAIY,gBAAgB,EAAE;YACzCZ,gBAAgB,GAAG,IAAI,CAAA;YACvBD,SAAS,EAAE,CAAA;QACZ,CAAA;IACH,CAAC,EACD;QAAEW,oBAAoB,EAAE,IAAA;IAAI,CAAE,CAC/B,CAAA;AACH,CAAA;AAEgB,SAAAK,4BAA4BA,CAC1CjB,SAAsD,EACtDC,SAAqB,EAAA;IAErBM,MAAM,CAACC,OAAO,CAACU,EAAE,CAAC,mBAAmB,EAAEnB,4BAA4B,CAACC,SAAS,EAAEC,SAAS,CAAC,CAAC,CAAA;AAC5F,CAAA;AAEM,SAAUkB,6BAA6BA,CAACnB,SAAwD,EAAA;IACpGO,MAAM,CAACC,OAAO,CAACU,EAAE,CAAC,oBAAoB,GAAGE,MAAe,IAAI;QAC1DpB,SAAS,CAACoB,MAAM,EAAE;YAChBL,SAAS,EAAE;gBACT9C,IAAI,EAAE,sBAAsB;gBAC5B+C,OAAO,EAAE,KAAA;YACV,CAAA;QACF,CAAA,CAAC,CAAA;IACJ,CAAC,CAAC,CAAA;AACJ;AChEA,kGAAA;AACA,iCAAA;AAIM,SAAUK,OAAOA,CAACC,SAAkB,EAAA;IACxC,OAAO,OAAOC,KAAK,KAAK,WAAW,IAAIC,YAAY,CAACF,SAAS,EAAEC,KAAK,CAAC,CAAA;AACvE,CAAA;AAEM,SAAUE,aAAaA,CAACH,SAAkB,EAAA;IAC9C,OAAOI,SAAS,CAACJ,SAAS,EAAE,QAAQ,CAAC,CAAA;AACvC,CAAA;AAEM,SAAUK,OAAOA,CAACL,SAAkB,EAAA;IACxC,OAAQnB,MAAM,CAACyB,SAAS,CAAClE,QAAQ,CAACmE,IAAI,CAACP,SAAS,CAAC;QAC/C,KAAK,gBAAgB,CAAA;QACrB,KAAK,oBAAoB,CAAA;QACzB,KAAK,uBAAuB,CAAA;QAC5B,KAAK,gCAAgC;YACnC,OAAO,IAAI,CAAA;QACb;YACE,OAAOE,YAAY,CAACF,SAAS,EAAEQ,KAAK,CAAC,CAAA;IACxC,CAAA;AACH,CAAA;AAEgB,SAAAN,YAAYA,CAACF,SAAkB,EAAES,IAAS,EAAA;IACxD,IAAI;QACF,OAAOT,SAAS,YAAYS,IAAI,CAAA;IACjC,CAAA,CAAC,OAAM;QACN,OAAO,KAAK,CAAA;IACb,CAAA;AACH,CAAA;AAEM,SAAUC,YAAYA,CAACrF,KAAc,EAAA;IACzC,OAAO+E,SAAS,CAAC/E,KAAK,EAAE,YAAY,CAAC,CAAA;AACvC,CAAA;AAEgB,SAAA+E,SAASA,CAACJ,SAAkB,EAAEW,SAAiB,EAAA;IAC7D,OAAO9B,MAAM,CAACyB,SAAS,CAAClE,QAAQ,CAACmE,IAAI,CAACP,SAAS,CAAC,KAAK,CAAA,QAAA,EAAWW,SAAS,CAAG,CAAA,CAAA,CAAA;AAC9E;ACvCA,kGAAA;AAcO,eAAeC,0BAA0BA,CAC9CC,WAAwB,EACxBC,cAAsC,EACtCC,KAAc,EACdC,IAAgB,EAAA;IAEhB,MAAMC,iBAAiB,GAAGD,IAAI,IAAIA,IAAI,CAACvB,SAAS,CAAA;IAChD,MAAMA,SAAS,GAAGwB,iBAAiB,IAAI;QACrCvB,OAAO,EAAE,IAAI;QACb/C,IAAI,EAAE,SAAA;KACP,CAAA;IAED,MAAMuE,SAAS,GAAGC,YAAY,CAAC1B,SAAS,EAAEsB,KAAK,EAAEC,IAAI,CAAC,CAAA;IACtD,MAAMxE,aAAa,GAAG,MAAM4E,OAAO,CAACC,GAAG,CACrCH,SAAS,CAACzE,GAAG,CAAC,OAAOsC,KAAK,IAAI;QAC5B,MAAMzC,SAAS,GAAG,MAAMgF,kBAAkB,CAACT,WAAW,EAAEC,cAAc,EAAE/B,KAAK,CAAC,CAAA;QAC9EzC,SAAS,CAACW,KAAK,GAAGX,SAAS,CAACW,KAAK,IAAI,EAAE,CAAA;QACvCX,SAAS,CAACK,IAAI,GAAGL,SAAS,CAACK,IAAI,IAAI,OAAO,CAAA;QAC1CL,SAAS,CAACmD,SAAS,GAAGA,SAAS,CAAA;QAC/B,OAAOnD,SAAS,CAAA;IAClB,CAAC,CAAC,CACH,CAAA;IAED,MAAMS,UAAU,GAAG;QAAEO,eAAe,EAAEd,aAAAA;KAAe,CAAA;IACrD,OAAOO,UAAU,CAAA;AACnB,CAAA;AAEA,6CAAA;AACA,oGAAA;AACA,SAASoE,YAAYA,CAAC1B,SAAoB,EAAEsB,KAAc,EAAEC,IAAgB,EAAA;IAC1E,MAAMjC,KAAK,GAAGwC,QAAQ,CAAC9B,SAAS,EAAEsB,KAAK,EAAEC,IAAI,CAAC,CAAA;IAC9C,IAAIjC,KAAK,CAACyC,KAAK,EAAE;QACf,OAAO;YAACzC,KAAK,EAAE;eAAGoC,YAAY,CAAC1B,SAAS,EAAEV,KAAK,CAACyC,KAAK,EAAER,IAAI,CAAC;SAAC,CAAA;IAC9D,CAAA;IACD,OAAO;QAACjC,KAAK;KAAC,CAAA;AAChB,CAAA;AAEA,SAASwC,QAAQA,CAAC9B,SAAoB,EAAEnD,SAAkB,EAAE0E,IAAgB,EAAA;IAC1E,IAAIX,OAAO,CAAC/D,SAAS,CAAC,EAAE;QACtB,OAAOA,SAAS,CAAA;IACjB,CAAA;IAEDmD,SAAS,CAACgC,SAAS,GAAG,IAAI,CAAA;IAE1B,IAAItB,aAAa,CAAC7D,SAAS,CAAC,EAAE;QAC5B,MAAMoF,aAAa,GAAGC,0BAA0B,CAACrF,SAAS,CAAC,CAAA;QAC3D,IAAIoF,aAAa,EAAE;YACjB,OAAOA,aAAa,CAAA;QACrB,CAAA;QAED,MAAMxE,OAAO,GAAG0E,mBAAmB,CAACtF,SAAS,CAAC,CAAA;QAC9C,MAAMuF,EAAE,GAAGb,IAAI,EAAEc,kBAAkB,IAAI,IAAItB,KAAK,CAACtD,OAAO,CAAC,CAAA;QACzD2E,EAAE,CAAC3E,OAAO,GAAGA,OAAO,CAAA;QAEpB,OAAO2E,EAAE,CAAA;IACV,CAAA;IAED,+DAAA;IACA,yEAAA;IACA,MAAMA,EAAE,GAAGb,IAAI,EAAEc,kBAAkB,IAAI,IAAItB,KAAK,CAAClE,SAAmB,CAAC,CAAA;IACrEuF,EAAE,CAAC3E,OAAO,GAAG,CAAA,EAAGZ,SAAS,CAAE,CAAA,CAAA;IAE3B,OAAOuF,EAAE,CAAA;AACX,CAAA;AAEA,4EAAA,GACA,SAASF,0BAA0BA,CAACI,GAA4B,EAAA;IAC9D,IAAK,MAAMC,IAAI,IAAID,GAAG,CAAE;QACtB,IAAIlD,MAAM,CAACyB,SAAS,CAAC2B,cAAc,CAAC1B,IAAI,CAACwB,GAAG,EAAEC,IAAI,CAAC,EAAE;YACnD,MAAM/E,KAAK,GAAG8E,GAAG,CAACC,IAAI,CAAC,CAAA;YACvB,IAAI3B,OAAO,CAACpD,KAAK,CAAC,EAAE;gBAClB,OAAOA,KAAK,CAAA;YACb,CAAA;QACF,CAAA;IACF,CAAA;IAED,OAAOpB,SAAS,CAAA;AAClB,CAAA;AAEA,SAAS+F,mBAAmBA,CAACtF,SAAkC,EAAA;IAC7D,IAAI,MAAM,IAAIA,SAAS,IAAI,OAAOA,SAAS,CAAC2B,IAAI,KAAK,QAAQ,EAAE;QAC7D,IAAIf,OAAO,GAAG,CAAA,CAAA,EAAIZ,SAAS,CAAC2B,IAAI,CAAyB,uBAAA,CAAA,CAAA;QAEzD,IAAI,SAAS,IAAI3B,SAAS,IAAI,OAAOA,SAAS,CAACY,OAAO,KAAK,QAAQ,EAAE;YACnEA,OAAO,IAAI,CAAA,eAAA,EAAkBZ,SAAS,CAACY,OAAO,CAAG,CAAA,CAAA,CAAA;QAClD,CAAA;QAED,OAAOA,OAAO,CAAA;IACf,CAAA,MAAM,IAAI,SAAS,IAAIZ,SAAS,IAAI,OAAOA,SAAS,CAACY,OAAO,KAAK,QAAQ,EAAE;QAC1E,OAAOZ,SAAS,CAACY,OAAO,CAAA;IACzB,CAAA;IAED,MAAMgF,IAAI,GAAGC,8BAA8B,CAAC7F,SAAS,CAAC,CAAA;IAEtD,sGAAA;IACA,+DAAA;IACA,IAAIoE,YAAY,CAACpE,SAAS,CAAC,EAAE;QAC3B,OAAO,CAA6DA,0DAAAA,EAAAA,SAAS,CAACY,OAAO,CAAI,EAAA,CAAA,CAAA;IAC1F,CAAA;IAED,MAAMyD,SAAS,GAAGyB,kBAAkB,CAAC9F,SAAS,CAAC,CAAA;IAE/C,OAAO,CAAGqE,EAAAA,SAAS,IAAIA,SAAS,KAAK,QAAQ,GAAG,CAAIA,CAAAA,EAAAA,SAAS,CAAG,CAAA,CAAA,GAAG,QAAQ,CAAA,kCAAA,EAAqCuB,IAAI,CAAE,CAAA,CAAA;AACxH,CAAA;AAEA,SAASE,kBAAkBA,CAACL,GAAY,EAAA;IACtC,IAAI;QACF,MAAMzB,SAAS,GAAmBzB,MAAM,CAACwD,cAAc,CAACN,GAAG,CAAC,CAAA;QAC5D,OAAOzB,SAAS,GAAGA,SAAS,CAACnC,WAAW,CAACF,IAAI,GAAGpC,SAAS,CAAA;KAC1D,CAAC,OAAOyG,CAAC,EAAE;IACV,qBAAA;IAAA,CAAA;AAEJ,CAAA;AAEA;;;;CAIG,GACH,SAASH,8BAA8BA,CAAC7F,SAAkC,EAAEiG,YAAoB,EAAE,EAAA;IAChG,MAAML,IAAI,GAAGrD,MAAM,CAACqD,IAAI,CAACM,oBAAoB,CAAClG,SAAS,CAAC,CAAC,CAAA;IACzD4F,IAAI,CAACO,IAAI,EAAE,CAAA;IAEX,MAAMC,QAAQ,GAAGR,IAAI,CAAC,CAAC,CAAC,CAAA;IAExB,IAAI,CAACQ,QAAQ,EAAE;QACb,OAAO,sBAAsB,CAAA;IAC9B,CAAA;IAED,IAAIA,QAAQ,CAACnD,MAAM,IAAIgD,SAAS,EAAE;QAChC,OAAOI,QAAQ,CAACD,QAAQ,EAAEH,SAAS,CAAC,CAAA;IACrC,CAAA;IAED,IAAK,IAAIK,YAAY,GAAGV,IAAI,CAAC3C,MAAM,EAAEqD,YAAY,GAAG,CAAC,EAAEA,YAAY,EAAE,CAAE;QACrE,MAAMC,UAAU,GAAGX,IAAI,CAACY,KAAK,CAAC,CAAC,EAAEF,YAAY,CAAC,CAACG,IAAI,CAAC,IAAI,CAAC,CAAA;QACzD,IAAIF,UAAU,CAACtD,MAAM,GAAGgD,SAAS,EAAE;YACjC,SAAA;QACD,CAAA;QACD,IAAIK,YAAY,KAAKV,IAAI,CAAC3C,MAAM,EAAE;YAChC,OAAOsD,UAAU,CAAA;QAClB,CAAA;QACD,OAAOF,QAAQ,CAACE,UAAU,EAAEN,SAAS,CAAC,CAAA;IACvC,CAAA;IAED,OAAO,EAAE,CAAA;AACX,CAAA;AAEA,SAASI,QAAQA,CAACK,GAAW,EAAEC,MAAc,CAAC,EAAA;IAC5C,IAAI,OAAOD,GAAG,KAAK,QAAQ,IAAIC,GAAG,KAAK,CAAC,EAAE;QACxC,OAAOD,GAAG,CAAA;IACX,CAAA;IACD,OAAOA,GAAG,CAACzD,MAAM,IAAI0D,GAAG,GAAGD,GAAG,GAAG,CAAGA,EAAAA,GAAG,CAACF,KAAK,CAAC,CAAC,EAAEG,GAAG,CAAC,CAAK,GAAA,CAAA,CAAA;AAC5D,CAAA;AAEA;;;;;;;CAOG,GACH,SAAST,oBAAoBA,CAAIvF,KAAQ,EAAA;IAevC,IAAIoD,OAAO,CAACpD,KAAK,CAAC,EAAE;QAClB,OAAO;YACLC,OAAO,EAAED,KAAK,CAACC,OAAO;YACtBe,IAAI,EAAEhB,KAAK,CAACgB,IAAI;YAChBiF,KAAK,EAAEjG,KAAK,CAACiG,KAAK;YAClB,GAAGC,gBAAgB,CAAClG,KAAK,CAAA;SAC1B,CAAA;IACF,CAAA,MAAM,IAAI8C,OAAO,CAAC9C,KAAK,CAAC,EAAE;QACzB,MAAMmG,MAAM,GAMR;YACFzG,IAAI,EAAEM,KAAK,CAACN,IAAI;YAChB0G,MAAM,EAAEC,oBAAoB,CAACrG,KAAK,CAACoG,MAAM,CAAC;YAC1CE,aAAa,EAAED,oBAAoB,CAACrG,KAAK,CAACsG,aAAa,CAAC;YACxD,GAAGJ,gBAAgB,CAAClG,KAAK,CAAA;SAC1B,CAAA;QAED,oGAAA;QACA,gFAAA;QACA,6DAAA;QACA,IAAA;QAEA,OAAOmG,MAAM,CAAA;IACd,CAAA,MAAM;QACL,OAAOnG,KAAK,CAAA;IACb,CAAA;AACH,CAAA;AAEA,mDAAA,GACA,SAASkG,gBAAgBA,CAACpB,GAAY,EAAA;IACpC,IAAI,OAAOA,GAAG,KAAK,QAAQ,IAAIA,GAAG,KAAK,IAAI,EAAE;QAC3C,MAAMyB,cAAc,GAA+B,CAAA,CAAE,CAAA;QACrD,IAAK,MAAMC,QAAQ,IAAI1B,GAAG,CAAE;YAC1B,IAAIlD,MAAM,CAACyB,SAAS,CAAC2B,cAAc,CAAC1B,IAAI,CAACwB,GAAG,EAAE0B,QAAQ,CAAC,EAAE;gBACvDD,cAAc,CAACC,QAAQ,CAAC,GAAI1B,GAA+B,CAAC0B,QAAQ,CAAC,CAAA;YACtE,CAAA;QACF,CAAA;QACD,OAAOD,cAAc,CAAA;IACtB,CAAA,MAAM;QACL,OAAO,CAAA,CAAE,CAAA;IACV,CAAA;AACH,CAAA;AAEA,uEAAA,GACA,SAASF,oBAAoBA,CAACD,MAAe,EAAA;IAC3C,IAAI;QACF,OAAOxE,MAAM,CAACyB,SAAS,CAAClE,QAAQ,CAACmE,IAAI,CAAC8C,MAAM,CAAC,CAAA;KAC9C,CAAC,OAAOK,GAAG,EAAE;QACZ,OAAO,WAAW,CAAA;IACnB,CAAA;AACH,CAAA;AAEA;;CAEG,GACH,eAAepC,kBAAkBA,CAC/BT,WAAwB,EACxBC,cAAsC,EACtC/B,KAAY,EAAA;IAEZ,MAAMzC,SAAS,GAAc;QAC3BK,IAAI,EAAEoC,KAAK,CAACd,IAAI,IAAIc,KAAK,CAACZ,WAAW,CAACF,IAAI;QAC1ChB,KAAK,EAAE8B,KAAK,CAAC7B,OAAAA;KACd,CAAA;IAED,IAAIN,MAAM,GAAG+G,gBAAgB,CAAC9C,WAAW,EAAE9B,KAAK,CAAC,CAAA;IAEjD,KAAK,MAAM6E,QAAQ,IAAI9C,cAAc,CAAE;QACrClE,MAAM,GAAG,MAAMgH,QAAQ,CAAChH,MAAM,CAAC,CAAA;IAChC,CAAA;IAED,IAAIA,MAAM,CAAC2C,MAAM,EAAE;QACjBjD,SAAS,CAACI,UAAU,GAAG;YAAEE,MAAM;YAAED,IAAI,EAAE,KAAA;SAAO,CAAA;IAC/C,CAAA;IAED,OAAOL,SAAS,CAAA;AAClB,CAAA;AAEA;;CAEG,GACH,SAASqH,gBAAgBA,CAAC9C,WAAwB,EAAE9B,KAAY,EAAA;IAC9D,OAAO8B,WAAW,CAAC9B,KAAK,CAACmE,KAAK,IAAI,EAAE,EAAE,CAAC,CAAC,CAAA;AAC1C;AC/QA,MAAMW,gBAAgB,GAAG,IAAI,CAAA;AAEf,MAAOC,aAAa,CAAA;IAOhC,aAAaC,gBAAgBA,CAC3BC,MAA4B,EAC5BjF,KAAc,EACdiC,IAAe,EACflD,UAAmB,EACnBmG,oBAAmD,EAAA;QAEnD,MAAMlH,UAAU,GAA+B;YAAE,GAAGkH,oBAAAA;SAAsB,CAAA;QAE1E,+FAAA;QACA,0FAAA;QACA,IAAI,CAACnG,UAAU,EAAE;YACff,UAAU,CAACmH,uBAAuB,GAAG,KAAK,CAAA;QAC3C,CAAA;QAED,MAAMC,mBAAmB,GAAG,MAAMvD,0BAA0B,CAAC,IAAI,CAACC,WAAW,EAAE,IAAI,CAACC,cAAc,EAAE/B,KAAK,EAAEiC,IAAI,CAAC,CAAA;QAEhHgD,MAAM,CAACnG,OAAO,CAAC;YACbxC,KAAK,EAAE,YAAY;YACnByC,UAAU,EAAEA,UAAU,IAAIsG,MAAM,EAAE;YAClCrH,UAAU,EAAE;gBACV,GAAGoH,mBAAmB;gBACtB,GAAGpH,UAAAA;YACJ,CAAA;QACF,CAAA,CAAC,CAAA;IACJ,CAAA;IAEAoB,WAAYA,CAAA6F,MAA4B,EAAEjI,OAAuB,CAAA;QAC/D,IAAI,CAACiI,MAAM,GAAGA,MAAM,CAAA;QACpB,IAAI,CAACK,4BAA4B,GAAGtI,OAAO,CAACuI,0BAA0B,IAAI,KAAK,CAAA;QAE/E,IAAI,CAACC,yBAAyB,EAAE,CAAA;IAClC,CAAA;IAEQA,yBAAyBA,GAAA;QAC/B,IAAI,IAAI,CAACC,SAAS,EAAE,EAAE;YACpB7E,4BAA4B,CAAC,IAAI,CAAC8E,WAAW,CAACC,IAAI,CAAC,IAAI,CAAC,EAAE,IAAI,CAACC,YAAY,CAACD,IAAI,CAAC,IAAI,CAAC,CAAC,CAAA;YACvF7E,6BAA6B,CAAC,IAAI,CAAC4E,WAAW,CAACC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAA;QAC3D,CAAA;IACH,CAAA;IAEQD,WAAWA,CAACnI,SAAkB,EAAE0E,IAAe,EAAA;QACrD8C,aAAa,CAACC,gBAAgB,CAAC,IAAI,CAACC,MAAM,EAAE1H,SAAS,EAAE0E,IAAI,CAAC,CAAA;IAC9D,CAAA;IAEQ,MAAM2D,YAAYA,GAAA;QACxB,MAAM,IAAI,CAACX,MAAM,CAACY,QAAQ,CAACf,gBAAgB,CAAC,CAAA;IAC9C,CAAA;IAEAW,SAASA,GAAA;QACP,OAAO,CAAC,IAAI,CAACR,MAAM,CAACa,UAAU,IAAI,IAAI,CAACR,4BAA4B,CAAA;IACrE,CAAA;AACD;AC7Ce,SAAAS,wBAAwBA,CACtC9J,QAA8B,EAC9B+J,GAEC,EAAA;IAEDA,GAAG,CAACC,GAAG,CAAC,CAACjG,KAAsB,EAAEkG,CAAC,EAAEC,EAAE,EAAEC,IAAsC,KAAU;QACtF,MAAMnE,IAAI,GAAG;YAAEvB,SAAS,EAAE;gBAAE9C,IAAI,EAAE,YAAY;gBAAE+C,OAAO,EAAE,KAAA;YAAK,CAAA;SAAI,CAAA;QAClE,uFAAA;QACA,2EAAA;QACAoE,aAAa,CAACC,gBAAgB,CAAC/I,QAAQ,EAAE+D,KAAK,EAAEiC,IAAI,EAAEoD,MAAM,EAAE,EAAE;YAAEF,uBAAuB,EAAE,KAAA;QAAK,CAAE,CAAC,CAAA;QACnGiB,IAAI,CAACpG,KAAK,CAAC,CAAA;IACb,CAAC,CAAC,CAAA;AACJ;ACpCA,kGAAA;AAKA,iEAAA,GACgB,SAAAqG,2BAA2BA,CACzCC,QAAA,GAAmBnG,OAAO,CAACoG,IAAI,CAAC,CAAC,CAAC,yGAAGC,UAAO,AAAPA,EAAQrG,OAAO,CAACoG,IAAI,CAAC,CAAC,CAAC,CAAC,GAAGpG,OAAO,CAACsG,GAAG,EAAE,EAC7EC,SAAqB,qGAAAC,MAAG,KAAK,IAAI,EAAA;IAEjC,MAAMC,cAAc,GAAGF,SAAS,GAAGG,oBAAoB,CAACP,QAAQ,CAAC,GAAGA,QAAQ,CAAA;IAE5E,QAAQQ,QAA4B,IAAI;QACtC,IAAI,CAACA,QAAQ,EAAE;YACb,OAAA;QACD,CAAA;QAED,MAAMC,kBAAkB,GAAGL,SAAS,GAAGG,oBAAoB,CAACC,QAAQ,CAAC,GAAGA,QAAQ,CAAA;QAEhF,wCAAA;QACA,IAAI,EAAEE,GAAG,EAAEtF,IAAI,EAAEuF,IAAI,EAAEC,GAAAA,EAAK,oGAAGC,SAAK,CAACC,KAAK,CAACL,kBAAkB,CAAC,CAAA;QAE9D,IAAIG,GAAG,KAAK,KAAK,IAAIA,GAAG,KAAK,MAAM,IAAIA,GAAG,KAAK,MAAM,EAAE;YACrDD,IAAI,GAAGA,IAAI,CAAClD,KAAK,CAAC,CAAC,EAAEmD,GAAG,CAAC1G,MAAM,GAAG,CAAC,CAAC,CAAC,CAAA;QACtC,CAAA;QAED,gEAAA;QACA,0BAAA;QACA,MAAM6G,WAAW,GAAGC,kBAAkB,CAACL,IAAI,CAAC,CAAA;QAE5C,IAAI,CAACD,GAAG,EAAE;YACR,wBAAA;YACAA,GAAG,GAAG,GAAG,CAAA;QACV,CAAA;QAED,MAAMO,CAAC,GAAGP,GAAG,CAACQ,WAAW,CAAC,eAAe,CAAC,CAAA;QAC1C,IAAID,CAAC,GAAG,CAAC,CAAC,EAAE;YACV,OAAO,GAAGP,GAAG,CAACjD,KAAK,CAACwD,CAAC,GAAG,EAAE,CAAC,CAACE,OAAO,CAAC,KAAK,EAAE,GAAG,CAAC,CAAA,CAAA,EAAIJ,WAAW,CAAE,CAAA,CAAA;QACjE,CAAA;QAED,8CAAA;QACA,6DAAA;QACA,IAAIL,GAAG,CAACU,UAAU,CAACd,cAAc,CAAC,EAAE;YAClC,MAAMe,UAAU,GAAGX,GAAG,CAACjD,KAAK,CAAC6C,cAAc,CAACpG,MAAM,GAAG,CAAC,CAAC,CAACiH,OAAO,CAAC,KAAK,EAAE,GAAG,CAAC,CAAA;YAC3E,OAAOE,UAAU,GAAG,CAAGA,EAAAA,UAAU,CAAA,CAAA,EAAIN,WAAW,CAAA,CAAE,GAAGA,WAAW,CAAA;QACjE,CAAA;QAED,OAAOA,WAAW,CAAA;KACnB,CAAA;AACH,CAAA;AAEA,6BAAA,GACA,SAASR,oBAAoBA,CAACe,IAAY,EAAA;IACxC,OAAOA,IAAI,CACRH,OAAO,CAAC,SAAS,EAAE,EAAE,CAAC,CAAA,8BAAA;KACtBA,OAAO,CAAC,KAAK,EAAE,GAAG,CAAC,CAAA,CAAA,qCAAA;AACxB;ACxDA,kGAAA;AACA,iCAAA;AAEA,qCAAA,SACaI,eAAe,CAAA;IAG1BzI,WAAAA,CAAoC0I,QAAgB,CAAA;QAAhB,IAAQ,CAAAA,QAAA,GAARA,QAAQ,CAAA;QAC1C,IAAI,CAACC,MAAM,GAAG,IAAIC,GAAG,EAAQ,CAAA;IAC/B,CAAA;IAEA,uGAAA,GACOC,GAAGA,CAACC,GAAM,EAAA;QACf,MAAMhK,KAAK,GAAG,IAAI,CAAC6J,MAAM,CAACE,GAAG,CAACC,GAAG,CAAC,CAAA;QAClC,IAAIhK,KAAK,KAAKpB,SAAS,EAAE;YACvB,OAAOA,SAAS,CAAA;QACjB,CAAA;QACD,2CAAA;QACA,IAAI,CAACiL,MAAM,CAACI,MAAM,CAACD,GAAG,CAAC,CAAA;QACvB,IAAI,CAACH,MAAM,CAACK,GAAG,CAACF,GAAG,EAAEhK,KAAK,CAAC,CAAA;QAC3B,OAAOA,KAAK,CAAA;IACd,CAAA;IAEA,sEAAA,GACOkK,GAAGA,CAACF,GAAM,EAAEhK,KAAQ,EAAA;QACzB,IAAI,CAAC6J,MAAM,CAACK,GAAG,CAACF,GAAG,EAAEhK,KAAK,CAAC,CAAA;IAC7B,CAAA;IAEA,gEAAA,GACOmK,MAAMA,GAAA;QACX,MAAO,IAAI,CAACN,MAAM,CAACO,IAAI,IAAI,IAAI,CAACR,QAAQ,CAAE;YACxC,MAAM5J,KAAK,GAAG,IAAI,CAAC6J,MAAM,CAAC5E,IAAI,EAAE,CAACiD,IAAI,EAAE,CAAClI,KAAK,CAAA;YAC7C,IAAIA,KAAK,EAAE;gBACT,yFAAA;gBACA,IAAI,CAAC6J,MAAM,CAACI,MAAM,CAACjK,KAAK,CAAC,CAAA;YAC1B,CAAA;QACF,CAAA;IACH,CAAA;AACD;ACtCD,kGAAA;AAQA,MAAMqK,uBAAuB,GAAG,IAAIV,eAAe,CAAiC,EAAE,CAAC,CAAA;AACvF,MAAMW,gCAAgC,GAAG,IAAIX,eAAe,CAAY,EAAE,CAAC,CAAA;AAC3E,MAAMY,wBAAwB,GAAG,CAAC,CAAA;AAClC,+GAAA;AACA,yEAAA;AACA,iCAAA;AACO,MAAMC,sBAAsB,GAAW,IAAI,CAAA;AAC3C,MAAMC,uBAAuB,GAAW,KAAK,CAAA;AAI7C,eAAeC,gBAAgBA,CAAC/K,MAAoB,EAAA;IACzD,mEAAA;IACA,wFAAA;IACA,MAAMgL,YAAY,GAA6B,CAAA,CAAE,CAAA;IAEjD,2EAAA;IACA,sFAAA;IACA,IAAK,IAAIC,CAAC,GAAGjL,MAAM,CAAC2C,MAAM,GAAG,CAAC,EAAEsI,CAAC,IAAI,CAAC,EAAEA,CAAC,EAAE,CAAE;QAC3C,MAAMhL,KAAK,GAA2BD,MAAM,CAACiL,CAAC,CAAC,CAAA;QAC/C,MAAMhC,QAAQ,GAAGhJ,KAAK,EAAEgJ,QAAQ,CAAA;QAEhC,IACE,CAAChJ,KAAK,IACN,OAAOgJ,QAAQ,KAAK,QAAQ,IAC5B,OAAOhJ,KAAK,CAACiL,MAAM,KAAK,QAAQ,IAChCC,6BAA6B,CAAClC,QAAQ,CAAC,IACvCmC,8BAA8B,CAACnL,KAAK,CAAC,EACrC;YACA,SAAA;QACD,CAAA;QAED,MAAMoL,kBAAkB,GAAGL,YAAY,CAAC/B,QAAQ,CAAC,CAAA;QACjD,IAAI,CAACoC,kBAAkB,EAAE;YACvBL,YAAY,CAAC/B,QAAQ,CAAC,GAAG,EAAE,CAAA;QAC5B,CAAA;QACD+B,YAAY,CAAC/B,QAAQ,CAAC,CAACqC,IAAI,CAACrL,KAAK,CAACiL,MAAM,CAAC,CAAA;IAC1C,CAAA;IAED,MAAMK,KAAK,GAAGtJ,MAAM,CAACqD,IAAI,CAAC0F,YAAY,CAAC,CAAA;IACvC,IAAIO,KAAK,CAAC5I,MAAM,IAAI,CAAC,EAAE;QACrB,OAAO3C,MAAM,CAAA;IACd,CAAA;IAED,MAAMwL,gBAAgB,GAAoB,EAAE,CAAA;IAC5C,KAAK,MAAMpC,IAAI,IAAImC,KAAK,CAAE;QACxB,+DAAA;QACA,IAAIZ,gCAAgC,CAACP,GAAG,CAAChB,IAAI,CAAC,EAAE;YAC9C,SAAA;QACD,CAAA;QAED,MAAMqC,iBAAiB,GAAGT,YAAY,CAAC5B,IAAI,CAAC,CAAA;QAC5C,IAAI,CAACqC,iBAAiB,EAAE;YACtB,SAAA;QACD,CAAA;QAED,+FAAA;QACAA,iBAAiB,CAAC5F,IAAI,CAAC,CAAC6F,CAAC,EAAEC,CAAC,GAAKD,CAAC,GAAGC,CAAC,CAAC,CAAA;QACvC,6FAAA;QACA,MAAMC,MAAM,GAAGC,oBAAoB,CAACJ,iBAAiB,CAAC,CAAA;QACtD,IAAIG,MAAM,CAACE,KAAK,EAAEC,CAAC,GAAKC,yBAAyB,CAAC5C,IAAI,EAAE2C,CAAC,CAAC,CAAC,EAAE;YAC3D,SAAA;QACD,CAAA;QAED,MAAME,KAAK,GAAGC,OAAO,CAACxB,uBAAuB,EAAEtB,IAAI,EAAE,CAAA,CAAE,CAAC,CAAA;QACxDoC,gBAAgB,CAACF,IAAI,CAACa,uBAAuB,CAAC/C,IAAI,EAAEwC,MAAM,EAAEK,KAAK,CAAC,CAAC,CAAA;IACpE,CAAA;IAED,+FAAA;IACA,MAAMzH,OAAO,CAACC,GAAG,CAAC+G,gBAAgB,CAAC,CAACY,KAAK,CAAC,KAAO,CAAA,AAAC,CAAC,CAAA;IAEnD,yFAAA;IACA,+CAAA;IACA,IAAIpM,MAAM,IAAIA,MAAM,CAAC2C,MAAM,GAAG,CAAC,EAAE;QAC/B0J,wBAAwB,CAACrM,MAAM,EAAE0K,uBAAuB,CAAC,CAAA;IAC1D,CAAA;IAED,iFAAA;IACA,6DAAA;IACAA,uBAAuB,CAACF,MAAM,EAAE,CAAA;IAEhC,OAAOxK,MAAM,CAAA;AACf,CAAA;AAEA;;CAEG,GACH,SAASmM,uBAAuBA,CAACpC,IAAY,EAAE6B,MAAuB,EAAEU,MAA8B,EAAA;IACpG,OAAO,IAAI9H,OAAO,CAAE+H,OAAO,IAAI;QAC7B,qGAAA;QACA,uCAAA;QACA,8CAAA;QACA,MAAMC,MAAM,qHAAGC,mBAAAA,AAAgB,EAAC1C,IAAI,CAAC,CAAA;QACrC,MAAM2C,UAAU,IAAGC,+IAAAA,AAAe,EAAC;YACjCxI,KAAK,EAAEqI,MAAAA;QACR,CAAA,CAAC,CAAA;QAEF,oEAAA;QACA,kEAAA;QACA,kHAAA;QACA,SAASI,uBAAuBA,GAAA;YAC9BJ,MAAM,CAACK,OAAO,EAAE,CAAA;YAChBN,OAAO,EAAE,CAAA;QACX,CAAA;QAEA,mFAAA;QACA,IAAIO,UAAU,GAAG,CAAC,CAAA;QAClB,IAAIC,iBAAiB,GAAG,CAAC,CAAA;QACzB,MAAMC,KAAK,GAAGpB,MAAM,CAACmB,iBAAiB,CAAC,CAAA;QACvC,IAAIC,KAAK,KAAK/N,SAAS,EAAE;YACvB,4GAAA;YACA2N,uBAAuB,EAAE,CAAA;YACzB,OAAA;QACD,CAAA;QACD,IAAIK,UAAU,GAAGD,KAAK,CAAC,CAAC,CAAC,CAAA;QACzB,IAAIE,QAAQ,GAAGF,KAAK,CAAC,CAAC,CAAC,CAAA;QAEvB,8FAAA;QACA,yDAAA;QACA,SAASG,aAAaA,GAAA;YACpB,uEAAA;YACAxC,gCAAgC,CAACJ,GAAG,CAACR,IAAI,EAAE,CAAC,CAAC,CAAA;YAC7C2C,UAAU,CAACU,KAAK,EAAE,CAAA;YAClBV,UAAU,CAACW,kBAAkB,EAAE,CAAA;YAC/BT,uBAAuB,EAAE,CAAA;QAC3B,CAAA;QAEA,sFAAA;QACA,4CAAA;QACAJ,MAAM,CAACxJ,EAAE,CAAC,OAAO,EAAEmK,aAAa,CAAC,CAAA;QACjCT,UAAU,CAAC1J,EAAE,CAAC,OAAO,EAAEmK,aAAa,CAAC,CAAA;QACrCT,UAAU,CAAC1J,EAAE,CAAC,OAAO,EAAE4J,uBAAuB,CAAC,CAAA;QAE/CF,UAAU,CAAC1J,EAAE,CAAC,MAAM,EAAGsK,IAAI,IAAI;YAC7BR,UAAU,EAAE,CAAA;YACZ,IAAIA,UAAU,GAAGG,UAAU,EAAE;gBAC3B,OAAA;YACD,CAAA;YAED,+EAAA;YACAX,MAAM,CAACQ,UAAU,CAAC,GAAGS,QAAQ,CAACD,IAAI,EAAE,CAAC,CAAC,CAAA;YAEtC,IAAIR,UAAU,IAAII,QAAQ,EAAE;gBAC1B,IAAIH,iBAAiB,KAAKnB,MAAM,CAACjJ,MAAM,GAAG,CAAC,EAAE;oBAC3C,4GAAA;oBACA+J,UAAU,CAACU,KAAK,EAAE,CAAA;oBAClBV,UAAU,CAACW,kBAAkB,EAAE,CAAA;oBAC/B,OAAA;gBACD,CAAA;gBACDN,iBAAiB,EAAE,CAAA;gBACnB,MAAMC,KAAK,GAAGpB,MAAM,CAACmB,iBAAiB,CAAC,CAAA;gBACvC,IAAIC,KAAK,KAAK/N,SAAS,EAAE;oBACvB,qEAAA;oBACAyN,UAAU,CAACU,KAAK,EAAE,CAAA;oBAClBV,UAAU,CAACW,kBAAkB,EAAE,CAAA;oBAC/B,OAAA;gBACD,CAAA;gBACDJ,UAAU,GAAGD,KAAK,CAAC,CAAC,CAAC,CAAA;gBACrBE,QAAQ,GAAGF,KAAK,CAAC,CAAC,CAAC,CAAA;YACpB,CAAA;QACH,CAAC,CAAC,CAAA;IACJ,CAAC,CAAC,CAAA;AACJ,CAAA;AAEA,iCAAA,GACA,SAASX,wBAAwBA,CAACrM,MAAoB,EAAEiM,KAAsD,EAAA;IAC5G,KAAK,MAAMhM,KAAK,IAAID,MAAM,CAAE;QAC1B,0EAAA;QACA,IAAIC,KAAK,CAACgJ,QAAQ,IAAIhJ,KAAK,CAACuN,YAAY,KAAKvO,SAAS,IAAI,OAAOgB,KAAK,CAACiL,MAAM,KAAK,QAAQ,EAAE;YAC1F,MAAMuC,QAAQ,GAAGxB,KAAK,CAAC7B,GAAG,CAACnK,KAAK,CAACgJ,QAAQ,CAAC,CAAA;YAC1C,IAAIwE,QAAQ,KAAKxO,SAAS,EAAE;gBAC1B,SAAA;YACD,CAAA;YAEDyO,iBAAiB,CAACzN,KAAK,CAACiL,MAAM,EAAEjL,KAAK,EAAEwN,QAAQ,CAAC,CAAA;QACjD,CAAA;IACF,CAAA;AACH,CAAA;AAEA;;CAEG,GACH,SAASC,iBAAiBA,CAACxC,MAAc,EAAEjL,KAAiB,EAAEwN,QAA4C,EAAA;IACxG,4GAAA;IACA,+GAAA;IACA,IAAIxN,KAAK,CAACiL,MAAM,KAAKjM,SAAS,IAAIwO,QAAQ,KAAKxO,SAAS,EAAE;QACxD,OAAA;IACD,CAAA;IAEDgB,KAAK,CAAC0N,WAAW,GAAG,EAAE,CAAA;IACtB,IAAK,IAAI1C,CAAC,GAAG2C,cAAc,CAAC1C,MAAM,CAAC,EAAED,CAAC,GAAGC,MAAM,EAAED,CAAC,EAAE,CAAE;QACpD,sGAAA;QACA,+FAAA;QACA,MAAMqC,IAAI,GAAGG,QAAQ,CAACxC,CAAC,CAAC,CAAA;QACxB,IAAIqC,IAAI,KAAKrO,SAAS,EAAE;YACtB4O,gBAAgB,CAAC5N,KAAK,CAAC,CAAA;YACvB,OAAA;QACD,CAAA;QAEDA,KAAK,CAAC0N,WAAW,CAACrC,IAAI,CAACgC,IAAI,CAAC,CAAA;IAC7B,CAAA;IAED,+GAAA;IACA,kCAAA;IACA,IAAIG,QAAQ,CAACvC,MAAM,CAAC,KAAKjM,SAAS,EAAE;QAClC4O,gBAAgB,CAAC5N,KAAK,CAAC,CAAA;QACvB,OAAA;IACD,CAAA;IAEDA,KAAK,CAACuN,YAAY,GAAGC,QAAQ,CAACvC,MAAM,CAAC,CAAA;IAErC,MAAM4C,GAAG,GAAGC,YAAY,CAAC7C,MAAM,CAAC,CAAA;IAChCjL,KAAK,CAAC+N,YAAY,GAAG,EAAE,CAAA;IACvB,IAAK,IAAI/C,CAAC,GAAGC,MAAM,GAAG,CAAC,EAAED,CAAC,IAAI6C,GAAG,EAAE7C,CAAC,EAAE,CAAE;QACtC,uGAAA;QACA,+CAAA;QACA,MAAMqC,IAAI,GAAGG,QAAQ,CAACxC,CAAC,CAAC,CAAA;QACxB,IAAIqC,IAAI,KAAKrO,SAAS,EAAE;YACtB,MAAA;QACD,CAAA;QACDgB,KAAK,CAAC+N,YAAY,CAAC1C,IAAI,CAACgC,IAAI,CAAC,CAAA;IAC9B,CAAA;AACH,CAAA;AAEA;;;CAGG,GACH,SAASO,gBAAgBA,CAAC5N,KAAiB,EAAA;IACzC,OAAOA,KAAK,CAAC0N,WAAW,CAAA;IACxB,OAAO1N,KAAK,CAACuN,YAAY,CAAA;IACzB,OAAOvN,KAAK,CAAC+N,YAAY,CAAA;AAC3B,CAAA;AAEA;;;;;CAKG,GACH,SAAS7C,6BAA6BA,CAACpB,IAAY,EAAA;IACjD,yEAAA;IACA,0FAAA;IACA,OACEA,IAAI,CAACF,UAAU,CAAC,OAAO,CAAC,IACxBE,IAAI,CAACkE,QAAQ,CAAC,SAAS,CAAC,IACxBlE,IAAI,CAACkE,QAAQ,CAAC,UAAU,CAAC,IACzBlE,IAAI,CAACkE,QAAQ,CAAC,UAAU,CAAC,IACzBlE,IAAI,CAACF,UAAU,CAAC,OAAO,CAAC,CAAA;AAE5B,CAAA;AAEA;;CAEG,GACH,SAASuB,8BAA8BA,CAACnL,KAAiB,EAAA;IACvD,IAAIA,KAAK,CAACiL,MAAM,KAAKjM,SAAS,IAAIgB,KAAK,CAACiL,MAAM,GAAGJ,uBAAuB,EAAE;QACxE,OAAO,IAAI,CAAA;IACZ,CAAA;IACD,IAAI7K,KAAK,CAACiO,KAAK,KAAKjP,SAAS,IAAIgB,KAAK,CAACiO,KAAK,GAAGrD,sBAAsB,EAAE;QACrE,OAAO,IAAI,CAAA;IACZ,CAAA;IACD,OAAO,KAAK,CAAA;AACd,CAAA;AAEA;;CAEG,GACH,SAASmB,yBAAyBA,CAAC5C,IAAY,EAAE4D,KAAoB,EAAA;IACnE,MAAMS,QAAQ,GAAG/C,uBAAuB,CAACN,GAAG,CAAChB,IAAI,CAAC,CAAA;IAClD,IAAIqE,QAAQ,KAAKxO,SAAS,EAAE;QAC1B,OAAO,KAAK,CAAA;IACb,CAAA;IAED,IAAK,IAAIgM,CAAC,GAAG+B,KAAK,CAAC,CAAC,CAAC,EAAE/B,CAAC,IAAI+B,KAAK,CAAC,CAAC,CAAC,EAAE/B,CAAC,EAAE,CAAE;QACzC,IAAIwC,QAAQ,CAACxC,CAAC,CAAC,KAAKhM,SAAS,EAAE;YAC7B,OAAO,KAAK,CAAA;QACb,CAAA;IACF,CAAA;IAED,OAAO,IAAI,CAAA;AACb,CAAA;AAEA;;;CAGG,GACH,SAAS4M,oBAAoBA,CAACsC,KAAe,EAAA;IAC3C,IAAI,CAACA,KAAK,CAACxL,MAAM,EAAE;QACjB,OAAO,EAAE,CAAA;IACV,CAAA;IAED,IAAIsI,CAAC,GAAG,CAAC,CAAA;IACT,MAAMqC,IAAI,GAAGa,KAAK,CAAC,CAAC,CAAC,CAAA;IAErB,IAAI,OAAOb,IAAI,KAAK,QAAQ,EAAE;QAC5B,OAAO,EAAE,CAAA;IACV,CAAA;IAED,IAAIc,OAAO,GAAGC,gBAAgB,CAACf,IAAI,CAAC,CAAA;IACpC,MAAMgB,GAAG,GAAoB,EAAE,CAAA;IAC/B,MAAO,IAAI,CAAE;QACX,IAAIrD,CAAC,KAAKkD,KAAK,CAACxL,MAAM,GAAG,CAAC,EAAE;YAC1B2L,GAAG,CAAChD,IAAI,CAAC8C,OAAO,CAAC,CAAA;YACjB,MAAA;QACD,CAAA;QAED,mGAAA;QACA,MAAM7F,IAAI,GAAG4F,KAAK,CAAClD,CAAC,GAAG,CAAC,CAAC,CAAA;QACzB,IAAI,OAAO1C,IAAI,KAAK,QAAQ,EAAE;YAC5B,MAAA;QACD,CAAA;QACD,IAAIA,IAAI,IAAI6F,OAAO,CAAC,CAAC,CAAC,EAAE;YACtBA,OAAO,CAAC,CAAC,CAAC,GAAG7F,IAAI,GAAGqC,wBAAwB,CAAA;QAC7C,CAAA,MAAM;YACL0D,GAAG,CAAChD,IAAI,CAAC8C,OAAO,CAAC,CAAA;YACjBA,OAAO,GAAGC,gBAAgB,CAAC9F,IAAI,CAAC,CAAA;QACjC,CAAA;QAED0C,CAAC,EAAE,CAAA;IACJ,CAAA;IAED,OAAOqD,GAAG,CAAA;AACZ,CAAA;AACA,iEAAA;AACA,SAASD,gBAAgBA,CAACf,IAAY,EAAA;IACpC,OAAO;QAACM,cAAc,CAACN,IAAI,CAAC;QAAES,YAAY,CAACT,IAAI,CAAC;KAAC,CAAA;AACnD,CAAA;AACA,sCAAA;AACA,SAASM,cAAcA,CAACN,IAAY,EAAA;IAClC,OAAOiB,IAAI,CAAClI,GAAG,CAAC,CAAC,EAAEiH,IAAI,GAAG1C,wBAAwB,CAAC,CAAA;AACrD,CAAA;AACA,wCAAA;AACA,SAASmD,YAAYA,CAACT,IAAY,EAAA;IAChC,OAAOA,IAAI,GAAG1C,wBAAwB,CAAA;AACxC,CAAA;AAEA;;CAEG,GACH,SAASsB,OAAOA,CAAuDrM,GAAM,EAAEwK,GAAM,EAAEoD,QAAW,EAAA;IAChG,MAAMpN,KAAK,GAAGR,GAAG,CAACuK,GAAG,CAACC,GAAG,CAAC,CAAA;IAE1B,IAAIhK,KAAK,KAAKpB,SAAS,EAAE;QACvBY,GAAG,CAAC0K,GAAG,CAACF,GAAG,EAAEoD,QAAQ,CAAC,CAAA;QACtB,OAAOA,QAAQ,CAAA;IAChB,CAAA;IAED,OAAOpN,KAAK,CAAA;AACd,CAAA;AAEA,SAASkN,QAAQA,CAACD,IAAY,EAAEY,KAAa,EAAA;IAC3C,IAAIM,OAAO,GAAGlB,IAAI,CAAA;IAClB,MAAMmB,UAAU,GAAGD,OAAO,CAAC7L,MAAM,CAAA;IACjC,IAAI8L,UAAU,IAAI,GAAG,EAAE;QACrB,OAAOD,OAAO,CAAA;IACf,CAAA;IACD,IAAIN,KAAK,GAAGO,UAAU,EAAE;QACtBP,KAAK,GAAGO,UAAU,CAAA;IACnB,CAAA;IAED,IAAIC,KAAK,GAAGH,IAAI,CAAClI,GAAG,CAAC6H,KAAK,GAAG,EAAE,EAAE,CAAC,CAAC,CAAA;IACnC,IAAIQ,KAAK,GAAG,CAAC,EAAE;QACbA,KAAK,GAAG,CAAC,CAAA;IACV,CAAA;IAED,IAAIZ,GAAG,GAAGS,IAAI,CAACI,GAAG,CAACD,KAAK,GAAG,GAAG,EAAED,UAAU,CAAC,CAAA;IAC3C,IAAIX,GAAG,GAAGW,UAAU,GAAG,CAAC,EAAE;QACxBX,GAAG,GAAGW,UAAU,CAAA;IACjB,CAAA;IACD,IAAIX,GAAG,KAAKW,UAAU,EAAE;QACtBC,KAAK,GAAGH,IAAI,CAAClI,GAAG,CAACyH,GAAG,GAAG,GAAG,EAAE,CAAC,CAAC,CAAA;IAC/B,CAAA;IAEDU,OAAO,GAAGA,OAAO,CAACtI,KAAK,CAACwI,KAAK,EAAEZ,GAAG,CAAC,CAAA;IACnC,IAAIY,KAAK,GAAG,CAAC,EAAE;QACbF,OAAO,GAAG,CAAMA,GAAAA,EAAAA,OAAO,CAAE,CAAA,CAAA;IAC1B,CAAA;IACD,IAAIV,GAAG,GAAGW,UAAU,EAAE;QACpBD,OAAO,IAAI,KAAK,CAAA;IACjB,CAAA;IAED,OAAOA,OAAO,CAAA;AAChB;;AC5UA,IAAY,wBAyBX,CAAA;AAzBD,CAAA,SAAY,wBAAwB,EAAA;IAClC,wBAAA,CAAA,aAAA,CAAA,GAAA,cAA4B,CAAA;IAC5B,wBAAA,CAAA,YAAA,CAAA,GAAA,aAA0B,CAAA;IAC1B,wBAAA,CAAA,OAAA,CAAA,GAAA,OAAe,CAAA;IACf,wBAAA,CAAA,oBAAA,CAAA,GAAA,sBAA2C,CAAA;IAC3C,wBAAA,CAAA,cAAA,CAAA,GAAA,eAA8B,CAAA;IAC9B,wBAAA,CAAA,qBAAA,CAAA,GAAA,uBAA6C,CAAA;IAC7C,wBAAA,CAAA,6BAAA,CAAA,GAAA,gCAA8D,CAAA;IAC9D,wBAAA,CAAA,uBAAA,CAAA,GAAA,yBAAiD,CAAA;IACjD,wBAAA,CAAA,8BAAA,CAAA,GAAA,iCAAgE,CAAA;IAChE,wBAAA,CAAA,sBAAA,CAAA,GAAA,wBAA+C,CAAA;IAC/C,wBAAA,CAAA,OAAA,CAAA,GAAA,OAAe,CAAA;IACf,wBAAA,CAAA,UAAA,CAAA,GAAA,WAAsB,CAAA;IACtB,wBAAA,CAAA,WAAA,CAAA,GAAA,YAAwB,CAAA;IACxB,wBAAA,CAAA,sBAAA,CAAA,GAAA,mBAA0C,CAAA;IAC1C,wBAAA,CAAA,kBAAA,CAAA,GAAA,mBAAsC,CAAA;IACtC,wBAAA,CAAA,iBAAA,CAAA,GAAA,kBAAoC,CAAA;IACpC,wBAAA,CAAA,mBAAA,CAAA,GAAA,qBAAyC,CAAA;IACzC,wBAAA,CAAA,qBAAA,CAAA,GAAA,uBAA6C,CAAA;IAC7C,wBAAA,CAAA,eAAA,CAAA,GAAA,gBAAgC,CAAA;IAChC,wBAAA,CAAA,sBAAA,CAAA,GAAA,yBAAgD,CAAA;IAChD,wBAAA,CAAA,oBAAA,CAAA,GAAA,uBAA4C,CAAA;IAC5C,wBAAA,CAAA,aAAA,CAAA,GAAA,cAA4B,CAAA;IAC5B,wBAAA,CAAA,SAAA,CAAA,GAAA,SAAmB,CAAA;IACnB,wBAAA,CAAA,cAAA,CAAA,GAAA,eAA8B,CAAA;AAChC,CAAC,EAzBW,wBAAwB,IAAA,CAAxB,wBAAwB,GAyBnC,CAAA,CAAA,CAAA,CAAA,CAAA;AA8MD,IAAY,cAIX,CAAA;AAJD,CAAA,SAAY,cAAc,EAAA;IACxB,cAAA,CAAA,MAAA,CAAA,GAAA,MAAa,CAAA;IACb,cAAA,CAAA,OAAA,CAAA,GAAA,OAAe,CAAA;IACf,cAAA,CAAA,QAAA,CAAA,GAAA,QAAiB,CAAA;AACnB,CAAC,EAJW,cAAc,IAAA,CAAd,cAAc,GAIzB,CAAA,CAAA,CAAA,CAAA,CAAA;AAED,IAAY,gBAIX,CAAA;AAJD,CAAA,SAAY,gBAAgB,EAAA;IAC1B,gBAAA,CAAA,QAAA,CAAA,GAAA,QAAiB,CAAA;IACjB,gBAAA,CAAA,KAAA,CAAA,GAAA,KAAW,CAAA;IACX,gBAAA,CAAA,UAAA,CAAA,GAAA,UAAqB,CAAA;AACvB,CAAC,EAJW,gBAAgB,IAAA,CAAhB,gBAAgB,GAI3B,CAAA,CAAA,CAAA,CAAA,CAAA;AAED,IAAY,UAIX,CAAA;AAJD,CAAA,SAAY,UAAU,EAAA;IACpB,UAAA,CAAA,SAAA,CAAA,GAAA,SAAmB,CAAA;IACnB,UAAA,CAAA,KAAA,CAAA,GAAA,KAAW,CAAA;IACX,UAAA,CAAA,QAAA,CAAA,GAAA,QAAiB,CAAA;AACnB,CAAC,EAJW,UAAU,IAAA,CAAV,UAAU,GAIrB,CAAA,CAAA,CAAA,CAAA,CAAA;AAID,IAAY,oCAGX,CAAA;AAHD,CAAA,SAAY,oCAAoC,EAAA;IAC9C,oCAAA,CAAA,MAAA,CAAA,GAAA,MAAa,CAAA;IACb,oCAAA,CAAA,MAAA,CAAA,GAAA,MAAa,CAAA;AACf,CAAC,EAHW,oCAAoC,IAAA,CAApC,oCAAoC,GAG/C,CAAA,CAAA,CAAA,CAAA,CAAA;AA8BD,IAAY,mBAGX,CAAA;AAHD,CAAA,SAAY,mBAAmB,EAAA;IAC7B,mBAAA,CAAA,QAAA,CAAA,GAAA,QAAiB,CAAA;IACjB,mBAAA,CAAA,OAAA,CAAA,GAAA,OAAe,CAAA;AACjB,CAAC,EAHW,mBAAmB,IAAA,CAAnB,mBAAmB,GAG9B,CAAA,CAAA,CAAA,CAAA,CAAA;AASD,IAAY,kBAMX,CAAA;AAND,CAAA,SAAY,kBAAkB,EAAA;IAC5B,kBAAA,CAAA,MAAA,CAAA,GAAA,MAAa,CAAA;IACb,kBAAA,CAAA,gBAAA,CAAA,GAAA,iBAAkC,CAAA;IAClC,kBAAA,CAAA,cAAA,CAAA,GAAA,eAA8B,CAAA;IAC9B,kBAAA,CAAA,QAAA,CAAA,GAAA,QAAiB,CAAA;IACjB,kBAAA,CAAA,MAAA,CAAA,GAAA,MAAa,CAAA;AACf,CAAC,EANW,kBAAkB,IAAA,CAAlB,kBAAkB,GAM7B,CAAA,CAAA,CAAA,CAAA,CAAA;AAED,IAAY,2BAKX,CAAA;AALD,CAAA,SAAY,2BAA2B,EAAA;IACrC,2BAAA,CAAA,cAAA,CAAA,GAAA,eAA8B,CAAA;IAC9B,2BAAA,CAAA,KAAA,CAAA,GAAA,KAAW,CAAA;IACX,2BAAA,CAAA,eAAA,CAAA,GAAA,gBAAgC,CAAA;IAChC,2BAAA,CAAA,kBAAA,CAAA,GAAA,mBAAsC,CAAA;AACxC,CAAC,EALW,2BAA2B,IAAA,CAA3B,2BAA2B,GAKtC,CAAA,CAAA,CAAA,CAAA,CAAA;AA0BD,IAAY,eAOX,CAAA;AAPD,CAAA,SAAY,eAAe,EAAA;IACzB,eAAA,CAAA,OAAA,CAAA,GAAA,OAAe,CAAA;IACf,eAAA,CAAA,UAAA,CAAA,GAAA,WAAsB,CAAA;IACtB,eAAA,CAAA,OAAA,CAAA,GAAA,OAAe,CAAA;IACf,eAAA,CAAA,OAAA,CAAA,GAAA,QAAgB,CAAA;IAChB,eAAA,CAAA,WAAA,CAAA,GAAA,WAAuB,CAAA;IACvB,eAAA,CAAA,cAAA,CAAA,GAAA,eAA8B,CAAA;AAChC,CAAC,EAPW,eAAe,IAAA,CAAf,eAAe,GAO1B,CAAA,CAAA,CAAA,CAAA,CAAA;AAkED,yCAAA,GACA,IAAY,wBAIX,CAAA;AAJD,CAAA,SAAY,wBAAwB,EAAA;IAClC,wBAAA,CAAA,UAAA,CAAA,GAAA,UAAqB,CAAA;IACrB,wBAAA,CAAA,OAAA,CAAA,GAAA,OAAe,CAAA;IACf,wBAAA,CAAA,OAAA,CAAA,GAAA,OAAe,CAAA;AACjB,CAAC,EAJW,wBAAwB,IAAA,CAAxB,wBAAwB,GAInC,CAAA,CAAA,CAAA,CAAA;AC5cM,MAAM,uBAAuB,GAAG,CACrC,cAEwF,KACzD;IAC/B,IAAI,OAAO,IAAI,cAAc,EAAE;;QAE7B,MAAM,YAAY,GAAG,sBAAsB,CAAC,cAAc,CAAC,KAAK,CAAC,CAAA;QACjE,MAAM,mBAAmB,GAAG,oBAAoB,CAAC,cAAc,CAAC,KAAK,CAAC,CAAA;QAEtE,OAAO;YACL,GAAG,cAAc;YACjB,YAAY;YACZ,mBAAmB;SACpB,CAAA;IACF,CAAA,MAAM;;QAEL,MAAM,YAAY,GAAG,cAAc,CAAC,YAAY,IAAI,CAAA,CAAE,CAAA;QACtD,MAAM,mBAAmB,GAAG,MAAM,CAAC,WAAW,CAC5C,MAAM,CAAC,OAAO,CAAC,cAAc,CAAC,mBAAmB,IAAI,CAAA,CAAE,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,GAAK;gBAAC,CAAC;gBAAE,YAAY,CAAC,CAAC,CAAC;aAAC,CAAC,CAC/F,CAAA;QAED,MAAM,KAAK,GAAG,MAAM,CAAC,WAAW,CAC9B,MAAM,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,EAAE,KAAK,CAAC,GAAK;gBACjD,GAAG;gBACH,+BAA+B,CAAC,GAAG,EAAE,KAAK,EAAE,mBAAmB,CAAC,GAAG,CAAC,CAAC;aACtE,CAAC,CACH,CAAA;QAED,OAAO;YACL,GAAG,cAAc;YACjB,YAAY;YACZ,mBAAmB;YACnB,KAAK;SACN,CAAA;IACF,CAAA;AACH,CAAC,CAAA;AAED,SAAS,+BAA+B,CACtC,GAAW,EACX,KAAuB,EACvB,OAA6B,EAAA;IAE7B,OAAO;QACL,GAAG,EAAE,GAAG;QACR,OAAO,EAAE,OAAO,KAAK,KAAK,QAAQ,GAAG,IAAI,GAAG,KAAK;QACjD,OAAO,EAAE,OAAO,KAAK,KAAK,QAAQ,GAAG,KAAK,GAAG,SAAS;QACtD,MAAM,EAAE,SAAS;QACjB,QAAQ,EAAE;YACR,EAAE,EAAE,SAAS;YACb,OAAO,EAAE,SAAS;YAClB,OAAO,EAAE,OAAO,GAAG,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,GAAG,SAAS;YACtD,WAAW,EAAE,SAAS;QACvB,CAAA;KACF,CAAA;AACH,CAAC;AAED;;;;CAIG,GACI,MAAM,sBAAsB,GAAG,CACpC,KAAqC,KACI;IACzC,OAAO,MAAM,CAAC,WAAW,CACvB,MAAM,CAAC,OAAO,CAAC,KAAK,IAAI,CAAA,CAAE,CAAC,CACxB,GAAG,CAAC,CAAC,CAAC,GAAG,EAAE,MAAM,CAAC,GAAK;YAAC,GAAG;YAAE,mBAAmB,CAAC,MAAM,CAAC;SAAC,CAAC,CAC1D,MAAM,CAAC,CAAC,GAAG,KAAK,CAAC,GAAc,KAAK,KAAK,SAAS,CAAC,CACvD,CAAA;AACH,CAAC,CAAA;AAED;;;;CAIG,GACI,MAAM,oBAAoB,GAAG,CAClC,KAAqC,KACW;IAChD,MAAM,SAAS,GAAG,KAAK,IAAI,CAAA,CAAE,CAAA;IAC7B,OAAO,MAAM,CAAC,WAAW,CACvB,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,CACnB,MAAM,CAAC,CAAC,IAAI,KAAI;QACf,MAAM,OAAO,GAAG,SAAS,CAAC,IAAI,CAAC,CAAA;QAC/B,OAAO,OAAO,CAAC,OAAO,IAAI,OAAO,CAAC,QAAQ,IAAI,OAAO,CAAC,QAAQ,CAAC,OAAO,KAAK,SAAS,CAAA;IACtF,CAAC,CAAC,CACD,GAAG,CAAC,CAAC,IAAI,KAAI;QACZ,MAAM,OAAO,GAAG,SAAS,CAAC,IAAI,CAAC,CAAC,QAAQ,EAAE,OAAiB,CAAA;QAC3D,OAAO;YAAC,IAAI;YAAE,OAAO,GAAG,YAAY,CAAC,OAAO,CAAC,GAAG,SAAS;SAAC,CAAA;KAC3D,CAAC,CACL,CAAA;AACH,CAAC,CAAA;AA+BM,MAAM,mBAAmB,GAAG,CAAC,MAAqC,KAAkC;IACzG,OAAO,MAAM,KAAK,SAAS,GAAG,SAAS,GAAG,MAAM,CAAC,OAAO,IAAI,MAAM,CAAC,OAAO,CAAA;AAC5E,CAAC,CAAA;AAEM,MAAM,YAAY,GAAG,CAAC,QAAa,KAAS;IACjD,IAAI,OAAO,QAAQ,KAAK,QAAQ,EAAE;QAChC,OAAO,QAAQ,CAAA;IAChB,CAAA;IAED,IAAI;QACF,OAAO,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAA;IAC5B,CAAA,CAAC,OAAM;QACN,OAAO,QAAQ,CAAA;IAChB,CAAA;AACH,CAAC;ACnJD,oBAAA;AACO,MAAM,4BAA4B,GAAG,CAAC,CAAA;AAC7C,+EAAA;AACA,yFAAA;AACA,iFAAA;AACO,MAAM,yBAAyB,GAAG,IAAI,GAAG,CAAC;;IAE/C,UAAU;IACV,UAAU;IACV,UAAU;IACV,UAAU;IACV,UAAU;IACV,UAAU;IACV,UAAU;IACV,UAAU;IACV,UAAU;IACV,UAAU;IACV,UAAU;IACV,UAAU;IACV,UAAU;IACV,UAAU;IACV,UAAU;IACV,UAAU;IACV,UAAU;IACV,UAAU;IACV,UAAU;IACV,UAAU;IACV,UAAU;IACV,UAAU;;IAEV,UAAU;IACV,UAAU;IACV,UAAU;IACV,UAAU;IACV,UAAU;IACV,UAAU;IACV,UAAU;IACV,UAAU;IACV,UAAU;;IAEV,UAAU;IACV,UAAU;IACV,UAAU;IACV,UAAU;IACV,UAAU;IACV,UAAU;IACV,UAAU;IACV,UAAU;IACV,UAAU;IACV,UAAU;IACV,UAAU;IACV,UAAU;IACV,UAAU;IACV,UAAU;IACV,UAAU;IACV,UAAU;IACV,UAAU;IACV,UAAU;IACV,UAAU;IACV,UAAU;IACV,UAAU;IACV,UAAU;IACV,UAAU;IACV,UAAU;IACV,UAAU;IACV,UAAU;IACV,UAAU;IACV,UAAU;IACV,UAAU;IACV,UAAU;IACV,UAAU;IACV,UAAU;IACV,UAAU;IACV,UAAU;IACV,UAAU;IACV,UAAU;IACV,UAAU;IACV,UAAU;IACV,UAAU;IACV,UAAU;IACV,UAAU;IACV,UAAU;IACV,UAAU;CACX,CAAC,CAAA;AAEK,MAAM,aAAa,GAAG,MAAM,CAAA;AAEnB,SAAA,MAAM,CAAC,WAAgB,EAAE,OAAe,EAAA;IACtD,IAAI,CAAC,WAAW,IAAI,OAAO,WAAW,KAAK,QAAQ,IAAI,OAAO,CAAC,WAAW,CAAC,EAAE;QAC3E,MAAM,IAAI,KAAK,CAAC,OAAO,CAAC,CAAA;IACzB,CAAA;AACH,CAAC;AAED,SAAS,OAAO,CAAC,WAAmB,EAAA;IAClC,IAAI,WAAW,CAAC,IAAI,EAAE,CAAC,MAAM,KAAK,CAAC,EAAE;QACnC,OAAO,IAAI,CAAA;IACZ,CAAA;IACD,OAAO,KAAK,CAAA;AACd,CAAC;AAEK,SAAU,mBAAmB,CAAC,GAAW,EAAA;IAC7C,OAAO,GAAG,EAAE,OAAO,CAAC,MAAM,EAAE,EAAE,CAAC,CAAA;AACjC,CAAC;AAQM,eAAe,SAAS,CAAI,EAAoB,EAAE,KAAuB,EAAA;IAC9E,IAAI,SAAS,GAAG,IAAI,CAAA;IAEpB,IAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,UAAU,GAAG,CAAC,EAAE,CAAC,EAAE,CAAE;QAC7C,IAAI,CAAC,GAAG,CAAC,EAAE;;YAET,MAAM,IAAI,OAAO,CAAO,CAAC,CAAC,GAAK,UAAU,CAAC,CAAC,EAAE,KAAK,CAAC,UAAU,CAAC,CAAC,CAAA;QAChE,CAAA;QAED,IAAI;YACF,MAAM,GAAG,GAAG,MAAM,EAAE,EAAE,CAAA;YACtB,OAAO,GAAG,CAAA;QACX,CAAA,CAAC,OAAO,CAAC,EAAE;YACV,SAAS,GAAG,CAAC,CAAA;YACb,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC,CAAC,EAAE;gBACxB,MAAM,CAAC,CAAA;YACR,CAAA;QACF,CAAA;IACF,CAAA;IAED,MAAM,SAAS,CAAA;AACjB,CAAC;SAEe,gBAAgB,GAAA;IAC9B,OAAO,IAAI,IAAI,EAAE,CAAC,OAAO,EAAE,CAAA;AAC7B,CAAC;SAEe,cAAc,GAAA;IAC5B,OAAO,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE,CAAA;AACjC,CAAC;AAEe,SAAA,cAAc,CAAC,EAAc,EAAE,OAAe,EAAA;;;IAG5D,MAAM,CAAC,GAAG,UAAU,CAAC,EAAE,EAAE,OAAO,CAAQ,CAAA;;IAExC,CAAC,EAAE,KAAK,IAAI,CAAC,EAAE,KAAK,EAAE,CAAA;IACtB,OAAO,CAAC,CAAA;AACV,CAAC;SAWe,QAAQ,GAAA;IACtB,OAAO,OAAO,KAAK,KAAK,WAAW,GAAG,KAAK,GAAG,OAAO,UAAU,CAAC,KAAK,KAAK,WAAW,GAAG,UAAU,CAAC,KAAK,GAAG,SAAS,CAAA;AACtH,CAAC;AAED,uBAAA;AACA,6EAAA;AACA,iFAAA;AACA,wDAAA;AACA,SAAS,KAAK,CAAC,GAAW,EAAA;IACxB,IAAI,IAAI,GAAG,UAAU,CAAA,CAAA,mBAAA;IACrB,IAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG,CAAC,MAAM,EAAE,CAAC,EAAE,CAAE;QACnC,IAAI,IAAI,GAAG,CAAC,UAAU,CAAC,CAAC,CAAC,CAAA;QACzB,IAAI,IAAI,CAAC,IAAI,IAAI,CAAC,IAAA,CAAK,IAAI,IAAI,CAAC,CAAC,GAAA,CAAI,IAAI,IAAI,CAAC,CAAC,GAAA,CAAI,IAAI,IAAI,CAAC,CAAC,GAAA,CAAI,IAAI,IAAI,EAAE,CAAC,CAAA;IAC7E,CAAA;;IAED,OAAO,CAAC,IAAI,KAAK,CAAC,EAAE,QAAQ,CAAC,EAAE,CAAC,CAAC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,CAAA;AACnD,CAAC;AAEK,SAAU,gBAAgB,CAAC,KAAa,EAAE,UAAqB,GAAA,CAAC,EAAE,cAA4B,EAAA;IAClG,MAAM,SAAS,GAAG,KAAK,CAAC,KAAK,CAAC,CAAA;;IAE9B,IAAI,cAAc,EAAE,GAAG,CAAC,SAAS,CAAC,EAAE;QAClC,OAAO,KAAK,CAAA;IACb,CAAA;;IAGD,MAAM,OAAO,GAAG,QAAQ,CAAC,SAAS,EAAE,EAAE,CAAC,CAAA;IACvC,MAAM,SAAS,GAAG,OAAO,GAAG,UAAU,CAAA;IAEtC,OAAO,SAAS,GAAG,UAAU,CAAA;AAC/B,CAAC;AAEK,SAAU,UAAU,CACxB,QAA2C,EAAA;IAE3C,OAAO,OAAO,CAAC,GAAG,CAChB,QAAQ,CAAC,GAAG,CAAC,CAAC,CAAC,GACb,CAAC,CAAC,IAAI,OAAO,CAAC,OAAO,EAAE,EAAE,IAAI,CAC3B,CAAC,KAAU,GAAA,CAAM;gBAAE,MAAM,EAAE,WAAoB;gBAAE,KAAK;YAAA,CAAE,CAAC,EACzD,CAAC,MAAW,GAAA,CAAM;gBAAE,MAAM,EAAE,UAAmB;gBAAE,MAAM;YAAA,CAAE,CAAC,CAC3D,CACF,CACF,CAAA;AACH;AC5MA,mDAAA;AACA,8DAAA;AACA,0CAAA;AACA,gEAAA;AACA,EAAA;AACA,uCAAA;AACA,uDAAA;AACA,EAAA;AACA,gDAAA;AAEA,mBAAA;AACA,MAAM,CAAC,GAAG,MAAM,CAAC,YAAY,CAAA;AAC7B,MAAM,YAAY,GAAG,mEAAmE,CAAA;AACxF,MAAM,cAAc,GAAQ,CAAA,CAAE,CAAA;AAE9B,SAAS,YAAY,CAAC,QAAa,EAAE,SAAc,EAAA;IACjD,IAAI,CAAC,cAAc,CAAC,QAAQ,CAAC,EAAE;QAC7B,cAAc,CAAC,QAAQ,CAAC,GAAG,CAAA,CAAE,CAAA;QAC7B,IAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,QAAQ,CAAC,MAAM,EAAE,CAAC,EAAE,CAAE;YACxC,cAAc,CAAC,QAAQ,CAAC,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAA;QACjD,CAAA;IACF,CAAA;IACD,OAAO,cAAc,CAAC,QAAQ,CAAC,CAAC,SAAS,CAAC,CAAA;AAC5C,CAAC;AAEM,MAAM,QAAQ,GAAG;IACtB,gBAAgB,EAAE,SAAU,KAAU,EAAA;QACpC,IAAI,KAAK,IAAI,IAAI,EAAE;YACjB,OAAO,EAAE,CAAA;QACV,CAAA;QACD,MAAM,GAAG,GAAG,QAAQ,CAAC,SAAS,CAAC,KAAK,EAAE,CAAC,EAAE,SAAU,CAAM,EAAA;YACvD,OAAO,YAAY,CAAC,MAAM,CAAC,CAAC,CAAC,CAAA;QAC/B,CAAC,CAAC,CAAA;QACF,OACE,GAAG,CAAC,MAAM,GAAG,CAAC,CAAA,0BAAA;;YAEd,QAAQ;YACR,KAAK,CAAC;gBACJ,OAAO,GAAG,CAAA;YACZ,KAAK,CAAC;gBACJ,OAAO,GAAG,GAAG,KAAK,CAAA;YACpB,KAAK,CAAC;gBACJ,OAAO,GAAG,GAAG,IAAI,CAAA;YACnB,KAAK,CAAC;gBACJ,OAAO,GAAG,GAAG,GAAG,CAAA;QACnB,CAAA;KACF;IAED,oBAAoB,EAAE,SAAU,KAAU,EAAA;QACxC,IAAI,KAAK,IAAI,IAAI,EAAE;YACjB,OAAO,EAAE,CAAA;QACV,CAAA;QACD,IAAI,KAAK,IAAI,EAAE,EAAE;YACf,OAAO,IAAI,CAAA;QACZ,CAAA;QACD,OAAO,QAAQ,CAAC,WAAW,CAAC,KAAK,CAAC,MAAM,EAAE,EAAE,EAAE,SAAU,KAAU,EAAA;YAChE,OAAO,YAAY,CAAC,YAAY,EAAE,KAAK,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,CAAA;QACxD,CAAC,CAAC,CAAA;KACH;IAED,QAAQ,EAAE,SAAU,YAAiB,EAAA;QACnC,OAAO,QAAQ,CAAC,SAAS,CAAC,YAAY,EAAE,EAAE,EAAE,SAAU,CAAM,EAAA;YAC1D,OAAO,CAAC,CAAC,CAAC,CAAC,CAAA;QACb,CAAC,CAAC,CAAA;KACH;IACD,SAAS,EAAE,SAAU,YAAiB,EAAE,WAAgB,EAAE,cAAmB,EAAA;QAC3E,IAAI,YAAY,IAAI,IAAI,EAAE;YACxB,OAAO,EAAE,CAAA;QACV,CAAA;QACD,MAAM,kBAAkB,GAAQ,CAAA,CAAE,EAChC,0BAA0B,GAAQ,CAAA,CAAE,EACpC,YAAY,GAAG,EAAE,CAAA;QAEnB,IAAI,CAAC,EACH,KAAK,EACL,SAAS,GAAG,EAAE,EACd,UAAU,GAAG,EAAE,EACf,SAAS,GAAG,EAAE,EACd,iBAAiB,GAAG,CAAC,EACrB,gBAAgB,GAAG,CAAC,EACpB,eAAe,GAAG,CAAC,EACnB,gBAAgB,GAAG,CAAC,EACpB,qBAAqB,GAAG,CAAC,EACzB,EAAE,CAAA;QAEJ,IAAK,EAAE,GAAG,CAAC,EAAE,EAAE,GAAG,YAAY,CAAC,MAAM,EAAE,EAAE,IAAI,CAAC,CAAE;YAC9C,SAAS,GAAG,YAAY,CAAC,MAAM,CAAC,EAAE,CAAC,CAAA;YACnC,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,kBAAkB,EAAE,SAAS,CAAC,EAAE;gBACxE,kBAAkB,CAAC,SAAS,CAAC,GAAG,gBAAgB,EAAE,CAAA;gBAClD,0BAA0B,CAAC,SAAS,CAAC,GAAG,IAAI,CAAA;YAC7C,CAAA;YAED,UAAU,GAAG,SAAS,GAAG,SAAS,CAAA;YAClC,IAAI,MAAM,CAAC,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,kBAAkB,EAAE,UAAU,CAAC,EAAE;gBACxE,SAAS,GAAG,UAAU,CAAA;YACvB,CAAA,MAAM;gBACL,IAAI,MAAM,CAAC,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,0BAA0B,EAAE,SAAS,CAAC,EAAE;oBAC/E,IAAI,SAAS,CAAC,UAAU,CAAC,CAAC,CAAC,GAAG,GAAG,EAAE;wBACjC,IAAK,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,eAAe,EAAE,CAAC,EAAE,CAAE;4BACpC,gBAAgB,GAAG,gBAAgB,IAAI,CAAC,CAAA;4BACxC,IAAI,qBAAqB,IAAI,WAAW,GAAG,CAAC,EAAE;gCAC5C,qBAAqB,GAAG,CAAC,CAAA;gCACzB,YAAY,CAAC,IAAI,CAAC,cAAc,CAAC,gBAAgB,CAAC,CAAC,CAAA;gCACnD,gBAAgB,GAAG,CAAC,CAAA;4BACrB,CAAA,MAAM;gCACL,qBAAqB,EAAE,CAAA;4BACxB,CAAA;wBACF,CAAA;wBACD,KAAK,GAAG,SAAS,CAAC,UAAU,CAAC,CAAC,CAAC,CAAA;wBAC/B,IAAK,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,CAAE;4BACtB,gBAAgB,GAAG,AAAC,gBAAgB,IAAI,CAAC,GAAK,KAAK,GAAG,CAAC,CAAC,CAAA;4BACxD,IAAI,qBAAqB,IAAI,WAAW,GAAG,CAAC,EAAE;gCAC5C,qBAAqB,GAAG,CAAC,CAAA;gCACzB,YAAY,CAAC,IAAI,CAAC,cAAc,CAAC,gBAAgB,CAAC,CAAC,CAAA;gCACnD,gBAAgB,GAAG,CAAC,CAAA;4BACrB,CAAA,MAAM;gCACL,qBAAqB,EAAE,CAAA;4BACxB,CAAA;4BACD,KAAK,GAAG,KAAK,IAAI,CAAC,CAAA;wBACnB,CAAA;oBACF,CAAA,MAAM;wBACL,KAAK,GAAG,CAAC,CAAA;wBACT,IAAK,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,eAAe,EAAE,CAAC,EAAE,CAAE;4BACpC,gBAAgB,GAAG,AAAC,gBAAgB,IAAI,CAAC,GAAI,KAAK,CAAA;4BAClD,IAAI,qBAAqB,IAAI,WAAW,GAAG,CAAC,EAAE;gCAC5C,qBAAqB,GAAG,CAAC,CAAA;gCACzB,YAAY,CAAC,IAAI,CAAC,cAAc,CAAC,gBAAgB,CAAC,CAAC,CAAA;gCACnD,gBAAgB,GAAG,CAAC,CAAA;4BACrB,CAAA,MAAM;gCACL,qBAAqB,EAAE,CAAA;4BACxB,CAAA;4BACD,KAAK,GAAG,CAAC,CAAA;wBACV,CAAA;wBACD,KAAK,GAAG,SAAS,CAAC,UAAU,CAAC,CAAC,CAAC,CAAA;wBAC/B,IAAK,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,EAAE,EAAE,CAAC,EAAE,CAAE;4BACvB,gBAAgB,GAAG,AAAC,gBAAgB,IAAI,CAAC,GAAK,KAAK,GAAG,CAAC,CAAC,CAAA;4BACxD,IAAI,qBAAqB,IAAI,WAAW,GAAG,CAAC,EAAE;gCAC5C,qBAAqB,GAAG,CAAC,CAAA;gCACzB,YAAY,CAAC,IAAI,CAAC,cAAc,CAAC,gBAAgB,CAAC,CAAC,CAAA;gCACnD,gBAAgB,GAAG,CAAC,CAAA;4BACrB,CAAA,MAAM;gCACL,qBAAqB,EAAE,CAAA;4BACxB,CAAA;4BACD,KAAK,GAAG,KAAK,IAAI,CAAC,CAAA;wBACnB,CAAA;oBACF,CAAA;oBACD,iBAAiB,EAAE,CAAA;oBACnB,IAAI,iBAAiB,IAAI,CAAC,EAAE;wBAC1B,iBAAiB,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,eAAe,CAAC,CAAA;wBAChD,eAAe,EAAE,CAAA;oBAClB,CAAA;oBACD,OAAO,0BAA0B,CAAC,SAAS,CAAC,CAAA;gBAC7C,CAAA,MAAM;oBACL,KAAK,GAAG,kBAAkB,CAAC,SAAS,CAAC,CAAA;oBACrC,IAAK,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,eAAe,EAAE,CAAC,EAAE,CAAE;wBACpC,gBAAgB,GAAG,AAAC,gBAAgB,IAAI,CAAC,GAAK,KAAK,GAAG,CAAC,CAAC,CAAA;wBACxD,IAAI,qBAAqB,IAAI,WAAW,GAAG,CAAC,EAAE;4BAC5C,qBAAqB,GAAG,CAAC,CAAA;4BACzB,YAAY,CAAC,IAAI,CAAC,cAAc,CAAC,gBAAgB,CAAC,CAAC,CAAA;4BACnD,gBAAgB,GAAG,CAAC,CAAA;wBACrB,CAAA,MAAM;4BACL,qBAAqB,EAAE,CAAA;wBACxB,CAAA;wBACD,KAAK,GAAG,KAAK,IAAI,CAAC,CAAA;oBACnB,CAAA;gBACF,CAAA;gBACD,iBAAiB,EAAE,CAAA;gBACnB,IAAI,iBAAiB,IAAI,CAAC,EAAE;oBAC1B,iBAAiB,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,eAAe,CAAC,CAAA;oBAChD,eAAe,EAAE,CAAA;gBAClB,CAAA;;gBAED,kBAAkB,CAAC,UAAU,CAAC,GAAG,gBAAgB,EAAE,CAAA;gBACnD,SAAS,GAAG,MAAM,CAAC,SAAS,CAAC,CAAA;YAC9B,CAAA;QACF,CAAA;;QAGD,IAAI,SAAS,KAAK,EAAE,EAAE;YACpB,IAAI,MAAM,CAAC,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,0BAA0B,EAAE,SAAS,CAAC,EAAE;gBAC/E,IAAI,SAAS,CAAC,UAAU,CAAC,CAAC,CAAC,GAAG,GAAG,EAAE;oBACjC,IAAK,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,eAAe,EAAE,CAAC,EAAE,CAAE;wBACpC,gBAAgB,GAAG,gBAAgB,IAAI,CAAC,CAAA;wBACxC,IAAI,qBAAqB,IAAI,WAAW,GAAG,CAAC,EAAE;4BAC5C,qBAAqB,GAAG,CAAC,CAAA;4BACzB,YAAY,CAAC,IAAI,CAAC,cAAc,CAAC,gBAAgB,CAAC,CAAC,CAAA;4BACnD,gBAAgB,GAAG,CAAC,CAAA;wBACrB,CAAA,MAAM;4BACL,qBAAqB,EAAE,CAAA;wBACxB,CAAA;oBACF,CAAA;oBACD,KAAK,GAAG,SAAS,CAAC,UAAU,CAAC,CAAC,CAAC,CAAA;oBAC/B,IAAK,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,CAAE;wBACtB,gBAAgB,GAAG,AAAC,gBAAgB,IAAI,CAAC,GAAK,KAAK,GAAG,CAAC,CAAC,CAAA;wBACxD,IAAI,qBAAqB,IAAI,WAAW,GAAG,CAAC,EAAE;4BAC5C,qBAAqB,GAAG,CAAC,CAAA;4BACzB,YAAY,CAAC,IAAI,CAAC,cAAc,CAAC,gBAAgB,CAAC,CAAC,CAAA;4BACnD,gBAAgB,GAAG,CAAC,CAAA;wBACrB,CAAA,MAAM;4BACL,qBAAqB,EAAE,CAAA;wBACxB,CAAA;wBACD,KAAK,GAAG,KAAK,IAAI,CAAC,CAAA;oBACnB,CAAA;gBACF,CAAA,MAAM;oBACL,KAAK,GAAG,CAAC,CAAA;oBACT,IAAK,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,eAAe,EAAE,CAAC,EAAE,CAAE;wBACpC,gBAAgB,GAAG,AAAC,gBAAgB,IAAI,CAAC,GAAI,KAAK,CAAA;wBAClD,IAAI,qBAAqB,IAAI,WAAW,GAAG,CAAC,EAAE;4BAC5C,qBAAqB,GAAG,CAAC,CAAA;4BACzB,YAAY,CAAC,IAAI,CAAC,cAAc,CAAC,gBAAgB,CAAC,CAAC,CAAA;4BACnD,gBAAgB,GAAG,CAAC,CAAA;wBACrB,CAAA,MAAM;4BACL,qBAAqB,EAAE,CAAA;wBACxB,CAAA;wBACD,KAAK,GAAG,CAAC,CAAA;oBACV,CAAA;oBACD,KAAK,GAAG,SAAS,CAAC,UAAU,CAAC,CAAC,CAAC,CAAA;oBAC/B,IAAK,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,EAAE,EAAE,CAAC,EAAE,CAAE;wBACvB,gBAAgB,GAAG,AAAC,gBAAgB,IAAI,CAAC,GAAK,KAAK,GAAG,CAAC,CAAC,CAAA;wBACxD,IAAI,qBAAqB,IAAI,WAAW,GAAG,CAAC,EAAE;4BAC5C,qBAAqB,GAAG,CAAC,CAAA;4BACzB,YAAY,CAAC,IAAI,CAAC,cAAc,CAAC,gBAAgB,CAAC,CAAC,CAAA;4BACnD,gBAAgB,GAAG,CAAC,CAAA;wBACrB,CAAA,MAAM;4BACL,qBAAqB,EAAE,CAAA;wBACxB,CAAA;wBACD,KAAK,GAAG,KAAK,IAAI,CAAC,CAAA;oBACnB,CAAA;gBACF,CAAA;gBACD,iBAAiB,EAAE,CAAA;gBACnB,IAAI,iBAAiB,IAAI,CAAC,EAAE;oBAC1B,iBAAiB,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,eAAe,CAAC,CAAA;oBAChD,eAAe,EAAE,CAAA;gBAClB,CAAA;gBACD,OAAO,0BAA0B,CAAC,SAAS,CAAC,CAAA;YAC7C,CAAA,MAAM;gBACL,KAAK,GAAG,kBAAkB,CAAC,SAAS,CAAC,CAAA;gBACrC,IAAK,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,eAAe,EAAE,CAAC,EAAE,CAAE;oBACpC,gBAAgB,GAAG,AAAC,gBAAgB,IAAI,CAAC,GAAK,KAAK,GAAG,CAAC,CAAC,CAAA;oBACxD,IAAI,qBAAqB,IAAI,WAAW,GAAG,CAAC,EAAE;wBAC5C,qBAAqB,GAAG,CAAC,CAAA;wBACzB,YAAY,CAAC,IAAI,CAAC,cAAc,CAAC,gBAAgB,CAAC,CAAC,CAAA;wBACnD,gBAAgB,GAAG,CAAC,CAAA;oBACrB,CAAA,MAAM;wBACL,qBAAqB,EAAE,CAAA;oBACxB,CAAA;oBACD,KAAK,GAAG,KAAK,IAAI,CAAC,CAAA;gBACnB,CAAA;YACF,CAAA;YACD,iBAAiB,EAAE,CAAA;YACnB,IAAI,iBAAiB,IAAI,CAAC,EAAE;gBAC1B,iBAAiB,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,eAAe,CAAC,CAAA;gBAChD,eAAe,EAAE,CAAA;YAClB,CAAA;QACF,CAAA;;QAGD,KAAK,GAAG,CAAC,CAAA;QACT,IAAK,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,eAAe,EAAE,CAAC,EAAE,CAAE;YACpC,gBAAgB,GAAG,AAAC,gBAAgB,IAAI,CAAC,GAAK,KAAK,GAAG,CAAC,CAAC,CAAA;YACxD,IAAI,qBAAqB,IAAI,WAAW,GAAG,CAAC,EAAE;gBAC5C,qBAAqB,GAAG,CAAC,CAAA;gBACzB,YAAY,CAAC,IAAI,CAAC,cAAc,CAAC,gBAAgB,CAAC,CAAC,CAAA;gBACnD,gBAAgB,GAAG,CAAC,CAAA;YACrB,CAAA,MAAM;gBACL,qBAAqB,EAAE,CAAA;YACxB,CAAA;YACD,KAAK,GAAG,KAAK,IAAI,CAAC,CAAA;QACnB,CAAA;;QAGD,MAAO,IAAI,CAAE;YACX,gBAAgB,GAAG,gBAAgB,IAAI,CAAC,CAAA;YACxC,IAAI,qBAAqB,IAAI,WAAW,GAAG,CAAC,EAAE;gBAC5C,YAAY,CAAC,IAAI,CAAC,cAAc,CAAC,gBAAgB,CAAC,CAAC,CAAA;gBACnD,MAAK;YACN,CAAA,MAAM;gBACL,qBAAqB,EAAE,CAAA;YACxB,CAAA;QACF,CAAA;QACD,OAAO,YAAY,CAAC,IAAI,CAAC,EAAE,CAAC,CAAA;KAC7B;IAED,UAAU,EAAE,SAAU,UAAe,EAAA;QACnC,IAAI,UAAU,IAAI,IAAI,EAAE;YACtB,OAAO,EAAE,CAAA;QACV,CAAA;QACD,IAAI,UAAU,IAAI,EAAE,EAAE;YACpB,OAAO,IAAI,CAAA;QACZ,CAAA;QACD,OAAO,QAAQ,CAAC,WAAW,CAAC,UAAU,CAAC,MAAM,EAAE,KAAK,EAAE,SAAU,KAAU,EAAA;YACxE,OAAO,UAAU,CAAC,UAAU,CAAC,KAAK,CAAC,CAAA;QACrC,CAAC,CAAC,CAAA;KACH;IAED,WAAW,EAAE,SAAU,MAAW,EAAE,UAAe,EAAE,YAAiB,EAAA;QACpE,MAAM,UAAU,GAAG,EAAE,EACnB,MAAM,GAAG,EAAE,EACX,IAAI,GAAG;YAAE,GAAG,EAAE,YAAY,CAAC,CAAC,CAAC;YAAE,QAAQ,EAAE,UAAU;YAAE,KAAK,EAAE,CAAC;QAAA,CAAE,CAAA;QAEjE,IACE,SAAS,GAAG,CAAC,CAAA,CACb,QAAQ,GAAG,CAAC,CAAA,CACZ,OAAO,GAAG,CAAC,CACX,CAAA,KAAK,GAAQ,EAAE,CACf,CAAA,CAAC,CACD,CAAA,CAAC,EACD,IAAI,CAAA,CACJ,IAAI,CAAA,CACJ,QAAQ,CAAA,CACR,KAAK,CAAA,CACL,EAAC;QAEH,IAAK,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,CAAE;YACzB,UAAU,CAAC,CAAC,CAAC,GAAG,CAAC,CAAA;QAClB,CAAA;QAED,IAAI,GAAG,CAAC,CAAA;QACR,QAAQ,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC,CAAA;QACzB,KAAK,GAAG,CAAC,CAAA;QACT,MAAO,KAAK,IAAI,QAAQ,CAAE;YACxB,IAAI,GAAG,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,QAAQ,CAAA;YAC/B,IAAI,CAAC,QAAQ,KAAK,CAAC,CAAA;YACnB,IAAI,IAAI,CAAC,QAAQ,IAAI,CAAC,EAAE;gBACtB,IAAI,CAAC,QAAQ,GAAG,UAAU,CAAA;gBAC1B,IAAI,CAAC,GAAG,GAAG,YAAY,CAAC,IAAI,CAAC,KAAK,EAAE,CAAC,CAAA;YACtC,CAAA;YACD,IAAI,IAAI,CAAC,IAAI,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,IAAI,KAAK,CAAA;YAClC,KAAK,KAAK,CAAC,CAAA;QACZ,CAAA;;QAGD,OAAgB,IAAI;YAClB,KAAK,CAAC;gBACJ,IAAI,GAAG,CAAC,CAAA;gBACR,QAAQ,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC,CAAA;gBACzB,KAAK,GAAG,CAAC,CAAA;gBACT,MAAO,KAAK,IAAI,QAAQ,CAAE;oBACxB,IAAI,GAAG,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,QAAQ,CAAA;oBAC/B,IAAI,CAAC,QAAQ,KAAK,CAAC,CAAA;oBACnB,IAAI,IAAI,CAAC,QAAQ,IAAI,CAAC,EAAE;wBACtB,IAAI,CAAC,QAAQ,GAAG,UAAU,CAAA;wBAC1B,IAAI,CAAC,GAAG,GAAG,YAAY,CAAC,IAAI,CAAC,KAAK,EAAE,CAAC,CAAA;oBACtC,CAAA;oBACD,IAAI,IAAI,CAAC,IAAI,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,IAAI,KAAK,CAAA;oBAClC,KAAK,KAAK,CAAC,CAAA;gBACZ,CAAA;gBACD,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,CAAA;gBACX,MAAK;YACP,KAAK,CAAC;gBACJ,IAAI,GAAG,CAAC,CAAA;gBACR,QAAQ,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,EAAE,CAAC,CAAA;gBAC1B,KAAK,GAAG,CAAC,CAAA;gBACT,MAAO,KAAK,IAAI,QAAQ,CAAE;oBACxB,IAAI,GAAG,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,QAAQ,CAAA;oBAC/B,IAAI,CAAC,QAAQ,KAAK,CAAC,CAAA;oBACnB,IAAI,IAAI,CAAC,QAAQ,IAAI,CAAC,EAAE;wBACtB,IAAI,CAAC,QAAQ,GAAG,UAAU,CAAA;wBAC1B,IAAI,CAAC,GAAG,GAAG,YAAY,CAAC,IAAI,CAAC,KAAK,EAAE,CAAC,CAAA;oBACtC,CAAA;oBACD,IAAI,IAAI,CAAC,IAAI,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,IAAI,KAAK,CAAA;oBAClC,KAAK,KAAK,CAAC,CAAA;gBACZ,CAAA;gBACD,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,CAAA;gBACX,MAAK;YACP,KAAK,CAAC;gBACJ,OAAO,EAAE,CAAA;QACZ,CAAA;QACD,UAAU,CAAC,CAAC,CAAC,GAAG,CAAC,CAAA;QACjB,CAAC,GAAG,CAAC,CAAA;QACL,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,CAAA;QACd,MAAO,IAAI,CAAE;YACX,IAAI,IAAI,CAAC,KAAK,GAAG,MAAM,EAAE;gBACvB,OAAO,EAAE,CAAA;YACV,CAAA;YAED,IAAI,GAAG,CAAC,CAAA;YACR,QAAQ,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,OAAO,CAAC,CAAA;YAC/B,KAAK,GAAG,CAAC,CAAA;YACT,MAAO,KAAK,IAAI,QAAQ,CAAE;gBACxB,IAAI,GAAG,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,QAAQ,CAAA;gBAC/B,IAAI,CAAC,QAAQ,KAAK,CAAC,CAAA;gBACnB,IAAI,IAAI,CAAC,QAAQ,IAAI,CAAC,EAAE;oBACtB,IAAI,CAAC,QAAQ,GAAG,UAAU,CAAA;oBAC1B,IAAI,CAAC,GAAG,GAAG,YAAY,CAAC,IAAI,CAAC,KAAK,EAAE,CAAC,CAAA;gBACtC,CAAA;gBACD,IAAI,IAAI,CAAC,IAAI,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,IAAI,KAAK,CAAA;gBAClC,KAAK,KAAK,CAAC,CAAA;YACZ,CAAA;YAED,OAAS,CAAC,GAAG,IAAI;gBACf,KAAK,CAAC;oBACJ,IAAI,GAAG,CAAC,CAAA;oBACR,QAAQ,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC,CAAA;oBACzB,KAAK,GAAG,CAAC,CAAA;oBACT,MAAO,KAAK,IAAI,QAAQ,CAAE;wBACxB,IAAI,GAAG,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,QAAQ,CAAA;wBAC/B,IAAI,CAAC,QAAQ,KAAK,CAAC,CAAA;wBACnB,IAAI,IAAI,CAAC,QAAQ,IAAI,CAAC,EAAE;4BACtB,IAAI,CAAC,QAAQ,GAAG,UAAU,CAAA;4BAC1B,IAAI,CAAC,GAAG,GAAG,YAAY,CAAC,IAAI,CAAC,KAAK,EAAE,CAAC,CAAA;wBACtC,CAAA;wBACD,IAAI,IAAI,CAAC,IAAI,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,IAAI,KAAK,CAAA;wBAClC,KAAK,KAAK,CAAC,CAAA;oBACZ,CAAA;oBAED,UAAU,CAAC,QAAQ,EAAE,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,CAAA;oBAChC,CAAC,GAAG,QAAQ,GAAG,CAAC,CAAA;oBAChB,SAAS,EAAE,CAAA;oBACX,MAAK;gBACP,KAAK,CAAC;oBACJ,IAAI,GAAG,CAAC,CAAA;oBACR,QAAQ,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,EAAE,CAAC,CAAA;oBAC1B,KAAK,GAAG,CAAC,CAAA;oBACT,MAAO,KAAK,IAAI,QAAQ,CAAE;wBACxB,IAAI,GAAG,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,QAAQ,CAAA;wBAC/B,IAAI,CAAC,QAAQ,KAAK,CAAC,CAAA;wBACnB,IAAI,IAAI,CAAC,QAAQ,IAAI,CAAC,EAAE;4BACtB,IAAI,CAAC,QAAQ,GAAG,UAAU,CAAA;4BAC1B,IAAI,CAAC,GAAG,GAAG,YAAY,CAAC,IAAI,CAAC,KAAK,EAAE,CAAC,CAAA;wBACtC,CAAA;wBACD,IAAI,IAAI,CAAC,IAAI,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,IAAI,KAAK,CAAA;wBAClC,KAAK,KAAK,CAAC,CAAA;oBACZ,CAAA;oBACD,UAAU,CAAC,QAAQ,EAAE,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,CAAA;oBAChC,CAAC,GAAG,QAAQ,GAAG,CAAC,CAAA;oBAChB,SAAS,EAAE,CAAA;oBACX,MAAK;gBACP,KAAK,CAAC;oBACJ,OAAO,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,CAAA;YACzB,CAAA;YAED,IAAI,SAAS,IAAI,CAAC,EAAE;gBAClB,SAAS,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,OAAO,CAAC,CAAA;gBAChC,OAAO,EAAE,CAAA;YACV,CAAA;YAED,IAAI,UAAU,CAAC,CAAC,CAAC,EAAE;gBACjB,KAAK,GAAG,UAAU,CAAC,CAAC,CAAC,CAAA;YACtB,CAAA,MAAM;gBACL,IAAI,CAAC,KAAK,QAAQ,EAAE;oBAClB,KAAK,GAAG,CAAC,GAAG,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAA;gBACxB,CAAA,MAAM;oBACL,OAAO,IAAI,CAAA;gBACZ,CAAA;YACF,CAAA;YACD,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAA;;YAGlB,UAAU,CAAC,QAAQ,EAAE,CAAC,GAAG,CAAC,GAAG,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,CAAA;YAC5C,SAAS,EAAE,CAAA;YAEX,CAAC,GAAG,KAAK,CAAA;YAET,IAAI,SAAS,IAAI,CAAC,EAAE;gBAClB,SAAS,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,OAAO,CAAC,CAAA;gBAChC,OAAO,EAAE,CAAA;YACV,CAAA;QACF,CAAA;KACF;CACF;MC5cY,kBAAkB,CAAA;IAG7B,WAAA,EAAA;QAFA,IAAM,CAAA,MAAA,GAAoD,CAAA,CAAE,CAAA;QAG1D,IAAI,CAAC,MAAM,GAAG,CAAA,CAAE,CAAA;KACjB;IAED,EAAE,CAAC,KAAa,EAAE,QAAkC,EAAA;QAClD,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE;YACvB,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,GAAG,EAAE,CAAA;QACxB,CAAA;QACD,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAA;QAEjC,OAAO,MAAK;YACV,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,GAAG,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,GAAK,CAAC,KAAK,QAAQ,CAAC,CAAA;QACvE,CAAC,CAAA;KACF;IAED,IAAI,CAAC,KAAa,EAAE,OAAY,EAAA;QAC9B,KAAK,MAAM,QAAQ,IAAI,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,IAAI,EAAE,CAAE;YAC/C,QAAQ,CAAC,OAAO,CAAC,CAAA;QAClB,CAAA;QACD,KAAK,MAAM,QAAQ,IAAI,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,IAAI,EAAE,CAAE;YAC7C,QAAQ,CAAC,KAAK,EAAE,OAAO,CAAC,CAAA;QACzB,CAAA;KACF;AACF;AC4BD,MAAM,qBAAsB,SAAQ,KAAK,CAAA;IAGvC,WAAmB,CAAA,QAA8B,EAAS,aAAqB,CAAA;QAC7E,KAAK,CAAC,4CAA4C,GAAG,QAAQ,CAAC,MAAM,GAAG,kBAAkB,GAAG,aAAa,CAAC,CAAA;QADzF,IAAQ,CAAA,QAAA,GAAR,QAAQ,CAAsB;QAAS,IAAa,CAAA,aAAA,GAAb,aAAa,CAAQ;QAF/E,IAAI,CAAA,IAAA,GAAG,uBAAuB,CAAA;KAI7B;IAED,IAAI,MAAM,GAAA;QACR,OAAO,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAA;KAC5B;IAED,IAAI,IAAI,GAAA;QACN,OAAO,IAAI,CAAC,QAAQ,CAAC,IAAI,EAAE,CAAA;KAC5B;IAED,IAAI,IAAI,GAAA;QACN,OAAO,IAAI,CAAC,QAAQ,CAAC,IAAI,EAAE,CAAA;KAC5B;AACF,CAAA;AAED,MAAM,wBAAyB,SAAQ,KAAK,CAAA;IAG1C,WAAA,CAAmB,KAAc,CAAA;;;;QAI/B,KAAK,CAAC,sCAAsC,EAAE,KAAK,YAAY,KAAK,GAAG;YAAE,KAAK,EAAE,KAAK;QAAA,CAAE,GAAG,CAAA,CAAE,CAAC,CAAA;QAJ5E,IAAK,CAAA,KAAA,GAAL,KAAK,CAAS;QAFjC,IAAI,CAAA,IAAA,GAAG,0BAA0B,CAAA;KAOhC;AACF,CAAA;AAKM,eAAe,aAAa,CAAC,GAAQ,EAAA;IAC1C,IAAI,GAAG,YAAY,qBAAqB,EAAE;QACxC,IAAI,IAAI,GAAG,EAAE,CAAA;QACb,IAAI;YACF,IAAI,GAAG,MAAM,GAAG,CAAC,IAAI,CAAA;QACtB,CAAA,CAAC,OAAM,CAAA,CAAE;QAEV,OAAO,CAAC,KAAK,CAAC,CAAA,sCAAA,EAAyC,GAAG,CAAC,OAAO,CAAA,gBAAA,EAAmB,IAAI,CAAA,CAAE,EAAE,GAAG,CAAC,CAAA;IAClG,CAAA,MAAM;QACL,OAAO,CAAC,KAAK,CAAC,8BAA8B,EAAE,GAAG,CAAC,CAAA;IACnD,CAAA;IACD,OAAO,OAAO,CAAC,OAAO,EAAE,CAAA;AAC1B,CAAC;AAED,SAAS,mBAAmB,CAAC,GAAY,EAAA;IACvC,OAAO,OAAO,GAAG,KAAK,QAAQ,IAAA,CAAK,GAAG,YAAY,qBAAqB,IAAI,GAAG,YAAY,wBAAwB,CAAC,CAAA;AACrH,CAAC;AAED,SAAS,kCAAkC,CAAC,GAAY,EAAA;IACtD,OAAO,OAAO,GAAG,KAAK,QAAQ,IAAI,GAAG,YAAY,qBAAqB,IAAI,GAAG,CAAC,MAAM,KAAK,GAAG,CAAA;AAC9F,CAAC;AAED,IAAK,mBAGJ,CAAA;AAHD,CAAA,SAAK,mBAAmB,EAAA;IACtB,mBAAA,CAAA,cAAA,CAAA,GAAA,eAA8B,CAAA;IAC9B,mBAAA,CAAA,YAAA,CAAA,GAAA,YAAyB,CAAA;AAC3B,CAAC,EAHI,mBAAmB,IAAA,CAAnB,mBAAmB,GAGvB,CAAA,CAAA,CAAA,CAAA,CAAA;MAEqB,oBAAoB,CAAA;IA0CxC,WAAY,CAAA,MAAc,EAAE,OAA4B,CAAA;QAhChD,IAAY,CAAA,YAAA,GAAwB,IAAI,CAAA;QACxC,IAAe,CAAA,eAAA,GAAyB,IAAI,CAAA;QAW5C,IAAe,CAAA,eAAA,GAAiC,CAAA,CAAE,CAAA;;QAGhD,IAAA,CAAA,OAAO,GAAG,IAAI,kBAAkB,EAAE,CAAA;QAIlC,IAAc,CAAA,cAAA,GAAY,KAAK,CAAA;QAcvC,MAAM,CAAC,MAAM,EAAE,+CAA+C,CAAC,CAAA;QAE/D,IAAI,CAAC,MAAM,GAAG,MAAM,CAAA;QACpB,IAAI,CAAC,IAAI,GAAG,mBAAmB,CAAC,OAAO,EAAE,IAAI,IAAI,0BAA0B,CAAC,CAAA;QAC5E,IAAI,CAAC,OAAO,GAAG,OAAO,EAAE,OAAO,GAAG,IAAI,CAAC,GAAG,CAAC,OAAO,EAAE,OAAO,EAAE,CAAC,CAAC,GAAG,EAAE,CAAA;QACpE,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,OAAO,EAAE,OAAO,EAAE,YAAY,IAAI,GAAG,CAAC,CAAA;QACxE,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,OAAO,EAAE,OAAO,EAAE,YAAY,IAAI,IAAI,CAAC,CAAA;QACzE,IAAI,CAAC,aAAa,GAAG,OAAO,EAAE,aAAa,IAAI,KAAK,CAAA;QACpD,IAAI,CAAC,WAAW,GAAG,OAAO,EAAE,WAAW,IAAI,MAAM,CAAA;QACjD,IAAI,CAAC,mBAAmB,GAAG,OAAO,EAAE,mBAAmB,IAAI,IAAI,CAAA;;QAE/D,IAAI,CAAC,YAAY,GAAG,OAAO,EAAE,YAAY,IAAI,IAAI,CAAA;QACjD,IAAI,CAAC,cAAc,GAAG,OAAO,EAAE,cAAc,IAAI,KAAK,CAAA;QAEtD,IAAI,CAAC,aAAa,GAAG;YACnB,UAAU,EAAE,OAAO,EAAE,eAAe,IAAI,CAAC;YACzC,UAAU,EAAE,OAAO,EAAE,eAAe,IAAI,IAAI;YAC5C,UAAU,EAAE,mBAAmB;SAChC,CAAA;QACD,IAAI,CAAC,cAAc,GAAG,OAAO,EAAE,cAAc,IAAI,KAAK,CAAA,CAAA,aAAA;QACtD,IAAI,CAAC,4BAA4B,GAAG,OAAO,EAAE,4BAA4B,IAAI,IAAI,CAAA,CAAA,YAAA;QACjF,IAAI,CAAC,4BAA4B,GAAG,OAAO,EAAE,4BAA4B,IAAI,IAAI,CAAA,CAAA,YAAA;QACjF,IAAI,CAAC,YAAY,GAAG,OAAO,EAAE,YAAY,IAAI,IAAI,CAAA;QACjD,IAAI,CAAC,QAAQ,GAAG,OAAO,EAAE,QAAQ,IAAI,KAAK,CAAA;QAC1C,IAAI,CAAC,mBAAmB,GAAG,OAAO,EAAE,mBAAmB,IAAI,KAAK,CAAA;;QAEhE,IAAI,CAAC,YAAY,GAAG,OAAO,CAAC,OAAO,EAAE,CAAA;QACrC,IAAI,CAAC,cAAc,GAAG,IAAI,CAAA;KAC3B;IAES,aAAa,CAAC,EAAc,EAAA;QACpC,IAAI,IAAI,CAAC,OAAO,EAAE;YAChB,EAAE,EAAE,CAAA;QACL,CAAA;KACF;IAES,IAAI,CAAC,EAAc,EAAA;QAC3B,IAAI,IAAI,CAAC,QAAQ,EAAE;YACjB,IAAI,CAAC,aAAa,CAAC,IAAM,OAAO,CAAC,IAAI,CAAC,kCAAkC,CAAC,CAAC,CAAA;YAC1E,OAAM;QACP,CAAA;QAED,IAAI,IAAI,CAAC,cAAc,EAAE;;YAEvB,OAAO,EAAE,EAAE,CAAA;QACZ,CAAA;QAED,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,IAAM,EAAE,EAAE,CAAC,CAAA;KACnC;IAES,wBAAwB,GAAA;QAChC,OAAO;YACL,IAAI,EAAE,IAAI,CAAC,YAAY,EAAE;YACzB,YAAY,EAAE,IAAI,CAAC,iBAAiB,EAAE;SACvC,CAAA;KACF;IAED,IAAW,QAAQ,GAAA;QACjB,OAAO,IAAI,CAAC,oBAAoB,CAAC,wBAAwB,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,YAAY,CAAA;KAC1F;IAED,MAAM,KAAK,GAAA;QACT,IAAI,CAAC,IAAI,CAAC,MAAK;YACb,IAAI,CAAC,oBAAoB,CAAC,wBAAwB,CAAC,QAAQ,EAAE,KAAK,CAAC,CAAA;QACrE,CAAC,CAAC,CAAA;KACH;IAED,MAAM,MAAM,GAAA;QACV,IAAI,CAAC,IAAI,CAAC,MAAK;YACb,IAAI,CAAC,oBAAoB,CAAC,wBAAwB,CAAC,QAAQ,EAAE,IAAI,CAAC,CAAA;QACpE,CAAC,CAAC,CAAA;KACH;IAED,EAAE,CAAC,KAAa,EAAE,EAA4B,EAAA;QAC5C,OAAO,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC,KAAK,EAAE,EAAE,CAAC,CAAA;KAClC;IAED,KAAK,CAAC,UAAmB,IAAI,EAAA;QAC3B,IAAI,CAAC,mBAAmB,IAAI,CAAA;QAE5B,IAAI,OAAO,EAAE;YACX,MAAM,mBAAmB,GAAG,IAAI,CAAC,EAAE,CAAC,GAAG,EAAE,CAAC,KAAK,EAAE,OAAO,GAAK,OAAO,CAAC,GAAG,CAAC,eAAe,EAAE,KAAK,EAAE,OAAO,CAAC,CAAC,CAAA;YAC1G,IAAI,CAAC,mBAAmB,GAAG,MAAK;gBAC9B,mBAAmB,EAAE,CAAA;gBACrB,IAAI,CAAC,mBAAmB,GAAG,SAAS,CAAA;YACtC,CAAC,CAAA;QACF,CAAA;KACF;IAED,IAAI,OAAO,GAAA;QACT,OAAO,CAAC,CAAC,IAAI,CAAC,mBAAmB,CAAA;KAClC;IAED,IAAI,UAAU,GAAA;QACZ,OAAO,IAAI,CAAC,QAAQ,CAAA;KACrB;IAEO,YAAY,CAAC,OAIpB,EAAA;QACC,OAAO;YACL,WAAW,EAAE,OAAO,CAAC,WAAW;YAChC,KAAK,EAAE,OAAO,CAAC,KAAK;YACpB,UAAU,EAAE;gBACV,GAAI,OAAO,CAAC,UAAU,IAAI,CAAA,CAAE,CAAC;gBAC7B,GAAG,IAAI,CAAC,wBAAwB,EAAE;YACnC,CAAA;SACF,CAAA;KACF;IAES,iBAAiB,CAAI,OAAmB,EAAA;QAChD,MAAM,WAAW,GAAG,MAAM,EAAE,CAAA;QAC5B,IAAI,CAAC,eAAe,CAAC,WAAW,CAAC,GAAG,OAAO,CAAA;QAC3C,OAAO,CACJ,KAAK,CAAC,KAAK,CAAA,AAAG,CAAC,CACf,OAAO,CAAC,MAAK;YACZ,OAAO,IAAI,CAAC,eAAe,CAAC,WAAW,CAAC,CAAA;QAC1C,CAAC,CAAC,CAAA;QAEJ,OAAO,OAAO,CAAA;KACf;IAED;;OAEK,GACK,iBAAiB,CACzB,UAAkB,EAClB,UAAmC,EACnC,OAA+B,EAAA;QAE/B,IAAI,CAAC,IAAI,CAAC,MAAK;;;YAIb,MAAM,OAAO,GAAG;gBACd,GAAG,IAAI,CAAC,YAAY,CAAC;oBACnB,WAAW,EAAE,UAAU;oBACvB,KAAK,EAAE,WAAW;oBAClB,UAAU;iBACX,CAAC;aACH,CAAA;YAED,IAAI,CAAC,OAAO,CAAC,UAAU,EAAE,OAAO,EAAE,OAAO,CAAC,CAAA;QAC5C,CAAC,CAAC,CAAA;KACH;IAES,MAAM,0BAA0B,CACxC,UAAkB,EAClB,UAAmC,EACnC,OAA+B,EAAA;QAE/B,MAAM,OAAO,GAAG;YACd,GAAG,IAAI,CAAC,YAAY,CAAC;gBACnB,WAAW,EAAE,UAAU;gBACvB,KAAK,EAAE,WAAW;gBAClB,UAAU;aACX,CAAC;SACH,CAAA;QAED,MAAM,IAAI,CAAC,aAAa,CAAC,UAAU,EAAE,OAAO,EAAE,OAAO,CAAC,CAAA;KACvD;IAES,gBAAgB,CACxB,UAAkB,EAClB,KAAa,EACb,UAAmC,EACnC,OAA+B,EAAA;QAE/B,IAAI,CAAC,IAAI,CAAC,MAAK;YACb,MAAM,OAAO,GAAG,IAAI,CAAC,YAAY,CAAC;gBAAE,WAAW,EAAE,UAAU;gBAAE,KAAK;gBAAE,UAAU;YAAA,CAAE,CAAC,CAAA;YACjF,IAAI,CAAC,OAAO,CAAC,SAAS,EAAE,OAAO,EAAE,OAAO,CAAC,CAAA;QAC3C,CAAC,CAAC,CAAA;KACH;IAES,MAAM,yBAAyB,CACvC,UAAkB,EAClB,KAAa,EACb,UAAmC,EACnC,OAA+B,EAAA;QAE/B,MAAM,OAAO,GAAG,IAAI,CAAC,YAAY,CAAC;YAAE,WAAW,EAAE,UAAU;YAAE,KAAK;YAAE,UAAU;QAAA,CAAE,CAAC,CAAA;QACjF,MAAM,IAAI,CAAC,aAAa,CAAC,SAAS,EAAE,OAAO,EAAE,OAAO,CAAC,CAAA;KACtD;IAES,cAAc,CACtB,KAAa,EACb,UAAkB,EAClB,UAAmC,EACnC,OAA+B,EAAA;QAE/B,IAAI,CAAC,IAAI,CAAC,MAAK;YACb,MAAM,OAAO,GAAG,IAAI,CAAC,YAAY,CAAC;gBAChC,KAAK,EAAE,eAAe;gBACtB,WAAW,EAAE,UAAU;gBACvB,UAAU,EAAE;oBACV,GAAI,UAAU,IAAI,CAAA,CAAE,CAAC;oBACrB,WAAW,EAAE,UAAU;oBACvB,KAAK;gBACN,CAAA;YACF,CAAA,CAAC,CAAA;YAEF,IAAI,CAAC,OAAO,CAAC,OAAO,EAAE,OAAO,EAAE,OAAO,CAAC,CAAA;QACzC,CAAC,CAAC,CAAA;KACH;IAES,MAAM,uBAAuB,CACrC,KAAa,EACb,UAAkB,EAClB,UAAmC,EACnC,OAA+B,EAAA;QAE/B,MAAM,OAAO,GAAG,IAAI,CAAC,YAAY,CAAC;YAChC,KAAK,EAAE,eAAe;YACtB,WAAW,EAAE,UAAU;YACvB,UAAU,EAAE;gBACV,GAAI,UAAU,IAAI,CAAA,CAAE,CAAC;gBACrB,WAAW,EAAE,UAAU;gBACvB,KAAK;YACN,CAAA;QACF,CAAA,CAAC,CAAA;QAEF,MAAM,IAAI,CAAC,aAAa,CAAC,OAAO,EAAE,OAAO,EAAE,OAAO,CAAC,CAAA;KACpD;IAED;;OAEK,GACK,sBAAsB,CAC9B,SAAiB,EACjB,QAAyB,EACzB,eAAwC,EACxC,OAA+B,EAC/B,UAAmB,EACnB,eAAwC,EAAA;QAExC,IAAI,CAAC,IAAI,CAAC,MAAK;YACb,MAAM,OAAO,GAAG,IAAI,CAAC,YAAY,CAAC;gBAChC,WAAW,EAAE,UAAU,IAAI,CAAA,CAAA,EAAI,SAAS,CAAA,CAAA,EAAI,QAAQ,CAAE,CAAA;gBACtD,KAAK,EAAE,gBAAgB;gBACvB,UAAU,EAAE;oBACV,WAAW,EAAE,SAAS;oBACtB,UAAU,EAAE,QAAQ;oBACpB,UAAU,EAAE,eAAe,IAAI,CAAA,CAAE;oBACjC,GAAI,eAAe,IAAI,CAAA,CAAE,CAAC;gBAC3B,CAAA;YACF,CAAA,CAAC,CAAA;YAEF,IAAI,CAAC,OAAO,CAAC,SAAS,EAAE,OAAO,EAAE,OAAO,CAAC,CAAA;QAC3C,CAAC,CAAC,CAAA;KACH;IAES,MAAM,eAAe,GAAA;QAC7B,MAAM,IAAI,CAAC,YAAY,CAAA;QAEvB,IAAI,IAAI,GAAG,IAAI,CAAC,IAAI,CAAA;QAEpB,IAAI,IAAI,KAAK,0BAA0B,EAAE;YACvC,IAAI,GAAG,iCAAiC,CAAA;QACzC,CAAA,MAAM,IAAI,IAAI,KAAK,0BAA0B,EAAE;YAC9C,IAAI,GAAG,iCAAiC,CAAA;QACzC,CAAA;QAED,MAAM,GAAG,GAAG,CAAG,EAAA,IAAI,CAAA,OAAA,EAAU,IAAI,CAAC,MAAM,CAAA,OAAA,CAAS,CAAA;QACjD,MAAM,YAAY,GAAwB;YACxC,MAAM,EAAE,KAAK;YACb,OAAO,EAAE;gBAAE,GAAG,IAAI,CAAC,gBAAgB,EAAE;gBAAE,cAAc,EAAE,kBAAkB;YAAA,CAAE;SAC5E,CAAA;;QAED,OAAO,IAAI,CAAC,cAAc,CAAC,GAAG,EAAE,YAAY,EAAE;YAAE,UAAU,EAAE,CAAC;QAAA,CAAE,EAAE,IAAI,CAAC,4BAA4B,CAAC,CAChG,IAAI,CAAC,CAAC,QAAQ,GAAK,QAAQ,CAAC,IAAI,EAAkC,CAAC,CACnE,KAAK,CAAC,CAAC,KAAK,KAAI;YACf,IAAI,CAAC,aAAa,CAAC,IAAM,OAAO,CAAC,KAAK,CAAC,mCAAmC,EAAE,KAAK,CAAC,CAAC,CAAA;YACnF,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,OAAO,EAAE,KAAK,CAAC,CAAA;YACjC,OAAO,SAAS,CAAA;QAClB,CAAC,CAAC,CAAA;KACL;IAED;;OAEK,GAEK,MAAM,SAAS,CACvB,UAAkB,EAClB,MAA0C,GAAA,CAAA,CAAE,EAC5C,gBAAA,GAA2C,CAAA,CAAE,EAC7C,eAAA,GAA0D,CAAA,CAAE,EAC5D,eAAoC,CAAA,CAAE,EAAA;QAEtC,MAAM,IAAI,CAAC,YAAY,CAAA;;;;QAKvB,MAAM,QAAQ,GAAG,gBAAgB,CAAC,IAAI,CAAC,MAAM,EAAE,4BAA4B,EAAE,yBAAyB,CAAC,CAAA;QAEvG,MAAM,GAAG,GAAG,QAAQ,GAAG,GAAG,IAAI,CAAC,IAAI,CAAa,WAAA,CAAA,GAAG,CAAA,EAAG,IAAI,CAAC,IAAI,CAAA,YAAA,CAAc,CAAA;QAC7E,MAAM,YAAY,GAAwB;YACxC,MAAM,EAAE,MAAM;YACd,OAAO,EAAE;gBAAE,GAAG,IAAI,CAAC,gBAAgB,EAAE;gBAAE,cAAc,EAAE,kBAAkB;YAAA,CAAE;YAC3E,IAAI,EAAE,IAAI,CAAC,SAAS,CAAC;gBACnB,KAAK,EAAE,IAAI,CAAC,MAAM;gBAClB,WAAW,EAAE,UAAU;gBACvB,MAAM;gBACN,iBAAiB,EAAE,gBAAgB;gBACnC,gBAAgB,EAAE,eAAe;gBACjC,GAAG,YAAY;aAChB,CAAC;SACH,CAAA;QAED,IAAI,CAAC,aAAa,CAAC,IAAM,OAAO,CAAC,GAAG,CAAC,eAAe,EAAE,YAAY,EAAE,GAAG,CAAC,CAAC,CAAA;;QAGzE,OAAO,IAAI,CAAC,cAAc,CAAC,GAAG,EAAE,YAAY,EAAE;YAAE,UAAU,EAAE,CAAC;QAAA,CAAE,EAAE,IAAI,CAAC,4BAA4B,CAAC,CAChG,IAAI,CAAC,CAAC,QAAQ,GAAK,QAAQ,CAAC,IAAI,EAAgE,CAAC,CACjG,IAAI,CAAC,CAAC,QAAQ,GAAK,uBAAuB,CAAC,QAAQ,CAAC,CAAC,CACrD,KAAK,CAAC,CAAC,KAAK,KAAI;YACf,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,OAAO,EAAE,KAAK,CAAC,CAAA;YACjC,OAAO,SAAS,CAAA;QAClB,CAAC,CAA+C,CAAA;KACnD;IAES,MAAM,uBAAuB,CACrC,GAAW,EACX,UAAkB,EAClB,MAAA,GAAiC,CAAA,CAAE,EACnC,mBAA2C,CAAA,CAAE,EAC7C,eAA0D,GAAA,CAAA,CAAE,EAC5D,YAAsB,EAAA;QAKtB,MAAM,IAAI,CAAC,YAAY,CAAA;QAEvB,MAAM,kBAAkB,GAAG,MAAM,IAAI,CAAC,6BAA6B,CACjE,GAAG,EACH,UAAU,EACV,MAAM,EACN,gBAAgB,EAChB,eAAe,EACf,YAAY,CACb,CAAA;QAED,IAAI,kBAAkB,KAAK,SAAS,EAAE;;YAEpC,OAAO;gBACL,QAAQ,EAAE,SAAS;gBACnB,SAAS,EAAE,SAAS;aACrB,CAAA;QACF,CAAA;QAED,IAAI,QAAQ,GAAG,mBAAmB,CAAC,kBAAkB,CAAC,QAAQ,CAAC,CAAA;QAE/D,IAAI,QAAQ,KAAK,SAAS,EAAE;;YAE1B,QAAQ,GAAG,KAAK,CAAA;QACjB,CAAA;;QAGD,OAAO;YACL,QAAQ;YACR,SAAS,EAAE,kBAAkB,CAAC,SAAS;SACxC,CAAA;KACF;IAES,MAAM,6BAA6B,CAC3C,GAAW,EACX,UAAkB,EAClB,MAAA,GAAiC,CAAA,CAAE,EACnC,mBAA2C,CAAA,CAAE,EAC7C,eAA0D,GAAA,CAAA,CAAE,EAC5D,YAAsB,EAAA;QAQtB,MAAM,IAAI,CAAC,YAAY,CAAA;QAEvB,MAAM,cAAc,GAAG,MAAM,IAAI,CAAC,8BAA8B,CAC9D,UAAU,EACV,MAAM,EACN,gBAAgB,EAChB,eAAe,EACf,YAAY,EACZ;YAAC,GAAG;SAAC,CACN,CAAA;QAED,IAAI,cAAc,KAAK,SAAS,EAAE;YAChC,OAAO,SAAS,CAAA;QACjB,CAAA;QAED,MAAM,YAAY,GAAG,cAAc,CAAC,KAAK,CAAA;QAEzC,MAAM,UAAU,GAAG,YAAY,CAAC,GAAG,CAAC,CAAA;QAEpC,OAAO;YACL,QAAQ,EAAE,UAAU;YACpB,SAAS,EAAE,cAAc,CAAC,SAAS;SACpC,CAAA;KACF;IAES,MAAM,8BAA8B,CAC5C,GAAW,EACX,UAAkB,EAClB,MAAA,GAAiC,CAAA,CAAE,EACnC,mBAA2C,CAAA,CAAE,EAC7C,eAA0D,GAAA,CAAA,CAAE,EAC5D,YAAsB,EAAA;QAEtB,MAAM,IAAI,CAAC,YAAY,CAAA;QAEvB,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,+BAA+B,CACzD,UAAU,EACV,MAAM,EACN,gBAAgB,EAChB,eAAe,EACf,YAAY,EACZ;YAAC,GAAG;SAAC,CACN,CAAA;QAED,IAAI,CAAC,QAAQ,EAAE;YACb,OAAO,SAAS,CAAA;QACjB,CAAA;QAED,MAAM,QAAQ,GAAG,QAAQ,CAAC,GAAG,CAAC,CAAA;;QAG9B,IAAI,QAAQ,KAAK,SAAS,EAAE;YAC1B,OAAO,IAAI,CAAA;QACZ,CAAA;QAED,OAAO,QAAQ,CAAA;KAChB;IAES,MAAM,+BAA+B,CAC7C,UAAkB,EAClB,SAAiC,CAAA,CAAE,EACnC,gBAA2C,GAAA,CAAA,CAAE,EAC7C,eAA0D,GAAA,CAAA,CAAE,EAC5D,YAAsB,EACtB,kBAA6B,EAAA;QAE7B,MAAM,IAAI,CAAC,YAAY,CAAA;QAEvB,MAAM,QAAQ,GAAG,CACf,MAAM,IAAI,CAAC,mCAAmC,CAC5C,UAAU,EACV,MAAM,EACN,gBAAgB,EAChB,eAAe,EACf,YAAY,EACZ,kBAAkB,CACnB,EACD,QAAQ,CAAA;QAEV,OAAO,QAAQ,CAAA;KAChB;IAES,MAAM,wBAAwB,CACtC,UAAkB,EAClB,SAA0C,CAAA,CAAE,EAC5C,gBAA2C,GAAA,CAAA,CAAE,EAC7C,eAA0D,GAAA,CAAA,CAAE,EAC5D,YAAsB,EACtB,kBAA6B,EAAA;QAM7B,MAAM,IAAI,CAAC,YAAY,CAAA;QAEvB,OAAO,MAAM,IAAI,CAAC,mCAAmC,CACnD,UAAU,EACV,MAAM,EACN,gBAAgB,EAChB,eAAe,EACf,YAAY,EACZ,kBAAkB,CACnB,CAAA;KACF;IAES,MAAM,mCAAmC,CACjD,UAAkB,EAClB,SAA0C,CAAA,CAAE,EAC5C,gBAA2C,GAAA,CAAA,CAAE,EAC7C,eAA0D,GAAA,CAAA,CAAE,EAC5D,YAAsB,EACtB,kBAA6B,EAAA;QAM7B,MAAM,IAAI,CAAC,YAAY,CAAA;QAEvB,MAAM,kBAAkB,GAAG,MAAM,IAAI,CAAC,8BAA8B,CAClE,UAAU,EACV,MAAM,EACN,gBAAgB,EAChB,eAAe,EACf,YAAY,EACZ,kBAAkB,CACnB,CAAA;QAED,IAAI,CAAC,kBAAkB,EAAE;YACvB,OAAO;gBACL,KAAK,EAAE,SAAS;gBAChB,QAAQ,EAAE,SAAS;gBACnB,SAAS,EAAE,SAAS;aACrB,CAAA;QACF,CAAA;QAED,OAAO;YACL,KAAK,EAAE,kBAAkB,CAAC,YAAY;YACtC,QAAQ,EAAE,kBAAkB,CAAC,mBAAmB;YAChD,SAAS,EAAE,kBAAkB,CAAC,SAAS;SACxC,CAAA;KACF;IAES,MAAM,8BAA8B,CAC5C,UAAkB,EAClB,SAA0C,CAAA,CAAE,EAC5C,gBAA2C,GAAA,CAAA,CAAE,EAC7C,eAA0D,GAAA,CAAA,CAAE,EAC5D,YAAsB,EACtB,kBAA6B,EAAA;QAE7B,MAAM,IAAI,CAAC,YAAY,CAAA;QAEvB,MAAM,YAAY,GAAwB,CAAA,CAAE,CAAA;QAC5C,IAAI,YAAY,IAAI,IAAI,CAAC,YAAY,EAAE;YACrC,YAAY,CAAC,eAAe,CAAC,GAAG,IAAI,CAAA;QACrC,CAAA;QACD,IAAI,kBAAkB,EAAE;YACtB,YAAY,CAAC,uBAAuB,CAAC,GAAG,kBAAkB,CAAA;QAC3D,CAAA;QACD,MAAM,cAAc,GAAG,MAAM,IAAI,CAAC,SAAS,CAAC,UAAU,EAAE,MAAM,EAAE,gBAAgB,EAAE,eAAe,EAAE,YAAY,CAAC,CAAA;QAEhH,IAAI,cAAc,KAAK,SAAS,EAAE;;YAEhC,OAAO,SAAS,CAAA;QACjB,CAAA;;QAGD,IAAI,cAAc,CAAC,yBAAyB,EAAE;YAC5C,OAAO,CAAC,KAAK,CACX,kKAAkK,CACnK,CAAA;QACF,CAAA;;QAGD,IAAI,cAAc,CAAC,YAAY,EAAE,QAAQ,CAAC,mBAAmB,CAAC,YAAY,CAAC,EAAE;YAC3E,OAAO,CAAC,IAAI,CACV,mKAAmK,CACpK,CAAA;YACD,OAAO;gBACL,KAAK,EAAE,CAAA,CAAE;gBACT,YAAY,EAAE,CAAA,CAAE;gBAChB,mBAAmB,EAAE,CAAA,CAAE;gBACvB,SAAS,EAAE,cAAc,EAAE,SAAS;aACrC,CAAA;QACF,CAAA;QAED,OAAO,cAAc,CAAA;KACtB;IAED;;OAEK,GAEE,MAAM,mBAAmB,GAAA;QAC9B,MAAM,IAAI,CAAC,YAAY,CAAA;QAEvB,IAAI,IAAI,CAAC,cAAc,KAAK,IAAI,EAAE;YAChC,IAAI,CAAC,aAAa,CAAC,IAAM,OAAO,CAAC,GAAG,CAAC,eAAe,EAAE,8BAA8B,CAAC,CAAC,CAAA;YACtF,OAAO,EAAE,CAAA;QACV,CAAA;QAED,MAAM,GAAG,GAAG,CAAA,EAAG,IAAI,CAAC,IAAI,CAAA,oBAAA,EAAuB,IAAI,CAAC,MAAM,CAAA,CAAE,CAAA;QAC5D,MAAM,YAAY,GAAwB;YACxC,MAAM,EAAE,KAAK;YACb,OAAO,EAAE;gBAAE,GAAG,IAAI,CAAC,gBAAgB,EAAE;gBAAE,cAAc,EAAE,kBAAkB;YAAA,CAAE;SAC5E,CAAA;QAED,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,cAAc,CAAC,GAAG,EAAE,YAAY,CAAC,CAC1D,IAAI,CAAC,CAAC,QAAQ,KAAI;YACjB,IAAI,QAAQ,CAAC,MAAM,KAAK,GAAG,IAAI,CAAC,QAAQ,CAAC,IAAI,EAAE;gBAC7C,MAAM,GAAG,GAAG,CAAA,iCAAA,EAAoC,QAAQ,CAAC,MAAM,EAAE,CAAA;gBACjE,MAAM,KAAK,GAAG,IAAI,KAAK,CAAC,GAAG,CAAC,CAAA;gBAC5B,IAAI,CAAC,aAAa,CAAC,IAAM,OAAO,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,CAAA;gBAE9C,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,OAAO,EAAE,IAAI,KAAK,CAAC,GAAG,CAAC,CAAC,CAAA;gBAC1C,OAAO,SAAS,CAAA;YACjB,CAAA;YAED,OAAO,QAAQ,CAAC,IAAI,EAA6B,CAAA;QACnD,CAAC,CAAC,CACD,KAAK,CAAC,CAAC,KAAK,KAAI;YACf,IAAI,CAAC,aAAa,CAAC,IAAM,OAAO,CAAC,KAAK,CAAC,iCAAiC,EAAE,KAAK,CAAC,CAAC,CAAA;YAEjF,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,OAAO,EAAE,KAAK,CAAC,CAAA;YACjC,OAAO,SAAS,CAAA;QAClB,CAAC,CAAC,CAAA;QAEJ,MAAM,UAAU,GAAG,QAAQ,EAAE,OAAO,CAAA;QAEpC,IAAI,UAAU,EAAE;YACd,IAAI,CAAC,aAAa,CAAC,IAAM,OAAO,CAAC,GAAG,CAAC,eAAe,EAAE,4BAA4B,EAAE,IAAI,CAAC,SAAS,CAAC,UAAU,CAAC,CAAC,CAAC,CAAA;QACjH,CAAA;QAED,OAAO,UAAU,IAAI,EAAE,CAAA;KACxB;IAOD,IAAc,KAAK,GAAA;QACjB,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE;YAChB,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,oBAAoB,CAAyB,wBAAwB,CAAC,KAAK,CAAC,CAAA;QAChG,CAAA;QACD,OAAO,IAAI,CAAC,MAAM,IAAI,CAAA,CAAE,CAAA;KACzB;IAED,IAAc,KAAK,CAAC,GAAuC,EAAA;QACzD,IAAI,CAAC,MAAM,GAAG,GAAG,CAAA;KAClB;IAED,MAAM,QAAQ,CAAC,UAAkC,EAAA;QAC/C,IAAI,CAAC,IAAI,CAAC,MAAK;YACb,IAAI,CAAC,KAAK,GAAG;gBACX,GAAG,IAAI,CAAC,KAAK;gBACb,GAAG,UAAU;aACd,CAAA;YACD,IAAI,CAAC,oBAAoB,CAAyB,wBAAwB,CAAC,KAAK,EAAE,IAAI,CAAC,KAAK,CAAC,CAAA;QAC/F,CAAC,CAAC,CAAA;KACH;IAED,MAAM,UAAU,CAAC,QAAgB,EAAA;QAC/B,IAAI,CAAC,IAAI,CAAC,MAAK;YACb,OAAO,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAA;YAC3B,IAAI,CAAC,oBAAoB,CAAyB,wBAAwB,CAAC,KAAK,EAAE,IAAI,CAAC,KAAK,CAAC,CAAA;QAC/F,CAAC,CAAC,CAAA;KACH;IAED;;OAEK,GACK,OAAO,CAAC,IAAY,EAAE,QAAa,EAAE,OAA+B,EAAA;QAC5E,IAAI,CAAC,IAAI,CAAC,MAAK;YACb,IAAI,IAAI,CAAC,QAAQ,EAAE;gBACjB,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,EAAE,CAA4E,0EAAA,CAAA,CAAC,CAAA;gBACrG,OAAM;YACP,CAAA;YAED,MAAM,OAAO,GAAG,IAAI,CAAC,cAAc,CAAC,IAAI,EAAE,QAAQ,EAAE,OAAO,CAAC,CAAA;YAE5D,MAAM,KAAK,GAAG,IAAI,CAAC,oBAAoB,CAAqB,wBAAwB,CAAC,KAAK,CAAC,IAAI,EAAE,CAAA;YAEjG,IAAI,KAAK,CAAC,MAAM,IAAI,IAAI,CAAC,YAAY,EAAE;gBACrC,KAAK,CAAC,KAAK,EAAE,CAAA;gBACb,IAAI,CAAC,aAAa,CAAC,IAAM,OAAO,CAAC,IAAI,CAAC,6CAA6C,CAAC,CAAC,CAAA;YACtF,CAAA;YAED,KAAK,CAAC,IAAI,CAAC;gBAAE,OAAO;YAAA,CAAE,CAAC,CAAA;YACvB,IAAI,CAAC,oBAAoB,CAAqB,wBAAwB,CAAC,KAAK,EAAE,KAAK,CAAC,CAAA;YAEpF,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,EAAE,OAAO,CAAC,CAAA;;YAGhC,IAAI,KAAK,CAAC,MAAM,IAAI,IAAI,CAAC,OAAO,EAAE;gBAChC,IAAI,CAAC,eAAe,EAAE,CAAA;YACvB,CAAA;YAED,IAAI,IAAI,CAAC,aAAa,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE;gBAC3C,IAAI,CAAC,WAAW,GAAG,cAAc,CAAC,IAAM,IAAI,CAAC,eAAe,EAAE,EAAE,IAAI,CAAC,aAAa,CAAC,CAAA;YACpF,CAAA;QACH,CAAC,CAAC,CAAA;KACH;IAES,MAAM,aAAa,CAAC,IAAY,EAAE,QAAa,EAAE,OAA+B,EAAA;QACxF,IAAI,IAAI,CAAC,QAAQ,EAAE;YACjB,IAAI,CAAC,aAAa,CAAC,IAAM,OAAO,CAAC,IAAI,CAAC,kCAAkC,CAAC,CAAC,CAAA;YAC1E,OAAM;QACP,CAAA;QAED,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE;YACxB,MAAM,IAAI,CAAC,YAAY,CAAA;QACxB,CAAA;QAED,IAAI,IAAI,CAAC,QAAQ,EAAE;YACjB,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,EAAE,CAA4E,0EAAA,CAAA,CAAC,CAAA;YACrG,OAAM;QACP,CAAA;QAED,MAAM,IAAI,GAAwB;YAChC,OAAO,EAAE,IAAI,CAAC,MAAM;YACpB,KAAK,EAAE;gBAAC,IAAI,CAAC,cAAc,CAAC,IAAI,EAAE,QAAQ,EAAE,OAAO,CAAC;aAAC;YACrD,OAAO,EAAE,cAAc,EAAE;SAC1B,CAAA;QAED,IAAI,IAAI,CAAC,mBAAmB,EAAE;YAC5B,IAAI,CAAC,oBAAoB,GAAG,IAAI,CAAA;QACjC,CAAA;QAED,MAAM,OAAO,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,CAAA;QAEpC,MAAM,GAAG,GACP,IAAI,CAAC,WAAW,KAAK,MAAM,GACvB,CAAA,EAAG,IAAI,CAAC,IAAI,CAAA,WAAA,EAAc,gBAAgB,EAAE,CAAM,GAAA,EAAA,IAAI,CAAC,iBAAiB,EAAE,CAAE,CAAA,GAC5E,CAAG,EAAA,IAAI,CAAC,IAAI,CAAA,OAAA,CAAS,CAAA;QAE3B,MAAM,YAAY,GAChB,IAAI,CAAC,WAAW,KAAK,MAAM,GACvB;YACE,MAAM,EAAE,MAAM;YACd,IAAI,EAAE,SAAS;YACf,WAAW,EAAE,MAAM;YACnB,OAAO,EAAE;gBAAE,GAAG,IAAI,CAAC,gBAAgB,EAAE;gBAAE,cAAc,EAAE,mCAAmC;YAAA,CAAE;YAC5F,IAAI,EAAE,CAAQ,KAAA,EAAA,kBAAkB,CAAC,QAAQ,CAAC,gBAAgB,CAAC,OAAO,CAAC,CAAC,CAAmB,iBAAA,CAAA;QACxF,CAAA,GACD;YACE,MAAM,EAAE,MAAM;YACd,OAAO,EAAE;gBAAE,GAAG,IAAI,CAAC,gBAAgB,EAAE;gBAAE,cAAc,EAAE,kBAAkB;YAAA,CAAE;YAC3E,IAAI,EAAE,OAAO;SACd,CAAA;QAEP,IAAI;YACF,MAAM,IAAI,CAAC,cAAc,CAAC,GAAG,EAAE,YAAY,CAAC,CAAA;QAC7C,CAAA,CAAC,OAAO,GAAG,EAAE;YACZ,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,OAAO,EAAE,GAAG,CAAC,CAAA;QAChC,CAAA;KACF;IAEO,cAAc,CAAC,IAAY,EAAE,QAAa,EAAE,OAA+B,EAAA;QACjF,MAAM,OAAO,GAAG;YACd,GAAG,QAAQ;YACX,IAAI,EAAE,IAAI;YACV,OAAO,EAAE,IAAI,CAAC,YAAY,EAAE;YAC5B,eAAe,EAAE,IAAI,CAAC,iBAAiB,EAAE;YACzC,SAAS,EAAE,OAAO,EAAE,SAAS,GAAG,OAAO,EAAE,SAAS,GAAG,cAAc,EAAE;YACrE,IAAI,EAAE,OAAO,EAAE,IAAI,GAAG,OAAO,CAAC,IAAI,GAAG,MAAM,EAAE;SAC9C,CAAA;QAED,MAAM,uBAAuB,GAAG,OAAO,EAAE,YAAY,IAAI,IAAI,CAAC,YAAY,CAAA;QAC1E,IAAI,uBAAuB,EAAE;YAC3B,IAAI,CAAC,OAAO,CAAC,UAAU,EAAE;gBACvB,OAAO,CAAC,UAAU,GAAG,CAAA,CAAE,CAAA;YACxB,CAAA;YACD,OAAO,CAAC,YAAY,CAAC,CAAC,gBAAgB,CAAC,GAAG,IAAI,CAAA;QAC/C,CAAA;QAED,IAAI,OAAO,CAAC,UAAU,EAAE;YACtB,OAAO,CAAC,WAAW,GAAG,OAAO,CAAC,UAAU,CAAA;YACxC,OAAO,OAAO,CAAC,UAAU,CAAA;QAC1B,CAAA;QAED,OAAO,OAAO,CAAA;KACf;IAEO,eAAe,GAAA;QACrB,IAAI,IAAI,CAAC,WAAW,EAAE;YACpB,YAAY,CAAC,IAAI,CAAC,WAAW,CAAC,CAAA;YAC9B,IAAI,CAAC,WAAW,GAAG,SAAS,CAAA;QAC7B,CAAA;KACF;IAED;;;KAGG,GACK,eAAe,GAAA;QACrB,KAAK,IAAI,CAAC,KAAK,EAAE,CAAC,KAAK,CAAC,OAAO,GAAG,KAAI;YACpC,MAAM,aAAa,CAAC,GAAG,CAAC,CAAA;QAC1B,CAAC,CAAC,CAAA;KACH;IAED;;;;;;;;;;;;;;;;;;;;;KAqBG,GACH,MAAM,KAAK,GAAA;;;;QAIT,MAAM,gBAAgB,GAAG,UAAU,CAAC;YAAC,IAAI,CAAC,YAAY;SAAC,CAAC,CAAC,IAAI,CAAC,MAAK;YACjE,OAAO,IAAI,CAAC,MAAM,EAAE,CAAA;QACtB,CAAC,CAAC,CAAA;QAEF,IAAI,CAAC,YAAY,GAAG,gBAAgB,CAAA;QACpC,KAAK,IAAI,CAAC,iBAAiB,CAAC,gBAAgB,CAAC,CAAA;QAE7C,UAAU,CAAC;YAAC,gBAAgB;SAAC,CAAC,CAAC,IAAI,CAAC,MAAK;;;YAGvC,IAAI,IAAI,CAAC,YAAY,KAAK,gBAAgB,EAAE;gBAC1C,IAAI,CAAC,YAAY,GAAG,IAAI,CAAA;YACzB,CAAA;QACH,CAAC,CAAC,CAAA;QAEF,OAAO,gBAAgB,CAAA;KACxB;IAES,gBAAgB,GAAA;;;;;QAKxB,MAAM,eAAe,GAAG,IAAI,CAAC,kBAAkB,EAAE,CAAA;QACjD,MAAM,OAAO,GAA8B,CAAA,CAAE,CAAA;QAC7C,IAAI,eAAe,IAAI,eAAe,KAAK,EAAE,EAAE;YAC7C,OAAO,CAAC,YAAY,CAAC,GAAG,eAAe,CAAA;QACxC,CAAA;QACD,OAAO,OAAO,CAAA;KACf;IAEO,MAAM,MAAM,GAAA;QAClB,IAAI,CAAC,eAAe,EAAE,CAAA;QACtB,MAAM,IAAI,CAAC,YAAY,CAAA;QAEvB,IAAI,KAAK,GAAG,IAAI,CAAC,oBAAoB,CAAqB,wBAAwB,CAAC,KAAK,CAAC,IAAI,EAAE,CAAA;QAE/F,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE;YACjB,OAAM;QACP,CAAA;QAED,MAAM,YAAY,GAAU,EAAE,CAAA;QAC9B,MAAM,mBAAmB,GAAG,KAAK,CAAC,MAAM,CAAA;QAExC,MAAO,KAAK,CAAC,MAAM,GAAG,CAAC,IAAI,YAAY,CAAC,MAAM,GAAG,mBAAmB,CAAE;YACpE,MAAM,UAAU,GAAG,KAAK,CAAC,KAAK,CAAC,CAAC,EAAE,IAAI,CAAC,YAAY,CAAC,CAAA;YACpD,MAAM,aAAa,GAAG,UAAU,CAAC,GAAG,CAAC,CAAC,IAAI,GAAK,IAAI,CAAC,OAAO,CAAC,CAAA;YAE5D,MAAM,kBAAkB,GAAG,MAAW;gBACpC,MAAM,cAAc,GAAG,IAAI,CAAC,oBAAoB,CAAqB,wBAAwB,CAAC,KAAK,CAAC,IAAI,EAAE,CAAA;gBAC1G,MAAM,QAAQ,GAAG,cAAc,CAAC,KAAK,CAAC,UAAU,CAAC,MAAM,CAAC,CAAA;gBACxD,IAAI,CAAC,oBAAoB,CAAqB,wBAAwB,CAAC,KAAK,EAAE,QAAQ,CAAC,CAAA;gBACvF,KAAK,GAAG,QAAQ,CAAA;YAClB,CAAC,CAAA;YAED,MAAM,IAAI,GAAwB;gBAChC,OAAO,EAAE,IAAI,CAAC,MAAM;gBACpB,KAAK,EAAE,aAAa;gBACpB,OAAO,EAAE,cAAc,EAAE;aAC1B,CAAA;YAED,IAAI,IAAI,CAAC,mBAAmB,EAAE;gBAC5B,IAAI,CAAC,oBAAoB,GAAG,IAAI,CAAA;YACjC,CAAA;YAED,MAAM,OAAO,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,CAAA;YAEpC,MAAM,GAAG,GACP,IAAI,CAAC,WAAW,KAAK,MAAM,GACvB,CAAA,EAAG,IAAI,CAAC,IAAI,CAAA,WAAA,EAAc,gBAAgB,EAAE,CAAM,GAAA,EAAA,IAAI,CAAC,iBAAiB,EAAE,CAAE,CAAA,GAC5E,CAAG,EAAA,IAAI,CAAC,IAAI,CAAA,OAAA,CAAS,CAAA;YAE3B,MAAM,YAAY,GAChB,IAAI,CAAC,WAAW,KAAK,MAAM,GACvB;gBACE,MAAM,EAAE,MAAM;gBACd,IAAI,EAAE,SAAS;gBACf,WAAW,EAAE,MAAM;gBACnB,OAAO,EAAE;oBAAE,GAAG,IAAI,CAAC,gBAAgB,EAAE;oBAAE,cAAc,EAAE,mCAAmC;gBAAA,CAAE;gBAC5F,IAAI,EAAE,CAAQ,KAAA,EAAA,kBAAkB,CAAC,QAAQ,CAAC,gBAAgB,CAAC,OAAO,CAAC,CAAC,CAAmB,iBAAA,CAAA;YACxF,CAAA,GACD;gBACE,MAAM,EAAE,MAAM;gBACd,OAAO,EAAE;oBAAE,GAAG,IAAI,CAAC,gBAAgB,EAAE;oBAAE,cAAc,EAAE,kBAAkB;gBAAA,CAAE;gBAC3E,IAAI,EAAE,OAAO;aACd,CAAA;YAEP,MAAM,YAAY,GAA8B;gBAC9C,UAAU,EAAE,CAAC,GAAG,KAAI;;oBAElB,IAAI,kCAAkC,CAAC,GAAG,CAAC,EAAE;wBAC3C,OAAO,KAAK,CAAA;oBACb,CAAA;;oBAED,OAAO,mBAAmB,CAAC,GAAG,CAAC,CAAA;iBAChC;aACF,CAAA;YAED,IAAI;gBACF,MAAM,IAAI,CAAC,cAAc,CAAC,GAAG,EAAE,YAAY,EAAE,YAAY,CAAC,CAAA;YAC3D,CAAA,CAAC,OAAO,GAAG,EAAE;gBACZ,IAAI,kCAAkC,CAAC,GAAG,CAAC,IAAI,aAAa,CAAC,MAAM,GAAG,CAAC,EAAE;;oBAEvE,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC,KAAK,CAAC,aAAa,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAA;oBACrE,IAAI,CAAC,aAAa,CAAC,IACjB,OAAO,CAAC,IAAI,CACV,CAAA,wCAAA,EAA2C,aAAa,CAAC,MAAM,CAA4B,yBAAA,EAAA,IAAI,CAAC,YAAY,CAAA,CAAE,CAC/G,CACF,CAAA;oBAED,SAAQ;gBACT,CAAA;;;gBAID,IAAI,CAAA,CAAE,GAAG,YAAY,wBAAwB,CAAC,EAAE;oBAC9C,kBAAkB,EAAE,CAAA;gBACrB,CAAA;gBACD,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,OAAO,EAAE,GAAG,CAAC,CAAA;gBAE/B,MAAM,GAAG,CAAA;YACV,CAAA;YAED,kBAAkB,EAAE,CAAA;YAEpB,YAAY,CAAC,IAAI,CAAC,GAAG,aAAa,CAAC,CAAA;QACpC,CAAA;QACD,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,OAAO,EAAE,YAAY,CAAC,CAAA;KACzC;IAEO,MAAM,cAAc,CAC1B,GAAW,EACX,OAA4B,EAC5B,YAAwC,EACxC,cAAuB,EAAA;;QAEtB,CAAC,EAAA,GAAA,WAAmB,EAAC,OAAO,IAAA,CAAA,EAAA,CAAP,OAAO,GAAK,SAAS,OAAO,CAAC,EAAU,EAAA;YAC3D,MAAM,IAAI,GAAG,IAAI,eAAe,EAAE,CAAA;YAClC,UAAU,CAAC,IAAM,IAAI,CAAC,KAAK,EAAE,EAAE,EAAE,CAAC,CAAA;YAClC,OAAO,IAAI,CAAC,MAAM,CAAA;QACpB,CAAC,CAAA,CAAA;QAED,MAAM,IAAI,GAAG,OAAO,CAAC,IAAI,GAAG,OAAO,CAAC,IAAI,GAAG,EAAE,CAAA;QAC7C,IAAI,aAAa,GAAG,CAAC,CAAC,CAAA;QACtB,IAAI;YACF,aAAa,GAAG,MAAM,CAAC,UAAU,CAAC,IAAI,EAAE,aAAa,CAAC,CAAA;QACvD,CAAA,CAAC,OAAM;YACN,MAAM,OAAO,GAAG,IAAI,WAAW,EAAE,CAAC,MAAM,CAAC,IAAI,CAAC,CAAA;YAC9C,aAAa,GAAG,OAAO,CAAC,MAAM,CAAA;QAC/B,CAAA;QAED,OAAO,MAAM,SAAS,CACpB,YAAW;YACT,IAAI,GAAG,GAAgC,IAAI,CAAA;YAC3C,IAAI;gBACF,GAAG,GAAG,MAAM,IAAI,CAAC,KAAK,CAAC,GAAG,EAAE;oBAC1B,MAAM,EAAG,WAAmB,CAAC,OAAO,CAAC,cAAc,IAAI,IAAI,CAAC,cAAc,CAAC;oBAC3E,GAAG,OAAO;gBACX,CAAA,CAAC,CAAA;YACH,CAAA,CAAC,OAAO,CAAC,EAAE;;gBAEV,MAAM,IAAI,wBAAwB,CAAC,CAAC,CAAC,CAAA;YACtC,CAAA;;;;YAID,MAAM,QAAQ,GAAG,OAAO,CAAC,IAAI,KAAK,SAAS,CAAA;YAC3C,IAAI,CAAC,QAAQ,IAAA,CAAK,GAAG,CAAC,MAAM,GAAG,GAAG,IAAI,GAAG,CAAC,MAAM,IAAI,GAAG,CAAC,EAAE;gBACxD,MAAM,IAAI,qBAAqB,CAAC,GAAG,EAAE,aAAa,CAAC,CAAA;YACpD,CAAA;YACD,OAAO,GAAG,CAAA;SACX,EACD;YAAE,GAAG,IAAI,CAAC,aAAa;YAAE,GAAG,YAAY;QAAA,CAAE,CAC3C,CAAA;KACF;IAED,MAAM,SAAS,CAAC,iBAAA,GAA4B,KAAK,EAAA;;;QAI/C,MAAM,IAAI,CAAC,YAAY,CAAA;QACvB,IAAI,WAAW,GAAG,KAAK,CAAA;QACvB,IAAI,CAAC,eAAe,EAAE,CAAA;QAEtB,MAAM,UAAU,GAAG,YAA0B;YAC3C,IAAI;gBACF,MAAM,OAAO,CAAC,GAAG,CAAC,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC,CAAA;gBAEtD,MAAO,IAAI,CAAE;oBACX,MAAM,KAAK,GAAG,IAAI,CAAC,oBAAoB,CAAqB,wBAAwB,CAAC,KAAK,CAAC,IAAI,EAAE,CAAA;oBAEjG,IAAI,KAAK,CAAC,MAAM,KAAK,CAAC,EAAE;wBACtB,MAAK;oBACN,CAAA;;;;oBAKD,MAAM,IAAI,CAAC,KAAK,EAAE,CAAA;oBAElB,IAAI,WAAW,EAAE;wBACf,MAAK;oBACN,CAAA;gBACF,CAAA;YACF,CAAA,CAAC,OAAO,CAAC,EAAE;gBACV,IAAI,CAAC,mBAAmB,CAAC,CAAC,CAAC,EAAE;oBAC3B,MAAM,CAAC,CAAA;gBACR,CAAA;gBAED,MAAM,aAAa,CAAC,CAAC,CAAC,CAAA;YACvB,CAAA;QACH,CAAC,CAAA;QAED,OAAO,OAAO,CAAC,IAAI,CAAC;YAClB,IAAI,OAAO,CAAO,CAAC,CAAC,EAAE,MAAM,KAAI;gBAC9B,cAAc,CAAC,MAAK;oBAClB,IAAI,CAAC,aAAa,CAAC,IAAM,OAAO,CAAC,KAAK,CAAC,uCAAuC,CAAC,CAAC,CAAA;oBAChF,WAAW,GAAG,IAAI,CAAA;oBAClB,MAAM,CAAC,0EAA0E,CAAC,CAAA;iBACnF,EAAE,iBAAiB,CAAC,CAAA;YACvB,CAAC,CAAC;YACF,UAAU,EAAE;SACb,CAAC,CAAA;KACH;IAED;;;;KAIG,GACH,MAAM,QAAQ,CAAC,iBAAA,GAA4B,KAAK,EAAA;QAC9C,IAAI,IAAI,CAAC,eAAe,EAAE;YACxB,IAAI,CAAC,aAAa,CAAC,IACjB,OAAO,CAAC,IAAI,CACV,gJAAgJ,CACjJ,CACF,CAAA;QACF,CAAA,MAAM;YACL,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC,SAAS,CAAC,iBAAiB,CAAC,CAAC,OAAO,CAAC,MAAK;gBACpE,IAAI,CAAC,eAAe,GAAG,IAAI,CAAA;YAC7B,CAAC,CAAC,CAAA;QACH,CAAA;QACD,OAAO,IAAI,CAAC,eAAe,CAAA;KAC5B;AACF;AC5rCD;;;;;;;CAOG,GAKH,IAAII,MAAM,GAA0BC,QAAQ,EAAE,CAAA;AAE9C,IAAI,CAACD,MAAM,EAAE;IACX,8DAAA;IACA,MAAME,KAAK,GAAGC,OAAO,CAAC,OAAO,CAAC,CAAA;IAE9BH,MAAM,GAAG,OAAOI,GAAW,EAAE7P,OAA4B,KAAmC;QAC1F,MAAM8P,GAAG,GAAG,MAAMH,KAAK,CAACI,OAAO,CAAC;YAC9BF,GAAG;YACHG,OAAO,EAAEhQ,OAAO,CAACgQ,OAAO;YACxBC,MAAM,EAAEjQ,OAAO,CAACiQ,MAAM,CAACC,WAAW,EAAE;YACpCC,IAAI,EAAEnQ,OAAO,CAACoQ,IAAI;YAClBC,MAAM,EAAErQ,OAAO,CAACqQ,MAAM;YACtB,0DAAA;YACAC,cAAc,EAAEA,IAAM,IAAA;QACvB,CAAA,CAAC,CAAA;QAEF,OAAO;YACLC,MAAM,EAAET,GAAG,CAACS,MAAM;YAClBC,IAAI,EAAE,UAAYV,GAAG,CAACK,IAAI;YAC1BM,IAAI,EAAE,UAAYX,GAAG,CAACK,IAAAA;SACvB,CAAA;KACF,CAAA;AACF,CAAA;AAED,0IAAA;AACA,IAAA,UAAeV,MAAmB;ACtClC;;CAEG,SACUiB,IAAI,CAAA;IAKftO,WAAAA,CAAYuO,OAAyB,CAAA;QACnC,IAAI,CAACA,OAAO,GAAGA,OAAO,CAAA;IACxB,CAAA;IAEA;;;GAGG,GACH,MAAMC,QAAQA,GAAA;QACZ,IAAI,IAAI,CAAC1P,KAAK,KAAKpB,SAAS,EAAE;YAC5B,OAAO,IAAI,CAACoB,KAAK,CAAA;QAClB,CAAA;QAED,IAAI,IAAI,CAAC2P,qBAAqB,KAAK/Q,SAAS,EAAE;YAC5C,IAAI,CAAC+Q,qBAAqB,GAAG,CAAC,YAAW;gBACvC,IAAI;oBACF,MAAMC,MAAM,GAAG,MAAM,IAAI,CAACH,OAAO,EAAE,CAAA;oBACnC,IAAI,CAACzP,KAAK,GAAG4P,MAAM,CAAA;oBACnB,OAAOA,MAAM,CAAA;gBACd,CAAA,QAAS;oBACR,8CAAA;oBACA,IAAI,CAACD,qBAAqB,GAAG/Q,SAAS,CAAA;gBACvC,CAAA;YACH,CAAC,GAAG,CAAA;QACL,CAAA;QAED,OAAO,IAAI,CAAC+Q,qBAAqB,CAAA;IACnC,CAAA;IAEA;;GAEG,GACHE,aAAaA,GAAA;QACX,OAAO,IAAI,CAAC7P,KAAK,KAAKpB,SAAS,CAAA;IACjC,CAAA;IAEA;;;GAGG,GACH,MAAMkR,qBAAqBA,GAAA;QACzB,IAAI,IAAI,CAACD,aAAa,EAAE,EAAE;YACxB,OAAA;QACD,CAAA;QACD,MAAM,IAAI,CAACH,QAAQ,EAAE,CAAA;IACvB,CAAA;AACD;ACtDD,2BAAA;AAGA,MAAMK,UAAU,GAAG,IAAIP,IAAI,CAAC,YAAW;IACrC,IAAI;QACF,OAAO,MAAM,OAAO,QAAQ,CAAC,CAAA;IAC9B,CAAA,CAAC,OAAM;QACN,OAAO5Q,SAAS,CAAA;IACjB,CAAA;AACH,CAAC,CAAC,CAAA;AAEK,eAAeoR,aAAaA,GAAA;IACjC,OAAO,MAAMD,UAAU,CAACL,QAAQ,EAAE,CAAA;AACpC,CAAA;AAEA,MAAMO,SAAS,GAAG,IAAIT,IAAI,CAAC,YAA8C;IACvE,IAAI,OAAOU,UAAU,CAACC,MAAM,EAAEC,MAAM,KAAK,WAAW,EAAE;QACpD,OAAOF,UAAU,CAACC,MAAM,CAACC,MAAM,CAAA;IAChC,CAAA;IAED,IAAI;QACF,0DAAA;QACA,MAAMD,MAAM,GAAG,MAAMJ,UAAU,CAACL,QAAQ,EAAE,CAAA;QAC1C,IAAIS,MAAM,EAAEE,SAAS,EAAED,MAAM,EAAE;YAC7B,OAAOD,MAAM,CAACE,SAAS,CAACD,MAAsB,CAAA;QAC/C,CAAA;IACF,CAAA,CAAC,OAAM;IACN,0BAAA;IAAA,CAAA;IAGF,OAAOxR,SAAS,CAAA;AAClB,CAAC,CAAC,CAAA;AAEK,eAAe0R,YAAYA,GAAA;IAChC,OAAO,MAAML,SAAS,CAACP,QAAQ,EAAE,CAAA;AACnC;ACnCA,2BAAA;AAIO,eAAea,QAAQA,CAACjB,IAAY,EAAA;IACzC,2BAAA;IACA,MAAMS,UAAU,GAAG,MAAMC,aAAa,EAAE,CAAA;IACxC,IAAID,UAAU,EAAE;QACd,OAAOA,UAAU,CAACS,UAAU,CAAC,MAAM,CAAC,CAACC,MAAM,CAACnB,IAAI,CAAC,CAACoB,MAAM,CAAC,KAAK,CAAC,CAAA;IAChE,CAAA;IAED,MAAMT,SAAS,GAAG,MAAMK,YAAY,EAAE,CAAA;IAEtC,8BAAA;IACA,IAAIL,SAAS,EAAE;QACb,MAAMU,UAAU,GAAG,MAAMV,SAAS,CAACS,MAAM,CAAC,OAAO,EAAE,IAAIE,WAAW,EAAE,CAACC,MAAM,CAACvB,IAAI,CAAC,CAAC,CAAA;QAClF,MAAMwB,SAAS,GAAGC,KAAK,CAACC,IAAI,CAAC,IAAIC,UAAU,CAACN,UAAU,CAAC,CAAC,CAAA;QACxD,OAAOG,SAAS,CAACtR,GAAG,EAAE0R,IAAI,GAAKA,IAAI,CAAC/R,QAAQ,CAAC,EAAE,CAAC,CAACgS,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,CAACrL,IAAI,CAAC,EAAE,CAAC,CAAA;IAC5E,CAAA;IAED,MAAM,IAAIvC,KAAK,CAAC,oFAAoF,CAAC,CAAA;AACvG;ACfA,MAAM6N,aAAa,GAAG,EAAE,GAAG,IAAI,CAAA;AAE/B,2BAAA;AACA,MAAMC,UAAU,GAAG,iBAAiB,CAAA;AAEpC,MAAMC,6BAA6B,GAAG;IAAC,QAAQ;CAAC,CAAA;AAChD,MAAMC,WAAY,SAAQhO,KAAK,CAAA;IAC7BrC,WAAAA,CAAYjB,OAAe,CAAA;QACzB,KAAK,EAAE,CAAA;QACPsD,KAAK,CAACiO,iBAAiB,CAAC,IAAI,EAAE,IAAI,CAACtQ,WAAW,CAAC,CAAA;QAC/C,IAAI,CAACF,IAAI,GAAG,aAAa,CAAA;QACzB,IAAI,CAACf,OAAO,GAAGA,OAAO,CAAA;QACtB2B,MAAM,CAAC6P,cAAc,CAAC,IAAI,EAAEF,WAAW,CAAClO,SAAS,CAAC,CAAA;IACpD,CAAA;AACD,CAAA;AAED,MAAMqO,sBAAuB,SAAQnO,KAAK,CAAA;IACxCrC,WAAAA,CAAYjB,OAAe,CAAA;QACzB,KAAK,CAACA,OAAO,CAAC,CAAA;QACd,IAAI,CAACe,IAAI,GAAG,IAAI,CAACE,WAAW,CAACF,IAAI,CAAA;QACjCuC,KAAK,CAACiO,iBAAiB,CAAC,IAAI,EAAE,IAAI,CAACtQ,WAAW,CAAC,CAAA;QAC/C,wCAAA;QACA,mGAAA;QACA,yBAAA;QACAU,MAAM,CAAC6P,cAAc,CAAC,IAAI,EAAEC,sBAAsB,CAACrO,SAAS,CAAC,CAAA;IAC/D,CAAA;AACD,CAAA;AAcD,MAAMsO,kBAAkB,CAAA;IAoBtBzQ,WAAAA,CAAY,EACV0Q,eAAe,EACfC,cAAc,EACdC,aAAa,EACbC,OAAO,EACPhT,IAAI,EACJiT,aAAa,EACb,GAAGlT,OAAAA,EACuB,CAAA;QAf5B,IAAS,CAAAmT,SAAA,GAAY,KAAK,CAAA;QAG1B,IAA6B,CAAAC,6BAAA,GAAY,KAAK,CAAA;QAC9C,IAAY,CAAAC,YAAA,GAAW,CAAC,CAAA;QAYtB,IAAI,CAACP,eAAe,GAAGA,eAAe,CAAA;QACtC,IAAI,CAACC,cAAc,GAAGA,cAAc,CAAA;QACpC,IAAI,CAACO,YAAY,GAAG,EAAE,CAAA;QACtB,IAAI,CAACC,iBAAiB,GAAG,CAAA,CAAE,CAAA;QAC3B,IAAI,CAACC,gBAAgB,GAAG,CAAA,CAAE,CAAA;QAC1B,IAAI,CAACC,OAAO,GAAG,CAAA,CAAE,CAAA;QACjB,IAAI,CAACC,sBAAsB,GAAG,KAAK,CAAA;QACnC,IAAI,CAACT,OAAO,GAAGA,OAAO,CAAA;QACtB,IAAI,CAACD,aAAa,GAAGA,aAAa,CAAA;QAClC,IAAI,CAAC/S,IAAI,GAAGA,IAAI,CAAA;QAChB,IAAI,CAAC0T,MAAM,GAAG7T,SAAS,CAAA;QACvB,IAAI,CAAC8T,KAAK,GAAG5T,OAAO,CAAC4T,KAAK,IAAIA,OAAK,CAAA;QACnC,IAAI,CAACC,OAAO,GAAG7T,OAAO,CAAC6T,OAAO,CAAA;QAC9B,IAAI,CAACX,aAAa,GAAGA,aAAa,CAAA;QAClC,IAAI,CAACY,MAAM,GAAG9T,OAAO,CAAC8T,MAAM,CAAA;QAC5B,KAAK,IAAI,CAACC,gBAAgB,EAAE,CAAA;IAC9B,CAAA;IAEAC,KAAKA,CAACC,UAAmB,IAAI,EAAA;QAC3B,IAAI,CAACd,SAAS,GAAGc,OAAO,CAAA;IAC1B,CAAA;IAEQC,aAAaA,CAACC,EAAc,EAAA;QAClC,IAAI,IAAI,CAAChB,SAAS,EAAE;YAClBgB,EAAE,EAAE,CAAA;QACL,CAAA;IACH,CAAA;IAEA,MAAMC,cAAcA,CAClBlJ,GAAW,EACXnJ,UAAkB,EAClBsS,MAAiC,GAAA,CAAA,CAAE,EACnCC,gBAAA,GAA2C,CAAA,CAAE,EAC7CC,kBAA0D,CAAA,CAAE,EAAA;QAE5D,MAAM,IAAI,CAACR,gBAAgB,EAAE,CAAA;QAE7B,IAAIS,QAAQ,GAAiC1U,SAAS,CAAA;QACtD,IAAI2U,WAAW,GAAG3U,SAAS,CAAA;QAE3B,IAAI,CAAC,IAAI,CAAC4T,sBAAsB,EAAE;YAChC,OAAOc,QAAQ,CAAA;QAChB,CAAA;QAED,KAAK,MAAME,IAAI,IAAI,IAAI,CAACpB,YAAY,CAAE;YACpC,IAAIpI,GAAG,KAAKwJ,IAAI,CAACxJ,GAAG,EAAE;gBACpBuJ,WAAW,GAAGC,IAAI,CAAA;gBAClB,MAAA;YACD,CAAA;QACF,CAAA;QAED,IAAID,WAAW,KAAK3U,SAAS,EAAE;YAC7B,IAAI;gBACF0U,QAAQ,GAAG,MAAM,IAAI,CAACG,kBAAkB,CAACF,WAAW,EAAE1S,UAAU,EAAEsS,MAAM,EAAEC,gBAAgB,EAAEC,eAAe,CAAC,CAAA;gBAC5G,IAAI,CAACL,aAAa,CAAC,IAAMU,OAAO,CAACZ,KAAK,CAAC,CAAA,oCAAA,EAAuC9I,GAAG,CAAA,IAAA,EAAOsJ,QAAQ,CAAA,CAAE,CAAC,CAAC,CAAA;aACrG,CAAC,OAAOjO,CAAC,EAAE;gBACV,IAAIA,CAAC,YAAYqM,sBAAsB,EAAE;oBACvC,IAAI,CAACsB,aAAa,CAAC,IAAMU,OAAO,CAACZ,KAAK,CAAC,CAAA,oDAAA,EAAuD9I,GAAG,CAAA,EAAA,EAAK3E,CAAC,CAAA,CAAE,CAAC,CAAC,CAAA;gBAC5G,CAAA,MAAM,IAAIA,CAAC,YAAY9B,KAAK,EAAE;oBAC7B,IAAI,CAACoP,OAAO,GAAG,IAAIpP,KAAK,CAAC,CAAiCyG,8BAAAA,EAAAA,GAAG,CAAK3E,EAAAA,EAAAA,CAAC,CAAE,CAAA,CAAC,CAAC,CAAA;gBACxE,CAAA;YACF,CAAA;QACF,CAAA;QAED,OAAOiO,QAAQ,CAAA;IACjB,CAAA;IAEA,MAAMK,gCAAgCA,CAAC3J,GAAW,EAAE4J,UAA4B,EAAA;QAC9E,MAAM,IAAI,CAACf,gBAAgB,EAAE,CAAA;QAE7B,IAAIS,QAAQ,GAAG1U,SAAS,CAAA;QAExB,IAAI,CAAC,IAAI,CAAC4T,sBAAsB,EAAE;YAChC,OAAO5T,SAAS,CAAA;QACjB,CAAA;QAED,IAAI,OAAOgV,UAAU,IAAI,SAAS,EAAE;YAClCN,QAAQ,GAAG,IAAI,CAACjB,iBAAiB,EAAA,CAAGrI,GAAG,CAAC,EAAE6J,OAAO,EAAEC,QAAQ,EAAA,CAAGF,UAAU,CAACzU,QAAQ,EAAE,CAAC,CAAA;QACrF,CAAA,MAAM,IAAI,OAAOyU,UAAU,IAAI,QAAQ,EAAE;YACxCN,QAAQ,GAAG,IAAI,CAACjB,iBAAiB,EAAA,CAAGrI,GAAG,CAAC,EAAE6J,OAAO,EAAEC,QAAQ,EAAA,CAAGF,UAAU,CAAC,CAAA;QAC1E,CAAA;QAED,yGAAA;QACA,IAAIN,QAAQ,KAAK1U,SAAS,IAAI0U,QAAQ,KAAK,IAAI,EAAE;YAC/C,OAAO,IAAI,CAAA;QACZ,CAAA;QAED,IAAI;YACF,OAAOS,IAAI,CAAC7K,KAAK,CAACoK,QAAQ,CAAC,CAAA;QAC5B,CAAA,CAAC,OAAM;YACN,OAAOA,QAAQ,CAAA;QAChB,CAAA;IACH,CAAA;IAEA,MAAMU,sBAAsBA,CAC1BnT,UAAkB,EAClBsS,MAAA,GAAiC,CAAA,CAAE,EACnCC,gBAA2C,GAAA,CAAA,CAAE,EAC7CC,eAAA,GAA0D,CAAA,CAAE,EAAA;QAM5D,MAAM,IAAI,CAACR,gBAAgB,EAAE,CAAA;QAE7B,MAAMS,QAAQ,GAAqC,CAAA,CAAE,CAAA;QACrD,MAAMQ,QAAQ,GAA6B,CAAA,CAAE,CAAA;QAC7C,IAAIG,gBAAgB,GAAG,IAAI,CAAC7B,YAAY,CAAC9P,MAAM,IAAI,CAAC,CAAA;QAEpD,MAAM6B,OAAO,CAACC,GAAG,CACf,IAAI,CAACgO,YAAY,CAAC5S,GAAG,CAAC,OAAOgU,IAAI,IAAI;YACnC,IAAI;gBACF,MAAMI,UAAU,GAAG,MAAM,IAAI,CAACH,kBAAkB,CAACD,IAAI,EAAE3S,UAAU,EAAEsS,MAAM,EAAEC,gBAAgB,EAAEC,eAAe,CAAC,CAAA;gBAC7GC,QAAQ,CAACE,IAAI,CAACxJ,GAAG,CAAC,GAAG4J,UAAU,CAAA;gBAC/B,MAAMM,YAAY,GAAG,MAAM,IAAI,CAACP,gCAAgC,CAACH,IAAI,CAACxJ,GAAG,EAAE4J,UAAU,CAAC,CAAA;gBACtF,IAAIM,YAAY,EAAE;oBAChBJ,QAAQ,CAACN,IAAI,CAACxJ,GAAG,CAAC,GAAGkK,YAAY,CAAA;gBAClC,CAAA;aACF,CAAC,OAAO7O,CAAC,EAAE;gBACV,IAAIA,CAAC,YAAYqM,sBAAsB,EAAE,CAExC;qBAAM,IAAIrM,CAAC,YAAY9B,KAAK,EAAE;oBAC7B,IAAI,CAACoP,OAAO,GAAG,IAAIpP,KAAK,CAAC,CAAA,8BAAA,EAAiCiQ,IAAI,CAACxJ,GAAG,CAAA,EAAA,EAAK3E,CAAC,CAAA,CAAE,CAAC,CAAC,CAAA;gBAC7E,CAAA;gBACD4O,gBAAgB,GAAG,IAAI,CAAA;YACxB,CAAA;QACH,CAAC,CAAC,CACH,CAAA;QAED,OAAO;YAAEX,QAAQ;YAAEQ,QAAQ;YAAEG,gBAAAA;SAAkB,CAAA;IACjD,CAAA;IAEA,MAAMR,kBAAkBA,CACtBD,IAAwB,EACxB3S,UAAkB,EAClBsS,MAAiC,GAAA,CAAA,CAAE,EACnCC,gBAAA,GAA2C,CAAA,CAAE,EAC7CC,kBAA0D,CAAA,CAAE,EAAA;QAE5D,IAAIG,IAAI,CAACW,4BAA4B,EAAE;YACrC,MAAM,IAAIzC,sBAAsB,CAAC,wCAAwC,CAAC,CAAA;QAC3E,CAAA;QAED,IAAI,CAAC8B,IAAI,CAACY,MAAM,EAAE;YAChB,OAAO,KAAK,CAAA;QACb,CAAA;QAED,MAAMC,WAAW,GAAGb,IAAI,CAACK,OAAO,IAAI,CAAA,CAAE,CAAA;QACtC,MAAMS,4BAA4B,GAAGD,WAAW,CAACC,4BAA4B,CAAA;QAE7E,IAAIA,4BAA4B,IAAI1V,SAAS,EAAE;YAC7C,MAAM2V,SAAS,GAAG,IAAI,CAACjC,gBAAgB,CAACkC,MAAM,CAACF,4BAA4B,CAAC,CAAC,CAAA;YAE7E,IAAI,CAACC,SAAS,EAAE;gBACd,IAAI,CAACvB,aAAa,CAAC,IACjBU,OAAO,CAACe,IAAI,CACV,CAA4CH,yCAAAA,EAAAA,4BAA4B,CAAA,kBAAA,EAAqBd,IAAI,CAACxJ,GAAG,CAAA,CAAE,CACxG,CACF,CAAA;gBACD,MAAM,IAAI0H,sBAAsB,CAAC,mCAAmC,CAAC,CAAA;YACtE,CAAA;YAED,IAAI,CAAA,CAAE6C,SAAS,IAAIpB,MAAM,CAAC,EAAE;gBAC1B,IAAI,CAACH,aAAa,CAAC,IACjBU,OAAO,CAACe,IAAI,CAAC,CAAA,kDAAA,EAAqDjB,IAAI,CAACxJ,GAAG,CAAA,8BAAA,CAAgC,CAAC,CAC5G,CAAA;gBACD,OAAO,KAAK,CAAA;YACb,CAAA;YAED,MAAM0K,sBAAsB,GAAGrB,eAAe,CAACkB,SAAS,CAAC,CAAA;YACzD,OAAO,MAAM,IAAI,CAACI,0BAA0B,CAACnB,IAAI,EAAEL,MAAM,CAACoB,SAAS,CAAC,EAAEG,sBAAsB,CAAC,CAAA;QAC9F,CAAA,MAAM;YACL,OAAO,MAAM,IAAI,CAACC,0BAA0B,CAACnB,IAAI,EAAE3S,UAAU,EAAEuS,gBAAgB,CAAC,CAAA;QACjF,CAAA;IACH,CAAA;IAEA,MAAMuB,0BAA0BA,CAC9BnB,IAAwB,EACxB3S,UAAkB,EAClBf,UAAkC,EAAA;QAElC,MAAMuU,WAAW,GAAGb,IAAI,CAACK,OAAO,IAAI,CAAA,CAAE,CAAA;QACtC,MAAMe,cAAc,GAAGP,WAAW,CAAClB,MAAM,IAAI,EAAE,CAAA;QAC/C,IAAI0B,cAAc,GAAG,KAAK,CAAA;QAC1B,IAAIjF,MAAM,GAAGhR,SAAS,CAAA;QAEtB,mHAAA;QACA,0FAAA;QACA,MAAMkW,oBAAoB,GAAG,CAAC;eAAGF,cAAc;SAAC,CAACpP,IAAI,CAAC,CAACuP,UAAU,EAAEC,UAAU,KAAI;YAC/E,MAAMC,mBAAmB,GAAG,CAAC,CAACF,UAAU,CAACG,OAAO,CAAA;YAChD,MAAMC,mBAAmB,GAAG,CAAC,CAACH,UAAU,CAACE,OAAO,CAAA;YAEhD,IAAID,mBAAmB,IAAIE,mBAAmB,EAAE;gBAC9C,OAAO,CAAC,CAAA;aACT,MAAM,IAAIF,mBAAmB,EAAE;gBAC9B,OAAO,CAAC,CAAC,CAAA;aACV,MAAM,IAAIE,mBAAmB,EAAE;gBAC9B,OAAO,CAAC,CAAA;YACT,CAAA,MAAM;gBACL,OAAO,CAAC,CAAA;YACT,CAAA;QACH,CAAC,CAAC,CAAA;QAEF,KAAK,MAAMC,SAAS,IAAIN,oBAAoB,CAAE;YAC5C,IAAI;gBACF,IAAI,MAAM,IAAI,CAACO,gBAAgB,CAAC7B,IAAI,EAAE3S,UAAU,EAAEuU,SAAS,EAAEtV,UAAU,CAAC,EAAE;oBACxE,MAAMwV,eAAe,GAAGF,SAAS,CAACF,OAAO,CAAA;oBACzC,MAAMK,YAAY,GAAGlB,WAAW,CAACmB,YAAY,EAAEC,QAAQ,IAAI,EAAE,CAAA;oBAC7D,IAAIH,eAAe,IAAIC,YAAY,CAACG,IAAI,EAAER,OAAO,GAAKA,OAAO,CAAClL,GAAG,KAAKsL,eAAe,CAAC,EAAE;wBACtF1F,MAAM,GAAG0F,eAAe,CAAA;oBACzB,CAAA,MAAM;wBACL1F,MAAM,GAAG,AAAC,MAAM,IAAI,CAAC+F,kBAAkB,CAACnC,IAAI,EAAE3S,UAAU,CAAC,IAAK,IAAI,CAAA;oBACnE,CAAA;oBACD,MAAA;gBACD,CAAA;aACF,CAAC,OAAOwE,CAAC,EAAE;gBACV,IAAIA,CAAC,YAAYqM,sBAAsB,EAAE;oBACvCmD,cAAc,GAAG,IAAI,CAAA;gBACtB,CAAA,MAAM;oBACL,MAAMxP,CAAC,CAAA;gBACR,CAAA;YACF,CAAA;QACF,CAAA;QAED,IAAIuK,MAAM,KAAKhR,SAAS,EAAE;YACxB,OAAOgR,MAAM,CAAA;SACd,MAAM,IAAIiF,cAAc,EAAE;YACzB,MAAM,IAAInD,sBAAsB,CAAC,yEAAyE,CAAC,CAAA;QAC5G,CAAA;QAED,yDAAA;QACA,OAAO,KAAK,CAAA;IACd,CAAA;IAEA,MAAM2D,gBAAgBA,CACpB7B,IAAwB,EACxB3S,UAAkB,EAClBuU,SAA+B,EAC/BtV,UAAkC,EAAA;QAElC,MAAM8V,iBAAiB,GAAGR,SAAS,CAACS,kBAAkB,CAAA;QACtD,MAAMC,YAAY,IAAIC,GAAW,IAAU;YACzC,IAAI,CAAC/C,aAAa,CAAC,IAAMU,OAAO,CAACe,IAAI,CAACsB,GAAG,CAAC,CAAC,CAAA;SAC5C,CAAA;QACD,IAAI,CAACX,SAAS,CAACtV,UAAU,IAAI,EAAE,EAAEwC,MAAM,GAAG,CAAC,EAAE;YAC3C,KAAK,MAAMyC,IAAI,IAAIqQ,SAAS,CAACtV,UAAU,CAAE;gBACvC,MAAMkW,YAAY,GAAGjR,IAAI,CAACrF,IAAI,CAAA;gBAC9B,IAAIuW,OAAO,GAAG,KAAK,CAAA;gBAEnB,IAAID,YAAY,KAAK,QAAQ,EAAE;oBAC7BC,OAAO,GAAGC,WAAW,CAACnR,IAAI,EAAEjF,UAAU,EAAE,IAAI,CAACyS,OAAO,EAAE,IAAI,CAACN,SAAS,CAAC,CAAA;gBACtE,CAAA,MAAM;oBACLgE,OAAO,GAAGE,aAAa,CAACpR,IAAI,EAAEjF,UAAU,EAAEgW,YAAY,CAAC,CAAA;gBACxD,CAAA;gBAED,IAAI,CAACG,OAAO,EAAE;oBACZ,OAAO,KAAK,CAAA;gBACb,CAAA;YACF,CAAA;YAED,IAAIL,iBAAiB,IAAIhX,SAAS,EAAE;gBAClC,OAAO,IAAI,CAAA;YACZ,CAAA;QACF,CAAA;QAED,IAAIgX,iBAAiB,IAAIhX,SAAS,IAAI,AAAC,MAAMwX,KAAK,CAAC5C,IAAI,CAACxJ,GAAG,EAAEnJ,UAAU,CAAC,GAAI+U,iBAAiB,GAAG,KAAK,EAAE;YACrG,OAAO,KAAK,CAAA;QACb,CAAA;QAED,OAAO,IAAI,CAAA;IACb,CAAA;IAEA,MAAMD,kBAAkBA,CAACnC,IAAwB,EAAE3S,UAAkB,EAAA;QACnE,MAAMwV,SAAS,GAAG,MAAMD,KAAK,CAAC5C,IAAI,CAACxJ,GAAG,EAAEnJ,UAAU,EAAE,SAAS,CAAC,CAAA;QAC9D,MAAMyV,eAAe,GAAG,IAAI,CAACC,kBAAkB,CAAC/C,IAAI,CAAC,CAACgD,IAAI,EAAEtB,OAAO,IAAI;YACrE,OAAOmB,SAAS,IAAInB,OAAO,CAACuB,QAAQ,IAAIJ,SAAS,GAAGnB,OAAO,CAACwB,QAAQ,CAAA;QACtE,CAAC,CAAC,CAAA;QAEF,IAAIJ,eAAe,EAAE;YACnB,OAAOA,eAAe,CAACtM,GAAG,CAAA;QAC3B,CAAA;QACD,OAAOpL,SAAS,CAAA;IAClB,CAAA;IAEA2X,kBAAkBA,CAAC/C,IAAwB,EAAA;QACzC,MAAMmD,WAAW,GAA0D,EAAE,CAAA;QAC7E,IAAIF,QAAQ,GAAG,CAAC,CAAA;QAChB,IAAIC,QAAQ,GAAG,CAAC,CAAA;QAChB,MAAMrC,WAAW,GAAGb,IAAI,CAACK,OAAO,IAAI,CAAA,CAAE,CAAA;QACtC,MAAM+C,aAAa,GAGbvC,WAAW,CAACmB,YAAY,EAAEC,QAAQ,IAAI,EAAE,CAAA;QAE9CmB,aAAa,CAACC,OAAO,EAAE3B,OAAO,IAAI;YAChCwB,QAAQ,GAAGD,QAAQ,GAAGvB,OAAO,CAACW,kBAAkB,GAAG,KAAK,CAAA;YACxDc,WAAW,CAAC1L,IAAI,CAAC;gBAAEwL,QAAQ;gBAAEC,QAAQ;gBAAE1M,GAAG,EAAEkL,OAAO,CAAClL,GAAAA;YAAG,CAAE,CAAC,CAAA;YAC1DyM,QAAQ,GAAGC,QAAQ,CAAA;QACrB,CAAC,CAAC,CAAA;QACF,OAAOC,WAAW,CAAA;IACpB,CAAA;IAEA,MAAM9D,gBAAgBA,CAACiE,WAAW,GAAG,KAAK,EAAA;QACxC,IAAI,CAAC,IAAI,CAACtE,sBAAsB,IAAIsE,WAAW,EAAE;YAC/C,MAAM,IAAI,CAACC,iBAAiB,EAAE,CAAA;QAC/B,CAAA;IACH,CAAA;IAEA;;;GAGG,GACHC,sBAAsBA,GAAA;QACpB,OAAO,CAAC,IAAI,CAACxE,sBAAsB,IAAI,KAAK,KAAK,CAAC,IAAI,CAACJ,YAAY,EAAE9P,MAAM,IAAI,CAAC,IAAI,CAAC,CAAA;IACvF,CAAA;IAEA;;;;;GAKG,GACK2U,kBAAkBA,GAAA;QACxB,IAAI,CAAC,IAAI,CAAC/E,6BAA6B,EAAE;YACvC,OAAO,IAAI,CAACN,eAAe,CAAA;QAC5B,CAAA;QAED,OAAO1D,IAAI,CAACI,GAAG,CAAC8C,aAAa,EAAE,IAAI,CAACQ,eAAe,GAAG,CAAC,IAAI,IAAI,CAACO,YAAY,CAAC,CAAA;IAC/E,CAAA;IAEA,MAAM4E,iBAAiBA,GAAA;QACrB,IAAI,IAAI,CAACtE,MAAM,EAAE;YACfyE,YAAY,CAAC,IAAI,CAACzE,MAAM,CAAC,CAAA;YACzB,IAAI,CAACA,MAAM,GAAG7T,SAAS,CAAA;QACxB,CAAA;QAED,IAAI,CAAC6T,MAAM,GAAG0E,UAAU,CAAC,IAAM,IAAI,CAACJ,iBAAiB,EAAE,EAAE,IAAI,CAACE,kBAAkB,EAAE,CAAC,CAAA;QAEnF,IAAI;YACF,MAAMrI,GAAG,GAAG,MAAM,IAAI,CAACwI,8BAA8B,EAAE,CAAA;YAEvD,4FAAA;YACA,IAAI,CAACxI,GAAG,EAAE;gBACR,0DAAA;gBACA,OAAA;YACD,CAAA;YAED,mDAAA;YACA,EAAA;YACA,mGAAA;YACA,4FAAA;YACA,+FAAA;YACA,+FAAA;YACA,4EAAA;YACA,EAAA;YACA,+FAAA;YACA,2FAAA;YACA,oEAAA;YACA,OAAQA,GAAG,CAACS,MAAM;gBAChB,KAAK,GAAG;oBACN,kBAAA;oBACA,IAAI,CAAC6C,6BAA6B,GAAG,IAAI,CAAA;oBACzC,IAAI,CAACC,YAAY,IAAI,CAAC,CAAA;oBACtB,MAAM,IAAIZ,WAAW,CACnB,CAAqF,kFAAA,EAAA,IAAI,CAAC0F,kBAAkB,EAAE,CAAA,gEAAA,CAAkE,CACjL,CAAA;gBAEH,KAAK,GAAG;oBACN,mCAAA;oBACAvD,OAAO,CAACe,IAAI,CACV,mKAAmK,CACpK,CAAA;oBACD,IAAI,CAACrC,YAAY,GAAG,EAAE,CAAA;oBACtB,IAAI,CAACC,iBAAiB,GAAG,CAAA,CAAE,CAAA;oBAC3B,IAAI,CAACC,gBAAgB,GAAG,CAAA,CAAE,CAAA;oBAC1B,IAAI,CAACC,OAAO,GAAG,CAAA,CAAE,CAAA;oBACjB,OAAA;gBAEF,KAAK,GAAG;oBACN,oBAAA;oBACA,IAAI,CAACL,6BAA6B,GAAG,IAAI,CAAA;oBACzC,IAAI,CAACC,YAAY,IAAI,CAAC,CAAA;oBACtB,MAAM,IAAIZ,WAAW,CACnB,CAA2I,wIAAA,EAAA,IAAI,CAAC0F,kBAAkB,EAAE,CAAA,oIAAA,CAAsI,CAC3S,CAAA;gBAEH,KAAK,GAAG;oBACN,eAAA;oBACA,IAAI,CAAC/E,6BAA6B,GAAG,IAAI,CAAA;oBACzC,IAAI,CAACC,YAAY,IAAI,CAAC,CAAA;oBACtB,MAAM,IAAIZ,WAAW,CACnB,CAAgE,6DAAA,EAAA,IAAI,CAAC0F,kBAAkB,EAAE,CAAA,gEAAA,CAAkE,CAC5J,CAAA;gBAEH,KAAK,GAAG;oBAAE;wBACR,8BAAA;wBACA,MAAMI,YAAY,GAAK,AAAD,MAAOzI,GAAG,CAACW,IAAI,EAAE,IAAgC,CAAA,CAAE,CAAA;wBACzE,IAAI,CAAA,CAAE,OAAO,IAAI8H,YAAY,CAAC,EAAE;4BAC9B,IAAI,CAAC1E,OAAO,GAAG,IAAIpP,KAAK,CAAC,CAAA,6CAAA,EAAgDwQ,IAAI,CAACuD,SAAS,CAACD,YAAY,CAAC,CAAA,CAAE,CAAC,CAAC,CAAA;4BACzG,OAAA;wBACD,CAAA;wBAED,IAAI,CAACjF,YAAY,GAAIiF,YAAY,CAACE,KAA8B,IAAI,EAAE,CAAA;wBACtE,IAAI,CAAClF,iBAAiB,GAAG,IAAI,CAACD,YAAY,CAACjI,MAAM,CAC/C,CAACqN,GAAG,EAAEC,IAAI,GAAA,CAAOD,GAAG,CAACC,IAAI,CAACzN,GAAG,CAAC,GAAGyN,IAAI,EAAGD,GAAG,CAAC,EACR,CAAA,CAAE,CACvC,CAAA;wBACD,IAAI,CAAClF,gBAAgB,GAAI+E,YAAY,CAACK,kBAA6C,IAAI,CAAA,CAAE,CAAA;wBACzF,IAAI,CAACnF,OAAO,GAAI8E,YAAY,CAAC9E,OAAyC,IAAI,CAAA,CAAE,CAAA;wBAC5E,IAAI,CAACC,sBAAsB,GAAG,IAAI,CAAA;wBAClC,IAAI,CAACN,6BAA6B,GAAG,KAAK,CAAA;wBAC1C,IAAI,CAACC,YAAY,GAAG,CAAC,CAAA;wBACrB,IAAI,CAACS,MAAM,GAAG,IAAI,CAACR,YAAY,CAAC9P,MAAM,CAAC,CAAA;wBACvC,MAAA;oBACD,CAAA;gBAED;oBACE,oDAAA;oBACA,8CAAA;oBACA,OAAA;YACH,CAAA;SACF,CAAC,OAAOqV,GAAG,EAAE;YACZ,IAAIA,GAAG,YAAYpG,WAAW,EAAE;gBAC9B,IAAI,CAACoB,OAAO,GAAGgF,GAAG,CAAC,CAAA;YACpB,CAAA;QACF,CAAA;IACH,CAAA;IAEQC,+BAA+BA,CAAC7I,SAA2C,KAAK,EAAA;QACtF,OAAO;YACLA,MAAM;YACND,OAAO,EAAE;gBACP,GAAG,IAAI,CAACkD,aAAa;gBACrB,cAAc,EAAE,kBAAkB;gBAClC6F,aAAa,EAAE,CAAA,OAAA,EAAU,IAAI,CAAChG,cAAc,CAAA,CAAA;YAC7C,CAAA;SACF,CAAA;IACH,CAAA;IAEA,MAAMuF,8BAA8BA,GAAA;QAClC,MAAMzI,GAAG,GAAG,CAAA,EAAG,IAAI,CAAC5P,IAAI,CAA4C,yCAAA,EAAA,IAAI,CAAC+S,aAAa,CAAe,aAAA,CAAA,CAAA;QAErG,MAAMhT,OAAO,GAAG,IAAI,CAAC8Y,+BAA+B,EAAE,CAAA;QAEtD,IAAIE,YAAY,GAAG,IAAI,CAAA;QAEvB,IAAI,IAAI,CAAC/F,OAAO,IAAI,OAAO,IAAI,CAACA,OAAO,KAAK,QAAQ,EAAE;YACpD,MAAMgG,UAAU,GAAG,IAAIC,eAAe,EAAE,CAAA;YACxCF,YAAY,GAAGG,cAAc,CAAC,MAAK;gBACjCF,UAAU,CAACG,KAAK,EAAE,CAAA;YACpB,CAAC,EAAE,IAAI,CAACnG,OAAO,CAAC,CAAA;YAChBjT,OAAO,CAACqQ,MAAM,GAAG4I,UAAU,CAAC5I,MAAM,CAAA;QACnC,CAAA;QAED,IAAI;YACF,OAAO,MAAM,IAAI,CAACuD,KAAK,CAAC/D,GAAG,EAAE7P,OAAO,CAAC,CAAA;QACtC,CAAA,QAAS;YACRoY,YAAY,CAACY,YAAY,CAAC,CAAA;QAC3B,CAAA;IACH,CAAA;IAEAK,UAAUA,GAAA;QACRjB,YAAY,CAAC,IAAI,CAACzE,MAAM,CAAC,CAAA;IAC3B,CAAA;IAEA2F,2BAA2BA,CAACC,OAAe,EAAA;QACzC,MAAM1J,GAAG,GAAG,CAAG,EAAA,IAAI,CAAC5P,IAAI,CAAA,qCAAA,EAAwCsZ,OAAO,CAAiB,eAAA,CAAA,CAAA;QAExF,MAAMvZ,OAAO,GAAG,IAAI,CAAC8Y,+BAA+B,EAAE,CAAA;QAEtD,IAAIE,YAAY,GAAG,IAAI,CAAA;QACvB,IAAI,IAAI,CAAC/F,OAAO,IAAI,OAAO,IAAI,CAACA,OAAO,KAAK,QAAQ,EAAE;YACpD,MAAMgG,UAAU,GAAG,IAAIC,eAAe,EAAE,CAAA;YACxCF,YAAY,GAAGG,cAAc,CAAC,MAAK;gBACjCF,UAAU,CAACG,KAAK,EAAE,CAAA;YACpB,CAAC,EAAE,IAAI,CAACnG,OAAO,CAAC,CAAA;YAChBjT,OAAO,CAACqQ,MAAM,GAAG4I,UAAU,CAAC5I,MAAM,CAAA;QACnC,CAAA;QACD,IAAI;YACF,OAAO,IAAI,CAACuD,KAAK,CAAC/D,GAAG,EAAE7P,OAAO,CAAC,CAAA;QAChC,CAAA,QAAS;YACRoY,YAAY,CAACY,YAAY,CAAC,CAAA;QAC3B,CAAA;IACH,CAAA;AACD,CAAA;AAED,kGAAA;AACA,6FAAA;AACA,gGAAA;AACA,4CAAA;AACA,eAAe1B,KAAKA,CAACpM,GAAW,EAAEnJ,UAAkB,EAAEyX,OAAe,EAAE,EAAA;IACrE,MAAMC,UAAU,GAAG,MAAMhI,QAAQ,CAAC,CAAA,EAAGvG,GAAG,CAAA,CAAA,EAAInJ,UAAU,CAAA,EAAGyX,IAAI,CAAA,CAAE,CAAC,CAAA;IAChE,OAAOE,QAAQ,CAACD,UAAU,CAAC1S,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,EAAE,CAAC,GAAGwL,UAAU,CAAA;AAC3D,CAAA;AAEA,SAAS8E,aAAaA,CACpB3P,QAAoD,EACpDiS,cAAmC,EACnC3C,YAAoC,EAAA;IAEpC,MAAM9L,GAAG,GAAGxD,QAAQ,CAACwD,GAAG,CAAA;IACxB,MAAMhK,KAAK,GAAGwG,QAAQ,CAACxG,KAAK,CAAA;IAC5B,MAAM0Y,QAAQ,GAAGlS,QAAQ,CAACkS,QAAQ,IAAI,OAAO,CAAA;IAE7C,IAAI,CAAA,CAAE1O,GAAG,IAAIyO,cAAc,CAAC,EAAE;QAC5B,MAAM,IAAI/G,sBAAsB,CAAC,CAAY1H,SAAAA,EAAAA,GAAG,CAAA,4BAAA,CAA8B,CAAC,CAAA;IAChF,CAAA,MAAM,IAAI0O,QAAQ,KAAK,YAAY,EAAE;QACpC,MAAM,IAAIhH,sBAAsB,CAAC,CAAA,oCAAA,CAAsC,CAAC,CAAA;IACzE,CAAA;IAED,MAAMiH,aAAa,GAAGF,cAAc,CAACzO,GAAG,CAAC,CAAA;IACzC,IAAI2O,aAAa,IAAI,IAAI,IAAI,CAACrH,6BAA6B,CAAChT,QAAQ,CAACoa,QAAQ,CAAC,EAAE;QAC9E,8DAAA;QACA,gFAAA;QACA,IAAI5C,YAAY,EAAE;YAChBA,YAAY,CAAC,CAAY9L,SAAAA,EAAAA,GAAG,CAAmD0O,gDAAAA,EAAAA,QAAQ,CAAA,SAAA,CAAW,CAAC,CAAA;QACpG,CAAA;QAED,OAAO,KAAK,CAAA;IACb,CAAA;IAED,SAASE,iBAAiBA,CAAC5Y,KAAU,EAAE2Y,aAAkB,EAAA;QACvD,IAAI5H,KAAK,CAAC8H,OAAO,CAAC7Y,KAAK,CAAC,EAAE;YACxB,OAAOA,KAAK,CAACR,GAAG,EAAEsZ,GAAG,GAAKtE,MAAM,CAACsE,GAAG,CAAC,CAAC9J,WAAW,EAAE,CAAC,CAAC1Q,QAAQ,CAACkW,MAAM,CAACmE,aAAa,CAAC,CAAC3J,WAAW,EAAE,CAAC,CAAA;QACnG,CAAA;QACD,OAAOwF,MAAM,CAACxU,KAAK,CAAC,CAACgP,WAAW,EAAE,KAAKwF,MAAM,CAACmE,aAAa,CAAC,CAAC3J,WAAW,EAAE,CAAA;IAC5E,CAAA;IAEA,SAAS+J,OAAOA,CAACC,GAAQ,EAAEC,GAAQ,EAAEP,QAAgB,EAAA;QACnD,IAAIA,QAAQ,KAAK,IAAI,EAAE;YACrB,OAAOM,GAAG,GAAGC,GAAG,CAAA;QACjB,CAAA,MAAM,IAAIP,QAAQ,KAAK,KAAK,EAAE;YAC7B,OAAOM,GAAG,IAAIC,GAAG,CAAA;QAClB,CAAA,MAAM,IAAIP,QAAQ,KAAK,IAAI,EAAE;YAC5B,OAAOM,GAAG,GAAGC,GAAG,CAAA;QACjB,CAAA,MAAM,IAAIP,QAAQ,KAAK,KAAK,EAAE;YAC7B,OAAOM,GAAG,IAAIC,GAAG,CAAA;QAClB,CAAA,MAAM;YACL,MAAM,IAAI1V,KAAK,CAAC,CAAqBmV,kBAAAA,EAAAA,QAAQ,EAAE,CAAC,CAAA;QACjD,CAAA;IACH,CAAA;IAEA,OAAQA,QAAQ;QACd,KAAK,OAAO;YACV,OAAOE,iBAAiB,CAAC5Y,KAAK,EAAE2Y,aAAa,CAAC,CAAA;QAChD,KAAK,QAAQ;YACX,OAAO,CAACC,iBAAiB,CAAC5Y,KAAK,EAAE2Y,aAAa,CAAC,CAAA;QACjD,KAAK,QAAQ;YACX,OAAO3O,GAAG,IAAIyO,cAAc,CAAA;QAC9B,KAAK,WAAW;YACd,OAAOjE,MAAM,CAACmE,aAAa,CAAC,CAAC3J,WAAW,EAAE,CAAC1Q,QAAQ,CAACkW,MAAM,CAACxU,KAAK,CAAC,CAACgP,WAAW,EAAE,CAAC,CAAA;QAClF,KAAK,eAAe;YAClB,OAAO,CAACwF,MAAM,CAACmE,aAAa,CAAC,CAAC3J,WAAW,EAAE,CAAC1Q,QAAQ,CAACkW,MAAM,CAACxU,KAAK,CAAC,CAACgP,WAAW,EAAE,CAAC,CAAA;QACnF,KAAK,OAAO;YACV,OAAOkK,YAAY,CAAC1E,MAAM,CAACxU,KAAK,CAAC,CAAC,IAAIwU,MAAM,CAACmE,aAAa,CAAC,CAACQ,KAAK,CAAC3E,MAAM,CAACxU,KAAK,CAAC,CAAC,KAAK,IAAI,CAAA;QAC3F,KAAK,WAAW;YACd,OAAOkZ,YAAY,CAAC1E,MAAM,CAACxU,KAAK,CAAC,CAAC,IAAIwU,MAAM,CAACmE,aAAa,CAAC,CAACQ,KAAK,CAAC3E,MAAM,CAACxU,KAAK,CAAC,CAAC,KAAK,IAAI,CAAA;QAC3F,KAAK,IAAI,CAAA;QACT,KAAK,KAAK,CAAA;QACV,KAAK,IAAI,CAAA;QACT,KAAK,KAAK;YAAE;gBACV,uEAAA;gBACA,4EAAA;gBACA,IAAIoZ,WAAW,GAAG,OAAOpZ,KAAK,KAAK,QAAQ,GAAGA,KAAK,GAAG,IAAI,CAAA;gBAE1D,IAAI,OAAOA,KAAK,KAAK,QAAQ,EAAE;oBAC7B,IAAI;wBACFoZ,WAAW,GAAGC,UAAU,CAACrZ,KAAK,CAAC,CAAA;qBAChC,CAAC,OAAO2X,GAAG,EAAE;oBACZ,OAAA;oBAAA,CAAA;gBAEH,CAAA;gBAED,IAAIyB,WAAW,IAAI,IAAI,IAAIT,aAAa,IAAI,IAAI,EAAE;oBAChD,gCAAA;oBACA,IAAI,OAAOA,aAAa,KAAK,QAAQ,EAAE;wBACrC,OAAOI,OAAO,CAACJ,aAAa,EAAEnE,MAAM,CAACxU,KAAK,CAAC,EAAE0Y,QAAQ,CAAC,CAAA;oBACvD,CAAA,MAAM;wBACL,OAAOK,OAAO,CAACJ,aAAa,EAAES,WAAW,EAAEV,QAAQ,CAAC,CAAA;oBACrD,CAAA;gBACF,CAAA,MAAM;oBACL,OAAOK,OAAO,CAACvE,MAAM,CAACmE,aAAa,CAAC,EAAEnE,MAAM,CAACxU,KAAK,CAAC,EAAE0Y,QAAQ,CAAC,CAAA;gBAC/D,CAAA;YACF,CAAA;QACD,KAAK,eAAe,CAAA;QACpB,KAAK,gBAAgB;YAAE;gBACrB,IAAIY,UAAU,GAAGC,uCAAuC,CAAC/E,MAAM,CAACxU,KAAK,CAAC,CAAC,CAAA;gBACvE,IAAIsZ,UAAU,IAAI,IAAI,EAAE;oBACtBA,UAAU,GAAGE,iBAAiB,CAACxZ,KAAK,CAAC,CAAA;gBACtC,CAAA;gBAED,IAAIsZ,UAAU,IAAI,IAAI,EAAE;oBACtB,MAAM,IAAI5H,sBAAsB,CAAC,CAAiB1R,cAAAA,EAAAA,KAAK,EAAE,CAAC,CAAA;gBAC3D,CAAA;gBACD,MAAMyZ,YAAY,GAAGD,iBAAiB,CAACb,aAAa,CAAC,CAAA;gBACrD,IAAI;oBAAC,gBAAgB;iBAAC,CAACra,QAAQ,CAACoa,QAAQ,CAAC,EAAE;oBACzC,OAAOe,YAAY,GAAGH,UAAU,CAAA;gBACjC,CAAA;gBACD,OAAOG,YAAY,GAAGH,UAAU,CAAA;YACjC,CAAA;QACD;YACE,MAAM,IAAI5H,sBAAsB,CAAC,CAAqBgH,kBAAAA,EAAAA,QAAQ,EAAE,CAAC,CAAA;IACpE,CAAA;AACH,CAAA;AAEA,SAASxC,WAAWA,CAClB1P,QAAoD,EACpDiS,cAAmC,EACnCiB,gBAA+C,EAC/CzH,SAAA,GAAqB,KAAK,EAAA;IAE1B,MAAM0H,QAAQ,GAAGnF,MAAM,CAAChO,QAAQ,CAACxG,KAAK,CAAC,CAAA;IACvC,IAAI,CAAA,CAAE2Z,QAAQ,IAAID,gBAAgB,CAAC,EAAE;QACnC,MAAM,IAAIhI,sBAAsB,CAAC,0DAA0D,CAAC,CAAA;IAC7F,CAAA;IAED,MAAMkI,aAAa,GAAGF,gBAAgB,CAACC,QAAQ,CAAC,CAAA;IAChD,OAAOE,kBAAkB,CAACD,aAAa,EAAEnB,cAAc,EAAEiB,gBAAgB,EAAEzH,SAAS,CAAC,CAAA;AACvF,CAAA;AAEA,SAAS4H,kBAAkBA,CACzBD,aAA4B,EAC5BnB,cAAmC,EACnCiB,gBAA+C,EAC/CzH,SAAA,GAAqB,KAAK,EAAA;IAE1B,IAAI,CAAC2H,aAAa,EAAE;QAClB,OAAO,IAAI,CAAA;IACZ,CAAA;IAED,MAAME,iBAAiB,GAAGF,aAAa,CAACla,IAAI,CAAA;IAC5C,MAAMI,UAAU,GAAG8Z,aAAa,CAACta,MAAM,CAAA;IAEvC,IAAI,CAACQ,UAAU,IAAIA,UAAU,CAACwC,MAAM,KAAK,CAAC,EAAE;QAC1C,wCAAA;QACA,OAAO,IAAI,CAAA;IACZ,CAAA;IAED,IAAIyX,oBAAoB,GAAG,KAAK,CAAA;IAEhC,IAAI,QAAQ,IAAIja,UAAU,CAAC,CAAC,CAAC,EAAE;QAC7B,0BAAA;QACA,KAAK,MAAMiF,IAAI,IAAIjF,UAA6B,CAAE;YAChD,IAAI;gBACF,MAAMmW,OAAO,GAAG4D,kBAAkB,CAAC9U,IAAI,EAAE0T,cAAc,EAAEiB,gBAAgB,EAAEzH,SAAS,CAAC,CAAA;gBACrF,IAAI6H,iBAAiB,KAAK,KAAK,EAAE;oBAC/B,IAAI,CAAC7D,OAAO,EAAE;wBACZ,OAAO,KAAK,CAAA;oBACb,CAAA;gBACF,CAAA,MAAM;oBACL,WAAA;oBACA,IAAIA,OAAO,EAAE;wBACX,OAAO,IAAI,CAAA;oBACZ,CAAA;gBACF,CAAA;aACF,CAAC,OAAO0B,GAAG,EAAE;gBACZ,IAAIA,GAAG,YAAYjG,sBAAsB,EAAE;oBACzC,IAAIO,SAAS,EAAE;wBACbyB,OAAO,CAACZ,KAAK,CAAC,CAAA,2BAAA,EAA8B/N,IAAI,CAAa4S,UAAAA,EAAAA,GAAG,EAAE,CAAC,CAAA;oBACpE,CAAA;oBACDoC,oBAAoB,GAAG,IAAI,CAAA;gBAC5B,CAAA,MAAM;oBACL,MAAMpC,GAAG,CAAA;gBACV,CAAA;YACF,CAAA;QACF,CAAA;QAED,IAAIoC,oBAAoB,EAAE;YACxB,MAAM,IAAIrI,sBAAsB,CAAC,0DAA0D,CAAC,CAAA;QAC7F,CAAA;QACD,sEAAA;QACA,OAAOoI,iBAAiB,KAAK,KAAK,CAAA;IACnC,CAAA,MAAM;QACL,KAAK,MAAM/U,IAAI,IAAIjF,UAA4B,CAAE;YAC/C,IAAI;gBACF,IAAImW,OAAgB,CAAA;gBACpB,IAAIlR,IAAI,CAACrF,IAAI,KAAK,QAAQ,EAAE;oBAC1BuW,OAAO,GAAGC,WAAW,CAACnR,IAAI,EAAE0T,cAAc,EAAEiB,gBAAgB,EAAEzH,SAAS,CAAC,CAAA;gBACzE,CAAA,MAAM;oBACLgE,OAAO,GAAGE,aAAa,CAACpR,IAAI,EAAE0T,cAAc,CAAC,CAAA;gBAC9C,CAAA;gBAED,MAAMuB,QAAQ,GAAGjV,IAAI,CAACiV,QAAQ,IAAI,KAAK,CAAA;gBAEvC,IAAIF,iBAAiB,KAAK,KAAK,EAAE;oBAC/B,sCAAA;oBACA,IAAI,CAAC7D,OAAO,IAAI,CAAC+D,QAAQ,EAAE;wBACzB,OAAO,KAAK,CAAA;oBACb,CAAA;oBACD,IAAI/D,OAAO,IAAI+D,QAAQ,EAAE;wBACvB,OAAO,KAAK,CAAA;oBACb,CAAA;gBACF,CAAA,MAAM;oBACL,WAAA;oBACA,IAAI/D,OAAO,IAAI,CAAC+D,QAAQ,EAAE;wBACxB,OAAO,IAAI,CAAA;oBACZ,CAAA;oBACD,IAAI,CAAC/D,OAAO,IAAI+D,QAAQ,EAAE;wBACxB,OAAO,IAAI,CAAA;oBACZ,CAAA;gBACF,CAAA;aACF,CAAC,OAAOrC,GAAG,EAAE;gBACZ,IAAIA,GAAG,YAAYjG,sBAAsB,EAAE;oBACzC,IAAIO,SAAS,EAAE;wBACbyB,OAAO,CAACZ,KAAK,CAAC,CAAA,2BAAA,EAA8B/N,IAAI,CAAa4S,UAAAA,EAAAA,GAAG,EAAE,CAAC,CAAA;oBACpE,CAAA;oBACDoC,oBAAoB,GAAG,IAAI,CAAA;gBAC5B,CAAA,MAAM;oBACL,MAAMpC,GAAG,CAAA;gBACV,CAAA;YACF,CAAA;QACF,CAAA;QAED,IAAIoC,oBAAoB,EAAE;YACxB,MAAM,IAAIrI,sBAAsB,CAAC,0DAA0D,CAAC,CAAA;QAC7F,CAAA;QAED,sEAAA;QACA,OAAOoI,iBAAiB,KAAK,KAAK,CAAA;IACnC,CAAA;AACH,CAAA;AAEA,SAASZ,YAAYA,CAACe,KAAa,EAAA;IACjC,IAAI;QACF,IAAIC,MAAM,CAACD,KAAK,CAAC,CAAA;QACjB,OAAO,IAAI,CAAA;KACZ,CAAC,OAAOtC,GAAG,EAAE;QACZ,OAAO,KAAK,CAAA;IACb,CAAA;AACH,CAAA;AAEA,SAAS6B,iBAAiBA,CAACxZ,KAAmD,EAAA;IAC5E,IAAIA,KAAK,YAAYma,IAAI,EAAE;QACzB,OAAOna,KAAK,CAAA;KACb,MAAM,IAAI,OAAOA,KAAK,KAAK,QAAQ,IAAI,OAAOA,KAAK,KAAK,QAAQ,EAAE;QACjE,MAAMoa,IAAI,GAAG,IAAID,IAAI,CAACna,KAAK,CAAC,CAAA;QAC5B,IAAI,CAACqa,KAAK,CAACD,IAAI,CAACE,OAAO,EAAE,CAAC,EAAE;YAC1B,OAAOF,IAAI,CAAA;QACZ,CAAA;QACD,MAAM,IAAI1I,sBAAsB,CAAC,CAAG1R,EAAAA,KAAK,CAAA,6BAAA,CAA+B,CAAC,CAAA;IAC1E,CAAA,MAAM;QACL,MAAM,IAAI0R,sBAAsB,CAAC,CAAqB1R,kBAAAA,EAAAA,KAAK,CAAA,yCAAA,CAA2C,CAAC,CAAA;IACxG,CAAA;AACH,CAAA;AAEA,SAASuZ,uCAAuCA,CAACvZ,KAAa,EAAA;IAC5D,MAAMia,KAAK,GAAG,yCAAyC,CAAA;IACvD,MAAMd,KAAK,GAAGnZ,KAAK,CAACmZ,KAAK,CAACc,KAAK,CAAC,CAAA;IAChC,MAAMM,QAAQ,GAAG,IAAIJ,IAAI,CAAC,IAAIA,IAAI,EAAE,CAACK,WAAW,EAAE,CAAC,CAAA;IAEnD,IAAIrB,KAAK,EAAE;QACT,IAAI,CAACA,KAAK,CAAChG,MAAM,EAAE;YACjB,OAAO,IAAI,CAAA;QACZ,CAAA;QAED,MAAMsH,MAAM,GAAGjC,QAAQ,CAACW,KAAK,CAAChG,MAAM,CAAC,QAAQ,CAAC,CAAC,CAAA;QAE/C,IAAIsH,MAAM,IAAI,KAAK,EAAE;YACnB,+DAAA;YACA,OAAO,IAAI,CAAA;QACZ,CAAA;QACD,MAAMC,QAAQ,GAAGvB,KAAK,CAAChG,MAAM,CAAC,UAAU,CAAC,CAAA;QACzC,IAAIuH,QAAQ,IAAI,GAAG,EAAE;YACnBH,QAAQ,CAACI,WAAW,CAACJ,QAAQ,CAACK,WAAW,EAAE,GAAGH,MAAM,CAAC,CAAA;QACtD,CAAA,MAAM,IAAIC,QAAQ,IAAI,GAAG,EAAE;YAC1BH,QAAQ,CAACM,UAAU,CAACN,QAAQ,CAACO,UAAU,EAAE,GAAGL,MAAM,CAAC,CAAA;QACpD,CAAA,MAAM,IAAIC,QAAQ,IAAI,GAAG,EAAE;YAC1BH,QAAQ,CAACM,UAAU,CAACN,QAAQ,CAACO,UAAU,EAAE,GAAGL,MAAM,GAAG,CAAC,CAAC,CAAA;QACxD,CAAA,MAAM,IAAIC,QAAQ,IAAI,GAAG,EAAE;YAC1BH,QAAQ,CAACQ,WAAW,CAACR,QAAQ,CAACS,WAAW,EAAE,GAAGP,MAAM,CAAC,CAAA;QACtD,CAAA,MAAM,IAAIC,QAAQ,IAAI,GAAG,EAAE;YAC1BH,QAAQ,CAACU,cAAc,CAACV,QAAQ,CAACW,cAAc,EAAE,GAAGT,MAAM,CAAC,CAAA;QAC5D,CAAA,MAAM;YACL,OAAO,IAAI,CAAA;QACZ,CAAA;QAED,OAAOF,QAAQ,CAAA;IAChB,CAAA,MAAM;QACL,OAAO,IAAI,CAAA;IACZ,CAAA;AACH;MCt1BaY,oBAAoB,CAAA;IAAjCja,WAAAA,EAAA;QACU,IAAc,CAAAka,cAAA,GAAuC,CAAA,CAAE,CAAA;IASjE,CAAA;IAPEC,WAAWA,CAACrR,GAA6B,EAAA;QACvC,OAAO,IAAI,CAACoR,cAAc,CAACpR,GAAG,CAAC,CAAA;IACjC,CAAA;IAEAsR,WAAWA,CAACtR,GAA6B,EAAEhK,KAAiB,EAAA;QAC1D,IAAI,CAACob,cAAc,CAACpR,GAAG,CAAC,GAAGhK,KAAK,KAAK,IAAI,GAAGA,KAAK,GAAGpB,SAAS,CAAA;IAC/D,CAAA;AACD;ACOD,0EAAA;AACA,uDAAA;AACA,MAAM2c,wBAAwB,GAAG,GAAG,CAAA;AACpC,MAAMC,cAAc,GAAG,EAAE,GAAG,IAAI,CAAA;AAChC,MAAMC,cAAc,GAAG,EAAE,GAAG,IAAI,CAAA;AAEhC,kCAAA;AACM,MAAgBC,oBAAqB,SAAQC,oBAAoB,CAAA;IAUrEza,WAAYA,CAAAhC,MAAc,EAAEJ,OAAA,GAA0B,CAAA,CAAE,CAAA;QACtD,KAAK,CAACI,MAAM,EAAEJ,OAAO,CAAC,CAAA;QAVhB,IAAA,CAAAsc,cAAc,GAAG,IAAID,oBAAoB,EAAE,CAAA;QAYjD,IAAI,CAACrc,OAAO,GAAGA,OAAO,CAAA;QAEtB,IAAI,CAACA,OAAO,CAAC8c,2BAA2B,GACtC,OAAO9c,OAAO,CAAC8c,2BAA2B,KAAK,QAAQ,GACnD1N,IAAI,CAAClI,GAAG,CAAClH,OAAO,CAAC8c,2BAA2B,EAAEL,wBAAwB,CAAC,GACvEC,cAAc,CAAA;QAEpB,IAAI1c,OAAO,CAAC+S,cAAc,EAAE;YAC1B,IAAI/S,OAAO,CAAC+S,cAAc,CAACvT,QAAQ,CAAC,MAAM,CAAC,EAAE;gBAC3C,MAAM,IAAIiF,KAAK,CACb,uHAAuH,CACxH,CAAA;YACF,CAAA;YAED,IAAI,CAACsY,kBAAkB,GAAG,IAAIlK,kBAAkB,CAAC;gBAC/CC,eAAe,EAAE,IAAI,CAAC9S,OAAO,CAAC8c,2BAA2B;gBACzD/J,cAAc,EAAE/S,OAAO,CAAC+S,cAAc;gBACtCC,aAAa,EAAE5S,MAAM;gBACrB6S,OAAO,EAAEjT,OAAO,CAACgd,cAAc,IAAI,KAAK;gBACxC/c,IAAI,EAAE,IAAI,CAACA,IAAI;gBACf2T,KAAK,EAAE5T,OAAO,CAAC4T,KAAK;gBACpBC,OAAO,GAAGgF,GAAU,IAAI;oBACtB,IAAI,CAACoE,OAAO,CAACC,IAAI,CAAC,OAAO,EAAErE,GAAG,CAAC,CAAA;iBAChC;gBACD/E,MAAM,GAAGqJ,KAAa,IAAI;oBACxB,IAAI,CAACF,OAAO,CAACC,IAAI,CAAC,4BAA4B,EAAEC,KAAK,CAAC,CAAA;iBACvD;gBACDjK,aAAa,EAAE,IAAI,CAACkK,gBAAgB,EAAE;YACvC,CAAA,CAAC,CAAA;QACH,CAAA;QACD,IAAI,CAACC,aAAa,GAAG,IAAItV,aAAa,CAAC,IAAI,EAAE/H,OAAO,CAAC,CAAA;QACrD,IAAI,CAACsd,0BAA0B,GAAG,CAAA,CAAE,CAAA;QACpC,IAAI,CAACC,YAAY,GAAGvd,OAAO,CAACud,YAAY,IAAIZ,cAAc,CAAA;IAC5D,CAAA;IAEAa,oBAAoBA,CAACtS,GAA6B,EAAA;QAChD,OAAO,IAAI,CAACoR,cAAc,CAACC,WAAW,CAACrR,GAAG,CAAC,CAAA;IAC7C,CAAA;IAEAuS,oBAAoBA,CAACvS,GAA6B,EAAEhK,KAAiB,EAAA;QACnE,OAAO,IAAI,CAACob,cAAc,CAACE,WAAW,CAACtR,GAAG,EAAEhK,KAAK,CAAC,CAAA;IACpD,CAAA;IAEA0S,KAAKA,CAAC/D,GAAW,EAAE7P,OAA4B,EAAA;QAC7C,OAAO,IAAI,CAACA,OAAO,CAAC4T,KAAK,GAAG,IAAI,CAAC5T,OAAO,CAAC4T,KAAK,CAAC/D,GAAG,EAAE7P,OAAO,CAAC,GAAG4T,OAAK,CAAC/D,GAAG,EAAE7P,OAAO,CAAC,CAAA;IACpF,CAAA;IACA0d,iBAAiBA,GAAA;QACf,OAAOC,OAAO,CAAA;IAChB,CAAA;IACAC,kBAAkBA,GAAA;QAChB,OAAO,CAAG,EAAA,IAAI,CAACC,YAAY,EAAE,CAAI,CAAA,EAAA,IAAI,CAACH,iBAAiB,EAAE,CAAE,CAAA,CAAA;IAC7D,CAAA;IAEAI,MAAMA,GAAA;QACJ,OAAO,KAAK,CAACC,KAAK,EAAE,CAAA;IACtB,CAAA;IAEAC,OAAOA,GAAA;QACL,OAAO,KAAK,CAACC,MAAM,EAAE,CAAA;IACvB,CAAA;IAEAjK,KAAKA,CAACC,UAAmB,IAAI,EAAA;QAC3B,KAAK,CAACD,KAAK,CAACC,OAAO,CAAC,CAAA;QACpB,IAAI,CAAC8I,kBAAkB,EAAE/I,KAAK,CAACC,OAAO,CAAC,CAAA;IACzC,CAAA;IAEAnS,OAAOA,CAACoc,KAAmB,EAAA;QACzB,IAAI,OAAOA,KAAK,KAAK,QAAQ,EAAE;YAC7B,IAAI,CAAChK,aAAa,CAAC,IACjBU,OAAO,CAACe,IAAI,CAAC,mFAAmF,CAAC,CAClG,CAAA;QACF,CAAA;QACD,MAAM,EAAE5T,UAAU,EAAEzC,KAAK,EAAE0B,UAAU,EAAEqT,MAAM,EAAE8J,gBAAgB,EAAEC,SAAS,EAAEC,YAAY,EAAEC,IAAAA,EAAM,GAC9FJ,KAAK,CAAA;QACP,MAAMK,QAAQ,IAAIL,KAAiC,IAAU;YAC3D,KAAK,CAACM,gBAAgB,CAACzc,UAAU,EAAEzC,KAAK,EAAE4e,KAAK,EAAE;gBAAEE,SAAS;gBAAEC,YAAY;gBAAEC,IAAAA;YAAI,CAAE,CAAC,CAAA;SACpF,CAAA;QAED,MAAMG,SAAS,GAAG,OAChB1c,UAAsC,EACtCsS,MAA8B,EAC9BgK,YAA0C,KACoB;YAC9D,OAAO,CAAC,MAAM,KAAK,CAACK,wBAAwB,CAAC3c,UAAU,EAAEsS,MAAM,EAAEvU,SAAS,EAAEA,SAAS,EAAEue,YAAY,CAAC,EAAE5F,KAAK,CAAA;SAC5G,CAAA;QAED,iHAAA;QACA,MAAMkG,cAAc,GAAGtZ,OAAO,CAAC+H,OAAO,EAAE,CACrCwR,IAAI,CAAC,YAAW;YACf,IAAIT,gBAAgB,EAAE;gBACpB,iFAAA;gBACA,sGAAA;gBACA,OAAO,MAAMM,SAAS,CAAC1c,UAAU,EAAEsS,MAAM,EAAEgK,YAAY,CAAC,CAAA;YACzD,CAAA;YAED,IAAI/e,KAAK,KAAK,sBAAsB,EAAE;gBACpC,gIAAA;gBACA,OAAO,CAAA,CAAE,CAAA;YACV,CAAA;YAED,IAAI,CAAC,IAAI,CAACyd,kBAAkB,EAAEzJ,YAAY,EAAE9P,MAAM,IAAI,CAAC,IAAI,CAAC,EAAE;gBAC5D,mGAAA;gBACA,MAAMqb,sBAAsB,GAA2B,CAAA,CAAE,CAAA;gBACzD,KAAK,MAAM,CAAC3T,GAAG,EAAEhK,KAAK,CAAC,IAAI4B,MAAM,CAACgc,OAAO,CAACzK,MAAM,IAAI,CAAA,CAAE,CAAC,CAAE;oBACvDwK,sBAAsB,CAAC3T,GAAG,CAAC,GAAGwK,MAAM,CAACxU,KAAK,CAAC,CAAA;gBAC5C,CAAA;gBAED,OAAO,MAAM,IAAI,CAAC6d,WAAW,CAAChd,UAAU,EAAE;oBACxCsS,MAAM,EAAEwK,sBAAsB;oBAC9BR,YAAY;oBACZW,mBAAmB,EAAE,IAAA;gBACtB,CAAA,CAAC,CAAA;YACH,CAAA;YACD,OAAO,CAAA,CAAE,CAAA;QACX,CAAC,CAAC,CACDJ,IAAI,EAAEnG,KAAK,IAAI;YACd,6CAAA;YACA,MAAMvQ,oBAAoB,GAAwB,CAAA,CAAE,CAAA;YACpD,IAAIuQ,KAAK,EAAE;gBACT,KAAK,MAAM,CAACwG,OAAO,EAAE7I,OAAO,CAAC,IAAItT,MAAM,CAACgc,OAAO,CAACrG,KAAK,CAAC,CAAE;oBACtDvQ,oBAAoB,CAAC,CAAY+W,SAAAA,EAAAA,OAAO,CAAE,CAAA,CAAC,GAAG7I,OAAO,CAAA;gBACtD,CAAA;YACF,CAAA;YACD,MAAM8I,WAAW,GAAGpc,MAAM,CAACqD,IAAI,CAACsS,KAAK,IAAI,CAAA,CAAE,CAAC,CACzCpV,MAAM,EAAEqR,IAAI,GAAK+D,KAAK,EAAA,CAAG/D,IAAI,CAAC,KAAK,KAAK,CAAC,CACzChO,IAAI,EAAE,CAAA;YACT,IAAIwY,WAAW,CAAC1b,MAAM,GAAG,CAAC,EAAE;gBAC1B0E,oBAAoB,CAAC,uBAAuB,CAAC,GAAGgX,WAAW,CAAA;YAC5D,CAAA;YAED,OAAOhX,oBAAoB,CAAA;QAC7B,CAAC,CAAC,CACD+E,KAAK,CAAC,MAAK;YACV,mFAAA;YACA,OAAO,CAAA,CAAE,CAAA;QACX,CAAC,CAAC,CACD2R,IAAI,EAAE1W,oBAAoB,IAAI;YAC7B,qCAAA;YACAqW,QAAQ,CAAC;gBAAE,GAAGrW,oBAAoB;gBAAE,GAAGlH,UAAU;gBAAEme,OAAO,EAAE9K,MAAAA;YAAM,CAAE,CAAC,CAAA;QACvE,CAAC,CAAC,CAAA;QAEJ,IAAI,CAAC+K,iBAAiB,CAACT,cAAc,CAAC,CAAA;IACxC,CAAA;IAEA,MAAMU,gBAAgBA,CAACnB,KAAmB,EAAA;QACxC,IAAI,OAAOA,KAAK,KAAK,QAAQ,EAAE;YAC7B,IAAI,CAAChK,aAAa,CAAC,IACjBU,OAAO,CAACe,IAAI,CAAC,mFAAmF,CAAC,CAClG,CAAA;QACF,CAAA;QACD,MAAM,EAAE5T,UAAU,EAAEzC,KAAK,EAAE0B,UAAU,EAAEqT,MAAM,EAAE8J,gBAAgB,EAAEC,SAAS,EAAEC,YAAY,EAAEC,IAAAA,EAAM,GAC9FJ,KAAK,CAAA;QAEP,MAAMK,QAAQ,IAAIL,KAAiC,IAAmB;YACpE,OAAO,KAAK,CAACoB,yBAAyB,CAACvd,UAAU,EAAEzC,KAAK,EAAE4e,KAAK,EAAE;gBAAEE,SAAS;gBAAEC,YAAY;gBAAEC,IAAAA;YAAI,CAAE,CAAC,CAAA;SACpG,CAAA;QAED,MAAMG,SAAS,GAAG,OAChB1c,UAAsC,EACtCsS,MAA8B,EAC9BgK,YAA0C,KACoB;YAC9D,OAAO,CAAC,MAAM,KAAK,CAACK,wBAAwB,CAAC3c,UAAU,EAAEsS,MAAM,EAAEvU,SAAS,EAAEA,SAAS,EAAEue,YAAY,CAAC,EAAE5F,KAAK,CAAA;SAC5G,CAAA;QAED,MAAMkG,cAAc,GAAGtZ,OAAO,CAAC+H,OAAO,EAAE,CACrCwR,IAAI,CAAC,YAAW;YACf,IAAIT,gBAAgB,EAAE;gBACpB,iFAAA;gBACA,sGAAA;gBACA,OAAO,MAAMM,SAAS,CAAC1c,UAAU,EAAEsS,MAAM,EAAEgK,YAAY,CAAC,CAAA;YACzD,CAAA;YAED,IAAI/e,KAAK,KAAK,sBAAsB,EAAE;gBACpC,gIAAA;gBACA,OAAO,CAAA,CAAE,CAAA;YACV,CAAA;YAED,IAAI,CAAC,IAAI,CAACyd,kBAAkB,EAAEzJ,YAAY,EAAE9P,MAAM,IAAI,CAAC,IAAI,CAAC,EAAE;gBAC5D,mGAAA;gBACA,MAAMqb,sBAAsB,GAA2B,CAAA,CAAE,CAAA;gBACzD,KAAK,MAAM,CAAC3T,GAAG,EAAEhK,KAAK,CAAC,IAAI4B,MAAM,CAACgc,OAAO,CAACzK,MAAM,IAAI,CAAA,CAAE,CAAC,CAAE;oBACvDwK,sBAAsB,CAAC3T,GAAG,CAAC,GAAGwK,MAAM,CAACxU,KAAK,CAAC,CAAA;gBAC5C,CAAA;gBAED,OAAO,MAAM,IAAI,CAAC6d,WAAW,CAAChd,UAAU,EAAE;oBACxCsS,MAAM,EAAEwK,sBAAsB;oBAC9BR,YAAY;oBACZW,mBAAmB,EAAE,IAAA;gBACtB,CAAA,CAAC,CAAA;YACH,CAAA;YACD,OAAO,CAAA,CAAE,CAAA;QACX,CAAC,CAAC,CACDJ,IAAI,EAAEnG,KAAK,IAAI;YACd,6CAAA;YACA,MAAMvQ,oBAAoB,GAAwB,CAAA,CAAE,CAAA;YACpD,IAAIuQ,KAAK,EAAE;gBACT,KAAK,MAAM,CAACwG,OAAO,EAAE7I,OAAO,CAAC,IAAItT,MAAM,CAACgc,OAAO,CAACrG,KAAK,CAAC,CAAE;oBACtDvQ,oBAAoB,CAAC,CAAY+W,SAAAA,EAAAA,OAAO,CAAE,CAAA,CAAC,GAAG7I,OAAO,CAAA;gBACtD,CAAA;YACF,CAAA;YACD,MAAM8I,WAAW,GAAGpc,MAAM,CAACqD,IAAI,CAACsS,KAAK,IAAI,CAAA,CAAE,CAAC,CACzCpV,MAAM,EAAEqR,IAAI,GAAK+D,KAAK,EAAA,CAAG/D,IAAI,CAAC,KAAK,KAAK,CAAC,CACzChO,IAAI,EAAE,CAAA;YACT,IAAIwY,WAAW,CAAC1b,MAAM,GAAG,CAAC,EAAE;gBAC1B0E,oBAAoB,CAAC,uBAAuB,CAAC,GAAGgX,WAAW,CAAA;YAC5D,CAAA;YAED,OAAOhX,oBAAoB,CAAA;QAC7B,CAAC,CAAC,CACD+E,KAAK,CAAC,MAAK;YACV,mFAAA;YACA,OAAO,CAAA,CAAE,CAAA;QACX,CAAC,CAAC,CACD2R,IAAI,CAAE1W,oBAAoB,IAAI;YAC7B,qCAAA;YACAqW,QAAQ,CAAC;gBAAE,GAAGrW,oBAAoB;gBAAE,GAAGlH,UAAU;gBAAEme,OAAO,EAAE9K,MAAAA;YAAM,CAAE,CAAC,CAAA;QACvE,CAAC,CAAC,CAAA;QAEJ,MAAMsK,cAAc,CAAA;IACtB,CAAA;IAEAY,QAAQA,CAAC,EAAExd,UAAU,EAAEf,UAAU,EAAEqd,YAAAA,EAA+B,EAAA;QAChE,iEAAA;QAEA,0CAAA;QACA,MAAMmB,aAAa,GAAGxe,UAAU,EAAEye,SAAS,CAAA;QAC3C,OAAOze,UAAU,EAAEye,SAAS,CAAA;QAE5B,2DAAA;QACA,MAAMC,SAAS,GAAG1e,UAAU,EAAE2e,IAAI,IAAI3e,UAAU,CAAA;QAEhD,KAAK,CAAC4e,iBAAiB,CACrB7d,UAAU,EACV;YACE4d,IAAI,EAAED,SAAS;YACfD,SAAS,EAAED,aAAAA;QACZ,CAAA,EACD;YAAEnB,YAAAA;QAAc,CAAA,CACjB,CAAA;IACH,CAAA;IAEA,MAAMwB,iBAAiBA,CAAC,EAAE9d,UAAU,EAAEf,UAAU,EAAEqd,YAAAA,EAA+B,EAAA;QAC/E,0CAAA;QACA,MAAMmB,aAAa,GAAGxe,UAAU,EAAEye,SAAS,CAAA;QAC3C,OAAOze,UAAU,EAAEye,SAAS,CAAA;QAE5B,2DAAA;QACA,MAAMC,SAAS,GAAG1e,UAAU,EAAE2e,IAAI,IAAI3e,UAAU,CAAA;QAEhD,MAAM,KAAK,CAAC8e,0BAA0B,CACpC/d,UAAU,EACV;YACE4d,IAAI,EAAED,SAAS;YACfD,SAAS,EAAED,aAAAA;QACZ,CAAA,EACD;YAAEnB,YAAAA;QAAc,CAAA,CACjB,CAAA;IACH,CAAA;IAEA0B,KAAKA,CAAC5P,IAAmE,EAAA;QACvE,KAAK,CAAC6P,cAAc,CAAC7P,IAAI,CAAC4P,KAAK,EAAE5P,IAAI,CAACpO,UAAU,EAAEjC,SAAS,EAAE;YAAEue,YAAY,EAAElO,IAAI,CAACkO,YAAAA;QAAc,CAAA,CAAC,CAAA;IACnG,CAAA;IAEA,MAAM4B,cAAcA,CAAC9P,IAAmE,EAAA;QACtF,MAAM,KAAK,CAAC+P,uBAAuB,CAAC/P,IAAI,CAAC4P,KAAK,EAAE5P,IAAI,CAACpO,UAAU,EAAEjC,SAAS,EAAE;YAAEue,YAAY,EAAElO,IAAI,CAACkO,YAAAA;QAAc,CAAA,CAAC,CAAA;IAClH,CAAA;IAEAnG,sBAAsBA,GAAA;QACpB,OAAO,IAAI,CAAC6E,kBAAkB,EAAE7E,sBAAsB,EAAE,IAAI,KAAK,CAAA;IACnE,CAAA;IAEA,MAAMiI,2BAA2BA,CAACC,SAAA,GAAoB1D,cAAc,EAAA;QAClE,IAAI,IAAI,CAACxE,sBAAsB,EAAE,EAAE;YACjC,OAAO,IAAI,CAAA;QACZ,CAAA;QAED,IAAI,IAAI,CAAC6E,kBAAkB,KAAKjd,SAAS,EAAE;YACzC,OAAO,KAAK,CAAA;QACb,CAAA;QAED,OAAO,IAAIuF,OAAO,EAAE+H,OAAO,IAAI;YAC7B,MAAM6F,OAAO,GAAGoF,UAAU,CAAC,MAAK;gBAC9BgI,OAAO,EAAE,CAAA;gBACTjT,OAAO,CAAC,KAAK,CAAC,CAAA;aACf,EAAEgT,SAAS,CAAC,CAAA;YAEb,MAAMC,OAAO,GAAG,IAAI,CAACpD,OAAO,CAACpZ,EAAE,CAAC,4BAA4B,GAAGsZ,KAAa,IAAI;gBAC9E/E,YAAY,CAACnF,OAAO,CAAC,CAAA;gBACrBoN,OAAO,EAAE,CAAA;gBACTjT,OAAO,CAAC+P,KAAK,GAAG,CAAC,CAAC,CAAA;YACpB,CAAC,CAAC,CAAA;QACJ,CAAC,CAAC,CAAA;IACJ,CAAA;IAEA,MAAM/I,cAAcA,CAClBlJ,GAAW,EACXnJ,UAAkB,EAClB/B,OAOC,EAAA;QAED,MAAM,EAAEqU,MAAM,EAAEgK,YAAAA,EAAc,GAAGre,OAAO,IAAI,CAAA,CAAE,CAAA;QAC9C,IAAI,EAAEgf,mBAAmB,EAAEsB,qBAAqB,EAAEhM,gBAAgB,EAAEC,eAAAA,EAAiB,GAAGvU,OAAO,IAAI,CAAA,CAAE,CAAA;QAErG,MAAMugB,kBAAkB,GAAG,IAAI,CAACC,gCAAgC,CAC9Dze,UAAU,EACVsS,MAAM,EACNC,gBAAgB,EAChBC,eAAe,CAChB,CAAA;QAEDD,gBAAgB,GAAGiM,kBAAkB,CAACE,mBAAmB,CAAA;QACzDlM,eAAe,GAAGgM,kBAAkB,CAACG,kBAAkB,CAAA;QAEvD,eAAA;QACA,IAAI1B,mBAAmB,IAAIlf,SAAS,EAAE;YACpCkf,mBAAmB,GAAG,KAAK,CAAA;QAC5B,CAAA;QACD,IAAIsB,qBAAqB,IAAIxgB,SAAS,EAAE;YACtCwgB,qBAAqB,GAAG,IAAI,CAAA;QAC7B,CAAA;QAED,IAAI9L,QAAQ,GAAG,MAAM,IAAI,CAACuI,kBAAkB,EAAE3I,cAAc,CAC1DlJ,GAAG,EACHnJ,UAAU,EACVsS,MAAM,EACNC,gBAAgB,EAChBC,eAAe,CAChB,CAAA;QAED,MAAMoM,uBAAuB,GAAGnM,QAAQ,KAAK1U,SAAS,CAAA;QACtD,IAAI8gB,SAAS,GAAG9gB,SAAS,CAAA;QACzB,IAAI+gB,UAAU,GAAkC/gB,SAAS,CAAA;QACzD,IAAI,CAAC6gB,uBAAuB,IAAI,CAAC3B,mBAAmB,EAAE;YACpD,MAAM8B,cAAc,GAAG,MAAM,KAAK,CAACC,6BAA6B,CAC9D7V,GAAG,EACHnJ,UAAU,EACVsS,MAAM,EACNC,gBAAgB,EAChBC,eAAe,EACf8J,YAAY,CACb,CAAA;YAED,IAAIyC,cAAc,KAAKhhB,SAAS,EAAE;gBAChC,OAAOA,SAAS,CAAA;YACjB,CAAA;YAED+gB,UAAU,GAAGC,cAAc,CAACtM,QAAQ,CAAA;YACpCA,QAAQ,GAAGwM,mBAAmB,CAACH,UAAU,CAAC,CAAA;YAC1CD,SAAS,GAAGE,cAAc,EAAEF,SAAS,CAAA;QACtC,CAAA;QAED,MAAMK,sBAAsB,GAAG,CAAA,EAAG/V,GAAG,CAAA,CAAA,EAAIsJ,QAAQ,CAAE,CAAA,CAAA;QAEnD,IACE8L,qBAAqB,IAAA,CACpB,CAAA,CAAEve,UAAU,IAAI,IAAI,CAACub,0BAA0B,CAAC,IAC/C,CAAC,IAAI,CAACA,0BAA0B,CAACvb,UAAU,CAAC,CAACvC,QAAQ,CAACyhB,sBAAsB,CAAC,CAAC,EAChF;YACA,IAAIne,MAAM,CAACqD,IAAI,CAAC,IAAI,CAACmX,0BAA0B,CAAC,CAAC9Z,MAAM,IAAI,IAAI,CAAC+Z,YAAY,EAAE;gBAC5E,IAAI,CAACD,0BAA0B,GAAG,CAAA,CAAE,CAAA;YACrC,CAAA;YACD,IAAIrL,KAAK,CAAC8H,OAAO,CAAC,IAAI,CAACuD,0BAA0B,CAACvb,UAAU,CAAC,CAAC,EAAE;gBAC9D,IAAI,CAACub,0BAA0B,CAACvb,UAAU,CAAC,CAACoK,IAAI,CAAC8U,sBAAsB,CAAC,CAAA;YACzE,CAAA,MAAM;gBACL,IAAI,CAAC3D,0BAA0B,CAACvb,UAAU,CAAC,GAAG;oBAACkf,sBAAsB;iBAAC,CAAA;YACvE,CAAA;YACD,IAAI,CAACnf,OAAO,CAAC;gBACXC,UAAU;gBACVzC,KAAK,EAAE,sBAAsB;gBAC7B0B,UAAU,EAAE;oBACVkgB,aAAa,EAAEhW,GAAG;oBAClBiW,sBAAsB,EAAE3M,QAAQ;oBAChC4M,gBAAgB,EAAEP,UAAU,EAAEQ,QAAQ,EAAEC,EAAE;oBAC1CC,qBAAqB,EAAEV,UAAU,EAAEQ,QAAQ,EAAE1D,OAAO;oBACpD6D,oBAAoB,EAAEX,UAAU,EAAE9c,MAAM,EAAE0d,WAAW,IAAIZ,UAAU,EAAE9c,MAAM,EAAE2d,IAAI;oBACjFC,iBAAiB,EAAEhB,uBAAuB;oBAC1C,CAAC,CAAYzV,SAAAA,EAAAA,GAAG,CAAE,CAAA,CAAA,EAAGsJ,QAAQ;oBAC7BoN,wBAAwB,EAAEhB,SAAAA;iBAC3B;gBACDvM,MAAM;gBACNgK,YAAAA;YACD,CAAA,CAAC,CAAA;QACH,CAAA;QACD,OAAO7J,QAAQ,CAAA;IACjB,CAAA;IAEA,MAAMqN,qBAAqBA,CACzB3W,GAAW,EACXnJ,UAAkB,EAClB+S,UAA6B,EAC7B9U,OAOC,EAAA;QAED,MAAM,EAAEqU,MAAM,EAAEgK,YAAAA,EAAc,GAAGre,OAAO,IAAI,CAAA,CAAE,CAAA;QAC9C,IAAI,EAAEgf,mBAAmB,EAAEsB,qBAAqB,EAAEhM,gBAAgB,EAAEC,eAAAA,EAAiB,GAAGvU,OAAO,IAAI,CAAA,CAAE,CAAA;QAErG,MAAMugB,kBAAkB,GAAG,IAAI,CAACC,gCAAgC,CAC9Dze,UAAU,EACVsS,MAAM,EACNC,gBAAgB,EAChBC,eAAe,CAChB,CAAA;QAEDD,gBAAgB,GAAGiM,kBAAkB,CAACE,mBAAmB,CAAA;QACzDlM,eAAe,GAAGgM,kBAAkB,CAACG,kBAAkB,CAAA;QAEvD,IAAIlM,QAAQ,GAAG1U,SAAS,CAAA;QAExB,MAAMgiB,sBAAsB,GAAG,IAAI,CAAC/E,kBAAkB,KAAKjd,SAAS,CAAA;QACpE,IAAIgiB,sBAAsB,EAAE;YAC1B,iDAAA;YACA,IAAI,CAAChN,UAAU,EAAE;gBACfA,UAAU,GAAG,MAAM,IAAI,CAACV,cAAc,CAAClJ,GAAG,EAAEnJ,UAAU,EAAE;oBACtD,GAAG/B,OAAO;oBACVgf,mBAAmB,EAAE,IAAI;oBACzBsB,qBAAqB,EAAE,KAAA;gBACxB,CAAA,CAAC,CAAA;YACH,CAAA;YAED,IAAIxL,UAAU,EAAE;gBACdN,QAAQ,GAAG,MAAM,IAAI,CAACuI,kBAAkB,EAAElI,gCAAgC,CAAC3J,GAAG,EAAE4J,UAAU,CAAC,CAAA;YAC5F,CAAA;QACF,CAAA;QACD,GAAA;QAEA,eAAA;QACA,IAAIkK,mBAAmB,IAAIlf,SAAS,EAAE;YACpCkf,mBAAmB,GAAG,KAAK,CAAA;QAC5B,CAAA;QACD,IAAIsB,qBAAqB,IAAIxgB,SAAS,EAAE;YACtCwgB,qBAAqB,GAAG,IAAI,CAAA;QAC7B,CAAA;QAED,eAAA;QACA,IAAItB,mBAAmB,IAAIlf,SAAS,EAAE;YACpCkf,mBAAmB,GAAG,KAAK,CAAA;QAC5B,CAAA;QAED,MAAM+C,0BAA0B,GAAGvN,QAAQ,KAAK1U,SAAS,CAAA;QAEzD,IAAI,CAACiiB,0BAA0B,IAAI,CAAC/C,mBAAmB,EAAE;YACvDxK,QAAQ,GAAG,MAAM,KAAK,CAACwN,8BAA8B,CACnD9W,GAAG,EACHnJ,UAAU,EACVsS,MAAM,EACNC,gBAAgB,EAChBC,eAAe,EACf8J,YAAY,CACb,CAAA;QACF,CAAA;QACD,OAAO7J,QAAQ,CAAA;IACjB,CAAA;IAEA,MAAMyN,sBAAsBA,CAAC1I,OAAe,EAAA;QAC1C,OAAO,CAAC,MAAM,IAAI,CAACwD,kBAAkB,EAAEzD,2BAA2B,CAACC,OAAO,CAAC,GAAG9I,IAAI,EAAE,CAAA;IACtF,CAAA;IAEA,MAAMyR,gBAAgBA,CACpBhX,GAAW,EACXnJ,UAAkB,EAClB/B,OAOC,EAAA;QAED,MAAMmiB,IAAI,GAAG,MAAM,IAAI,CAAC/N,cAAc,CAAClJ,GAAG,EAAEnJ,UAAU,EAAE/B,OAAO,CAAC,CAAA;QAChE,IAAImiB,IAAI,KAAKriB,SAAS,EAAE;YACtB,OAAOA,SAAS,CAAA;QACjB,CAAA;QACD,OAAO,CAAC,CAACqiB,IAAI,IAAI,KAAK,CAAA;IACxB,CAAA;IAEA,MAAMpD,WAAWA,CACfhd,UAAkB,EAClB/B,OAMC,EAAA;QAED,MAAMwU,QAAQ,GAAG,MAAM,IAAI,CAACU,sBAAsB,CAACnT,UAAU,EAAE/B,OAAO,CAAC,CAAA;QACvE,OAAOwU,QAAQ,CAAClB,YAAY,IAAI,CAAA,CAAE,CAAA;IACpC,CAAA;IAEA,MAAM4B,sBAAsBA,CAC1BnT,UAAkB,EAClB/B,OAMC,EAAA;QAED,MAAM,EAAEqU,MAAM,EAAEgK,YAAAA,EAAc,GAAGre,OAAO,IAAI,CAAA,CAAE,CAAA;QAC9C,IAAI,EAAEgf,mBAAmB,EAAE1K,gBAAgB,EAAEC,eAAAA,EAAiB,GAAGvU,OAAO,IAAI,CAAA,CAAE,CAAA;QAE9E,MAAMugB,kBAAkB,GAAG,IAAI,CAACC,gCAAgC,CAC9Dze,UAAU,EACVsS,MAAM,EACNC,gBAAgB,EAChBC,eAAe,CAChB,CAAA;QAEDD,gBAAgB,GAAGiM,kBAAkB,CAACE,mBAAmB,CAAA;QACzDlM,eAAe,GAAGgM,kBAAkB,CAACG,kBAAkB,CAAA;QAEvD,eAAA;QACA,IAAI1B,mBAAmB,IAAIlf,SAAS,EAAE;YACpCkf,mBAAmB,GAAG,KAAK,CAAA;QAC5B,CAAA;QAED,MAAMoD,qBAAqB,GAAG,MAAM,IAAI,CAACrF,kBAAkB,EAAE7H,sBAAsB,CACjFnT,UAAU,EACVsS,MAAM,EACNC,gBAAgB,EAChBC,eAAe,CAChB,CAAA;QAED,IAAIjB,YAAY,GAAG,CAAA,CAAE,CAAA;QACrB,IAAI+O,mBAAmB,GAAG,CAAA,CAAE,CAAA;QAC5B,IAAIlN,gBAAgB,GAAG,IAAI,CAAA;QAC3B,IAAIiN,qBAAqB,EAAE;YACzB9O,YAAY,GAAG8O,qBAAqB,CAAC5N,QAAQ,CAAA;YAC7C6N,mBAAmB,GAAGD,qBAAqB,CAACpN,QAAQ,CAAA;YACpDG,gBAAgB,GAAGiN,qBAAqB,CAACjN,gBAAgB,CAAA;QAC1D,CAAA;QAED,IAAIA,gBAAgB,IAAI,CAAC6J,mBAAmB,EAAE;YAC5C,MAAMsD,sBAAsB,GAAG,MAAM,KAAK,CAACC,mCAAmC,CAC5ExgB,UAAU,EACVsS,MAAM,EACNC,gBAAgB,EAChBC,eAAe,EACf8J,YAAY,CACb,CAAA;YACD/K,YAAY,GAAG;gBACb,GAAGA,YAAY;gBACf,GAAIgP,sBAAsB,CAAC7J,KAAK,IAAI,CAAA,CAAE,CAAA;aACvC,CAAA;YACD4J,mBAAmB,GAAG;gBACpB,GAAGA,mBAAmB;gBACtB,GAAIC,sBAAsB,CAACtN,QAAQ,IAAI,CAAA,CAAE,CAAA;aAC1C,CAAA;QACF,CAAA;QAED,OAAO;YAAE1B,YAAY;YAAE+O,mBAAAA;SAAqB,CAAA;IAC9C,CAAA;IAEAG,aAAaA,CAAC,EAAEC,SAAS,EAAEC,QAAQ,EAAE1hB,UAAU,EAAEe,UAAU,EAAEsc,YAAAA,EAAoC,EAAA;QAC/F,KAAK,CAACsE,sBAAsB,CAACF,SAAS,EAAEC,QAAQ,EAAE1hB,UAAU,EAAE;YAAEqd,YAAAA;SAAc,EAAEtc,UAAU,CAAC,CAAA;IAC7F,CAAA;IAEA;;;GAGG,GACH,MAAM6gB,kBAAkBA,GAAA;QACtB,MAAM,IAAI,CAAC7F,kBAAkB,EAAEhJ,gBAAgB,CAAC,IAAI,CAAC,CAAA;IACvD,CAAA;IAEA,MAAM8O,SAASA,CAACC,iBAA0B,EAAA;QACxC,IAAI,CAAC/F,kBAAkB,EAAE1D,UAAU,EAAE,CAAA;QACrC,OAAO,KAAK,CAACwJ,SAAS,CAACC,iBAAiB,CAAC,CAAA;IAC3C,CAAA;IAEQtC,gCAAgCA,CACtCze,UAAkB,EAClBsS,MAA+B,EAC/BC,gBAAyC,EACzCC,eAAwD,EAAA;QAExD,MAAMkM,mBAAmB,GAAG;YAAEsC,WAAW,EAAEhhB,UAAU;YAAE,GAAIuS,gBAAgB,IAAI,CAAA,CAAE,CAAA;SAAG,CAAA;QAEpF,MAAMoM,kBAAkB,GAA2C,CAAA,CAAE,CAAA;QACrE,IAAIrM,MAAM,EAAE;YACV,KAAK,MAAMoB,SAAS,IAAI3S,MAAM,CAACqD,IAAI,CAACkO,MAAM,CAAC,CAAE;gBAC3CqM,kBAAkB,CAACjL,SAAS,CAAC,GAAG;oBAC9BuN,UAAU,EAAE3O,MAAM,CAACoB,SAAS,CAAC;oBAC7B,GAAIlB,eAAe,EAAA,CAAGkB,SAAS,CAAC,IAAI,CAAA,CAAE,CAAA;iBACvC,CAAA;YACF,CAAA;QACF,CAAA;QAED,OAAO;YAAEgL,mBAAmB;YAAEC,kBAAAA;SAAoB,CAAA;IACpD,CAAA;IAEA1Y,gBAAgBA,CAAChF,KAAc,EAAEjB,UAAmB,EAAEmG,oBAAmD,EAAA;QACvG,MAAMnC,kBAAkB,GAAG,IAAItB,KAAK,CAAC,4BAA4B,CAAC,CAAA;QAClEsD,aAAa,CAACC,gBAAgB,CAAC,IAAI,EAAEhF,KAAK,EAAE;YAAE+C,kBAAAA;SAAoB,EAAEhE,UAAU,EAAEmG,oBAAoB,CAAC,CAAA;IACvG,CAAA;AACD;ACzoBD,kGAAA;AACA,iCAAA;AAIA,uFAAA;AACA,oCAAA;AACA,EAAA;AACA,oFAAA;AACA,6BAAA;AAEA,0FAAA;AACA,EAAA;AACA,uFAAA;AACA,uFAAA;AACA,qFAAA;AACA,uFAAA;AACA,sFAAA;AACA,cAAA;AACA,EAAA;AACA,wFAAA;AACA,2CAAA;AACA,EAAA;AACA,sFAAA;AACA,gFAAA;AACA,oFAAA;AACA,uFAAA;AACA,uFAAA;AACA,gDAAA;AAEA,MAAM+a,oBAAoB,GAAG,iBAAiB,CAAA;AAC9C,MAAMC,sBAAsB,GAAG,EAAE,CAAA;AAEjC,MAAMC,gBAAgB,GAAG,GAAG,CAAA;AAE5B,2BAAA,GACA,SAASC,IAAIA,CAACC,SAAuB,EAAA;IACnC,MAAMC,cAAc,GAAG,cAAc,CAAA;IACrC,MAAMC,UAAU,GAAG,+DAA+D,CAAA;IAElF,QAAQpV,IAAY,IAAI;QACtB,MAAMqV,SAAS,GAAGrV,IAAI,CAACkM,KAAK,CAACkJ,UAAU,CAAC,CAAA;QAExC,IAAIC,SAAS,EAAE;YACb,IAAIC,MAA0B,CAAA;YAC9B,IAAIxT,MAA0B,CAAA;YAC9B,IAAIyT,YAAgC,CAAA;YACpC,IAAIC,QAA4B,CAAA;YAChC,IAAIC,UAA8B,CAAA;YAElC,IAAIJ,SAAS,CAAC,CAAC,CAAC,EAAE;gBAChBE,YAAY,GAAGF,SAAS,CAAC,CAAC,CAAC,CAAA;gBAE3B,IAAIK,WAAW,GAAGH,YAAY,CAAClZ,WAAW,CAAC,GAAG,CAAC,CAAA;gBAC/C,IAAIkZ,YAAY,CAACG,WAAW,GAAG,CAAC,CAAC,KAAK,GAAG,EAAE;oBACzCA,WAAW,EAAE,CAAA;gBACd,CAAA;gBAED,IAAIA,WAAW,GAAG,CAAC,EAAE;oBACnBJ,MAAM,GAAGC,YAAY,CAAC3c,KAAK,CAAC,CAAC,EAAE8c,WAAW,CAAC,CAAA;oBAC3C5T,MAAM,GAAGyT,YAAY,CAAC3c,KAAK,CAAC8c,WAAW,GAAG,CAAC,CAAC,CAAA;oBAC5C,MAAMC,SAAS,GAAGL,MAAM,CAACM,OAAO,CAAC,SAAS,CAAC,CAAA;oBAC3C,IAAID,SAAS,GAAG,CAAC,EAAE;wBACjBJ,YAAY,GAAGA,YAAY,CAAC3c,KAAK,CAAC+c,SAAS,GAAG,CAAC,CAAC,CAAA;wBAChDL,MAAM,GAAGA,MAAM,CAAC1c,KAAK,CAAC,CAAC,EAAE+c,SAAS,CAAC,CAAA;oBACpC,CAAA;gBACF,CAAA;gBACDH,QAAQ,GAAG7jB,SAAS,CAAA;YACrB,CAAA;YAED,IAAImQ,MAAM,EAAE;gBACV0T,QAAQ,GAAGF,MAAM,CAAA;gBACjBG,UAAU,GAAG3T,MAAM,CAAA;YACpB,CAAA;YAED,IAAIA,MAAM,KAAK,aAAa,EAAE;gBAC5B2T,UAAU,GAAG9jB,SAAS,CAAA;gBACtB4jB,YAAY,GAAG5jB,SAAS,CAAA;YACzB,CAAA;YAED,IAAI4jB,YAAY,KAAK5jB,SAAS,EAAE;gBAC9B8jB,UAAU,GAAGA,UAAU,IAAIT,gBAAgB,CAAA;gBAC3CO,YAAY,GAAGC,QAAQ,GAAG,CAAA,EAAGA,QAAQ,CAAIC,CAAAA,EAAAA,UAAU,CAAE,CAAA,GAAGA,UAAU,CAAA;YACnE,CAAA;YAED,IAAI9Z,QAAQ,GAAG0Z,SAAS,CAAC,CAAC,CAAC,EAAE9Y,UAAU,CAAC,SAAS,CAAC,GAAG8Y,SAAS,CAAC,CAAC,CAAC,CAACzc,KAAK,CAAC,CAAC,CAAC,GAAGyc,SAAS,CAAC,CAAC,CAAC,CAAA;YACzF,MAAMQ,QAAQ,GAAGR,SAAS,CAAC,CAAC,CAAC,KAAK,QAAQ,CAAA;YAE1C,oFAAA;YACA,IAAI1Z,QAAQ,EAAEuQ,KAAK,CAAC,UAAU,CAAC,EAAE;gBAC/BvQ,QAAQ,GAAGA,QAAQ,CAAC/C,KAAK,CAAC,CAAC,CAAC,CAAA;YAC7B,CAAA;YAED,IAAI,CAAC+C,QAAQ,IAAI0Z,SAAS,CAAC,CAAC,CAAC,IAAI,CAACQ,QAAQ,EAAE;gBAC1Cla,QAAQ,GAAG0Z,SAAS,CAAC,CAAC,CAAC,CAAA;YACxB,CAAA;YAED,OAAO;gBACL1Z,QAAQ,EAAEA,QAAQ,GAAGma,SAAS,CAACna,QAAQ,CAAC,GAAGhK,SAAS;gBACpDokB,MAAM,EAAEb,SAAS,GAAGA,SAAS,CAACvZ,QAAQ,CAAC,GAAGhK,SAAS;gBACnDqkB,QAAQ,EAAET,YAAY;gBACtB3X,MAAM,EAAEqY,oBAAoB,CAACZ,SAAS,CAAC,CAAC,CAAC,CAAC;gBAC1CzU,KAAK,EAAEqV,oBAAoB,CAACZ,SAAS,CAAC,CAAC,CAAC,CAAC;gBACzCa,MAAM,EAAEC,eAAe,CAACxa,QAAQ,IAAI,EAAE,EAAEka,QAAQ,CAAC;gBACjDjjB,QAAQ,EAAE,iBAAA;aACX,CAAA;QACF,CAAA;QAED,IAAIoN,IAAI,CAACkM,KAAK,CAACiJ,cAAc,CAAC,EAAE;YAC9B,OAAO;gBACLxZ,QAAQ,EAAEqE,IAAI;gBACdpN,QAAQ,EAAE,iBAAA;aACX,CAAA;QACF,CAAA;QAED,OAAOjB,SAAS,CAAA;KACjB,CAAA;AACH,CAAA;AAEA;;CAEG,GACH,SAASwkB,eAAeA,CAACxa,QAAgB,EAAEka,WAAoB,KAAK,EAAA;IAClE,MAAMO,UAAU,GACdP,QAAQ,IACPla,QAAQ,IACP,mDAAA;IACA,CAACA,QAAQ,CAACY,UAAU,CAAC,GAAG,CAAC,IACzB,qDAAA;IACA,CAACZ,QAAQ,CAACuQ,KAAK,CAAC,SAAS,CAAC,IAC1B,uDAAA;IACA,CAACvQ,QAAQ,CAACY,UAAU,CAAC,GAAG,CAAC,IACzB,6IAAA;IACA,CAACZ,QAAQ,CAACuQ,KAAK,CAAC,kCAAkC,CAAE,CAAA,CAAA,mDAAA;IAExD,qFAAA;IACA,yEAAA;IACA,yDAAA;IAEA,OAAO,CAACkK,UAAU,IAAIza,QAAQ,KAAKhK,SAAS,IAAI,CAACgK,QAAQ,CAACtK,QAAQ,CAAC,eAAe,CAAC,CAAA;AACrF,CAAA;AAEA,SAAS4kB,oBAAoBA,CAACpf,KAAyB,EAAA;IACrD,OAAO0U,QAAQ,CAAC1U,KAAK,IAAI,EAAE,EAAE,EAAE,CAAC,IAAIlF,SAAS,CAAA;AAC/C,CAAA;AAEA,SAAS0kB,mBAAmBA,CAACnB,SAAuB,EAAA;IAClD,OAAO;QAAC,EAAE;QAAED,IAAI,CAACC,SAAS,CAAC;KAAC,CAAA;AAC9B,CAAA;AAEM,SAAUoB,iBAAiBA,CAACpB,SAAuB,EAAA;IACvD,MAAMqB,OAAO,GAAG;QAACF,mBAAmB,CAACnB,SAAS,CAAC;KAAC,CAAA;IAChD,MAAMsB,aAAa,GAAGD,OAAO,CAAChe,IAAI,CAAC,CAAC6F,CAAC,EAAEC,CAAC,GAAKD,CAAC,CAAC,CAAC,CAAC,GAAGC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC9L,GAAG,EAAEkkB,CAAC,GAAKA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAA;IAE1E,OAAO,CAACzd,KAAa,EAAE0d,cAAyB,GAAA,CAAC,KAAkB;QACjE,MAAMhkB,MAAM,GAAiB,EAAE,CAAA;QAC/B,MAAMmO,KAAK,GAAG7H,KAAK,CAAC2d,KAAK,CAAC,IAAI,CAAC,CAAA;QAE/B,IAAK,IAAIhZ,CAAC,GAAG+Y,cAAc,EAAE/Y,CAAC,GAAGkD,KAAK,CAACxL,MAAM,EAAEsI,CAAC,EAAE,CAAE;YAClD,MAAMqC,IAAI,GAAGa,KAAK,CAAClD,CAAC,CAAW,CAAA;YAC/B,iEAAA;YACA,IAAIqC,IAAI,CAAC3K,MAAM,GAAG,IAAI,EAAE;gBACtB,SAAA;YACD,CAAA;YAED,6DAAA;YACA,qCAAA;YACA,MAAMuhB,WAAW,GAAG9B,oBAAoB,CAAC+B,IAAI,CAAC7W,IAAI,CAAC,GAAGA,IAAI,CAAC1D,OAAO,CAACwY,oBAAoB,EAAE,IAAI,CAAC,GAAG9U,IAAI,CAAA;YAErG,6DAAA;YACA,oBAAA;YACA,IAAI4W,WAAW,CAAC1K,KAAK,CAAC,YAAY,CAAC,EAAE;gBACnC,SAAA;YACD,CAAA;YAED,KAAK,MAAM4K,MAAM,IAAIN,aAAa,CAAE;gBAClC,MAAM7jB,KAAK,GAAGmkB,MAAM,CAACF,WAAW,CAAC,CAAA;gBAEjC,IAAIjkB,KAAK,EAAE;oBACTD,MAAM,CAACsL,IAAI,CAACrL,KAAK,CAAC,CAAA;oBAClB,MAAA;gBACD,CAAA;YACF,CAAA;YAED,IAAID,MAAM,CAAC2C,MAAM,IAAI0f,sBAAsB,EAAE;gBAC3C,MAAA;YACD,CAAA;QACF,CAAA;QAED,OAAOgC,qBAAqB,CAACrkB,MAAM,CAAC,CAAA;KACrC,CAAA;AACH,CAAA;AAEA,SAASqkB,qBAAqBA,CAAC/d,KAAgC,EAAA;IAC7D,IAAI,CAACA,KAAK,CAAC3D,MAAM,EAAE;QACjB,OAAO,EAAE,CAAA;IACV,CAAA;IAED,MAAM2hB,UAAU,GAAGlT,KAAK,CAACC,IAAI,CAAC/K,KAAK,CAAC,CAAA;IAEpCge,UAAU,CAACC,OAAO,EAAE,CAAA;IAEpB,OAAOD,UAAU,CAACpe,KAAK,CAAC,CAAC,EAAEmc,sBAAsB,CAAC,CAACxiB,GAAG,EAAEI,KAAK,GAAA,CAAM;YACjE,GAAGA,KAAK;YACRgJ,QAAQ,EAAEhJ,KAAK,CAACgJ,QAAQ,IAAIub,iBAAiB,CAACF,UAAU,CAAC,CAACrb,QAAQ;YAClEqa,QAAQ,EAAErjB,KAAK,CAACqjB,QAAQ,IAAIhB,gBAAAA;QAC7B,CAAA,CAAC,CAAC,CAAA;AACL,CAAA;AAEA,SAASkC,iBAAiBA,CAACC,GAAiB,EAAA;IAC1C,OAAOA,GAAG,CAACA,GAAG,CAAC9hB,MAAM,GAAG,CAAC,CAAC,IAAI,CAAA,CAAE,CAAA;AAClC;AC1MAuE,aAAa,CAACjD,WAAW,GAAG2f,iBAAiB,CAACpb,2BAA2B,EAAE,CAAC,CAAA;AAC5EtB,aAAa,CAAChD,cAAc,GAAG;IAAC6G,gBAAgB;CAAC,CAAA;AAE3C,MAAO2Z,OAAQ,SAAQ3I,oBAAoB,CAAA;IAC/CiB,YAAYA,GAAA;QACV,OAAO,cAAc,CAAA;IACvB,CAAA;AACD", "ignoreList": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24], "debugId": null}}]}