{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "file": "embla-carousel-reactive-utils.esm.js", "sources": ["file:///C:/Users/<USER>/Documents/Cubentweb/cubent/node_modules/.pnpm/embla-carousel-reactive-utils%408.6.0_embla-carousel%408.6.0/node_modules/embla-carousel-reactive-utils/src/components/utils.ts"], "sourcesContent": ["import { EmblaPluginType } from 'embla-carousel'\n\nexport function isObject(subject: unknown): subject is Record<string, unknown> {\n  return Object.prototype.toString.call(subject) === '[object Object]'\n}\n\nexport function isRecord(\n  subject: unknown\n): subject is Record<string | number, unknown> {\n  return isObject(subject) || Array.isArray(subject)\n}\n\nexport function canUseDOM(): boolean {\n  return !!(\n    typeof window !== 'undefined' &&\n    window.document &&\n    window.document.createElement\n  )\n}\n\nexport function areOptionsEqual(\n  optionsA: Record<string, unknown>,\n  optionsB: Record<string, unknown>\n): boolean {\n  const optionsAKeys = Object.keys(optionsA)\n  const optionsBKeys = Object.keys(optionsB)\n\n  if (optionsAKeys.length !== optionsBKeys.length) return false\n\n  const breakpointsA = JSON.stringify(Object.keys(optionsA.breakpoints || {}))\n  const breakpointsB = JSON.stringify(Object.keys(optionsB.breakpoints || {}))\n\n  if (breakpointsA !== breakpointsB) return false\n\n  return optionsAKeys.every((key) => {\n    const valueA = optionsA[key]\n    const valueB = optionsB[key]\n    if (typeof valueA === 'function') return `${valueA}` === `${valueB}`\n    if (!isRecord(valueA) || !isRecord(valueB)) return valueA === valueB\n    return areOptionsEqual(valueA, valueB)\n  })\n}\n\nexport function sortAndMapPluginToOptions(\n  plugins: EmblaPluginType[]\n): EmblaPluginType['options'][] {\n  return plugins\n    .concat()\n    .sort((a, b) => (a.name > b.name ? 1 : -1))\n    .map((plugin) => plugin.options)\n}\n\nexport function arePluginsEqual(\n  pluginsA: EmblaPluginType[],\n  pluginsB: EmblaPluginType[]\n): boolean {\n  if (pluginsA.length !== pluginsB.length) return false\n\n  const optionsA = sortAndMapPluginToOptions(pluginsA)\n  const optionsB = sortAndMapPluginToOptions(pluginsB)\n\n  return optionsA.every((optionA, index) => {\n    const optionB = optionsB[index]\n    return areOptionsEqual(optionA, optionB)\n  })\n}\n"], "names": ["isObject", "subject", "Object", "prototype", "toString", "call", "isRecord", "Array", "isArray", "canUseDOM", "window", "document", "createElement", "areOptionsEqual", "optionsA", "optionsB", "optionsAKeys", "keys", "optionsBKeys", "length", "breakpointsA", "JSON", "stringify", "breakpoints", "breakpointsB", "every", "key", "valueA", "valueB", "sortAndMapPluginToOptions", "plugins", "concat", "sort", "a", "b", "name", "map", "plugin", "options", "arePluginsEqual", "pluginsA", "pluginsB", "optionA", "index", "optionB"], "mappings": ";;;;;;AAEM,SAAUA,QAAQA,CAACC,OAAgB,EAAA;IACvC,OAAOC,MAAM,CAACC,SAAS,CAACC,QAAQ,CAACC,IAAI,CAACJ,OAAO,CAAC,KAAK,iBAAiB;AACtE;AAEM,SAAUK,QAAQA,CACtBL,OAAgB,EAAA;IAEhB,OAAOD,QAAQ,CAACC,OAAO,CAAC,IAAIM,KAAK,CAACC,OAAO,CAACP,OAAO,CAAC;AACpD;SAEgBQ,SAASA,GAAA;IACvB,OAAO,CAAC,CAAA,CACN,OAAOC,MAAM,KAAK,WAAW,IAC7BA,MAAM,CAACC,QAAQ,IACfD,MAAM,CAACC,QAAQ,CAACC,aAAa,CAC9B;AACH;AAEgB,SAAAC,eAAeA,CAC7BC,QAAiC,EACjCC,QAAiC,EAAA;IAEjC,MAAMC,YAAY,GAAGd,MAAM,CAACe,IAAI,CAACH,QAAQ,CAAC;IAC1C,MAAMI,YAAY,GAAGhB,MAAM,CAACe,IAAI,CAACF,QAAQ,CAAC;IAE1C,IAAIC,YAAY,CAACG,MAAM,KAAKD,YAAY,CAACC,MAAM,EAAE,OAAO,KAAK;IAE7D,MAAMC,YAAY,GAAGC,IAAI,CAACC,SAAS,CAACpB,MAAM,CAACe,IAAI,CAACH,QAAQ,CAACS,WAAW,IAAI,CAAA,CAAE,CAAC,CAAC;IAC5E,MAAMC,YAAY,GAAGH,IAAI,CAACC,SAAS,CAACpB,MAAM,CAACe,IAAI,CAACF,QAAQ,CAACQ,WAAW,IAAI,CAAA,CAAE,CAAC,CAAC;IAE5E,IAAIH,YAAY,KAAKI,YAAY,EAAE,OAAO,KAAK;IAE/C,OAAOR,YAAY,CAACS,KAAK,EAAEC,GAAG,IAAI;QAChC,MAAMC,MAAM,GAAGb,QAAQ,CAACY,GAAG,CAAC;QAC5B,MAAME,MAAM,GAAGb,QAAQ,CAACW,GAAG,CAAC;QAC5B,IAAI,OAAOC,MAAM,KAAK,UAAU,EAAE,OAAO,CAAGA,EAAAA,MAAM,CAAE,CAAA,KAAK,CAAGC,EAAAA,MAAM,CAAE,CAAA;QACpE,IAAI,CAACtB,QAAQ,CAACqB,MAAM,CAAC,IAAI,CAACrB,QAAQ,CAACsB,MAAM,CAAC,EAAE,OAAOD,MAAM,KAAKC,MAAM;QACpE,OAAOf,eAAe,CAACc,MAAM,EAAEC,MAAM,CAAC;IACxC,CAAC,CAAC;AACJ;AAEM,SAAUC,yBAAyBA,CACvCC,OAA0B,EAAA;IAE1B,OAAOA,OAAO,CACXC,MAAM,EAAE,CACRC,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,GAAMD,CAAC,CAACE,IAAI,GAAGD,CAAC,CAACC,IAAI,GAAG,CAAC,GAAG,CAAC,CAAE,CAAC,CAC1CC,GAAG,EAAEC,MAAM,GAAKA,MAAM,CAACC,OAAO,CAAC;AACpC;AAEgB,SAAAC,eAAeA,CAC7BC,QAA2B,EAC3BC,QAA2B,EAAA;IAE3B,IAAID,QAAQ,CAACrB,MAAM,KAAKsB,QAAQ,CAACtB,MAAM,EAAE,OAAO,KAAK;IAErD,MAAML,QAAQ,GAAGe,yBAAyB,CAACW,QAAQ,CAAC;IACpD,MAAMzB,QAAQ,GAAGc,yBAAyB,CAACY,QAAQ,CAAC;IAEpD,OAAO3B,QAAQ,CAACW,KAAK,CAAC,CAACiB,OAAO,EAAEC,KAAK,KAAI;QACvC,MAAMC,OAAO,GAAG7B,QAAQ,CAAC4B,KAAK,CAAC;QAC/B,OAAO9B,eAAe,CAAC6B,OAAO,EAAEE,OAAO,CAAC;IAC1C,CAAC,CAAC;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 57, "column": 0}, "map": {"version": 3, "file": "embla-carousel.esm.js", "sources": ["file:///C:/Users/<USER>/Documents/Cubentweb/cubent/node_modules/.pnpm/embla-carousel%408.6.0/node_modules/embla-carousel/src/components/utils.ts", "file:///C:/Users/<USER>/Documents/Cubentweb/cubent/node_modules/.pnpm/embla-carousel%408.6.0/node_modules/embla-carousel/src/components/Alignment.ts", "file:///C:/Users/<USER>/Documents/Cubentweb/cubent/node_modules/.pnpm/embla-carousel%408.6.0/node_modules/embla-carousel/src/components/EventStore.ts", "file:///C:/Users/<USER>/Documents/Cubentweb/cubent/node_modules/.pnpm/embla-carousel%408.6.0/node_modules/embla-carousel/src/components/Animations.ts", "file:///C:/Users/<USER>/Documents/Cubentweb/cubent/node_modules/.pnpm/embla-carousel%408.6.0/node_modules/embla-carousel/src/components/Axis.ts", "file:///C:/Users/<USER>/Documents/Cubentweb/cubent/node_modules/.pnpm/embla-carousel%408.6.0/node_modules/embla-carousel/src/components/Limit.ts", "file:///C:/Users/<USER>/Documents/Cubentweb/cubent/node_modules/.pnpm/embla-carousel%408.6.0/node_modules/embla-carousel/src/components/Counter.ts", "file:///C:/Users/<USER>/Documents/Cubentweb/cubent/node_modules/.pnpm/embla-carousel%408.6.0/node_modules/embla-carousel/src/components/DragHandler.ts", "file:///C:/Users/<USER>/Documents/Cubentweb/cubent/node_modules/.pnpm/embla-carousel%408.6.0/node_modules/embla-carousel/src/components/DragTracker.ts", "file:///C:/Users/<USER>/Documents/Cubentweb/cubent/node_modules/.pnpm/embla-carousel%408.6.0/node_modules/embla-carousel/src/components/NodeRects.ts", "file:///C:/Users/<USER>/Documents/Cubentweb/cubent/node_modules/.pnpm/embla-carousel%408.6.0/node_modules/embla-carousel/src/components/PercentOfView.ts", "file:///C:/Users/<USER>/Documents/Cubentweb/cubent/node_modules/.pnpm/embla-carousel%408.6.0/node_modules/embla-carousel/src/components/ResizeHandler.ts", "file:///C:/Users/<USER>/Documents/Cubentweb/cubent/node_modules/.pnpm/embla-carousel%408.6.0/node_modules/embla-carousel/src/components/ScrollBody.ts", "file:///C:/Users/<USER>/Documents/Cubentweb/cubent/node_modules/.pnpm/embla-carousel%408.6.0/node_modules/embla-carousel/src/components/ScrollBounds.ts", "file:///C:/Users/<USER>/Documents/Cubentweb/cubent/node_modules/.pnpm/embla-carousel%408.6.0/node_modules/embla-carousel/src/components/ScrollContain.ts", "file:///C:/Users/<USER>/Documents/Cubentweb/cubent/node_modules/.pnpm/embla-carousel%408.6.0/node_modules/embla-carousel/src/components/ScrollLimit.ts", "file:///C:/Users/<USER>/Documents/Cubentweb/cubent/node_modules/.pnpm/embla-carousel%408.6.0/node_modules/embla-carousel/src/components/ScrollLooper.ts", "file:///C:/Users/<USER>/Documents/Cubentweb/cubent/node_modules/.pnpm/embla-carousel%408.6.0/node_modules/embla-carousel/src/components/ScrollProgress.ts", "file:///C:/Users/<USER>/Documents/Cubentweb/cubent/node_modules/.pnpm/embla-carousel%408.6.0/node_modules/embla-carousel/src/components/ScrollSnaps.ts", "file:///C:/Users/<USER>/Documents/Cubentweb/cubent/node_modules/.pnpm/embla-carousel%408.6.0/node_modules/embla-carousel/src/components/SlideRegistry.ts", "file:///C:/Users/<USER>/Documents/Cubentweb/cubent/node_modules/.pnpm/embla-carousel%408.6.0/node_modules/embla-carousel/src/components/ScrollTarget.ts", "file:///C:/Users/<USER>/Documents/Cubentweb/cubent/node_modules/.pnpm/embla-carousel%408.6.0/node_modules/embla-carousel/src/components/ScrollTo.ts", "file:///C:/Users/<USER>/Documents/Cubentweb/cubent/node_modules/.pnpm/embla-carousel%408.6.0/node_modules/embla-carousel/src/components/SlideFocus.ts", "file:///C:/Users/<USER>/Documents/Cubentweb/cubent/node_modules/.pnpm/embla-carousel%408.6.0/node_modules/embla-carousel/src/components/Vector1d.ts", "file:///C:/Users/<USER>/Documents/Cubentweb/cubent/node_modules/.pnpm/embla-carousel%408.6.0/node_modules/embla-carousel/src/components/Translate.ts", "file:///C:/Users/<USER>/Documents/Cubentweb/cubent/node_modules/.pnpm/embla-carousel%408.6.0/node_modules/embla-carousel/src/components/SlideLooper.ts", "file:///C:/Users/<USER>/Documents/Cubentweb/cubent/node_modules/.pnpm/embla-carousel%408.6.0/node_modules/embla-carousel/src/components/SlidesHandler.ts", "file:///C:/Users/<USER>/Documents/Cubentweb/cubent/node_modules/.pnpm/embla-carousel%408.6.0/node_modules/embla-carousel/src/components/SlidesInView.ts", "file:///C:/Users/<USER>/Documents/Cubentweb/cubent/node_modules/.pnpm/embla-carousel%408.6.0/node_modules/embla-carousel/src/components/SlideSizes.ts", "file:///C:/Users/<USER>/Documents/Cubentweb/cubent/node_modules/.pnpm/embla-carousel%408.6.0/node_modules/embla-carousel/src/components/SlidesToScroll.ts", "file:///C:/Users/<USER>/Documents/Cubentweb/cubent/node_modules/.pnpm/embla-carousel%408.6.0/node_modules/embla-carousel/src/components/Engine.ts", "file:///C:/Users/<USER>/Documents/Cubentweb/cubent/node_modules/.pnpm/embla-carousel%408.6.0/node_modules/embla-carousel/src/components/EventHandler.ts", "file:///C:/Users/<USER>/Documents/Cubentweb/cubent/node_modules/.pnpm/embla-carousel%408.6.0/node_modules/embla-carousel/src/components/Options.ts", "file:///C:/Users/<USER>/Documents/Cubentweb/cubent/node_modules/.pnpm/embla-carousel%408.6.0/node_modules/embla-carousel/src/components/OptionsHandler.ts", "file:///C:/Users/<USER>/Documents/Cubentweb/cubent/node_modules/.pnpm/embla-carousel%408.6.0/node_modules/embla-carousel/src/components/PluginsHandler.ts", "file:///C:/Users/<USER>/Documents/Cubentweb/cubent/node_modules/.pnpm/embla-carousel%408.6.0/node_modules/embla-carousel/src/components/EmblaCarousel.ts"], "sourcesContent": ["import { PointerEventType } from './DragTracker'\n\nexport type WindowType = Window & typeof globalThis\n\nexport function isNumber(subject: unknown): subject is number {\n  return typeof subject === 'number'\n}\n\nexport function isString(subject: unknown): subject is string {\n  return typeof subject === 'string'\n}\n\nexport function isBoolean(subject: unknown): subject is boolean {\n  return typeof subject === 'boolean'\n}\n\nexport function isObject(subject: unknown): subject is Record<string, unknown> {\n  return Object.prototype.toString.call(subject) === '[object Object]'\n}\n\nexport function mathAbs(n: number): number {\n  return Math.abs(n)\n}\n\nexport function mathSign(n: number): number {\n  return Math.sign(n)\n}\n\nexport function deltaAbs(valueB: number, valueA: number): number {\n  return mathAbs(valueB - valueA)\n}\n\nexport function factorAbs(valueB: number, valueA: number): number {\n  if (valueB === 0 || valueA === 0) return 0\n  if (mathAbs(valueB) <= mathAbs(valueA)) return 0\n  const diff = deltaAbs(mathAbs(valueB), mathAbs(valueA))\n  return mathAbs(diff / valueB)\n}\n\nexport function roundToTwoDecimals(num: number): number {\n  return Math.round(num * 100) / 100\n}\n\nexport function arrayKeys<Type>(array: Type[]): number[] {\n  return objectKeys(array).map(Number)\n}\n\nexport function arrayLast<Type>(array: Type[]): Type {\n  return array[arrayLastIndex(array)]\n}\n\nexport function arrayLastIndex<Type>(array: Type[]): number {\n  return Math.max(0, array.length - 1)\n}\n\nexport function arrayIsLastIndex<Type>(array: Type[], index: number): boolean {\n  return index === arrayLastIndex(array)\n}\n\nexport function arrayFromNumber(n: number, startAt: number = 0): number[] {\n  return Array.from(Array(n), (_, i) => startAt + i)\n}\n\nexport function objectKeys<Type extends object>(object: Type): string[] {\n  return Object.keys(object)\n}\n\nexport function objectsMergeDeep(\n  objectA: Record<string, unknown>,\n  objectB: Record<string, unknown>\n): Record<string, unknown> {\n  return [objectA, objectB].reduce((mergedObjects, currentObject) => {\n    objectKeys(currentObject).forEach((key) => {\n      const valueA = mergedObjects[key]\n      const valueB = currentObject[key]\n      const areObjects = isObject(valueA) && isObject(valueB)\n\n      mergedObjects[key] = areObjects\n        ? objectsMergeDeep(valueA, valueB)\n        : valueB\n    })\n    return mergedObjects\n  }, {})\n}\n\nexport function isMouseEvent(\n  evt: PointerEventType,\n  ownerWindow: WindowType\n): evt is MouseEvent {\n  return (\n    typeof ownerWindow.MouseEvent !== 'undefined' &&\n    evt instanceof ownerWindow.MouseEvent\n  )\n}\n", "import { isString } from './utils'\n\nexport type AlignmentOptionType =\n  | 'start'\n  | 'center'\n  | 'end'\n  | ((viewSize: number, snapSize: number, index: number) => number)\n\nexport type AlignmentType = {\n  measure: (n: number, index: number) => number\n}\n\nexport function Alignment(\n  align: AlignmentOptionType,\n  viewSize: number\n): AlignmentType {\n  const predefined = { start, center, end }\n\n  function start(): number {\n    return 0\n  }\n\n  function center(n: number): number {\n    return end(n) / 2\n  }\n\n  function end(n: number): number {\n    return viewSize - n\n  }\n\n  function measure(n: number, index: number): number {\n    if (isString(align)) return predefined[align](n)\n    return align(viewSize, n, index)\n  }\n\n  const self: AlignmentType = {\n    measure\n  }\n  return self\n}\n", "type EventNameType = keyof DocumentEventMap | keyof WindowEventMap\ntype EventHandlerType = (evt: any) => void\ntype EventOptionsType = boolean | AddEventListenerOptions | undefined\ntype EventRemoverType = () => void\n\nexport type EventStoreType = {\n  add: (\n    node: EventTarget,\n    type: EventNameType,\n    handler: EventHandlerType,\n    options?: EventOptionsType\n  ) => EventStoreType\n  clear: () => void\n}\n\nexport function EventStore(): EventStoreType {\n  let listeners: EventRemoverType[] = []\n\n  function add(\n    node: EventTarget,\n    type: EventNameType,\n    handler: EventHandlerType,\n    options: EventOptionsType = { passive: true }\n  ): EventStoreType {\n    let removeListener: EventRemoverType\n\n    if ('addEventListener' in node) {\n      node.addEventListener(type, handler, options)\n      removeListener = () => node.removeEventListener(type, handler, options)\n    } else {\n      const legacyMediaQueryList = <MediaQueryList>node\n      legacyMediaQueryList.addListener(handler)\n      removeListener = () => legacyMediaQueryList.removeListener(handler)\n    }\n\n    listeners.push(removeListener)\n    return self\n  }\n\n  function clear(): void {\n    listeners = listeners.filter((remove) => remove())\n  }\n\n  const self: EventStoreType = {\n    add,\n    clear\n  }\n  return self\n}\n", "import { EngineType } from './Engine'\nimport { EventStore } from './EventStore'\nimport { WindowType } from './utils'\n\nexport type AnimationsUpdateType = (engine: EngineType) => void\nexport type AnimationsRenderType = (engine: EngineType, alpha: number) => void\n\nexport type AnimationsType = {\n  init: () => void\n  destroy: () => void\n  start: () => void\n  stop: () => void\n  update: () => void\n  render: (alpha: number) => void\n}\n\nexport function Animations(\n  ownerDocument: Document,\n  ownerWindow: WindowType,\n  update: () => void,\n  render: (alpha: number) => void\n): AnimationsType {\n  const documentVisibleHandler = EventStore()\n  const fixedTimeStep = 1000 / 60\n\n  let lastTimeStamp: number | null = null\n  let accumulatedTime = 0\n  let animationId = 0\n\n  function init(): void {\n    documentVisibleHandler.add(ownerDocument, 'visibilitychange', () => {\n      if (ownerDocument.hidden) reset()\n    })\n  }\n\n  function destroy(): void {\n    stop()\n    documentVisibleHandler.clear()\n  }\n\n  function animate(timeStamp: DOMHighResTimeStamp): void {\n    if (!animationId) return\n    if (!lastTimeStamp) {\n      lastTimeStamp = timeStamp\n      update()\n      update()\n    }\n\n    const timeElapsed = timeStamp - lastTimeStamp\n    lastTimeStamp = timeStamp\n    accumulatedTime += timeElapsed\n\n    while (accumulatedTime >= fixedTimeStep) {\n      update()\n      accumulatedTime -= fixedTimeStep\n    }\n\n    const alpha = accumulatedTime / fixedTimeStep\n    render(alpha)\n\n    if (animationId) {\n      animationId = ownerWindow.requestAnimationFrame(animate)\n    }\n  }\n\n  function start(): void {\n    if (animationId) return\n    animationId = ownerWindow.requestAnimationFrame(animate)\n  }\n\n  function stop(): void {\n    ownerWindow.cancelAnimationFrame(animationId)\n    lastTimeStamp = null\n    accumulatedTime = 0\n    animationId = 0\n  }\n\n  function reset(): void {\n    lastTimeStamp = null\n    accumulatedTime = 0\n  }\n\n  const self: AnimationsType = {\n    init,\n    destroy,\n    start,\n    stop,\n    update,\n    render\n  }\n  return self\n}\n", "import { NodeRectType } from './NodeRects'\n\nexport type AxisOptionType = 'x' | 'y'\nexport type AxisDirectionOptionType = 'ltr' | 'rtl'\ntype AxisEdgeType = 'top' | 'right' | 'bottom' | 'left'\n\nexport type AxisType = {\n  scroll: AxisOptionType\n  cross: AxisOptionType\n  startEdge: AxisEdgeType\n  endEdge: AxisEdgeType\n  measureSize: (nodeRect: NodeRectType) => number\n  direction: (n: number) => number\n}\n\nexport function Axis(\n  axis: AxisOptionType,\n  contentDirection: AxisDirectionOptionType\n): AxisType {\n  const isRightToLeft = contentDirection === 'rtl'\n  const isVertical = axis === 'y'\n  const scroll = isVertical ? 'y' : 'x'\n  const cross = isVertical ? 'x' : 'y'\n  const sign = !isVertical && isRightToLeft ? -1 : 1\n  const startEdge = getStartEdge()\n  const endEdge = getEndEdge()\n\n  function measureSize(nodeRect: NodeRectType): number {\n    const { height, width } = nodeRect\n    return isVertical ? height : width\n  }\n\n  function getStartEdge(): AxisEdgeType {\n    if (isVertical) return 'top'\n    return isRightToLeft ? 'right' : 'left'\n  }\n\n  function getEndEdge(): AxisEdgeType {\n    if (isVertical) return 'bottom'\n    return isRightToLeft ? 'left' : 'right'\n  }\n\n  function direction(n: number): number {\n    return n * sign\n  }\n\n  const self: AxisType = {\n    scroll,\n    cross,\n    startEdge,\n    endEdge,\n    measureSize,\n    direction\n  }\n  return self\n}\n", "import { mathAbs } from './utils'\n\nexport type LimitType = {\n  min: number\n  max: number\n  length: number\n  constrain: (n: number) => number\n  reachedAny: (n: number) => boolean\n  reachedMax: (n: number) => boolean\n  reachedMin: (n: number) => boolean\n  removeOffset: (n: number) => number\n}\n\nexport function Limit(min: number = 0, max: number = 0): LimitType {\n  const length = mathAbs(min - max)\n\n  function reachedMin(n: number): boolean {\n    return n < min\n  }\n\n  function reachedMax(n: number): boolean {\n    return n > max\n  }\n\n  function reachedAny(n: number): boolean {\n    return reachedMin(n) || reachedMax(n)\n  }\n\n  function constrain(n: number): number {\n    if (!reachedAny(n)) return n\n    return reachedMin(n) ? min : max\n  }\n\n  function removeOffset(n: number): number {\n    if (!length) return n\n    return n - length * Math.ceil((n - max) / length)\n  }\n\n  const self: LimitType = {\n    length,\n    max,\n    min,\n    constrain,\n    reachedAny,\n    reachedMax,\n    reachedMin,\n    removeOffset\n  }\n  return self\n}\n", "import { Limit } from './Limit'\nimport { mathAbs } from './utils'\n\nexport type CounterType = {\n  get: () => number\n  set: (n: number) => CounterType\n  add: (n: number) => CounterType\n  clone: () => CounterType\n}\n\nexport function Counter(\n  max: number,\n  start: number,\n  loop: boolean\n): CounterType {\n  const { constrain } = Limit(0, max)\n  const loopEnd = max + 1\n  let counter = withinLimit(start)\n\n  function withinLimit(n: number): number {\n    return !loop ? constrain(n) : mathAbs((loopEnd + n) % loopEnd)\n  }\n\n  function get(): number {\n    return counter\n  }\n\n  function set(n: number): CounterType {\n    counter = withinLimit(n)\n    return self\n  }\n\n  function add(n: number): CounterType {\n    return clone().set(get() + n)\n  }\n\n  function clone(): CounterType {\n    return Counter(max, get(), loop)\n  }\n\n  const self: CounterType = {\n    get,\n    set,\n    add,\n    clone\n  }\n  return self\n}\n", "import { EmblaCarouselType } from './EmblaCarousel'\nimport { AnimationsType } from './Animations'\nimport { CounterType } from './Counter'\nimport { DragTrackerType, PointerEventType } from './DragTracker'\nimport { EventHandlerType } from './EventHandler'\nimport { AxisType } from './Axis'\nimport { EventStore } from './EventStore'\nimport { ScrollBodyType } from './ScrollBody'\nimport { ScrollTargetType } from './ScrollTarget'\nimport { ScrollToType } from './ScrollTo'\nimport { Vector1DType } from './Vector1d'\nimport { PercentOfViewType } from './PercentOfView'\nimport { Limit } from './Limit'\nimport {\n  deltaAbs,\n  factorAbs,\n  isBoolean,\n  isMouseEvent,\n  mathAbs,\n  mathSign,\n  WindowType\n} from './utils'\n\ntype DragHandlerCallbackType = (\n  emblaApi: EmblaCarouselType,\n  evt: PointerEventType\n) => boolean | void\n\nexport type DragHandlerOptionType = boolean | DragHandlerCallbackType\n\nexport type DragHandlerType = {\n  init: (emblaApi: EmblaCarouselType) => void\n  destroy: () => void\n  pointerDown: () => boolean\n}\n\nexport function DragHandler(\n  axis: AxisType,\n  rootNode: HTMLElement,\n  ownerDocument: Document,\n  ownerWindow: WindowType,\n  target: Vector1DType,\n  dragTracker: DragTrackerType,\n  location: Vector1DType,\n  animation: AnimationsType,\n  scrollTo: ScrollToType,\n  scrollBody: ScrollBodyType,\n  scrollTarget: ScrollTargetType,\n  index: CounterType,\n  eventHandler: EventHandlerType,\n  percentOfView: PercentOfViewType,\n  dragFree: boolean,\n  dragThreshold: number,\n  skipSnaps: boolean,\n  baseFriction: number,\n  watchDrag: DragHandlerOptionType\n): DragHandlerType {\n  const { cross: crossAxis, direction } = axis\n  const focusNodes = ['INPUT', 'SELECT', 'TEXTAREA']\n  const nonPassiveEvent = { passive: false }\n  const initEvents = EventStore()\n  const dragEvents = EventStore()\n  const goToNextThreshold = Limit(50, 225).constrain(percentOfView.measure(20))\n  const snapForceBoost = { mouse: 300, touch: 400 }\n  const freeForceBoost = { mouse: 500, touch: 600 }\n  const baseSpeed = dragFree ? 43 : 25\n\n  let isMoving = false\n  let startScroll = 0\n  let startCross = 0\n  let pointerIsDown = false\n  let preventScroll = false\n  let preventClick = false\n  let isMouse = false\n\n  function init(emblaApi: EmblaCarouselType): void {\n    if (!watchDrag) return\n\n    function downIfAllowed(evt: PointerEventType): void {\n      if (isBoolean(watchDrag) || watchDrag(emblaApi, evt)) down(evt)\n    }\n\n    const node = rootNode\n    initEvents\n      .add(node, 'dragstart', (evt) => evt.preventDefault(), nonPassiveEvent)\n      .add(node, 'touchmove', () => undefined, nonPassiveEvent)\n      .add(node, 'touchend', () => undefined)\n      .add(node, 'touchstart', downIfAllowed)\n      .add(node, 'mousedown', downIfAllowed)\n      .add(node, 'touchcancel', up)\n      .add(node, 'contextmenu', up)\n      .add(node, 'click', click, true)\n  }\n\n  function destroy(): void {\n    initEvents.clear()\n    dragEvents.clear()\n  }\n\n  function addDragEvents(): void {\n    const node = isMouse ? ownerDocument : rootNode\n    dragEvents\n      .add(node, 'touchmove', move, nonPassiveEvent)\n      .add(node, 'touchend', up)\n      .add(node, 'mousemove', move, nonPassiveEvent)\n      .add(node, 'mouseup', up)\n  }\n\n  function isFocusNode(node: Element): boolean {\n    const nodeName = node.nodeName || ''\n    return focusNodes.includes(nodeName)\n  }\n\n  function forceBoost(): number {\n    const boost = dragFree ? freeForceBoost : snapForceBoost\n    const type = isMouse ? 'mouse' : 'touch'\n    return boost[type]\n  }\n\n  function allowedForce(force: number, targetChanged: boolean): number {\n    const next = index.add(mathSign(force) * -1)\n    const baseForce = scrollTarget.byDistance(force, !dragFree).distance\n\n    if (dragFree || mathAbs(force) < goToNextThreshold) return baseForce\n    if (skipSnaps && targetChanged) return baseForce * 0.5\n\n    return scrollTarget.byIndex(next.get(), 0).distance\n  }\n\n  function down(evt: PointerEventType): void {\n    const isMouseEvt = isMouseEvent(evt, ownerWindow)\n    isMouse = isMouseEvt\n    preventClick = dragFree && isMouseEvt && !evt.buttons && isMoving\n    isMoving = deltaAbs(target.get(), location.get()) >= 2\n\n    if (isMouseEvt && evt.button !== 0) return\n    if (isFocusNode(evt.target as Element)) return\n\n    pointerIsDown = true\n    dragTracker.pointerDown(evt)\n    scrollBody.useFriction(0).useDuration(0)\n    target.set(location)\n    addDragEvents()\n    startScroll = dragTracker.readPoint(evt)\n    startCross = dragTracker.readPoint(evt, crossAxis)\n    eventHandler.emit('pointerDown')\n  }\n\n  function move(evt: PointerEventType): void {\n    const isTouchEvt = !isMouseEvent(evt, ownerWindow)\n    if (isTouchEvt && evt.touches.length >= 2) return up(evt)\n\n    const lastScroll = dragTracker.readPoint(evt)\n    const lastCross = dragTracker.readPoint(evt, crossAxis)\n    const diffScroll = deltaAbs(lastScroll, startScroll)\n    const diffCross = deltaAbs(lastCross, startCross)\n\n    if (!preventScroll && !isMouse) {\n      if (!evt.cancelable) return up(evt)\n      preventScroll = diffScroll > diffCross\n      if (!preventScroll) return up(evt)\n    }\n    const diff = dragTracker.pointerMove(evt)\n    if (diffScroll > dragThreshold) preventClick = true\n\n    scrollBody.useFriction(0.3).useDuration(0.75)\n    animation.start()\n    target.add(direction(diff))\n    evt.preventDefault()\n  }\n\n  function up(evt: PointerEventType): void {\n    const currentLocation = scrollTarget.byDistance(0, false)\n    const targetChanged = currentLocation.index !== index.get()\n    const rawForce = dragTracker.pointerUp(evt) * forceBoost()\n    const force = allowedForce(direction(rawForce), targetChanged)\n    const forceFactor = factorAbs(rawForce, force)\n    const speed = baseSpeed - 10 * forceFactor\n    const friction = baseFriction + forceFactor / 50\n\n    preventScroll = false\n    pointerIsDown = false\n    dragEvents.clear()\n    scrollBody.useDuration(speed).useFriction(friction)\n    scrollTo.distance(force, !dragFree)\n    isMouse = false\n    eventHandler.emit('pointerUp')\n  }\n\n  function click(evt: MouseEvent): void {\n    if (preventClick) {\n      evt.stopPropagation()\n      evt.preventDefault()\n      preventClick = false\n    }\n  }\n\n  function pointerDown(): boolean {\n    return pointerIsDown\n  }\n\n  const self: DragHandlerType = {\n    init,\n    destroy,\n    pointerDown\n  }\n  return self\n}\n", "import { AxisOptionType, AxisType } from './Axis'\nimport { isMouseEvent, mathAbs, WindowType } from './utils'\n\ntype PointerCoordType = keyof Touch | keyof MouseEvent\nexport type PointerEventType = TouchEvent | MouseEvent\n\nexport type DragTrackerType = {\n  pointerDown: (evt: PointerEventType) => number\n  pointerMove: (evt: PointerEventType) => number\n  pointerUp: (evt: PointerEventType) => number\n  readPoint: (evt: PointerEventType, evtAxis?: AxisOptionType) => number\n}\n\nexport function DragTracker(\n  axis: AxisType,\n  ownerWindow: WindowType\n): DragTrackerType {\n  const logInterval = 170\n\n  let startEvent: PointerEventType\n  let lastEvent: PointerEventType\n\n  function readTime(evt: PointerEventType): number {\n    return evt.timeStamp\n  }\n\n  function readPoint(evt: PointerEventType, evtAxis?: AxisOptionType): number {\n    const property = evtAxis || axis.scroll\n    const coord: PointerCoordType = `client${property === 'x' ? 'X' : 'Y'}`\n    return (isMouseEvent(evt, ownerWindow) ? evt : evt.touches[0])[coord]\n  }\n\n  function pointerDown(evt: PointerEventType): number {\n    startEvent = evt\n    lastEvent = evt\n    return readPoint(evt)\n  }\n\n  function pointerMove(evt: PointerEventType): number {\n    const diff = readPoint(evt) - readPoint(lastEvent)\n    const expired = readTime(evt) - readTime(startEvent) > logInterval\n\n    lastEvent = evt\n    if (expired) startEvent = evt\n    return diff\n  }\n\n  function pointerUp(evt: PointerEventType): number {\n    if (!startEvent || !lastEvent) return 0\n    const diffDrag = readPoint(lastEvent) - readPoint(startEvent)\n    const diffTime = readTime(evt) - readTime(startEvent)\n    const expired = readTime(evt) - readTime(lastEvent) > logInterval\n    const force = diffDrag / diffTime\n    const isFlick = diffTime && !expired && mathAbs(force) > 0.1\n\n    return isFlick ? force : 0\n  }\n\n  const self: DragTrackerType = {\n    pointerDown,\n    pointerMove,\n    pointerUp,\n    readPoint\n  }\n  return self\n}\n", "export type NodeRectType = {\n  top: number\n  right: number\n  bottom: number\n  left: number\n  width: number\n  height: number\n}\n\nexport type NodeRectsType = {\n  measure: (node: HTMLElement) => NodeRectType\n}\n\nexport function NodeRects(): NodeRectsType {\n  function measure(node: HTMLElement): NodeRectType {\n    const { offsetTop, offsetLeft, offsetWidth, offsetHeight } = node\n    const offset: NodeRectType = {\n      top: offsetTop,\n      right: offsetLeft + offsetWidth,\n      bottom: offsetTop + offsetHeight,\n      left: offsetLeft,\n      width: offsetWidth,\n      height: offsetHeight\n    }\n\n    return offset\n  }\n\n  const self: NodeRectsType = {\n    measure\n  }\n  return self\n}\n", "export type PercentOfViewType = {\n  measure: (n: number) => number\n}\n\nexport function PercentOfView(viewSize: number): PercentOfViewType {\n  function measure(n: number): number {\n    return viewSize * (n / 100)\n  }\n\n  const self: PercentOfViewType = {\n    measure\n  }\n  return self\n}\n", "import { AxisType } from './Axis'\nimport { EmblaCarouselType } from './EmblaCarousel'\nimport { EventHandlerType } from './EventHandler'\nimport { NodeRectsType } from './NodeRects'\nimport { isBoolean, mathAbs, WindowType } from './utils'\n\ntype ResizeHandlerCallbackType = (\n  emblaApi: EmblaCarouselType,\n  entries: ResizeObserverEntry[]\n) => boolean | void\n\nexport type ResizeHandlerOptionType = boolean | ResizeHandlerCallbackType\n\nexport type ResizeHandlerType = {\n  init: (emblaApi: EmblaCarouselType) => void\n  destroy: () => void\n}\n\nexport function ResizeHandler(\n  container: HTMLElement,\n  eventHandler: EventHandlerType,\n  ownerWindow: WindowType,\n  slides: HTMLElement[],\n  axis: AxisType,\n  watchResize: ResizeHandlerOptionType,\n  nodeRects: NodeRectsType\n): ResizeHandlerType {\n  const observeNodes = [container].concat(slides)\n  let resizeObserver: ResizeObserver\n  let containerSize: number\n  let slideSizes: number[] = []\n  let destroyed = false\n\n  function readSize(node: HTMLElement): number {\n    return axis.measureSize(nodeRects.measure(node))\n  }\n\n  function init(emblaApi: EmblaCarouselType): void {\n    if (!watchResize) return\n\n    containerSize = readSize(container)\n    slideSizes = slides.map(readSize)\n\n    function defaultCallback(entries: ResizeObserverEntry[]): void {\n      for (const entry of entries) {\n        if (destroyed) return\n\n        const isContainer = entry.target === container\n        const slideIndex = slides.indexOf(<HTMLElement>entry.target)\n        const lastSize = isContainer ? containerSize : slideSizes[slideIndex]\n        const newSize = readSize(isContainer ? container : slides[slideIndex])\n        const diffSize = mathAbs(newSize - lastSize)\n\n        if (diffSize >= 0.5) {\n          emblaApi.reInit()\n          eventHandler.emit('resize')\n\n          break\n        }\n      }\n    }\n\n    resizeObserver = new ResizeObserver((entries) => {\n      if (isBoolean(watchResize) || watchResize(emblaApi, entries)) {\n        defaultCallback(entries)\n      }\n    })\n\n    ownerWindow.requestAnimationFrame(() => {\n      observeNodes.forEach((node) => resizeObserver.observe(node))\n    })\n  }\n\n  function destroy(): void {\n    destroyed = true\n    if (resizeObserver) resizeObserver.disconnect()\n  }\n\n  const self: ResizeHandlerType = {\n    init,\n    destroy\n  }\n  return self\n}\n", "import { mathSign, mathAbs } from './utils'\nimport { Vector1DType } from './Vector1d'\n\nexport type ScrollBodyType = {\n  direction: () => number\n  duration: () => number\n  velocity: () => number\n  seek: () => ScrollBodyType\n  settled: () => boolean\n  useBaseFriction: () => ScrollBodyType\n  useBaseDuration: () => ScrollBodyType\n  useFriction: (n: number) => ScrollBodyType\n  useDuration: (n: number) => ScrollBodyType\n}\n\nexport function ScrollBody(\n  location: Vector1DType,\n  offsetLocation: Vector1DType,\n  previousLocation: Vector1DType,\n  target: Vector1DType,\n  baseDuration: number,\n  baseFriction: number\n): ScrollBodyType {\n  let scrollVelocity = 0\n  let scrollDirection = 0\n  let scrollDuration = baseDuration\n  let scrollFriction = baseFriction\n  let rawLocation = location.get()\n  let rawLocationPrevious = 0\n\n  function seek(): ScrollBodyType {\n    const displacement = target.get() - location.get()\n    const isInstant = !scrollDuration\n    let scrollDistance = 0\n\n    if (isInstant) {\n      scrollVelocity = 0\n      previousLocation.set(target)\n      location.set(target)\n\n      scrollDistance = displacement\n    } else {\n      previousLocation.set(location)\n\n      scrollVelocity += displacement / scrollDuration\n      scrollVelocity *= scrollFriction\n      rawLocation += scrollVelocity\n      location.add(scrollVelocity)\n\n      scrollDistance = rawLocation - rawLocationPrevious\n    }\n\n    scrollDirection = mathSign(scrollDistance)\n    rawLocationPrevious = rawLocation\n    return self\n  }\n\n  function settled(): boolean {\n    const diff = target.get() - offsetLocation.get()\n    return mathAbs(diff) < 0.001\n  }\n\n  function duration(): number {\n    return scrollDuration\n  }\n\n  function direction(): number {\n    return scrollDirection\n  }\n\n  function velocity(): number {\n    return scrollVelocity\n  }\n\n  function useBaseDuration(): ScrollBodyType {\n    return useDuration(baseDuration)\n  }\n\n  function useBaseFriction(): ScrollBodyType {\n    return useFriction(baseFriction)\n  }\n\n  function useDuration(n: number): ScrollBodyType {\n    scrollDuration = n\n    return self\n  }\n\n  function useFriction(n: number): ScrollBodyType {\n    scrollFriction = n\n    return self\n  }\n\n  const self: ScrollBodyType = {\n    direction,\n    duration,\n    velocity,\n    seek,\n    settled,\n    useBaseFriction,\n    useBaseDuration,\n    useFriction,\n    useDuration\n  }\n  return self\n}\n", "import { Limit, LimitType } from './Limit'\nimport { ScrollBodyType } from './ScrollBody'\nimport { Vector1DType } from './Vector1d'\nimport { mathAbs } from './utils'\nimport { PercentOfViewType } from './PercentOfView'\n\nexport type ScrollBoundsType = {\n  shouldConstrain: () => boolean\n  constrain: (pointerDown: boolean) => void\n  toggleActive: (active: boolean) => void\n}\n\nexport function ScrollBounds(\n  limit: LimitType,\n  location: Vector1DType,\n  target: Vector1DType,\n  scrollBody: ScrollBodyType,\n  percentOfView: PercentOfViewType\n): ScrollBoundsType {\n  const pullBackThreshold = percentOfView.measure(10)\n  const edgeOffsetTolerance = percentOfView.measure(50)\n  const frictionLimit = Limit(0.1, 0.99)\n  let disabled = false\n\n  function shouldConstrain(): boolean {\n    if (disabled) return false\n    if (!limit.reachedAny(target.get())) return false\n    if (!limit.reachedAny(location.get())) return false\n    return true\n  }\n\n  function constrain(pointerDown: boolean): void {\n    if (!shouldConstrain()) return\n    const edge = limit.reachedMin(location.get()) ? 'min' : 'max'\n    const diffToEdge = mathAbs(limit[edge] - location.get())\n    const diffToTarget = target.get() - location.get()\n    const friction = frictionLimit.constrain(diffToEdge / edgeOffsetTolerance)\n\n    target.subtract(diffToTarget * friction)\n\n    if (!pointerDown && mathAbs(diffToTarget) < pullBackThreshold) {\n      target.set(limit.constrain(target.get()))\n      scrollBody.useDuration(25).useBaseFriction()\n    }\n  }\n\n  function toggleActive(active: boolean): void {\n    disabled = !active\n  }\n\n  const self: ScrollBoundsType = {\n    shouldConstrain,\n    constrain,\n    toggleActive\n  }\n  return self\n}\n", "import { Limit, LimitType } from './Limit'\nimport { arrayIsLastIndex, arrayLast, deltaAbs } from './utils'\n\nexport type ScrollContainOptionType = false | 'trimSnaps' | 'keepSnaps'\n\nexport type ScrollContainType = {\n  snapsContained: number[]\n  scrollContainLimit: LimitType\n}\n\nexport function ScrollContain(\n  viewSize: number,\n  contentSize: number,\n  snapsAligned: number[],\n  containScroll: ScrollContainOptionType,\n  pixelTolerance: number\n): ScrollContainType {\n  const scrollBounds = Limit(-contentSize + viewSize, 0)\n  const snapsBounded = measureBounded()\n  const scrollContainLimit = findScrollContainLimit()\n  const snapsContained = measureContained()\n\n  function usePixelTolerance(bound: number, snap: number): boolean {\n    return deltaAbs(bound, snap) <= 1\n  }\n\n  function findScrollContainLimit(): LimitType {\n    const startSnap = snapsBounded[0]\n    const endSnap = arrayLast(snapsBounded)\n    const min = snapsBounded.lastIndexOf(startSnap)\n    const max = snapsBounded.indexOf(endSnap) + 1\n    return Limit(min, max)\n  }\n\n  function measureBounded(): number[] {\n    return snapsAligned\n      .map((snapAligned, index) => {\n        const { min, max } = scrollBounds\n        const snap = scrollBounds.constrain(snapAligned)\n        const isFirst = !index\n        const isLast = arrayIsLastIndex(snapsAligned, index)\n        if (isFirst) return max\n        if (isLast) return min\n        if (usePixelTolerance(min, snap)) return min\n        if (usePixelTolerance(max, snap)) return max\n        return snap\n      })\n      .map((scrollBound) => parseFloat(scrollBound.toFixed(3)))\n  }\n\n  function measureContained(): number[] {\n    if (contentSize <= viewSize + pixelTolerance) return [scrollBounds.max]\n    if (containScroll === 'keepSnaps') return snapsBounded\n    const { min, max } = scrollContainLimit\n    return snapsBounded.slice(min, max)\n  }\n\n  const self: ScrollContainType = {\n    snapsContained,\n    scrollContainLimit\n  }\n  return self\n}\n", "import { Limit, LimitType } from './Limit'\nimport { arrayLast } from './utils'\n\nexport type ScrollLimitType = {\n  limit: LimitType\n}\n\nexport function ScrollLimit(\n  contentSize: number,\n  scrollSnaps: number[],\n  loop: boolean\n): ScrollLimitType {\n  const max = scrollSnaps[0]\n  const min = loop ? max - contentSize : arrayLast(scrollSnaps)\n  const limit = Limit(min, max)\n\n  const self: ScrollLimitType = {\n    limit\n  }\n  return self\n}\n", "import { Limit, LimitType } from './Limit'\nimport { Vector1DType } from './Vector1d'\n\nexport type ScrollLooperType = {\n  loop: (direction: number) => void\n}\n\nexport function ScrollLooper(\n  contentSize: number,\n  limit: LimitType,\n  location: Vector1DType,\n  vectors: Vector1DType[]\n): ScrollLooperType {\n  const jointSafety = 0.1\n  const min = limit.min + jointSafety\n  const max = limit.max + jointSafety\n  const { reachedMin, reachedMax } = Limit(min, max)\n\n  function shouldLoop(direction: number): boolean {\n    if (direction === 1) return reachedMax(location.get())\n    if (direction === -1) return reachedMin(location.get())\n    return false\n  }\n\n  function loop(direction: number): void {\n    if (!shouldLoop(direction)) return\n\n    const loopDistance = contentSize * (direction * -1)\n    vectors.forEach((v) => v.add(loopDistance))\n  }\n\n  const self: ScrollLooperType = {\n    loop\n  }\n  return self\n}\n", "import { LimitType } from './Limit'\n\nexport type ScrollProgressType = {\n  get: (n: number) => number\n}\n\nexport function ScrollProgress(limit: LimitType): ScrollProgressType {\n  const { max, length } = limit\n\n  function get(n: number): number {\n    const currentLocation = n - max\n    return length ? currentLocation / -length : 0\n  }\n\n  const self: ScrollProgressType = {\n    get\n  }\n  return self\n}\n", "import { AlignmentType } from './Alignment'\nimport { AxisType } from './Axis'\nimport { NodeRectType } from './NodeRects'\nimport { SlidesToScrollType } from './SlidesToScroll'\nimport { arrayLast, mathAbs } from './utils'\n\nexport type ScrollSnapsType = {\n  snaps: number[]\n  snapsAligned: number[]\n}\n\nexport function ScrollSnaps(\n  axis: AxisType,\n  alignment: AlignmentType,\n  containerRect: NodeRectType,\n  slideRects: NodeRectType[],\n  slidesToScroll: SlidesToScrollType\n): ScrollSnapsType {\n  const { startEdge, endEdge } = axis\n  const { groupSlides } = slidesToScroll\n  const alignments = measureSizes().map(alignment.measure)\n  const snaps = measureUnaligned()\n  const snapsAligned = measureAligned()\n\n  function measureSizes(): number[] {\n    return groupSlides(slideRects)\n      .map((rects) => arrayLast(rects)[endEdge] - rects[0][startEdge])\n      .map(mathAbs)\n  }\n\n  function measureUnaligned(): number[] {\n    return slideRects\n      .map((rect) => containerRect[startEdge] - rect[startEdge])\n      .map((snap) => -mathAbs(snap))\n  }\n\n  function measureAligned(): number[] {\n    return groupSlides(snaps)\n      .map((g) => g[0])\n      .map((snap, index) => snap + alignments[index])\n  }\n\n  const self: ScrollSnapsType = {\n    snaps,\n    snapsAligned\n  }\n  return self\n}\n", "import { LimitType } from './Limit'\nimport { ScrollContainOptionType } from './ScrollContain'\nimport { SlidesToScrollType } from './SlidesToScroll'\nimport {\n  arrayFromNumber,\n  arrayIsLastIndex,\n  arrayLast,\n  arrayLastIndex\n} from './utils'\n\nexport type SlideRegistryType = {\n  slideRegistry: number[][]\n}\n\nexport function SlideRegistry(\n  containSnaps: boolean,\n  containScroll: ScrollContainOptionType,\n  scrollSnaps: number[],\n  scrollContainLimit: LimitType,\n  slidesToScroll: SlidesToScrollType,\n  slideIndexes: number[]\n): SlideRegistryType {\n  const { groupSlides } = slidesToScroll\n  const { min, max } = scrollContainLimit\n  const slideRegistry = createSlideRegistry()\n\n  function createSlideRegistry(): number[][] {\n    const groupedSlideIndexes = groupSlides(slideIndexes)\n    const doNotContain = !containSnaps || containScroll === 'keepSnaps'\n\n    if (scrollSnaps.length === 1) return [slideIndexes]\n    if (doNotContain) return groupedSlideIndexes\n\n    return groupedSlideIndexes.slice(min, max).map((group, index, groups) => {\n      const isFirst = !index\n      const isLast = arrayIsLastIndex(groups, index)\n\n      if (isFirst) {\n        const range = arrayLast(groups[0]) + 1\n        return arrayFromNumber(range)\n      }\n      if (isLast) {\n        const range = arrayLastIndex(slideIndexes) - arrayLast(groups)[0] + 1\n        return arrayFromNumber(range, arrayLast(groups)[0])\n      }\n      return group\n    })\n  }\n\n  const self: SlideRegistryType = {\n    slideRegistry\n  }\n  return self\n}\n", "import { LimitType } from './Limit'\nimport { Vector1DType } from './Vector1d'\nimport { arrayLast, mathAbs, mathSign } from './utils'\n\nexport type TargetType = {\n  distance: number\n  index: number\n}\n\nexport type ScrollTargetType = {\n  byIndex: (target: number, direction: number) => TargetType\n  byDistance: (force: number, snap: boolean) => TargetType\n  shortcut: (target: number, direction: number) => number\n}\n\nexport function ScrollTarget(\n  loop: boolean,\n  scrollSnaps: number[],\n  contentSize: number,\n  limit: LimitType,\n  targetVector: Vector1DType\n): ScrollTargetType {\n  const { reachedAny, removeOffset, constrain } = limit\n\n  function minDistance(distances: number[]): number {\n    return distances.concat().sort((a, b) => mathAbs(a) - mathAbs(b))[0]\n  }\n\n  function findTargetSnap(target: number): TargetType {\n    const distance = loop ? removeOffset(target) : constrain(target)\n    const ascDiffsToSnaps = scrollSnaps\n      .map((snap, index) => ({ diff: shortcut(snap - distance, 0), index }))\n      .sort((d1, d2) => mathAbs(d1.diff) - mathAbs(d2.diff))\n\n    const { index } = ascDiffsToSnaps[0]\n    return { index, distance }\n  }\n\n  function shortcut(target: number, direction: number): number {\n    const targets = [target, target + contentSize, target - contentSize]\n\n    if (!loop) return target\n    if (!direction) return minDistance(targets)\n\n    const matchingTargets = targets.filter((t) => mathSign(t) === direction)\n    if (matchingTargets.length) return minDistance(matchingTargets)\n    return arrayLast(targets) - contentSize\n  }\n\n  function byIndex(index: number, direction: number): TargetType {\n    const diffToSnap = scrollSnaps[index] - targetVector.get()\n    const distance = shortcut(diffToSnap, direction)\n    return { index, distance }\n  }\n\n  function byDistance(distance: number, snap: boolean): TargetType {\n    const target = targetVector.get() + distance\n    const { index, distance: targetSnapDistance } = findTargetSnap(target)\n    const reachedBound = !loop && reachedAny(target)\n\n    if (!snap || reachedBound) return { index, distance }\n\n    const diffToSnap = scrollSnaps[index] - targetSnapDistance\n    const snapDistance = distance + shortcut(diffToSnap, 0)\n\n    return { index, distance: snapDistance }\n  }\n\n  const self: ScrollTargetType = {\n    byDistance,\n    byIndex,\n    shortcut\n  }\n  return self\n}\n", "import { AnimationsType } from './Animations'\nimport { CounterType } from './Counter'\nimport { EventHandlerType } from './EventHandler'\nimport { ScrollBodyType } from './ScrollBody'\nimport { ScrollTargetType, TargetType } from './ScrollTarget'\nimport { Vector1DType } from './Vector1d'\n\nexport type ScrollToType = {\n  distance: (n: number, snap: boolean) => void\n  index: (n: number, direction: number) => void\n}\n\nexport function ScrollTo(\n  animation: AnimationsType,\n  indexCurrent: CounterType,\n  indexPrevious: CounterType,\n  scrollBody: ScrollBodyType,\n  scrollTarget: ScrollTargetType,\n  targetVector: Vector1DType,\n  eventHandler: EventHandlerType\n): ScrollToType {\n  function scrollTo(target: TargetType): void {\n    const distanceDiff = target.distance\n    const indexDiff = target.index !== indexCurrent.get()\n\n    targetVector.add(distanceDiff)\n\n    if (distanceDiff) {\n      if (scrollBody.duration()) {\n        animation.start()\n      } else {\n        animation.update()\n        animation.render(1)\n        animation.update()\n      }\n    }\n\n    if (indexDiff) {\n      indexPrevious.set(indexCurrent.get())\n      indexCurrent.set(target.index)\n      eventHandler.emit('select')\n    }\n  }\n\n  function distance(n: number, snap: boolean): void {\n    const target = scrollTarget.byDistance(n, snap)\n    scrollTo(target)\n  }\n\n  function index(n: number, direction: number): void {\n    const targetIndex = indexCurrent.clone().set(n)\n    const target = scrollTarget.byIndex(targetIndex.get(), direction)\n    scrollTo(target)\n  }\n\n  const self: ScrollToType = {\n    distance,\n    index\n  }\n  return self\n}\n", "import { EmblaCarouselType } from './EmblaCarousel'\nimport { EventHandlerType } from './EventHandler'\nimport { EventStoreType } from './EventStore'\nimport { ScrollBodyType } from './ScrollBody'\nimport { ScrollToType } from './ScrollTo'\nimport { SlideRegistryType } from './SlideRegistry'\nimport { isBoolean, isNumber } from './utils'\n\ntype FocusHandlerCallbackType = (\n  emblaApi: EmblaCarouselType,\n  evt: FocusEvent\n) => boolean | void\n\nexport type FocusHandlerOptionType = boolean | FocusHandlerCallbackType\n\nexport type SlideFocusType = {\n  init: (emblaApi: EmblaCarouselType) => void\n}\n\nexport function SlideFocus(\n  root: HTMLElement,\n  slides: HTMLElement[],\n  slideRegistry: SlideRegistryType['slideRegistry'],\n  scrollTo: ScrollToType,\n  scrollBody: ScrollBodyType,\n  eventStore: EventStoreType,\n  eventHandler: EventHandlerType,\n  watchFocus: FocusHandlerOptionType\n): SlideFocusType {\n  const focusListenerOptions = { passive: true, capture: true }\n  let lastTabPressTime = 0\n\n  function init(emblaApi: EmblaCarouselType): void {\n    if (!watchFocus) return\n\n    function defaultCallback(index: number): void {\n      const nowTime = new Date().getTime()\n      const diffTime = nowTime - lastTabPressTime\n\n      if (diffTime > 10) return\n\n      eventHandler.emit('slideFocusStart')\n      root.scrollLeft = 0\n\n      const group = slideRegistry.findIndex((group) => group.includes(index))\n\n      if (!isNumber(group)) return\n\n      scrollBody.useDuration(0)\n      scrollTo.index(group, 0)\n\n      eventHandler.emit('slideFocus')\n    }\n\n    eventStore.add(document, 'keydown', registerTabPress, false)\n\n    slides.forEach((slide, slideIndex) => {\n      eventStore.add(\n        slide,\n        'focus',\n        (evt: FocusEvent) => {\n          if (isBoolean(watchFocus) || watchFocus(emblaApi, evt)) {\n            defaultCallback(slideIndex)\n          }\n        },\n        focusListenerOptions\n      )\n    })\n  }\n\n  function registerTabPress(event: KeyboardEvent): void {\n    if (event.code === 'Tab') lastTabPressTime = new Date().getTime()\n  }\n\n  const self: SlideFocusType = {\n    init\n  }\n  return self\n}\n", "import { isNumber } from './utils'\n\nexport type Vector1DType = {\n  get: () => number\n  set: (n: Vector1DType | number) => void\n  add: (n: Vector1DType | number) => void\n  subtract: (n: Vector1DType | number) => void\n}\n\nexport function Vector1D(initialValue: number): Vector1DType {\n  let value = initialValue\n\n  function get(): number {\n    return value\n  }\n\n  function set(n: Vector1DType | number): void {\n    value = normalizeInput(n)\n  }\n\n  function add(n: Vector1DType | number): void {\n    value += normalizeInput(n)\n  }\n\n  function subtract(n: Vector1DType | number): void {\n    value -= normalizeInput(n)\n  }\n\n  function normalizeInput(n: Vector1DType | number): number {\n    return isNumber(n) ? n : n.get()\n  }\n\n  const self: Vector1DType = {\n    get,\n    set,\n    add,\n    subtract\n  }\n  return self\n}\n", "import { AxisType } from './Axis'\nimport { roundToTwoDecimals } from './utils'\n\nexport type TranslateType = {\n  clear: () => void\n  to: (target: number) => void\n  toggleActive: (active: boolean) => void\n}\n\nexport function Translate(\n  axis: AxisType,\n  container: HTMLElement\n): TranslateType {\n  const translate = axis.scroll === 'x' ? x : y\n  const containerStyle = container.style\n  let previousTarget: number | null = null\n  let disabled = false\n\n  function x(n: number): string {\n    return `translate3d(${n}px,0px,0px)`\n  }\n\n  function y(n: number): string {\n    return `translate3d(0px,${n}px,0px)`\n  }\n\n  function to(target: number): void {\n    if (disabled) return\n\n    const newTarget = roundToTwoDecimals(axis.direction(target))\n    if (newTarget === previousTarget) return\n\n    containerStyle.transform = translate(newTarget)\n    previousTarget = newTarget\n  }\n\n  function toggleActive(active: boolean): void {\n    disabled = !active\n  }\n\n  function clear(): void {\n    if (disabled) return\n    containerStyle.transform = ''\n    if (!container.getAttribute('style')) container.removeAttribute('style')\n  }\n\n  const self: TranslateType = {\n    clear,\n    to,\n    toggleActive\n  }\n  return self\n}\n", "import { AxisType } from './Axis'\nimport { arrayKeys } from './utils'\nimport { Vector1D, Vector1DType } from './Vector1d'\nimport { Translate, TranslateType } from './Translate'\n\ntype SlideBoundType = {\n  start: number\n  end: number\n}\n\ntype LoopPointType = {\n  loopPoint: number\n  index: number\n  translate: TranslateType\n  slideLocation: Vector1DType\n  target: () => number\n}\n\nexport type SlideLooperType = {\n  canLoop: () => boolean\n  clear: () => void\n  loop: () => void\n  loopPoints: LoopPointType[]\n}\n\nexport function SlideLooper(\n  axis: AxisType,\n  viewSize: number,\n  contentSize: number,\n  slideSizes: number[],\n  slideSizesWithGaps: number[],\n  snaps: number[],\n  scrollSnaps: number[],\n  location: Vector1DType,\n  slides: HTMLElement[]\n): SlideLooperType {\n  const roundingSafety = 0.5\n  const ascItems = arrayKeys(slideSizesWithGaps)\n  const descItems = arrayKeys(slideSizesWithGaps).reverse()\n  const loopPoints = startPoints().concat(endPoints())\n\n  function removeSlideSizes(indexes: number[], from: number): number {\n    return indexes.reduce((a: number, i) => {\n      return a - slideSizesWithGaps[i]\n    }, from)\n  }\n\n  function slidesInGap(indexes: number[], gap: number): number[] {\n    return indexes.reduce((a: number[], i) => {\n      const remainingGap = removeSlideSizes(a, gap)\n      return remainingGap > 0 ? a.concat([i]) : a\n    }, [])\n  }\n\n  function findSlideBounds(offset: number): SlideBoundType[] {\n    return snaps.map((snap, index) => ({\n      start: snap - slideSizes[index] + roundingSafety + offset,\n      end: snap + viewSize - roundingSafety + offset\n    }))\n  }\n\n  function findLoopPoints(\n    indexes: number[],\n    offset: number,\n    isEndEdge: boolean\n  ): LoopPointType[] {\n    const slideBounds = findSlideBounds(offset)\n\n    return indexes.map((index) => {\n      const initial = isEndEdge ? 0 : -contentSize\n      const altered = isEndEdge ? contentSize : 0\n      const boundEdge = isEndEdge ? 'end' : 'start'\n      const loopPoint = slideBounds[index][boundEdge]\n\n      return {\n        index,\n        loopPoint,\n        slideLocation: Vector1D(-1),\n        translate: Translate(axis, slides[index]),\n        target: () => (location.get() > loopPoint ? initial : altered)\n      }\n    })\n  }\n\n  function startPoints(): LoopPointType[] {\n    const gap = scrollSnaps[0]\n    const indexes = slidesInGap(descItems, gap)\n    return findLoopPoints(indexes, contentSize, false)\n  }\n\n  function endPoints(): LoopPointType[] {\n    const gap = viewSize - scrollSnaps[0] - 1\n    const indexes = slidesInGap(ascItems, gap)\n    return findLoopPoints(indexes, -contentSize, true)\n  }\n\n  function canLoop(): boolean {\n    return loopPoints.every(({ index }) => {\n      const otherIndexes = ascItems.filter((i) => i !== index)\n      return removeSlideSizes(otherIndexes, viewSize) <= 0.1\n    })\n  }\n\n  function loop(): void {\n    loopPoints.forEach((loopPoint) => {\n      const { target, translate, slideLocation } = loopPoint\n      const shiftLocation = target()\n      if (shiftLocation === slideLocation.get()) return\n      translate.to(shiftLocation)\n      slideLocation.set(shiftLocation)\n    })\n  }\n\n  function clear(): void {\n    loopPoints.forEach((loopPoint) => loopPoint.translate.clear())\n  }\n\n  const self: SlideLooperType = {\n    canLoop,\n    clear,\n    loop,\n    loopPoints\n  }\n  return self\n}\n", "import { EmblaCarouselType } from './EmblaCarousel'\nimport { EventHandlerType } from './EventHandler'\nimport { isBoolean } from './utils'\n\ntype SlidesHandlerCallbackType = (\n  emblaApi: EmblaCarouselType,\n  mutations: MutationRecord[]\n) => boolean | void\n\nexport type SlidesHandlerOptionType = boolean | SlidesHandlerCallbackType\n\nexport type SlidesHandlerType = {\n  init: (emblaApi: EmblaCarouselType) => void\n  destroy: () => void\n}\n\nexport function SlidesHandler(\n  container: HTMLElement,\n  eventHandler: EventHandlerType,\n  watchSlides: SlidesHandlerOptionType\n): SlidesHandlerType {\n  let mutationObserver: MutationObserver\n  let destroyed = false\n\n  function init(emblaApi: EmblaCarouselType): void {\n    if (!watchSlides) return\n\n    function defaultCallback(mutations: MutationRecord[]): void {\n      for (const mutation of mutations) {\n        if (mutation.type === 'childList') {\n          emblaApi.reInit()\n          eventHandler.emit('slidesChanged')\n          break\n        }\n      }\n    }\n\n    mutationObserver = new MutationObserver((mutations) => {\n      if (destroyed) return\n      if (isBoolean(watchSlides) || watchSlides(emblaApi, mutations)) {\n        defaultCallback(mutations)\n      }\n    })\n\n    mutationObserver.observe(container, { childList: true })\n  }\n\n  function destroy(): void {\n    if (mutationObserver) mutationObserver.disconnect()\n    destroyed = true\n  }\n\n  const self: SlidesHandlerType = {\n    init,\n    destroy\n  }\n  return self\n}\n", "import { EventHandlerType } from './EventHandler'\nimport { objectKeys } from './utils'\n\ntype IntersectionEntryMapType = {\n  [key: number]: IntersectionObserverEntry\n}\n\nexport type SlidesInViewOptionsType = IntersectionObserverInit['threshold']\n\nexport type SlidesInViewType = {\n  init: () => void\n  destroy: () => void\n  get: (inView?: boolean) => number[]\n}\n\nexport function SlidesInView(\n  container: HTMLElement,\n  slides: HTMLElement[],\n  eventHandler: EventHandlerType,\n  threshold: SlidesInViewOptionsType\n): SlidesInViewType {\n  const intersectionEntryMap: IntersectionEntryMapType = {}\n  let inViewCache: number[] | null = null\n  let notInViewCache: number[] | null = null\n  let intersectionObserver: IntersectionObserver\n  let destroyed = false\n\n  function init(): void {\n    intersectionObserver = new IntersectionObserver(\n      (entries) => {\n        if (destroyed) return\n\n        entries.forEach((entry) => {\n          const index = slides.indexOf(<HTMLElement>entry.target)\n          intersectionEntryMap[index] = entry\n        })\n\n        inViewCache = null\n        notInViewCache = null\n        eventHandler.emit('slidesInView')\n      },\n      {\n        root: container.parentElement,\n        threshold\n      }\n    )\n\n    slides.forEach((slide) => intersectionObserver.observe(slide))\n  }\n\n  function destroy(): void {\n    if (intersectionObserver) intersectionObserver.disconnect()\n    destroyed = true\n  }\n\n  function createInViewList(inView: boolean): number[] {\n    return objectKeys(intersectionEntryMap).reduce(\n      (list: number[], slideIndex) => {\n        const index = parseInt(slideIndex)\n        const { isIntersecting } = intersectionEntryMap[index]\n        const inViewMatch = inView && isIntersecting\n        const notInViewMatch = !inView && !isIntersecting\n\n        if (inViewMatch || notInViewMatch) list.push(index)\n        return list\n      },\n      []\n    )\n  }\n\n  function get(inView: boolean = true): number[] {\n    if (inView && inViewCache) return inViewCache\n    if (!inView && notInViewCache) return notInViewCache\n\n    const slideIndexes = createInViewList(inView)\n\n    if (inView) inViewCache = slideIndexes\n    if (!inView) notInViewCache = slideIndexes\n\n    return slideIndexes\n  }\n\n  const self: SlidesInViewType = {\n    init,\n    destroy,\n    get\n  }\n\n  return self\n}\n", "import { AxisType } from './Axis'\nimport { NodeRectType } from './NodeRects'\nimport { arrayIsLastIndex, arrayLast, mathAbs, WindowType } from './utils'\n\nexport type SlideSizesType = {\n  slideSizes: number[]\n  slideSizesWithGaps: number[]\n  startGap: number\n  endGap: number\n}\n\nexport function SlideSizes(\n  axis: AxisType,\n  containerRect: NodeRectType,\n  slideRects: NodeRectType[],\n  slides: HTMLElement[],\n  readEdgeGap: boolean,\n  ownerWindow: WindowType\n): SlideSizesType {\n  const { measureSize, startEdge, endEdge } = axis\n  const withEdgeGap = slideRects[0] && readEdgeGap\n  const startGap = measureStartGap()\n  const endGap = measureEndGap()\n  const slideSizes = slideRects.map(measureSize)\n  const slideSizesWithGaps = measureWithGaps()\n\n  function measureStartGap(): number {\n    if (!withEdgeGap) return 0\n    const slideRect = slideRects[0]\n    return mathAbs(containerRect[startEdge] - slideRect[startEdge])\n  }\n\n  function measureEndGap(): number {\n    if (!withEdgeGap) return 0\n    const style = ownerWindow.getComputedStyle(arrayLast(slides))\n    return parseFloat(style.getPropertyValue(`margin-${endEdge}`))\n  }\n\n  function measureWithGaps(): number[] {\n    return slideRects\n      .map((rect, index, rects) => {\n        const isFirst = !index\n        const isLast = arrayIsLastIndex(rects, index)\n        if (isFirst) return slideSizes[index] + startGap\n        if (isLast) return slideSizes[index] + endGap\n        return rects[index + 1][startEdge] - rect[startEdge]\n      })\n      .map(mathAbs)\n  }\n\n  const self: SlideSizesType = {\n    slideSizes,\n    slideSizesWithGaps,\n    startGap,\n    endGap\n  }\n  return self\n}\n", "import { AxisType } from './Axis'\nimport { NodeRectType } from './NodeRects'\nimport {\n  arrayKeys,\n  arrayLast,\n  arrayLastIndex,\n  isNumber,\n  mathAbs\n} from './utils'\n\nexport type SlidesToScrollOptionType = 'auto' | number\n\nexport type SlidesToScrollType = {\n  groupSlides: <Type>(array: Type[]) => Type[][]\n}\n\nexport function SlidesToScroll(\n  axis: AxisType,\n  viewSize: number,\n  slidesToScroll: SlidesToScrollOptionType,\n  loop: boolean,\n  containerRect: NodeRectType,\n  slideRects: NodeRectType[],\n  startGap: number,\n  endGap: number,\n  pixelTolerance: number\n): SlidesToScrollType {\n  const { startEdge, endEdge, direction } = axis\n  const groupByNumber = isNumber(slidesToScroll)\n\n  function byNumber<Type>(array: Type[], groupSize: number): Type[][] {\n    return arrayKeys(array)\n      .filter((i) => i % groupSize === 0)\n      .map((i) => array.slice(i, i + groupSize))\n  }\n\n  function bySize<Type>(array: Type[]): Type[][] {\n    if (!array.length) return []\n\n    return arrayKeys(array)\n      .reduce((groups: number[], rectB, index) => {\n        const rectA = arrayLast(groups) || 0\n        const isFirst = rectA === 0\n        const isLast = rectB === arrayLastIndex(array)\n\n        const edgeA = containerRect[startEdge] - slideRects[rectA][startEdge]\n        const edgeB = containerRect[startEdge] - slideRects[rectB][endEdge]\n        const gapA = !loop && isFirst ? direction(startGap) : 0\n        const gapB = !loop && isLast ? direction(endGap) : 0\n        const chunkSize = mathAbs(edgeB - gapB - (edgeA + gapA))\n\n        if (index && chunkSize > viewSize + pixelTolerance) groups.push(rectB)\n        if (isLast) groups.push(array.length)\n        return groups\n      }, [])\n      .map((currentSize, index, groups) => {\n        const previousSize = Math.max(groups[index - 1] || 0)\n        return array.slice(previousSize, currentSize)\n      })\n  }\n\n  function groupSlides<Type>(array: Type[]): Type[][] {\n    return groupByNumber ? byNumber(array, slidesToScroll) : bySize(array)\n  }\n\n  const self: SlidesToScrollType = {\n    groupSlides\n  }\n  return self\n}\n", "import { Alignment } from './Alignment'\nimport {\n  Animations,\n  AnimationsType,\n  AnimationsUpdateType,\n  AnimationsRenderType\n} from './Animations'\nimport { Axis, AxisType } from './Axis'\nimport { Counter, CounterType } from './Counter'\nimport { DragHandler, DragHandlerType } from './DragHandler'\nimport { DragTracker } from './DragTracker'\nimport { EventHandlerType } from './EventHandler'\nimport { EventStore, EventStoreType } from './EventStore'\nimport { LimitType } from './Limit'\nimport { NodeRectType, NodeRects } from './NodeRects'\nimport { OptionsType } from './Options'\nimport { PercentOfView, PercentOfViewType } from './PercentOfView'\nimport { ResizeHandler, ResizeHandlerType } from './ResizeHandler'\nimport { ScrollBody, ScrollBodyType } from './ScrollBody'\nimport { ScrollBounds, ScrollBoundsType } from './ScrollBounds'\nimport { ScrollContain } from './ScrollContain'\nimport { ScrollLimit } from './ScrollLimit'\nimport { Sc<PERSON>Looper, ScrollLooperType } from './ScrollLooper'\nimport { ScrollProgress, ScrollProgressType } from './ScrollProgress'\nimport { ScrollSnaps } from './ScrollSnaps'\nimport { SlideRegistry, SlideRegistryType } from './SlideRegistry'\nimport { ScrollTarget, ScrollTargetType } from './ScrollTarget'\nimport { ScrollTo, ScrollToType } from './ScrollTo'\nimport { SlideFocus, SlideFocusType } from './SlideFocus'\nimport { SlideLooper, SlideLooperType } from './SlideLooper'\nimport { SlidesHandler, SlidesHandlerType } from './SlidesHandler'\nimport { SlidesInView, SlidesInViewType } from './SlidesInView'\nimport { SlideSizes } from './SlideSizes'\nimport { SlidesToScroll, SlidesToScrollType } from './SlidesToScroll'\nimport { Translate, TranslateType } from './Translate'\nimport { arrayKeys, arrayLast, arrayLastIndex, WindowType } from './utils'\nimport { Vector1D, Vector1DType } from './Vector1d'\n\nexport type EngineType = {\n  ownerDocument: Document\n  ownerWindow: WindowType\n  eventHandler: EventHandlerType\n  axis: AxisType\n  animation: AnimationsType\n  scrollBounds: ScrollBoundsType\n  scrollLooper: ScrollLooperType\n  scrollProgress: ScrollProgressType\n  index: CounterType\n  indexPrevious: CounterType\n  limit: LimitType\n  location: Vector1DType\n  offsetLocation: Vector1DType\n  previousLocation: Vector1DType\n  options: OptionsType\n  percentOfView: PercentOfViewType\n  scrollBody: ScrollBodyType\n  dragHandler: DragHandlerType\n  eventStore: EventStoreType\n  slideLooper: SlideLooperType\n  slidesInView: SlidesInViewType\n  slidesToScroll: SlidesToScrollType\n  target: Vector1DType\n  translate: TranslateType\n  resizeHandler: ResizeHandlerType\n  slidesHandler: SlidesHandlerType\n  scrollTo: ScrollToType\n  scrollTarget: ScrollTargetType\n  scrollSnapList: number[]\n  scrollSnaps: number[]\n  slideIndexes: number[]\n  slideFocus: SlideFocusType\n  slideRegistry: SlideRegistryType['slideRegistry']\n  containerRect: NodeRectType\n  slideRects: NodeRectType[]\n}\n\nexport function Engine(\n  root: HTMLElement,\n  container: HTMLElement,\n  slides: HTMLElement[],\n  ownerDocument: Document,\n  ownerWindow: WindowType,\n  options: OptionsType,\n  eventHandler: EventHandlerType\n): EngineType {\n  // Options\n  const {\n    align,\n    axis: scrollAxis,\n    direction,\n    startIndex,\n    loop,\n    duration,\n    dragFree,\n    dragThreshold,\n    inViewThreshold,\n    slidesToScroll: groupSlides,\n    skipSnaps,\n    containScroll,\n    watchResize,\n    watchSlides,\n    watchDrag,\n    watchFocus\n  } = options\n\n  // Measurements\n  const pixelTolerance = 2\n  const nodeRects = NodeRects()\n  const containerRect = nodeRects.measure(container)\n  const slideRects = slides.map(nodeRects.measure)\n  const axis = Axis(scrollAxis, direction)\n  const viewSize = axis.measureSize(containerRect)\n  const percentOfView = PercentOfView(viewSize)\n  const alignment = Alignment(align, viewSize)\n  const containSnaps = !loop && !!containScroll\n  const readEdgeGap = loop || !!containScroll\n  const { slideSizes, slideSizesWithGaps, startGap, endGap } = SlideSizes(\n    axis,\n    containerRect,\n    slideRects,\n    slides,\n    readEdgeGap,\n    ownerWindow\n  )\n  const slidesToScroll = SlidesToScroll(\n    axis,\n    viewSize,\n    groupSlides,\n    loop,\n    containerRect,\n    slideRects,\n    startGap,\n    endGap,\n    pixelTolerance\n  )\n  const { snaps, snapsAligned } = ScrollSnaps(\n    axis,\n    alignment,\n    containerRect,\n    slideRects,\n    slidesToScroll\n  )\n  const contentSize = -arrayLast(snaps) + arrayLast(slideSizesWithGaps)\n  const { snapsContained, scrollContainLimit } = ScrollContain(\n    viewSize,\n    contentSize,\n    snapsAligned,\n    containScroll,\n    pixelTolerance\n  )\n  const scrollSnaps = containSnaps ? snapsContained : snapsAligned\n  const { limit } = ScrollLimit(contentSize, scrollSnaps, loop)\n\n  // Indexes\n  const index = Counter(arrayLastIndex(scrollSnaps), startIndex, loop)\n  const indexPrevious = index.clone()\n  const slideIndexes = arrayKeys(slides)\n\n  // Animation\n  const update: AnimationsUpdateType = ({\n    dragHandler,\n    scrollBody,\n    scrollBounds,\n    options: { loop }\n  }) => {\n    if (!loop) scrollBounds.constrain(dragHandler.pointerDown())\n    scrollBody.seek()\n  }\n\n  const render: AnimationsRenderType = (\n    {\n      scrollBody,\n      translate,\n      location,\n      offsetLocation,\n      previousLocation,\n      scrollLooper,\n      slideLooper,\n      dragHandler,\n      animation,\n      eventHandler,\n      scrollBounds,\n      options: { loop }\n    },\n    alpha\n  ) => {\n    const shouldSettle = scrollBody.settled()\n    const withinBounds = !scrollBounds.shouldConstrain()\n    const hasSettled = loop ? shouldSettle : shouldSettle && withinBounds\n    const hasSettledAndIdle = hasSettled && !dragHandler.pointerDown()\n\n    if (hasSettledAndIdle) animation.stop()\n\n    const interpolatedLocation =\n      location.get() * alpha + previousLocation.get() * (1 - alpha)\n\n    offsetLocation.set(interpolatedLocation)\n\n    if (loop) {\n      scrollLooper.loop(scrollBody.direction())\n      slideLooper.loop()\n    }\n\n    translate.to(offsetLocation.get())\n\n    if (hasSettledAndIdle) eventHandler.emit('settle')\n    if (!hasSettled) eventHandler.emit('scroll')\n  }\n\n  const animation = Animations(\n    ownerDocument,\n    ownerWindow,\n    () => update(engine),\n    (alpha: number) => render(engine, alpha)\n  )\n\n  // Shared\n  const friction = 0.68\n  const startLocation = scrollSnaps[index.get()]\n  const location = Vector1D(startLocation)\n  const previousLocation = Vector1D(startLocation)\n  const offsetLocation = Vector1D(startLocation)\n  const target = Vector1D(startLocation)\n  const scrollBody = ScrollBody(\n    location,\n    offsetLocation,\n    previousLocation,\n    target,\n    duration,\n    friction\n  )\n  const scrollTarget = ScrollTarget(\n    loop,\n    scrollSnaps,\n    contentSize,\n    limit,\n    target\n  )\n  const scrollTo = ScrollTo(\n    animation,\n    index,\n    indexPrevious,\n    scrollBody,\n    scrollTarget,\n    target,\n    eventHandler\n  )\n  const scrollProgress = ScrollProgress(limit)\n  const eventStore = EventStore()\n  const slidesInView = SlidesInView(\n    container,\n    slides,\n    eventHandler,\n    inViewThreshold\n  )\n  const { slideRegistry } = SlideRegistry(\n    containSnaps,\n    containScroll,\n    scrollSnaps,\n    scrollContainLimit,\n    slidesToScroll,\n    slideIndexes\n  )\n  const slideFocus = SlideFocus(\n    root,\n    slides,\n    slideRegistry,\n    scrollTo,\n    scrollBody,\n    eventStore,\n    eventHandler,\n    watchFocus\n  )\n\n  // Engine\n  const engine: EngineType = {\n    ownerDocument,\n    ownerWindow,\n    eventHandler,\n    containerRect,\n    slideRects,\n    animation,\n    axis,\n    dragHandler: DragHandler(\n      axis,\n      root,\n      ownerDocument,\n      ownerWindow,\n      target,\n      DragTracker(axis, ownerWindow),\n      location,\n      animation,\n      scrollTo,\n      scrollBody,\n      scrollTarget,\n      index,\n      eventHandler,\n      percentOfView,\n      dragFree,\n      dragThreshold,\n      skipSnaps,\n      friction,\n      watchDrag\n    ),\n    eventStore,\n    percentOfView,\n    index,\n    indexPrevious,\n    limit,\n    location,\n    offsetLocation,\n    previousLocation,\n    options,\n    resizeHandler: ResizeHandler(\n      container,\n      eventHandler,\n      ownerWindow,\n      slides,\n      axis,\n      watchResize,\n      nodeRects\n    ),\n    scrollBody,\n    scrollBounds: ScrollBounds(\n      limit,\n      offsetLocation,\n      target,\n      scrollBody,\n      percentOfView\n    ),\n    scrollLooper: ScrollLooper(contentSize, limit, offsetLocation, [\n      location,\n      offsetLocation,\n      previousLocation,\n      target\n    ]),\n    scrollProgress,\n    scrollSnapList: scrollSnaps.map(scrollProgress.get),\n    scrollSnaps,\n    scrollTarget,\n    scrollTo,\n    slideLooper: SlideLooper(\n      axis,\n      viewSize,\n      contentSize,\n      slideSizes,\n      slideSizesWithGaps,\n      snaps,\n      scrollSnaps,\n      offsetLocation,\n      slides\n    ),\n    slideFocus,\n    slidesHandler: SlidesHandler(container, eventHandler, watchSlides),\n    slidesInView,\n    slideIndexes,\n    slideRegistry,\n    slidesToScroll,\n    target,\n    translate: Translate(axis, container)\n  }\n\n  return engine\n}\n", "import { EmblaCarouselType } from './EmblaCarousel'\n\ntype CallbackType = (emblaApi: EmblaCarouselType, evt: EmblaEventType) => void\ntype ListenersType = Partial<{ [key in EmblaEventType]: CallbackType[] }>\n\nexport type EmblaEventType = EmblaEventListType[keyof EmblaEventListType]\n\nexport interface EmblaEventListType {\n  init: 'init'\n  pointerDown: 'pointerDown'\n  pointerUp: 'pointerUp'\n  slidesChanged: 'slidesChanged'\n  slidesInView: 'slidesInView'\n  scroll: 'scroll'\n  select: 'select'\n  settle: 'settle'\n  destroy: 'destroy'\n  reInit: 'reInit'\n  resize: 'resize'\n  slideFocusStart: 'slideFocusStart'\n  slideFocus: 'slideFocus'\n}\n\nexport type EventHandlerType = {\n  init: (emblaApi: EmblaCarouselType) => void\n  emit: (evt: EmblaEventType) => EventHandlerType\n  on: (evt: EmblaEventType, cb: CallbackType) => EventHandlerType\n  off: (evt: EmblaEventType, cb: CallbackType) => EventHandlerType\n  clear: () => void\n}\n\nexport function EventHandler(): EventHandlerType {\n  let listeners: ListenersType = {}\n  let api: EmblaCarouselType\n\n  function init(emblaApi: EmblaCarouselType): void {\n    api = emblaApi\n  }\n\n  function getListeners(evt: EmblaEventType): CallbackType[] {\n    return listeners[evt] || []\n  }\n\n  function emit(evt: EmblaEventType): EventHandlerType {\n    getListeners(evt).forEach((e) => e(api, evt))\n    return self\n  }\n\n  function on(evt: EmblaEventType, cb: CallbackType): EventHandlerType {\n    listeners[evt] = getListeners(evt).concat([cb])\n    return self\n  }\n\n  function off(evt: EmblaEventType, cb: CallbackType): EventHandlerType {\n    listeners[evt] = getListeners(evt).filter((e) => e !== cb)\n    return self\n  }\n\n  function clear(): void {\n    listeners = {}\n  }\n\n  const self: EventHandlerType = {\n    init,\n    emit,\n    off,\n    on,\n    clear\n  }\n  return self\n}\n", "import { AlignmentOptionType } from './Alignment'\nimport { AxisDirectionOptionType, AxisOptionType } from './Axis'\nimport { SlidesToScrollOptionType } from './SlidesToScroll'\nimport { ScrollContainOptionType } from './ScrollContain'\nimport { DragHandlerOptionType } from './DragHandler'\nimport { ResizeHandlerOptionType } from './ResizeHandler'\nimport { SlidesHandlerOptionType } from './SlidesHandler'\nimport { SlidesInViewOptionsType } from './SlidesInView'\nimport { FocusHandlerOptionType } from './SlideFocus'\n\nexport type LooseOptionsType = {\n  [key: string]: unknown\n}\n\nexport type CreateOptionsType<Type extends LooseOptionsType> = Type & {\n  active: boolean\n  breakpoints: {\n    [key: string]: Omit<Partial<CreateOptionsType<Type>>, 'breakpoints'>\n  }\n}\n\nexport type OptionsType = CreateOptionsType<{\n  align: AlignmentOptionType\n  axis: AxisOptionType\n  container: string | HTMLElement | null\n  slides: string | HTMLElement[] | NodeListOf<HTMLElement> | null\n  containScroll: ScrollContainOptionType\n  direction: AxisDirectionOptionType\n  slidesToScroll: SlidesToScrollOptionType\n  dragFree: boolean\n  dragThreshold: number\n  inViewThreshold: SlidesInViewOptionsType\n  loop: boolean\n  skipSnaps: boolean\n  duration: number\n  startIndex: number\n  watchDrag: DragHandlerOptionType\n  watchResize: ResizeHandlerOptionType\n  watchSlides: SlidesHandlerOptionType\n  watchFocus: FocusHandlerOptionType\n}>\n\nexport const defaultOptions: OptionsType = {\n  align: 'center',\n  axis: 'x',\n  container: null,\n  slides: null,\n  containScroll: 'trimSnaps',\n  direction: 'ltr',\n  slidesToScroll: 1,\n  inViewThreshold: 0,\n  breakpoints: {},\n  dragFree: false,\n  dragThreshold: 10,\n  loop: false,\n  skipSnaps: false,\n  duration: 25,\n  startIndex: 0,\n  active: true,\n  watchDrag: true,\n  watchResize: true,\n  watchSlides: true,\n  watchFocus: true\n}\n\nexport type EmblaOptionsType = Partial<OptionsType>\n", "import { LooseOptionsType, CreateOptionsType } from './Options'\nimport { objectKeys, objectsMergeDeep, WindowType } from './utils'\n\ntype OptionsType = Partial<CreateOptionsType<LooseOptionsType>>\n\nexport type OptionsHandlerType = {\n  mergeOptions: <TypeA extends OptionsType, TypeB extends OptionsType>(\n    optionsA: TypeA,\n    optionsB?: TypeB\n  ) => TypeA\n  optionsAtMedia: <Type extends OptionsType>(options: Type) => Type\n  optionsMediaQueries: (optionsList: OptionsType[]) => MediaQueryList[]\n}\n\nexport function OptionsHandler(ownerWindow: WindowType): OptionsHandlerType {\n  function mergeOptions<TypeA extends OptionsType, TypeB extends OptionsType>(\n    optionsA: TypeA,\n    optionsB?: TypeB\n  ): TypeA {\n    return <TypeA>objectsMergeDeep(optionsA, optionsB || {})\n  }\n\n  function optionsAtMedia<Type extends OptionsType>(options: Type): Type {\n    const optionsAtMedia = options.breakpoints || {}\n    const matchedMediaOptions = objectKeys(optionsAtMedia)\n      .filter((media) => ownerWindow.matchMedia(media).matches)\n      .map((media) => optionsAtMedia[media])\n      .reduce((a, mediaOption) => mergeOptions(a, mediaOption), {})\n\n    return mergeOptions(options, matchedMediaOptions)\n  }\n\n  function optionsMediaQueries(optionsList: OptionsType[]): MediaQueryList[] {\n    return optionsList\n      .map((options) => objectKeys(options.breakpoints || {}))\n      .reduce((acc, mediaQueries) => acc.concat(mediaQueries), [])\n      .map(ownerWindow.matchMedia)\n  }\n\n  const self: OptionsHandlerType = {\n    mergeOptions,\n    optionsAtMedia,\n    optionsMediaQueries\n  }\n  return self\n}\n", "import { EmblaCarouselType } from './EmblaCarousel'\nimport { OptionsHandlerType } from './OptionsHandler'\nimport { EmblaPluginsType, EmblaPluginType } from './Plugins'\n\nexport type PluginsHandlerType = {\n  init: (\n    emblaApi: EmblaCarouselType,\n    plugins: EmblaPluginType[]\n  ) => EmblaPluginsType\n  destroy: () => void\n}\n\nexport function PluginsHandler(\n  optionsHandler: OptionsHandlerType\n): PluginsHandlerType {\n  let activePlugins: EmblaPluginType[] = []\n\n  function init(\n    emblaApi: EmblaCarouselType,\n    plugins: EmblaPluginType[]\n  ): EmblaPluginsType {\n    activePlugins = plugins.filter(\n      ({ options }) => optionsHandler.optionsAtMedia(options).active !== false\n    )\n    activePlugins.forEach((plugin) => plugin.init(emblaApi, optionsHandler))\n\n    return plugins.reduce(\n      (map, plugin) => Object.assign(map, { [plugin.name]: plugin }),\n      {}\n    )\n  }\n\n  function destroy(): void {\n    activePlugins = activePlugins.filter((plugin) => plugin.destroy())\n  }\n\n  const self: PluginsHandlerType = {\n    init,\n    destroy\n  }\n  return self\n}\n", "import { Engine, EngineType } from './Engine'\nimport { EventStore } from './EventStore'\nimport { EventHandler, EventHandlerType } from './EventHandler'\nimport { defaultOptions, EmblaOptionsType, OptionsType } from './Options'\nimport { OptionsHandler } from './OptionsHandler'\nimport { PluginsHandler } from './PluginsHandler'\nimport { EmblaPluginsType, EmblaPluginType } from './Plugins'\nimport { isString, WindowType } from './utils'\n\nexport type EmblaCarouselType = {\n  canScrollNext: () => boolean\n  canScrollPrev: () => boolean\n  containerNode: () => HTMLElement\n  internalEngine: () => EngineType\n  destroy: () => void\n  off: EventHandlerType['off']\n  on: EventHandlerType['on']\n  emit: EventHandlerType['emit']\n  plugins: () => EmblaPluginsType\n  previousScrollSnap: () => number\n  reInit: (options?: EmblaOptionsType, plugins?: EmblaPluginType[]) => void\n  rootNode: () => HTMLElement\n  scrollNext: (jump?: boolean) => void\n  scrollPrev: (jump?: boolean) => void\n  scrollProgress: () => number\n  scrollSnapList: () => number[]\n  scrollTo: (index: number, jump?: boolean) => void\n  selectedScrollSnap: () => number\n  slideNodes: () => HTMLElement[]\n  slidesInView: () => number[]\n  slidesNotInView: () => number[]\n}\n\nfunction EmblaCarousel(\n  root: HTMLElement,\n  userOptions?: EmblaOptionsType,\n  userPlugins?: EmblaPluginType[]\n): EmblaCarouselType {\n  const ownerDocument = root.ownerDocument\n  const ownerWindow = <WindowType>ownerDocument.defaultView\n  const optionsHandler = OptionsHandler(ownerWindow)\n  const pluginsHandler = PluginsHandler(optionsHandler)\n  const mediaHandlers = EventStore()\n  const eventHandler = EventHandler()\n  const { mergeOptions, optionsAtMedia, optionsMediaQueries } = optionsHandler\n  const { on, off, emit } = eventHandler\n  const reInit = reActivate\n\n  let destroyed = false\n  let engine: EngineType\n  let optionsBase = mergeOptions(defaultOptions, EmblaCarousel.globalOptions)\n  let options = mergeOptions(optionsBase)\n  let pluginList: EmblaPluginType[] = []\n  let pluginApis: EmblaPluginsType\n\n  let container: HTMLElement\n  let slides: HTMLElement[]\n\n  function storeElements(): void {\n    const { container: userContainer, slides: userSlides } = options\n\n    const customContainer = isString(userContainer)\n      ? root.querySelector(userContainer)\n      : userContainer\n    container = <HTMLElement>(customContainer || root.children[0])\n\n    const customSlides = isString(userSlides)\n      ? container.querySelectorAll(userSlides)\n      : userSlides\n    slides = <HTMLElement[]>[].slice.call(customSlides || container.children)\n  }\n\n  function createEngine(options: OptionsType): EngineType {\n    const engine = Engine(\n      root,\n      container,\n      slides,\n      ownerDocument,\n      ownerWindow,\n      options,\n      eventHandler\n    )\n\n    if (options.loop && !engine.slideLooper.canLoop()) {\n      const optionsWithoutLoop = Object.assign({}, options, { loop: false })\n      return createEngine(optionsWithoutLoop)\n    }\n    return engine\n  }\n\n  function activate(\n    withOptions?: EmblaOptionsType,\n    withPlugins?: EmblaPluginType[]\n  ): void {\n    if (destroyed) return\n\n    optionsBase = mergeOptions(optionsBase, withOptions)\n    options = optionsAtMedia(optionsBase)\n    pluginList = withPlugins || pluginList\n\n    storeElements()\n\n    engine = createEngine(options)\n\n    optionsMediaQueries([\n      optionsBase,\n      ...pluginList.map(({ options }) => options)\n    ]).forEach((query) => mediaHandlers.add(query, 'change', reActivate))\n\n    if (!options.active) return\n\n    engine.translate.to(engine.location.get())\n    engine.animation.init()\n    engine.slidesInView.init()\n    engine.slideFocus.init(self)\n    engine.eventHandler.init(self)\n    engine.resizeHandler.init(self)\n    engine.slidesHandler.init(self)\n\n    if (engine.options.loop) engine.slideLooper.loop()\n    if (container.offsetParent && slides.length) engine.dragHandler.init(self)\n\n    pluginApis = pluginsHandler.init(self, pluginList)\n  }\n\n  function reActivate(\n    withOptions?: EmblaOptionsType,\n    withPlugins?: EmblaPluginType[]\n  ): void {\n    const startIndex = selectedScrollSnap()\n    deActivate()\n    activate(mergeOptions({ startIndex }, withOptions), withPlugins)\n    eventHandler.emit('reInit')\n  }\n\n  function deActivate(): void {\n    engine.dragHandler.destroy()\n    engine.eventStore.clear()\n    engine.translate.clear()\n    engine.slideLooper.clear()\n    engine.resizeHandler.destroy()\n    engine.slidesHandler.destroy()\n    engine.slidesInView.destroy()\n    engine.animation.destroy()\n    pluginsHandler.destroy()\n    mediaHandlers.clear()\n  }\n\n  function destroy(): void {\n    if (destroyed) return\n    destroyed = true\n    mediaHandlers.clear()\n    deActivate()\n    eventHandler.emit('destroy')\n    eventHandler.clear()\n  }\n\n  function scrollTo(index: number, jump?: boolean, direction?: number): void {\n    if (!options.active || destroyed) return\n    engine.scrollBody\n      .useBaseFriction()\n      .useDuration(jump === true ? 0 : options.duration)\n    engine.scrollTo.index(index, direction || 0)\n  }\n\n  function scrollNext(jump?: boolean): void {\n    const next = engine.index.add(1).get()\n    scrollTo(next, jump, -1)\n  }\n\n  function scrollPrev(jump?: boolean): void {\n    const prev = engine.index.add(-1).get()\n    scrollTo(prev, jump, 1)\n  }\n\n  function canScrollNext(): boolean {\n    const next = engine.index.add(1).get()\n    return next !== selectedScrollSnap()\n  }\n\n  function canScrollPrev(): boolean {\n    const prev = engine.index.add(-1).get()\n    return prev !== selectedScrollSnap()\n  }\n\n  function scrollSnapList(): number[] {\n    return engine.scrollSnapList\n  }\n\n  function scrollProgress(): number {\n    return engine.scrollProgress.get(engine.offsetLocation.get())\n  }\n\n  function selectedScrollSnap(): number {\n    return engine.index.get()\n  }\n\n  function previousScrollSnap(): number {\n    return engine.indexPrevious.get()\n  }\n\n  function slidesInView(): number[] {\n    return engine.slidesInView.get()\n  }\n\n  function slidesNotInView(): number[] {\n    return engine.slidesInView.get(false)\n  }\n\n  function plugins(): EmblaPluginsType {\n    return pluginApis\n  }\n\n  function internalEngine(): EngineType {\n    return engine\n  }\n\n  function rootNode(): HTMLElement {\n    return root\n  }\n\n  function containerNode(): HTMLElement {\n    return container\n  }\n\n  function slideNodes(): HTMLElement[] {\n    return slides\n  }\n\n  const self: EmblaCarouselType = {\n    canScrollNext,\n    canScrollPrev,\n    containerNode,\n    internalEngine,\n    destroy,\n    off,\n    on,\n    emit,\n    plugins,\n    previousScrollSnap,\n    reInit,\n    rootNode,\n    scrollNext,\n    scrollPrev,\n    scrollProgress,\n    scrollSnapList,\n    scrollTo,\n    selectedScrollSnap,\n    slideNodes,\n    slidesInView,\n    slidesNotInView\n  }\n\n  activate(userOptions, userPlugins)\n  setTimeout(() => eventHandler.emit('init'), 0)\n  return self\n}\n\ndeclare namespace EmblaCarousel {\n  let globalOptions: EmblaOptionsType | undefined\n}\n\nEmblaCarousel.globalOptions = undefined\n\nexport default EmblaCarousel\n"], "names": ["isNumber", "subject", "isString", "isBoolean", "isObject", "Object", "prototype", "toString", "call", "mathAbs", "n", "Math", "abs", "mathSign", "sign", "deltaAbs", "valueB", "valueA", "factorAbs", "diff", "roundToTwoDecimals", "num", "round", "arrayKeys", "array", "objectKeys", "map", "Number", "arrayLast", "arrayLastIndex", "max", "length", "arrayIsLastIndex", "index", "arrayFromNumber", "startAt", "Array", "from", "_", "i", "object", "keys", "objectsMergeDeep", "objectA", "objectB", "reduce", "mergedObjects", "currentObject", "for<PERSON>ach", "key", "areObjects", "isMouseEvent", "evt", "ownerWindow", "MouseEvent", "Alignment", "align", "viewSize", "predefined", "start", "center", "end", "measure", "self", "EventStore", "listeners", "add", "node", "type", "handler", "options", "passive", "removeListener", "addEventListener", "removeEventListener", "legacyMediaQueryList", "addListener", "push", "clear", "filter", "remove", "Animations", "ownerDocument", "update", "render", "documentVisibleHandler", "fixedTimeStep", "lastTimeStamp", "accumulatedTime", "animationId", "init", "hidden", "reset", "destroy", "stop", "animate", "timeStamp", "timeElapsed", "alpha", "requestAnimationFrame", "cancelAnimationFrame", "Axis", "axis", "contentDirection", "isRightToLeft", "isVertical", "scroll", "cross", "startEdge", "getStartEdge", "endEdge", "getEndEdge", "measureSize", "nodeRect", "height", "width", "direction", "Limit", "min", "reachedMin", "reachedMax", "reachedAny", "constrain", "removeOffset", "ceil", "Counter", "loop", "loopEnd", "counter", "withinLimit", "get", "set", "clone", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "rootNode", "target", "dragTracker", "location", "animation", "scrollTo", "scrollBody", "scrollTarget", "<PERSON><PERSON><PERSON><PERSON>", "percentOfView", "dragFree", "drag<PERSON><PERSON><PERSON><PERSON>", "skipSnaps", "baseFriction", "watchDrag", "crossAxis", "focusNodes", "nonPassiveEvent", "initEvents", "dragEvents", "goToNextThreshold", "snapForceBoost", "mouse", "touch", "freeForceBoost", "baseSpeed", "isMoving", "startScroll", "startCross", "pointerIsDown", "preventScroll", "preventClick", "isMouse", "emblaApi", "downIfAllowed", "down", "preventDefault", "undefined", "up", "click", "addDragEvents", "move", "isFocusNode", "nodeName", "includes", "forceBoost", "boost", "<PERSON><PERSON><PERSON><PERSON>", "force", "targetChanged", "next", "baseForce", "byDistance", "distance", "byIndex", "isMouseEvt", "buttons", "button", "pointerDown", "useFriction", "useDuration", "readPoint", "emit", "isTouchEvt", "touches", "lastScroll", "lastCross", "diffScroll", "diffCross", "cancelable", "pointer<PERSON><PERSON>", "currentLocation", "rawForce", "pointerUp", "forceFactor", "speed", "friction", "stopPropagation", "DragTracker", "logInterval", "startEvent", "lastEvent", "readTime", "evtAxis", "property", "coord", "expired", "diffDrag", "diffTime", "isFlick", "NodeRects", "offsetTop", "offsetLeft", "offsetWidth", "offsetHeight", "offset", "top", "right", "bottom", "left", "PercentOfView", "ResizeHandler", "container", "slides", "watchResize", "nodeRects", "observeNodes", "concat", "resizeObserver", "containerSize", "slideSizes", "destroyed", "readSize", "defaultCallback", "entries", "entry", "<PERSON><PERSON><PERSON><PERSON>", "slideIndex", "indexOf", "lastSize", "newSize", "diffSize", "reInit", "ResizeObserver", "observe", "disconnect", "ScrollBody", "offsetLocation", "previousLocation", "baseDuration", "scrollVelocity", "scrollDirection", "scrollDuration", "scrollFriction", "rawLocation", "rawLocationPrevious", "seek", "displacement", "isInstant", "scrollDistance", "settled", "duration", "velocity", "useBaseDuration", "useBaseFriction", "ScrollBounds", "limit", "pullBackThreshold", "edgeOffsetTolerance", "frictionLimit", "disabled", "shouldConstrain", "edge", "diffToEdge", "diffTo<PERSON>arget", "subtract", "toggleActive", "active", "ScrollContain", "contentSize", "snapsAligned", "containScroll", "pixelTolerance", "scrollBounds", "snapsBounded", "measureBounded", "scrollContainLimit", "findScrollContainLimit", "snapsContained", "measureContained", "usePixelTolerance", "bound", "snap", "startSnap", "endSnap", "lastIndexOf", "snapAligned", "<PERSON><PERSON><PERSON><PERSON>", "isLast", "scrollBound", "parseFloat", "toFixed", "slice", "ScrollLimit", "scrollSnaps", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "vectors", "jointSafety", "shouldLoop", "loopDistance", "v", "ScrollProgress", "ScrollSnaps", "alignment", "containerRect", "slideRects", "slidesToScroll", "groupSlides", "alignments", "measureSizes", "snaps", "measureUnaligned", "measureAligned", "rects", "rect", "g", "SlideRegistry", "containSnaps", "slideIndexes", "slideRegistry", "createSlideRegistry", "groupedSlideIndexes", "doNotContain", "group", "groups", "range", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "targetVector", "minDistance", "distances", "sort", "a", "b", "findTargetSnap", "ascDiffsToSnaps", "shortcut", "d1", "d2", "targets", "matchingTargets", "t", "diffToSnap", "targetSnapDistance", "reachedBound", "snapDistance", "ScrollTo", "indexCurrent", "indexPrevious", "distanceDiff", "indexDiff", "targetIndex", "SlideFocus", "root", "eventStore", "watchFocus", "focusListenerOptions", "capture", "lastTabPressTime", "nowTime", "Date", "getTime", "scrollLeft", "findIndex", "document", "registerTabPress", "slide", "event", "code", "Vector1D", "initialValue", "value", "normalizeInput", "Translate", "translate", "x", "y", "containerStyle", "style", "previousTarget", "to", "newTarget", "transform", "getAttribute", "removeAttribute", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "slideSizesWithGaps", "roundingSafety", "ascItems", "descItems", "reverse", "loopPoints", "startPoints", "endPoints", "removeSlideSizes", "indexes", "slidesInGap", "gap", "remainingGap", "findSlideBounds", "findLoopPoints", "isEndEdge", "slideBounds", "initial", "altered", "boundEdge", "loopPoint", "slideLocation", "canLoop", "every", "otherIndexes", "shiftLocation", "SlidesHandler", "watchSlides", "mutationObserver", "mutations", "mutation", "MutationObserver", "childList", "SlidesInView", "threshold", "intersectionEntryMap", "inView<PERSON>ache", "notInViewCache", "intersectionObserver", "IntersectionObserver", "parentElement", "createInViewList", "inView", "list", "parseInt", "isIntersecting", "inViewMatch", "notInViewMatch", "SlideSizes", "readEdgeGap", "withEdgeGap", "startGap", "measureStartGap", "endGap", "measureEndGap", "measureWithGaps", "slideRect", "getComputedStyle", "getPropertyValue", "SlidesToScroll", "groupByNumber", "byNumber", "groupSize", "bySize", "rectB", "rectA", "edgeA", "edgeB", "gapA", "gapB", "chunkSize", "currentSize", "previousSize", "Engine", "scrollAxis", "startIndex", "inViewThreshold", "<PERSON><PERSON><PERSON><PERSON>", "scrollLooper", "slideLooper", "<PERSON><PERSON><PERSON><PERSON>", "withinBounds", "hasSettled", "hasSettledAndIdle", "interpolatedLocation", "engine", "startLocation", "scrollProgress", "slidesInView", "slideFocus", "resize<PERSON><PERSON>ler", "scrollSnapList", "<PERSON><PERSON><PERSON>ler", "EventHandler", "api", "getListeners", "e", "on", "cb", "off", "defaultOptions", "breakpoints", "OptionsHandler", "mergeOptions", "optionsA", "optionsB", "optionsAtMedia", "matchedMediaOptions", "media", "matchMedia", "matches", "mediaOption", "optionsMediaQueries", "optionsList", "acc", "mediaQueries", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "optionsHandler", "activePlugins", "plugins", "plugin", "assign", "name", "EmblaCarousel", "userOptions", "userPlugins", "defaultView", "pluginsHandler", "mediaHandlers", "reActivate", "optionsBase", "globalOptions", "pluginList", "pluginApis", "storeElements", "userContainer", "userSlides", "customContainer", "querySelector", "children", "customSlides", "querySelectorAll", "createEngine", "optionsWithoutLoop", "activate", "withOptions", "with<PERSON><PERSON><PERSON>", "query", "offsetParent", "selectedScrollSnap", "deActivate", "jump", "scrollNext", "scrollPrev", "prev", "canScrollNext", "canScrollPrev", "previousScrollSnap", "slidesNotInView", "internalEngine", "containerNode", "slideNodes", "setTimeout"], "mappings": ";;;AAIM,SAAUA,QAAQA,CAACC,OAAgB,EAAA;IACvC,OAAO,OAAOA,OAAO,KAAK,QAAQ;AACpC;AAEM,SAAUC,QAAQA,CAACD,OAAgB,EAAA;IACvC,OAAO,OAAOA,OAAO,KAAK,QAAQ;AACpC;AAEM,SAAUE,SAASA,CAACF,OAAgB,EAAA;IACxC,OAAO,OAAOA,OAAO,KAAK,SAAS;AACrC;AAEM,SAAUG,QAAQA,CAACH,OAAgB,EAAA;IACvC,OAAOI,MAAM,CAACC,SAAS,CAACC,QAAQ,CAACC,IAAI,CAACP,OAAO,CAAC,KAAK,iBAAiB;AACtE;AAEM,SAAUQ,OAAOA,CAACC,CAAS,EAAA;IAC/B,OAAOC,IAAI,CAACC,GAAG,CAACF,CAAC,CAAC;AACpB;AAEM,SAAUG,QAAQA,CAACH,CAAS,EAAA;IAChC,OAAOC,IAAI,CAACG,IAAI,CAACJ,CAAC,CAAC;AACrB;AAEgB,SAAAK,QAAQA,CAACC,MAAc,EAAEC,MAAc,EAAA;IACrD,OAAOR,OAAO,CAACO,MAAM,GAAGC,MAAM,CAAC;AACjC;AAEgB,SAAAC,SAASA,CAACF,MAAc,EAAEC,MAAc,EAAA;IACtD,IAAID,MAAM,KAAK,CAAC,IAAIC,MAAM,KAAK,CAAC,EAAE,OAAO,CAAC;IAC1C,IAAIR,OAAO,CAACO,MAAM,CAAC,IAAIP,OAAO,CAACQ,MAAM,CAAC,EAAE,OAAO,CAAC;IAChD,MAAME,IAAI,GAAGJ,QAAQ,CAACN,OAAO,CAACO,MAAM,CAAC,EAAEP,OAAO,CAACQ,MAAM,CAAC,CAAC;IACvD,OAAOR,OAAO,CAACU,IAAI,GAAGH,MAAM,CAAC;AAC/B;AAEM,SAAUI,kBAAkBA,CAACC,GAAW,EAAA;IAC5C,OAAOV,IAAI,CAACW,KAAK,CAACD,GAAG,GAAG,GAAG,CAAC,GAAG,GAAG;AACpC;AAEM,SAAUE,SAASA,CAAOC,KAAa,EAAA;IAC3C,OAAOC,UAAU,CAACD,KAAK,CAAC,CAACE,GAAG,CAACC,MAAM,CAAC;AACtC;AAEM,SAAUC,SAASA,CAAOJ,KAAa,EAAA;IAC3C,OAAOA,KAAK,CAACK,cAAc,CAACL,KAAK,CAAC,CAAC;AACrC;AAEM,SAAUK,cAAcA,CAAOL,KAAa,EAAA;IAChD,OAAOb,IAAI,CAACmB,GAAG,CAAC,CAAC,EAAEN,KAAK,CAACO,MAAM,GAAG,CAAC,CAAC;AACtC;AAEgB,SAAAC,gBAAgBA,CAAOR,KAAa,EAAES,KAAa,EAAA;IACjE,OAAOA,KAAK,KAAKJ,cAAc,CAACL,KAAK,CAAC;AACxC;SAEgBU,eAAeA,CAACxB,CAAS,EAAEyB,UAAkB,CAAC,EAAA;IAC5D,OAAOC,KAAK,CAACC,IAAI,CAACD,KAAK,CAAC1B,CAAC,CAAC,EAAE,CAAC4B,CAAC,EAAEC,CAAC,GAAKJ,OAAO,GAAGI,CAAC,CAAC;AACpD;AAEM,SAAUd,UAAUA,CAAsBe,MAAY,EAAA;IAC1D,OAAOnC,MAAM,CAACoC,IAAI,CAACD,MAAM,CAAC;AAC5B;AAEgB,SAAAE,gBAAgBA,CAC9BC,OAAgC,EAChCC,OAAgC,EAAA;IAEhC,OAAO;QAACD,OAAO;QAAEC,OAAO;KAAC,CAACC,MAAM,CAAC,CAACC,aAAa,EAAEC,aAAa,KAAI;QAChEtB,UAAU,CAACsB,aAAa,CAAC,CAACC,OAAO,EAAEC,GAAG,IAAI;YACxC,MAAMhC,MAAM,GAAG6B,aAAa,CAACG,GAAG,CAAC;YACjC,MAAMjC,MAAM,GAAG+B,aAAa,CAACE,GAAG,CAAC;YACjC,MAAMC,UAAU,GAAG9C,QAAQ,CAACa,MAAM,CAAC,IAAIb,QAAQ,CAACY,MAAM,CAAC;YAEvD8B,aAAa,CAACG,GAAG,CAAC,GAAGC,UAAU,GAC3BR,gBAAgB,CAACzB,MAAM,EAAED,MAAM,CAAC,GAChCA,MAAM;QACZ,CAAC,CAAC;QACF,OAAO8B,aAAa;KACrB,EAAE,CAAA,CAAE,CAAC;AACR;AAEgB,SAAAK,YAAYA,CAC1BC,GAAqB,EACrBC,WAAuB,EAAA;IAEvB,OACE,OAAOA,WAAW,CAACC,UAAU,KAAK,WAAW,IAC7CF,GAAG,YAAYC,WAAW,CAACC,UAAU;AAEzC;ACjFgB,SAAAC,SAASA,CACvBC,KAA0B,EAC1BC,QAAgB,EAAA;IAEhB,MAAMC,UAAU,GAAG;QAAEC,KAAK;QAAEC,MAAM;QAAEC;KAAK;IAEzC,SAASF,KAAKA,GAAA;QACZ,OAAO,CAAC;IACV;IAEA,SAASC,MAAMA,CAAClD,CAAS,EAAA;QACvB,OAAOmD,GAAG,CAACnD,CAAC,CAAC,GAAG,CAAC;IACnB;IAEA,SAASmD,GAAGA,CAACnD,CAAS,EAAA;QACpB,OAAO+C,QAAQ,GAAG/C,CAAC;IACrB;IAEA,SAASoD,OAAOA,CAACpD,CAAS,EAAEuB,KAAa,EAAA;QACvC,IAAI/B,QAAQ,CAACsD,KAAK,CAAC,EAAE,OAAOE,UAAU,CAACF,KAAK,CAAC,CAAC9C,CAAC,CAAC;QAChD,OAAO8C,KAAK,CAACC,QAAQ,EAAE/C,CAAC,EAAEuB,KAAK,CAAC;IAClC;IAEA,MAAM8B,IAAI,GAAkB;QAC1BD;KACD;IACD,OAAOC,IAAI;AACb;SCxBgBC,UAAUA,GAAA;IACxB,IAAIC,SAAS,GAAuB,EAAE;IAEtC,SAASC,GAAGA,CACVC,IAAiB,EACjBC,IAAmB,EACnBC,OAAyB,EACzBC,OAA4B,GAAA;QAAEC,OAAO,EAAE;IAAM,CAAA,EAAA;QAE7C,IAAIC,cAAgC;QAEpC,IAAI,kBAAkB,IAAIL,IAAI,EAAE;YAC9BA,IAAI,CAACM,gBAAgB,CAACL,IAAI,EAAEC,OAAO,EAAEC,OAAO,CAAC;YAC7CE,cAAc,GAAGA,IAAML,IAAI,CAACO,mBAAmB,CAACN,IAAI,EAAEC,OAAO,EAAEC,OAAO,CAAC;QACzE,CAAC,MAAM;YACL,MAAMK,oBAAoB,GAAmBR,IAAI;YACjDQ,oBAAoB,CAACC,WAAW,CAACP,OAAO,CAAC;YACzCG,cAAc,GAAGA,IAAMG,oBAAoB,CAACH,cAAc,CAACH,OAAO,CAAC;QACrE;QAEAJ,SAAS,CAACY,IAAI,CAACL,cAAc,CAAC;QAC9B,OAAOT,IAAI;IACb;IAEA,SAASe,KAAKA,GAAA;QACZb,SAAS,GAAGA,SAAS,CAACc,MAAM,EAAEC,MAAM,GAAKA,MAAM,EAAE,CAAC;IACpD;IAEA,MAAMjB,IAAI,GAAmB;QAC3BG,GAAG;QACHY;KACD;IACD,OAAOf,IAAI;AACb;AChCM,SAAUkB,UAAUA,CACxBC,aAAuB,EACvB7B,WAAuB,EACvB8B,MAAkB,EAClBC,MAA+B,EAAA;IAE/B,MAAMC,sBAAsB,GAAGrB,UAAU,EAAE;IAC3C,MAAMsB,aAAa,GAAG,IAAI,GAAG,EAAE;IAE/B,IAAIC,aAAa,GAAkB,IAAI;IACvC,IAAIC,eAAe,GAAG,CAAC;IACvB,IAAIC,WAAW,GAAG,CAAC;IAEnB,SAASC,IAAIA,GAAA;QACXL,sBAAsB,CAACnB,GAAG,CAACgB,aAAa,EAAE,kBAAkB,EAAE,MAAK;YACjE,IAAIA,aAAa,CAACS,MAAM,EAAEC,KAAK,EAAE;QACnC,CAAC,CAAC;IACJ;IAEA,SAASC,OAAOA,GAAA;QACdC,IAAI,EAAE;QACNT,sBAAsB,CAACP,KAAK,EAAE;IAChC;IAEA,SAASiB,OAAOA,CAACC,SAA8B,EAAA;QAC7C,IAAI,CAACP,WAAW,EAAE;QAClB,IAAI,CAACF,aAAa,EAAE;YAClBA,aAAa,GAAGS,SAAS;YACzBb,MAAM,EAAE;YACRA,MAAM,EAAE;QACV;QAEA,MAAMc,WAAW,GAAGD,SAAS,GAAGT,aAAa;QAC7CA,aAAa,GAAGS,SAAS;QACzBR,eAAe,IAAIS,WAAW;QAE9B,MAAOT,eAAe,IAAIF,aAAa,CAAE;YACvCH,MAAM,EAAE;YACRK,eAAe,IAAIF,aAAa;QAClC;QAEA,MAAMY,KAAK,GAAGV,eAAe,GAAGF,aAAa;QAC7CF,MAAM,CAACc,KAAK,CAAC;QAEb,IAAIT,WAAW,EAAE;YACfA,WAAW,GAAGpC,WAAW,CAAC8C,qBAAqB,CAACJ,OAAO,CAAC;QAC1D;IACF;IAEA,SAASpC,KAAKA,GAAA;QACZ,IAAI8B,WAAW,EAAE;QACjBA,WAAW,GAAGpC,WAAW,CAAC8C,qBAAqB,CAACJ,OAAO,CAAC;IAC1D;IAEA,SAASD,IAAIA,GAAA;QACXzC,WAAW,CAAC+C,oBAAoB,CAACX,WAAW,CAAC;QAC7CF,aAAa,GAAG,IAAI;QACpBC,eAAe,GAAG,CAAC;QACnBC,WAAW,GAAG,CAAC;IACjB;IAEA,SAASG,KAAKA,GAAA;QACZL,aAAa,GAAG,IAAI;QACpBC,eAAe,GAAG,CAAC;IACrB;IAEA,MAAMzB,IAAI,GAAmB;QAC3B2B,IAAI;QACJG,OAAO;QACPlC,KAAK;QACLmC,IAAI;QACJX,MAAM;QACNC;KACD;IACD,OAAOrB,IAAI;AACb;AC5EgB,SAAAsC,IAAIA,CAClBC,IAAoB,EACpBC,gBAAyC,EAAA;IAEzC,MAAMC,aAAa,GAAGD,gBAAgB,KAAK,KAAK;IAChD,MAAME,UAAU,GAAGH,IAAI,KAAK,GAAG;IAC/B,MAAMI,MAAM,GAAGD,UAAU,GAAG,GAAG,GAAG,GAAG;IACrC,MAAME,KAAK,GAAGF,UAAU,GAAG,GAAG,GAAG,GAAG;IACpC,MAAM3F,IAAI,GAAG,CAAC2F,UAAU,IAAID,aAAa,GAAG,CAAC,CAAC,GAAG,CAAC;IAClD,MAAMI,SAAS,GAAGC,YAAY,EAAE;IAChC,MAAMC,OAAO,GAAGC,UAAU,EAAE;IAE5B,SAASC,WAAWA,CAACC,QAAsB,EAAA;QACzC,MAAM,EAAEC,MAAM,EAAEC,KAAAA,EAAO,GAAGF,QAAQ;QAClC,OAAOR,UAAU,GAAGS,MAAM,GAAGC,KAAK;IACpC;IAEA,SAASN,YAAYA,GAAA;QACnB,IAAIJ,UAAU,EAAE,OAAO,KAAK;QAC5B,OAAOD,aAAa,GAAG,OAAO,GAAG,MAAM;IACzC;IAEA,SAASO,UAAUA,GAAA;QACjB,IAAIN,UAAU,EAAE,OAAO,QAAQ;QAC/B,OAAOD,aAAa,GAAG,MAAM,GAAG,OAAO;IACzC;IAEA,SAASY,SAASA,CAAC1G,CAAS,EAAA;QAC1B,OAAOA,CAAC,GAAGI,IAAI;IACjB;IAEA,MAAMiD,IAAI,GAAa;QACrB2C,MAAM;QACNC,KAAK;QACLC,SAAS;QACTE,OAAO;QACPE,WAAW;QACXI;KACD;IACD,OAAOrD,IAAI;AACb;SC1CgBsD,KAAKA,CAACC,MAAc,CAAC,EAAExF,MAAc,CAAC,EAAA;IACpD,MAAMC,MAAM,GAAGtB,OAAO,CAAC6G,GAAG,GAAGxF,GAAG,CAAC;IAEjC,SAASyF,UAAUA,CAAC7G,CAAS,EAAA;QAC3B,OAAOA,CAAC,GAAG4G,GAAG;IAChB;IAEA,SAASE,UAAUA,CAAC9G,CAAS,EAAA;QAC3B,OAAOA,CAAC,GAAGoB,GAAG;IAChB;IAEA,SAAS2F,UAAUA,CAAC/G,CAAS,EAAA;QAC3B,OAAO6G,UAAU,CAAC7G,CAAC,CAAC,IAAI8G,UAAU,CAAC9G,CAAC,CAAC;IACvC;IAEA,SAASgH,SAASA,CAAChH,CAAS,EAAA;QAC1B,IAAI,CAAC+G,UAAU,CAAC/G,CAAC,CAAC,EAAE,OAAOA,CAAC;QAC5B,OAAO6G,UAAU,CAAC7G,CAAC,CAAC,GAAG4G,GAAG,GAAGxF,GAAG;IAClC;IAEA,SAAS6F,YAAYA,CAACjH,CAAS,EAAA;QAC7B,IAAI,CAACqB,MAAM,EAAE,OAAOrB,CAAC;QACrB,OAAOA,CAAC,GAAGqB,MAAM,GAAGpB,IAAI,CAACiH,IAAI,CAAC,CAAClH,CAAC,GAAGoB,GAAG,IAAIC,MAAM,CAAC;IACnD;IAEA,MAAMgC,IAAI,GAAc;QACtBhC,MAAM;QACND,GAAG;QACHwF,GAAG;QACHI,SAAS;QACTD,UAAU;QACVD,UAAU;QACVD,UAAU;QACVI;KACD;IACD,OAAO5D,IAAI;AACb;SCvCgB8D,OAAOA,CACrB/F,GAAW,EACX6B,KAAa,EACbmE,IAAa,EAAA;IAEb,MAAM,EAAEJ,SAAAA,EAAW,GAAGL,KAAK,CAAC,CAAC,EAAEvF,GAAG,CAAC;IACnC,MAAMiG,OAAO,GAAGjG,GAAG,GAAG,CAAC;IACvB,IAAIkG,OAAO,GAAGC,WAAW,CAACtE,KAAK,CAAC;IAEhC,SAASsE,WAAWA,CAACvH,CAAS,EAAA;QAC5B,OAAO,CAACoH,IAAI,GAAGJ,SAAS,CAAChH,CAAC,CAAC,GAAGD,OAAO,CAAC,CAACsH,OAAO,GAAGrH,CAAC,IAAIqH,OAAO,CAAC;IAChE;IAEA,SAASG,GAAGA,GAAA;QACV,OAAOF,OAAO;IAChB;IAEA,SAASG,GAAGA,CAACzH,CAAS,EAAA;QACpBsH,OAAO,GAAGC,WAAW,CAACvH,CAAC,CAAC;QACxB,OAAOqD,IAAI;IACb;IAEA,SAASG,GAAGA,CAACxD,CAAS,EAAA;QACpB,OAAO0H,KAAK,EAAE,CAACD,GAAG,CAACD,GAAG,EAAE,GAAGxH,CAAC,CAAC;IAC/B;IAEA,SAAS0H,KAAKA,GAAA;QACZ,OAAOP,OAAO,CAAC/F,GAAG,EAAEoG,GAAG,EAAE,EAAEJ,IAAI,CAAC;IAClC;IAEA,MAAM/D,IAAI,GAAgB;QACxBmE,GAAG;QACHC,GAAG;QACHjE,GAAG;QACHkE;KACD;IACD,OAAOrE,IAAI;AACb;SCXgBsE,WAAWA,CACzB/B,IAAc,EACdgC,QAAqB,EACrBpD,aAAuB,EACvB7B,WAAuB,EACvBkF,MAAoB,EACpBC,WAA4B,EAC5BC,QAAsB,EACtBC,SAAyB,EACzBC,QAAsB,EACtBC,UAA0B,EAC1BC,YAA8B,EAC9B5G,KAAkB,EAClB6G,YAA8B,EAC9BC,aAAgC,EAChCC,QAAiB,EACjBC,aAAqB,EACrBC,SAAkB,EAClBC,YAAoB,EACpBC,SAAgC,EAAA;IAEhC,MAAM,EAAEzC,KAAK,EAAE0C,SAAS,EAAEjC,SAAAA,EAAW,GAAGd,IAAI;IAC5C,MAAMgD,UAAU,GAAG;QAAC,OAAO;QAAE,QAAQ;QAAE,UAAU;KAAC;IAClD,MAAMC,eAAe,GAAG;QAAEhF,OAAO,EAAE;KAAO;IAC1C,MAAMiF,UAAU,GAAGxF,UAAU,EAAE;IAC/B,MAAMyF,UAAU,GAAGzF,UAAU,EAAE;IAC/B,MAAM0F,iBAAiB,GAAGrC,KAAK,CAAC,EAAE,EAAE,GAAG,CAAC,CAACK,SAAS,CAACqB,aAAa,CAACjF,OAAO,CAAC,EAAE,CAAC,CAAC;IAC7E,MAAM6F,cAAc,GAAG;QAAEC,KAAK,EAAE,GAAG;QAAEC,KAAK,EAAE;KAAK;IACjD,MAAMC,cAAc,GAAG;QAAEF,KAAK,EAAE,GAAG;QAAEC,KAAK,EAAE;KAAK;IACjD,MAAME,SAAS,GAAGf,QAAQ,GAAG,EAAE,GAAG,EAAE;IAEpC,IAAIgB,QAAQ,GAAG,KAAK;IACpB,IAAIC,WAAW,GAAG,CAAC;IACnB,IAAIC,UAAU,GAAG,CAAC;IAClB,IAAIC,aAAa,GAAG,KAAK;IACzB,IAAIC,aAAa,GAAG,KAAK;IACzB,IAAIC,YAAY,GAAG,KAAK;IACxB,IAAIC,OAAO,GAAG,KAAK;IAEnB,SAAS5E,IAAIA,CAAC6E,QAA2B,EAAA;QACvC,IAAI,CAACnB,SAAS,EAAE;QAEhB,SAASoB,aAAaA,CAACpH,GAAqB,EAAA;YAC1C,IAAIjD,SAAS,CAACiJ,SAAS,CAAC,IAAIA,SAAS,CAACmB,QAAQ,EAAEnH,GAAG,CAAC,EAAEqH,IAAI,CAACrH,GAAG,CAAC;QACjE;QAEA,MAAMe,IAAI,GAAGmE,QAAQ;QACrBkB,UAAU,CACPtF,GAAG,CAACC,IAAI,EAAE,WAAW,GAAGf,GAAG,GAAKA,GAAG,CAACsH,cAAc,EAAE,EAAEnB,eAAe,CAAC,CACtErF,GAAG,CAACC,IAAI,EAAE,WAAW,EAAE,IAAMwG,SAAS,EAAEpB,eAAe,CAAC,CACxDrF,GAAG,CAACC,IAAI,EAAE,UAAU,EAAE,IAAMwG,SAAS,CAAC,CACtCzG,GAAG,CAACC,IAAI,EAAE,YAAY,EAAEqG,aAAa,CAAC,CACtCtG,GAAG,CAACC,IAAI,EAAE,WAAW,EAAEqG,aAAa,CAAC,CACrCtG,GAAG,CAACC,IAAI,EAAE,aAAa,EAAEyG,EAAE,CAAC,CAC5B1G,GAAG,CAACC,IAAI,EAAE,aAAa,EAAEyG,EAAE,CAAC,CAC5B1G,GAAG,CAACC,IAAI,EAAE,OAAO,EAAE0G,KAAK,EAAE,IAAI,CAAC;IACpC;IAEA,SAAShF,OAAOA,GAAA;QACd2D,UAAU,CAAC1E,KAAK,EAAE;QAClB2E,UAAU,CAAC3E,KAAK,EAAE;IACpB;IAEA,SAASgG,aAAaA,GAAA;QACpB,MAAM3G,IAAI,GAAGmG,OAAO,GAAGpF,aAAa,GAAGoD,QAAQ;QAC/CmB,UAAU,CACPvF,GAAG,CAACC,IAAI,EAAE,WAAW,EAAE4G,IAAI,EAAExB,eAAe,CAAC,CAC7CrF,GAAG,CAACC,IAAI,EAAE,UAAU,EAAEyG,EAAE,CAAC,CACzB1G,GAAG,CAACC,IAAI,EAAE,WAAW,EAAE4G,IAAI,EAAExB,eAAe,CAAC,CAC7CrF,GAAG,CAACC,IAAI,EAAE,SAAS,EAAEyG,EAAE,CAAC;IAC7B;IAEA,SAASI,WAAWA,CAAC7G,IAAa,EAAA;QAChC,MAAM8G,QAAQ,GAAG9G,IAAI,CAAC8G,QAAQ,IAAI,EAAE;QACpC,OAAO3B,UAAU,CAAC4B,QAAQ,CAACD,QAAQ,CAAC;IACtC;IAEA,SAASE,UAAUA,GAAA;QACjB,MAAMC,KAAK,GAAGpC,QAAQ,GAAGc,cAAc,GAAGH,cAAc;QACxD,MAAMvF,IAAI,GAAGkG,OAAO,GAAG,OAAO,GAAG,OAAO;QACxC,OAAOc,KAAK,CAAChH,IAAI,CAAC;IACpB;IAEA,SAASiH,YAAYA,CAACC,KAAa,EAAEC,aAAsB,EAAA;QACzD,MAAMC,IAAI,GAAGvJ,KAAK,CAACiC,GAAG,CAACrD,QAAQ,CAACyK,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC;QAC5C,MAAMG,SAAS,GAAG5C,YAAY,CAAC6C,UAAU,CAACJ,KAAK,EAAE,CAACtC,QAAQ,CAAC,CAAC2C,QAAQ;QAEpE,IAAI3C,QAAQ,IAAIvI,OAAO,CAAC6K,KAAK,CAAC,GAAG5B,iBAAiB,EAAE,OAAO+B,SAAS;QACpE,IAAIvC,SAAS,IAAIqC,aAAa,EAAE,OAAOE,SAAS,GAAG,GAAG;QAEtD,OAAO5C,YAAY,CAAC+C,OAAO,CAACJ,IAAI,CAACtD,GAAG,EAAE,EAAE,CAAC,CAAC,CAACyD,QAAQ;IACrD;IAEA,SAASlB,IAAIA,CAACrH,GAAqB,EAAA;QACjC,MAAMyI,UAAU,GAAG1I,YAAY,CAACC,GAAG,EAAEC,WAAW,CAAC;QACjDiH,OAAO,GAAGuB,UAAU;QACpBxB,YAAY,GAAGrB,QAAQ,IAAI6C,UAAU,IAAI,CAACzI,GAAG,CAAC0I,OAAO,IAAI9B,QAAQ;QACjEA,QAAQ,GAAGjJ,QAAQ,CAACwH,MAAM,CAACL,GAAG,EAAE,EAAEO,QAAQ,CAACP,GAAG,EAAE,CAAC,IAAI,CAAC;QAEtD,IAAI2D,UAAU,IAAIzI,GAAG,CAAC2I,MAAM,KAAK,CAAC,EAAE;QACpC,IAAIf,WAAW,CAAC5H,GAAG,CAACmF,MAAiB,CAAC,EAAE;QAExC4B,aAAa,GAAG,IAAI;QACpB3B,WAAW,CAACwD,WAAW,CAAC5I,GAAG,CAAC;QAC5BwF,UAAU,CAACqD,WAAW,CAAC,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,CAAC;QACxC3D,MAAM,CAACJ,GAAG,CAACM,QAAQ,CAAC;QACpBqC,aAAa,EAAE;QACfb,WAAW,GAAGzB,WAAW,CAAC2D,SAAS,CAAC/I,GAAG,CAAC;QACxC8G,UAAU,GAAG1B,WAAW,CAAC2D,SAAS,CAAC/I,GAAG,EAAEiG,SAAS,CAAC;QAClDP,YAAY,CAACsD,IAAI,CAAC,aAAa,CAAC;IAClC;IAEA,SAASrB,IAAIA,CAAC3H,GAAqB,EAAA;QACjC,MAAMiJ,UAAU,GAAG,CAAClJ,YAAY,CAACC,GAAG,EAAEC,WAAW,CAAC;QAClD,IAAIgJ,UAAU,IAAIjJ,GAAG,CAACkJ,OAAO,CAACvK,MAAM,IAAI,CAAC,EAAE,OAAO6I,EAAE,CAACxH,GAAG,CAAC;QAEzD,MAAMmJ,UAAU,GAAG/D,WAAW,CAAC2D,SAAS,CAAC/I,GAAG,CAAC;QAC7C,MAAMoJ,SAAS,GAAGhE,WAAW,CAAC2D,SAAS,CAAC/I,GAAG,EAAEiG,SAAS,CAAC;QACvD,MAAMoD,UAAU,GAAG1L,QAAQ,CAACwL,UAAU,EAAEtC,WAAW,CAAC;QACpD,MAAMyC,SAAS,GAAG3L,QAAQ,CAACyL,SAAS,EAAEtC,UAAU,CAAC;QAEjD,IAAI,CAACE,aAAa,IAAI,CAACE,OAAO,EAAE;YAC9B,IAAI,CAAClH,GAAG,CAACuJ,UAAU,EAAE,OAAO/B,EAAE,CAACxH,GAAG,CAAC;YACnCgH,aAAa,GAAGqC,UAAU,GAAGC,SAAS;YACtC,IAAI,CAACtC,aAAa,EAAE,OAAOQ,EAAE,CAACxH,GAAG,CAAC;QACpC;QACA,MAAMjC,IAAI,GAAGqH,WAAW,CAACoE,WAAW,CAACxJ,GAAG,CAAC;QACzC,IAAIqJ,UAAU,GAAGxD,aAAa,EAAEoB,YAAY,GAAG,IAAI;QAEnDzB,UAAU,CAACqD,WAAW,CAAC,GAAG,CAAC,CAACC,WAAW,CAAC,IAAI,CAAC;QAC7CxD,SAAS,CAAC/E,KAAK,EAAE;QACjB4E,MAAM,CAACrE,GAAG,CAACkD,SAAS,CAACjG,IAAI,CAAC,CAAC;QAC3BiC,GAAG,CAACsH,cAAc,EAAE;IACtB;IAEA,SAASE,EAAEA,CAACxH,GAAqB,EAAA;QAC/B,MAAMyJ,eAAe,GAAGhE,YAAY,CAAC6C,UAAU,CAAC,CAAC,EAAE,KAAK,CAAC;QACzD,MAAMH,aAAa,GAAGsB,eAAe,CAAC5K,KAAK,KAAKA,KAAK,CAACiG,GAAG,EAAE;QAC3D,MAAM4E,QAAQ,GAAGtE,WAAW,CAACuE,SAAS,CAAC3J,GAAG,CAAC,GAAG+H,UAAU,EAAE;QAC1D,MAAMG,KAAK,GAAGD,YAAY,CAACjE,SAAS,CAAC0F,QAAQ,CAAC,EAAEvB,aAAa,CAAC;QAC9D,MAAMyB,WAAW,GAAG9L,SAAS,CAAC4L,QAAQ,EAAExB,KAAK,CAAC;QAC9C,MAAM2B,KAAK,GAAGlD,SAAS,GAAG,EAAE,GAAGiD,WAAW;QAC1C,MAAME,QAAQ,GAAG/D,YAAY,GAAG6D,WAAW,GAAG,EAAE;QAEhD5C,aAAa,GAAG,KAAK;QACrBD,aAAa,GAAG,KAAK;QACrBV,UAAU,CAAC3E,KAAK,EAAE;QAClB8D,UAAU,CAACsD,WAAW,CAACe,KAAK,CAAC,CAAChB,WAAW,CAACiB,QAAQ,CAAC;QACnDvE,QAAQ,CAACgD,QAAQ,CAACL,KAAK,EAAE,CAACtC,QAAQ,CAAC;QACnCsB,OAAO,GAAG,KAAK;QACfxB,YAAY,CAACsD,IAAI,CAAC,WAAW,CAAC;IAChC;IAEA,SAASvB,KAAKA,CAACzH,GAAe,EAAA;QAC5B,IAAIiH,YAAY,EAAE;YAChBjH,GAAG,CAAC+J,eAAe,EAAE;YACrB/J,GAAG,CAACsH,cAAc,EAAE;YACpBL,YAAY,GAAG,KAAK;QACtB;IACF;IAEA,SAAS2B,WAAWA,GAAA;QAClB,OAAO7B,aAAa;IACtB;IAEA,MAAMpG,IAAI,GAAoB;QAC5B2B,IAAI;QACJG,OAAO;QACPmG;KACD;IACD,OAAOjI,IAAI;AACb;AClMgB,SAAAqJ,WAAWA,CACzB9G,IAAc,EACdjD,WAAuB,EAAA;IAEvB,MAAMgK,WAAW,GAAG,GAAG;IAEvB,IAAIC,UAA4B;IAChC,IAAIC,SAA2B;IAE/B,SAASC,QAAQA,CAACpK,GAAqB,EAAA;QACrC,OAAOA,GAAG,CAAC4C,SAAS;IACtB;IAEA,SAASmG,SAASA,CAAC/I,GAAqB,EAAEqK,OAAwB,EAAA;QAChE,MAAMC,QAAQ,GAAGD,OAAO,IAAInH,IAAI,CAACI,MAAM;QACvC,MAAMiH,KAAK,GAAqB,CAAA,MAAA,EAASD,QAAQ,KAAK,GAAG,GAAG,GAAG,GAAG,GAAG,CAAE,CAAA;QACvE,OAAO,CAACvK,YAAY,CAACC,GAAG,EAAEC,WAAW,CAAC,GAAGD,GAAG,GAAGA,GAAG,CAACkJ,OAAO,CAAC,CAAC,CAAC,CAAA,CAAEqB,KAAK,CAAC;IACvE;IAEA,SAAS3B,WAAWA,CAAC5I,GAAqB,EAAA;QACxCkK,UAAU,GAAGlK,GAAG;QAChBmK,SAAS,GAAGnK,GAAG;QACf,OAAO+I,SAAS,CAAC/I,GAAG,CAAC;IACvB;IAEA,SAASwJ,WAAWA,CAACxJ,GAAqB,EAAA;QACxC,MAAMjC,IAAI,GAAGgL,SAAS,CAAC/I,GAAG,CAAC,GAAG+I,SAAS,CAACoB,SAAS,CAAC;QAClD,MAAMK,OAAO,GAAGJ,QAAQ,CAACpK,GAAG,CAAC,GAAGoK,QAAQ,CAACF,UAAU,CAAC,GAAGD,WAAW;QAElEE,SAAS,GAAGnK,GAAG;QACf,IAAIwK,OAAO,EAAEN,UAAU,GAAGlK,GAAG;QAC7B,OAAOjC,IAAI;IACb;IAEA,SAAS4L,SAASA,CAAC3J,GAAqB,EAAA;QACtC,IAAI,CAACkK,UAAU,IAAI,CAACC,SAAS,EAAE,OAAO,CAAC;QACvC,MAAMM,QAAQ,GAAG1B,SAAS,CAACoB,SAAS,CAAC,GAAGpB,SAAS,CAACmB,UAAU,CAAC;QAC7D,MAAMQ,QAAQ,GAAGN,QAAQ,CAACpK,GAAG,CAAC,GAAGoK,QAAQ,CAACF,UAAU,CAAC;QACrD,MAAMM,OAAO,GAAGJ,QAAQ,CAACpK,GAAG,CAAC,GAAGoK,QAAQ,CAACD,SAAS,CAAC,GAAGF,WAAW;QACjE,MAAM/B,KAAK,GAAGuC,QAAQ,GAAGC,QAAQ;QACjC,MAAMC,OAAO,GAAGD,QAAQ,IAAI,CAACF,OAAO,IAAInN,OAAO,CAAC6K,KAAK,CAAC,GAAG,GAAG;QAE5D,OAAOyC,OAAO,GAAGzC,KAAK,GAAG,CAAC;IAC5B;IAEA,MAAMvH,IAAI,GAAoB;QAC5BiI,WAAW;QACXY,WAAW;QACXG,SAAS;QACTZ;KACD;IACD,OAAOpI,IAAI;AACb;SCpDgBiK,SAASA,GAAA;IACvB,SAASlK,OAAOA,CAACK,IAAiB,EAAA;QAChC,MAAM,EAAE8J,SAAS,EAAEC,UAAU,EAAEC,WAAW,EAAEC,YAAAA,EAAc,GAAGjK,IAAI;QACjE,MAAMkK,MAAM,GAAiB;YAC3BC,GAAG,EAAEL,SAAS;YACdM,KAAK,EAAEL,UAAU,GAAGC,WAAW;YAC/BK,MAAM,EAAEP,SAAS,GAAGG,YAAY;YAChCK,IAAI,EAAEP,UAAU;YAChB/G,KAAK,EAAEgH,WAAW;YAClBjH,MAAM,EAAEkH;SACT;QAED,OAAOC,MAAM;IACf;IAEA,MAAMtK,IAAI,GAAkB;QAC1BD;KACD;IACD,OAAOC,IAAI;AACb;AC5BM,SAAU2K,aAAaA,CAACjL,QAAgB,EAAA;IAC5C,SAASK,OAAOA,CAACpD,CAAS,EAAA;QACxB,OAAO+C,QAAQ,GAAA,CAAI/C,CAAC,GAAG,GAAG,CAAC;IAC7B;IAEA,MAAMqD,IAAI,GAAsB;QAC9BD;KACD;IACD,OAAOC,IAAI;AACb;ACKgB,SAAA4K,aAAaA,CAC3BC,SAAsB,EACtB9F,YAA8B,EAC9BzF,WAAuB,EACvBwL,MAAqB,EACrBvI,IAAc,EACdwI,WAAoC,EACpCC,SAAwB,EAAA;IAExB,MAAMC,YAAY,GAAG;QAACJ,SAAS;KAAC,CAACK,MAAM,CAACJ,MAAM,CAAC;IAC/C,IAAIK,cAA8B;IAClC,IAAIC,aAAqB;IACzB,IAAIC,UAAU,GAAa,EAAE;IAC7B,IAAIC,SAAS,GAAG,KAAK;IAErB,SAASC,QAAQA,CAACnL,IAAiB,EAAA;QACjC,OAAOmC,IAAI,CAACU,WAAW,CAAC+H,SAAS,CAACjL,OAAO,CAACK,IAAI,CAAC,CAAC;IAClD;IAEA,SAASuB,IAAIA,CAAC6E,QAA2B,EAAA;QACvC,IAAI,CAACuE,WAAW,EAAE;QAElBK,aAAa,GAAGG,QAAQ,CAACV,SAAS,CAAC;QACnCQ,UAAU,GAAGP,MAAM,CAACnN,GAAG,CAAC4N,QAAQ,CAAC;QAEjC,SAASC,eAAeA,CAACC,OAA8B,EAAA;YACrD,KAAK,MAAMC,KAAK,IAAID,OAAO,CAAE;gBAC3B,IAAIH,SAAS,EAAE;gBAEf,MAAMK,WAAW,GAAGD,KAAK,CAAClH,MAAM,KAAKqG,SAAS;gBAC9C,MAAMe,UAAU,GAAGd,MAAM,CAACe,OAAO,CAAcH,KAAK,CAAClH,MAAM,CAAC;gBAC5D,MAAMsH,QAAQ,GAAGH,WAAW,GAAGP,aAAa,GAAGC,UAAU,CAACO,UAAU,CAAC;gBACrE,MAAMG,OAAO,GAAGR,QAAQ,CAACI,WAAW,GAAGd,SAAS,GAAGC,MAAM,CAACc,UAAU,CAAC,CAAC;gBACtE,MAAMI,QAAQ,GAAGtP,OAAO,CAACqP,OAAO,GAAGD,QAAQ,CAAC;gBAE5C,IAAIE,QAAQ,IAAI,GAAG,EAAE;oBACnBxF,QAAQ,CAACyF,MAAM,EAAE;oBACjBlH,YAAY,CAACsD,IAAI,CAAC,QAAQ,CAAC;oBAE3B;gBACF;YACF;QACF;QAEA8C,cAAc,GAAG,IAAIe,cAAc,EAAET,OAAO,IAAI;YAC9C,IAAIrP,SAAS,CAAC2O,WAAW,CAAC,IAAIA,WAAW,CAACvE,QAAQ,EAAEiF,OAAO,CAAC,EAAE;gBAC5DD,eAAe,CAACC,OAAO,CAAC;YAC1B;QACF,CAAC,CAAC;QAEFnM,WAAW,CAAC8C,qBAAqB,CAAC,MAAK;YACrC6I,YAAY,CAAChM,OAAO,EAAEmB,IAAI,GAAK+K,cAAc,CAACgB,OAAO,CAAC/L,IAAI,CAAC,CAAC;QAC9D,CAAC,CAAC;IACJ;IAEA,SAAS0B,OAAOA,GAAA;QACdwJ,SAAS,GAAG,IAAI;QAChB,IAAIH,cAAc,EAAEA,cAAc,CAACiB,UAAU,EAAE;IACjD;IAEA,MAAMpM,IAAI,GAAsB;QAC9B2B,IAAI;QACJG;KACD;IACD,OAAO9B,IAAI;AACb;ACpEgB,SAAAqM,UAAUA,CACxB3H,QAAsB,EACtB4H,cAA4B,EAC5BC,gBAA8B,EAC9B/H,MAAoB,EACpBgI,YAAoB,EACpBpH,YAAoB,EAAA;IAEpB,IAAIqH,cAAc,GAAG,CAAC;IACtB,IAAIC,eAAe,GAAG,CAAC;IACvB,IAAIC,cAAc,GAAGH,YAAY;IACjC,IAAII,cAAc,GAAGxH,YAAY;IACjC,IAAIyH,WAAW,GAAGnI,QAAQ,CAACP,GAAG,EAAE;IAChC,IAAI2I,mBAAmB,GAAG,CAAC;IAE3B,SAASC,IAAIA,GAAA;QACX,MAAMC,YAAY,GAAGxI,MAAM,CAACL,GAAG,EAAE,GAAGO,QAAQ,CAACP,GAAG,EAAE;QAClD,MAAM8I,SAAS,GAAG,CAACN,cAAc;QACjC,IAAIO,cAAc,GAAG,CAAC;QAEtB,IAAID,SAAS,EAAE;YACbR,cAAc,GAAG,CAAC;YAClBF,gBAAgB,CAACnI,GAAG,CAACI,MAAM,CAAC;YAC5BE,QAAQ,CAACN,GAAG,CAACI,MAAM,CAAC;YAEpB0I,cAAc,GAAGF,YAAY;QAC/B,CAAC,MAAM;YACLT,gBAAgB,CAACnI,GAAG,CAACM,QAAQ,CAAC;YAE9B+H,cAAc,IAAIO,YAAY,GAAGL,cAAc;YAC/CF,cAAc,IAAIG,cAAc;YAChCC,WAAW,IAAIJ,cAAc;YAC7B/H,QAAQ,CAACvE,GAAG,CAACsM,cAAc,CAAC;YAE5BS,cAAc,GAAGL,WAAW,GAAGC,mBAAmB;QACpD;QAEAJ,eAAe,GAAG5P,QAAQ,CAACoQ,cAAc,CAAC;QAC1CJ,mBAAmB,GAAGD,WAAW;QACjC,OAAO7M,IAAI;IACb;IAEA,SAASmN,OAAOA,GAAA;QACd,MAAM/P,IAAI,GAAGoH,MAAM,CAACL,GAAG,EAAE,GAAGmI,cAAc,CAACnI,GAAG,EAAE;QAChD,OAAOzH,OAAO,CAACU,IAAI,CAAC,GAAG,KAAK;IAC9B;IAEA,SAASgQ,QAAQA,GAAA;QACf,OAAOT,cAAc;IACvB;IAEA,SAAStJ,SAASA,GAAA;QAChB,OAAOqJ,eAAe;IACxB;IAEA,SAASW,QAAQA,GAAA;QACf,OAAOZ,cAAc;IACvB;IAEA,SAASa,eAAeA,GAAA;QACtB,OAAOnF,WAAW,CAACqE,YAAY,CAAC;IAClC;IAEA,SAASe,eAAeA,GAAA;QACtB,OAAOrF,WAAW,CAAC9C,YAAY,CAAC;IAClC;IAEA,SAAS+C,WAAWA,CAACxL,CAAS,EAAA;QAC5BgQ,cAAc,GAAGhQ,CAAC;QAClB,OAAOqD,IAAI;IACb;IAEA,SAASkI,WAAWA,CAACvL,CAAS,EAAA;QAC5BiQ,cAAc,GAAGjQ,CAAC;QAClB,OAAOqD,IAAI;IACb;IAEA,MAAMA,IAAI,GAAmB;QAC3BqD,SAAS;QACT+J,QAAQ;QACRC,QAAQ;QACRN,IAAI;QACJI,OAAO;QACPI,eAAe;QACfD,eAAe;QACfpF,WAAW;QACXC;KACD;IACD,OAAOnI,IAAI;AACb;AC5FM,SAAUwN,YAAYA,CAC1BC,KAAgB,EAChB/I,QAAsB,EACtBF,MAAoB,EACpBK,UAA0B,EAC1BG,aAAgC,EAAA;IAEhC,MAAM0I,iBAAiB,GAAG1I,aAAa,CAACjF,OAAO,CAAC,EAAE,CAAC;IACnD,MAAM4N,mBAAmB,GAAG3I,aAAa,CAACjF,OAAO,CAAC,EAAE,CAAC;IACrD,MAAM6N,aAAa,GAAGtK,KAAK,CAAC,GAAG,EAAE,IAAI,CAAC;IACtC,IAAIuK,QAAQ,GAAG,KAAK;IAEpB,SAASC,eAAeA,GAAA;QACtB,IAAID,QAAQ,EAAE,OAAO,KAAK;QAC1B,IAAI,CAACJ,KAAK,CAAC/J,UAAU,CAACc,MAAM,CAACL,GAAG,EAAE,CAAC,EAAE,OAAO,KAAK;QACjD,IAAI,CAACsJ,KAAK,CAAC/J,UAAU,CAACgB,QAAQ,CAACP,GAAG,EAAE,CAAC,EAAE,OAAO,KAAK;QACnD,OAAO,IAAI;IACb;IAEA,SAASR,SAASA,CAACsE,WAAoB,EAAA;QACrC,IAAI,CAAC6F,eAAe,EAAE,EAAE;QACxB,MAAMC,IAAI,GAAGN,KAAK,CAACjK,UAAU,CAACkB,QAAQ,CAACP,GAAG,EAAE,CAAC,GAAG,KAAK,GAAG,KAAK;QAC7D,MAAM6J,UAAU,GAAGtR,OAAO,CAAC+Q,KAAK,CAACM,IAAI,CAAC,GAAGrJ,QAAQ,CAACP,GAAG,EAAE,CAAC;QACxD,MAAM8J,YAAY,GAAGzJ,MAAM,CAACL,GAAG,EAAE,GAAGO,QAAQ,CAACP,GAAG,EAAE;QAClD,MAAMgF,QAAQ,GAAGyE,aAAa,CAACjK,SAAS,CAACqK,UAAU,GAAGL,mBAAmB,CAAC;QAE1EnJ,MAAM,CAAC0J,QAAQ,CAACD,YAAY,GAAG9E,QAAQ,CAAC;QAExC,IAAI,CAAClB,WAAW,IAAIvL,OAAO,CAACuR,YAAY,CAAC,GAAGP,iBAAiB,EAAE;YAC7DlJ,MAAM,CAACJ,GAAG,CAACqJ,KAAK,CAAC9J,SAAS,CAACa,MAAM,CAACL,GAAG,EAAE,CAAC,CAAC;YACzCU,UAAU,CAACsD,WAAW,CAAC,EAAE,CAAC,CAACoF,eAAe,EAAE;QAC9C;IACF;IAEA,SAASY,YAAYA,CAACC,MAAe,EAAA;QACnCP,QAAQ,GAAG,CAACO,MAAM;IACpB;IAEA,MAAMpO,IAAI,GAAqB;QAC7B8N,eAAe;QACfnK,SAAS;QACTwK;KACD;IACD,OAAOnO,IAAI;AACb;AC9CM,SAAUqO,aAAaA,CAC3B3O,QAAgB,EAChB4O,WAAmB,EACnBC,YAAsB,EACtBC,aAAsC,EACtCC,cAAsB,EAAA;IAEtB,MAAMC,YAAY,GAAGpL,KAAK,CAAC,CAACgL,WAAW,GAAG5O,QAAQ,EAAE,CAAC,CAAC;IACtD,MAAMiP,YAAY,GAAGC,cAAc,EAAE;IACrC,MAAMC,kBAAkB,GAAGC,sBAAsB,EAAE;IACnD,MAAMC,cAAc,GAAGC,gBAAgB,EAAE;IAEzC,SAASC,iBAAiBA,CAACC,KAAa,EAAEC,IAAY,EAAA;QACpD,OAAOnS,QAAQ,CAACkS,KAAK,EAAEC,IAAI,CAAC,IAAI,CAAC;IACnC;IAEA,SAASL,sBAAsBA,GAAA;QAC7B,MAAMM,SAAS,GAAGT,YAAY,CAAC,CAAC,CAAC;QACjC,MAAMU,OAAO,GAAGxR,SAAS,CAAC8Q,YAAY,CAAC;QACvC,MAAMpL,GAAG,GAAGoL,YAAY,CAACW,WAAW,CAACF,SAAS,CAAC;QAC/C,MAAMrR,GAAG,GAAG4Q,YAAY,CAAC9C,OAAO,CAACwD,OAAO,CAAC,GAAG,CAAC;QAC7C,OAAO/L,KAAK,CAACC,GAAG,EAAExF,GAAG,CAAC;IACxB;IAEA,SAAS6Q,cAAcA,GAAA;QACrB,OAAOL,YAAY,CAChB5Q,GAAG,CAAC,CAAC4R,WAAW,EAAErR,KAAK,KAAI;YAC1B,MAAM,EAAEqF,GAAG,EAAExF,GAAAA,EAAK,GAAG2Q,YAAY;YACjC,MAAMS,IAAI,GAAGT,YAAY,CAAC/K,SAAS,CAAC4L,WAAW,CAAC;YAChD,MAAMC,OAAO,GAAG,CAACtR,KAAK;YACtB,MAAMuR,MAAM,GAAGxR,gBAAgB,CAACsQ,YAAY,EAAErQ,KAAK,CAAC;YACpD,IAAIsR,OAAO,EAAE,OAAOzR,GAAG;YACvB,IAAI0R,MAAM,EAAE,OAAOlM,GAAG;YACtB,IAAI0L,iBAAiB,CAAC1L,GAAG,EAAE4L,IAAI,CAAC,EAAE,OAAO5L,GAAG;YAC5C,IAAI0L,iBAAiB,CAAClR,GAAG,EAAEoR,IAAI,CAAC,EAAE,OAAOpR,GAAG;YAC5C,OAAOoR,IAAI;QACb,CAAC,CAAC,CACDxR,GAAG,EAAE+R,WAAW,GAAKC,UAAU,CAACD,WAAW,CAACE,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC;IAC7D;IAEA,SAASZ,gBAAgBA,GAAA;QACvB,IAAIV,WAAW,IAAI5O,QAAQ,GAAG+O,cAAc,EAAE,OAAO;YAACC,YAAY,CAAC3Q,GAAG;SAAC;QACvE,IAAIyQ,aAAa,KAAK,WAAW,EAAE,OAAOG,YAAY;QACtD,MAAM,EAAEpL,GAAG,EAAExF,GAAAA,EAAK,GAAG8Q,kBAAkB;QACvC,OAAOF,YAAY,CAACkB,KAAK,CAACtM,GAAG,EAAExF,GAAG,CAAC;IACrC;IAEA,MAAMiC,IAAI,GAAsB;QAC9B+O,cAAc;QACdF;KACD;IACD,OAAO7O,IAAI;AACb;SCvDgB8P,WAAWA,CACzBxB,WAAmB,EACnByB,WAAqB,EACrBhM,IAAa,EAAA;IAEb,MAAMhG,GAAG,GAAGgS,WAAW,CAAC,CAAC,CAAC;IAC1B,MAAMxM,GAAG,GAAGQ,IAAI,GAAGhG,GAAG,GAAGuQ,WAAW,GAAGzQ,SAAS,CAACkS,WAAW,CAAC;IAC7D,MAAMtC,KAAK,GAAGnK,KAAK,CAACC,GAAG,EAAExF,GAAG,CAAC;IAE7B,MAAMiC,IAAI,GAAoB;QAC5ByN;KACD;IACD,OAAOzN,IAAI;AACb;ACbM,SAAUgQ,YAAYA,CAC1B1B,WAAmB,EACnBb,KAAgB,EAChB/I,QAAsB,EACtBuL,OAAuB,EAAA;IAEvB,MAAMC,WAAW,GAAG,GAAG;IACvB,MAAM3M,GAAG,GAAGkK,KAAK,CAAClK,GAAG,GAAG2M,WAAW;IACnC,MAAMnS,GAAG,GAAG0P,KAAK,CAAC1P,GAAG,GAAGmS,WAAW;IACnC,MAAM,EAAE1M,UAAU,EAAEC,UAAAA,EAAY,GAAGH,KAAK,CAACC,GAAG,EAAExF,GAAG,CAAC;IAElD,SAASoS,UAAUA,CAAC9M,SAAiB,EAAA;QACnC,IAAIA,SAAS,KAAK,CAAC,EAAE,OAAOI,UAAU,CAACiB,QAAQ,CAACP,GAAG,EAAE,CAAC;QACtD,IAAId,SAAS,KAAK,CAAC,CAAC,EAAE,OAAOG,UAAU,CAACkB,QAAQ,CAACP,GAAG,EAAE,CAAC;QACvD,OAAO,KAAK;IACd;IAEA,SAASJ,IAAIA,CAACV,SAAiB,EAAA;QAC7B,IAAI,CAAC8M,UAAU,CAAC9M,SAAS,CAAC,EAAE;QAE5B,MAAM+M,YAAY,GAAG9B,WAAW,GAAA,CAAIjL,SAAS,GAAG,CAAC,CAAC,CAAC;QACnD4M,OAAO,CAAChR,OAAO,EAAEoR,CAAC,GAAKA,CAAC,CAAClQ,GAAG,CAACiQ,YAAY,CAAC,CAAC;IAC7C;IAEA,MAAMpQ,IAAI,GAAqB;QAC7B+D;KACD;IACD,OAAO/D,IAAI;AACb;AC7BM,SAAUsQ,cAAcA,CAAC7C,KAAgB,EAAA;IAC7C,MAAM,EAAE1P,GAAG,EAAEC,MAAAA,EAAQ,GAAGyP,KAAK;IAE7B,SAAStJ,GAAGA,CAACxH,CAAS,EAAA;QACpB,MAAMmM,eAAe,GAAGnM,CAAC,GAAGoB,GAAG;QAC/B,OAAOC,MAAM,GAAG8K,eAAe,GAAG,CAAC9K,MAAM,GAAG,CAAC;IAC/C;IAEA,MAAMgC,IAAI,GAAuB;QAC/BmE;KACD;IACD,OAAOnE,IAAI;AACb;ACPM,SAAUuQ,WAAWA,CACzBhO,IAAc,EACdiO,SAAwB,EACxBC,aAA2B,EAC3BC,UAA0B,EAC1BC,cAAkC,EAAA;IAElC,MAAM,EAAE9N,SAAS,EAAEE,OAAAA,EAAS,GAAGR,IAAI;IACnC,MAAM,EAAEqO,WAAAA,EAAa,GAAGD,cAAc;IACtC,MAAME,UAAU,GAAGC,YAAY,EAAE,CAACnT,GAAG,CAAC6S,SAAS,CAACzQ,OAAO,CAAC;IACxD,MAAMgR,KAAK,GAAGC,gBAAgB,EAAE;IAChC,MAAMzC,YAAY,GAAG0C,cAAc,EAAE;IAErC,SAASH,YAAYA,GAAA;QACnB,OAAOF,WAAW,CAACF,UAAU,CAAC,CAC3B/S,GAAG,EAAEuT,KAAK,GAAKrT,SAAS,CAACqT,KAAK,CAAC,CAACnO,OAAO,CAAC,GAAGmO,KAAK,CAAC,CAAC,CAAC,CAACrO,SAAS,CAAC,CAAC,CAC/DlF,GAAG,CAACjB,OAAO,CAAC;IACjB;IAEA,SAASsU,gBAAgBA,GAAA;QACvB,OAAON,UAAU,CACd/S,GAAG,EAAEwT,IAAI,GAAKV,aAAa,CAAC5N,SAAS,CAAC,GAAGsO,IAAI,CAACtO,SAAS,CAAC,CAAC,CACzDlF,GAAG,EAAEwR,IAAI,GAAK,CAACzS,OAAO,CAACyS,IAAI,CAAC,CAAC;IAClC;IAEA,SAAS8B,cAAcA,GAAA;QACrB,OAAOL,WAAW,CAACG,KAAK,CAAC,CACtBpT,GAAG,EAAEyT,CAAC,GAAKA,CAAC,CAAC,CAAC,CAAC,CAAC,CAChBzT,GAAG,CAAC,CAACwR,IAAI,EAAEjR,KAAK,GAAKiR,IAAI,GAAG0B,UAAU,CAAC3S,KAAK,CAAC,CAAC;IACnD;IAEA,MAAM8B,IAAI,GAAoB;QAC5B+Q,KAAK;QACLxC;KACD;IACD,OAAOvO,IAAI;AACb;ACjCgB,SAAAqR,aAAaA,CAC3BC,YAAqB,EACrB9C,aAAsC,EACtCuB,WAAqB,EACrBlB,kBAA6B,EAC7B8B,cAAkC,EAClCY,YAAsB,EAAA;IAEtB,MAAM,EAAEX,WAAAA,EAAa,GAAGD,cAAc;IACtC,MAAM,EAAEpN,GAAG,EAAExF,GAAAA,EAAK,GAAG8Q,kBAAkB;IACvC,MAAM2C,aAAa,GAAGC,mBAAmB,EAAE;IAE3C,SAASA,mBAAmBA,GAAA;QAC1B,MAAMC,mBAAmB,GAAGd,WAAW,CAACW,YAAY,CAAC;QACrD,MAAMI,YAAY,GAAG,CAACL,YAAY,IAAI9C,aAAa,KAAK,WAAW;QAEnE,IAAIuB,WAAW,CAAC/R,MAAM,KAAK,CAAC,EAAE,OAAO;YAACuT,YAAY;SAAC;QACnD,IAAII,YAAY,EAAE,OAAOD,mBAAmB;QAE5C,OAAOA,mBAAmB,CAAC7B,KAAK,CAACtM,GAAG,EAAExF,GAAG,CAAC,CAACJ,GAAG,CAAC,CAACiU,KAAK,EAAE1T,KAAK,EAAE2T,MAAM,KAAI;YACtE,MAAMrC,OAAO,GAAG,CAACtR,KAAK;YACtB,MAAMuR,MAAM,GAAGxR,gBAAgB,CAAC4T,MAAM,EAAE3T,KAAK,CAAC;YAE9C,IAAIsR,OAAO,EAAE;gBACX,MAAMsC,KAAK,GAAGjU,SAAS,CAACgU,MAAM,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC;gBACtC,OAAO1T,eAAe,CAAC2T,KAAK,CAAC;YAC/B;YACA,IAAIrC,MAAM,EAAE;gBACV,MAAMqC,KAAK,GAAGhU,cAAc,CAACyT,YAAY,CAAC,GAAG1T,SAAS,CAACgU,MAAM,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC;gBACrE,OAAO1T,eAAe,CAAC2T,KAAK,EAAEjU,SAAS,CAACgU,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;YACrD;YACA,OAAOD,KAAK;QACd,CAAC,CAAC;IACJ;IAEA,MAAM5R,IAAI,GAAsB;QAC9BwR;KACD;IACD,OAAOxR,IAAI;AACb;ACtCM,SAAU+R,YAAYA,CAC1BhO,IAAa,EACbgM,WAAqB,EACrBzB,WAAmB,EACnBb,KAAgB,EAChBuE,YAA0B,EAAA;IAE1B,MAAM,EAAEtO,UAAU,EAAEE,YAAY,EAAED,SAAAA,EAAW,GAAG8J,KAAK;IAErD,SAASwE,WAAWA,CAACC,SAAmB,EAAA;QACtC,OAAOA,SAAS,CAAChH,MAAM,EAAE,CAACiH,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,GAAK3V,OAAO,CAAC0V,CAAC,CAAC,GAAG1V,OAAO,CAAC2V,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACtE;IAEA,SAASC,cAAcA,CAAC9N,MAAc,EAAA;QACpC,MAAMoD,QAAQ,GAAG7D,IAAI,GAAGH,YAAY,CAACY,MAAM,CAAC,GAAGb,SAAS,CAACa,MAAM,CAAC;QAChE,MAAM+N,eAAe,GAAGxC,WAAW,CAChCpS,GAAG,CAAC,CAACwR,IAAI,EAAEjR,KAAK,GAAA,CAAM;gBAAEd,IAAI,EAAEoV,QAAQ,CAACrD,IAAI,GAAGvH,QAAQ,EAAE,CAAC,CAAC;gBAAE1J;aAAO,CAAC,CAAC,CACrEiU,IAAI,CAAC,CAACM,EAAE,EAAEC,EAAE,GAAKhW,OAAO,CAAC+V,EAAE,CAACrV,IAAI,CAAC,GAAGV,OAAO,CAACgW,EAAE,CAACtV,IAAI,CAAC,CAAC;QAExD,MAAM,EAAEc,KAAAA,EAAO,GAAGqU,eAAe,CAAC,CAAC,CAAC;QACpC,OAAO;YAAErU,KAAK;YAAE0J;SAAU;IAC5B;IAEA,SAAS4K,QAAQA,CAAChO,MAAc,EAAEnB,SAAiB,EAAA;QACjD,MAAMsP,OAAO,GAAG;YAACnO,MAAM;YAAEA,MAAM,GAAG8J,WAAW;YAAE9J,MAAM,GAAG8J,WAAW;SAAC;QAEpE,IAAI,CAACvK,IAAI,EAAE,OAAOS,MAAM;QACxB,IAAI,CAACnB,SAAS,EAAE,OAAO4O,WAAW,CAACU,OAAO,CAAC;QAE3C,MAAMC,eAAe,GAAGD,OAAO,CAAC3R,MAAM,EAAE6R,CAAC,GAAK/V,QAAQ,CAAC+V,CAAC,CAAC,KAAKxP,SAAS,CAAC;QACxE,IAAIuP,eAAe,CAAC5U,MAAM,EAAE,OAAOiU,WAAW,CAACW,eAAe,CAAC;QAC/D,OAAO/U,SAAS,CAAC8U,OAAO,CAAC,GAAGrE,WAAW;IACzC;IAEA,SAASzG,OAAOA,CAAC3J,KAAa,EAAEmF,SAAiB,EAAA;QAC/C,MAAMyP,UAAU,GAAG/C,WAAW,CAAC7R,KAAK,CAAC,GAAG8T,YAAY,CAAC7N,GAAG,EAAE;QAC1D,MAAMyD,QAAQ,GAAG4K,QAAQ,CAACM,UAAU,EAAEzP,SAAS,CAAC;QAChD,OAAO;YAAEnF,KAAK;YAAE0J;SAAU;IAC5B;IAEA,SAASD,UAAUA,CAACC,QAAgB,EAAEuH,IAAa,EAAA;QACjD,MAAM3K,MAAM,GAAGwN,YAAY,CAAC7N,GAAG,EAAE,GAAGyD,QAAQ;QAC5C,MAAM,EAAE1J,KAAK,EAAE0J,QAAQ,EAAEmL,kBAAAA,EAAoB,GAAGT,cAAc,CAAC9N,MAAM,CAAC;QACtE,MAAMwO,YAAY,GAAG,CAACjP,IAAI,IAAIL,UAAU,CAACc,MAAM,CAAC;QAEhD,IAAI,CAAC2K,IAAI,IAAI6D,YAAY,EAAE,OAAO;YAAE9U,KAAK;YAAE0J;SAAU;QAErD,MAAMkL,UAAU,GAAG/C,WAAW,CAAC7R,KAAK,CAAC,GAAG6U,kBAAkB;QAC1D,MAAME,YAAY,GAAGrL,QAAQ,GAAG4K,QAAQ,CAACM,UAAU,EAAE,CAAC,CAAC;QAEvD,OAAO;YAAE5U,KAAK;YAAE0J,QAAQ,EAAEqL;SAAc;IAC1C;IAEA,MAAMjT,IAAI,GAAqB;QAC7B2H,UAAU;QACVE,OAAO;QACP2K;KACD;IACD,OAAOxS,IAAI;AACb;AC9DgB,SAAAkT,QAAQA,CACtBvO,SAAyB,EACzBwO,YAAyB,EACzBC,aAA0B,EAC1BvO,UAA0B,EAC1BC,YAA8B,EAC9BkN,YAA0B,EAC1BjN,YAA8B,EAAA;IAE9B,SAASH,QAAQA,CAACJ,MAAkB,EAAA;QAClC,MAAM6O,YAAY,GAAG7O,MAAM,CAACoD,QAAQ;QACpC,MAAM0L,SAAS,GAAG9O,MAAM,CAACtG,KAAK,KAAKiV,YAAY,CAAChP,GAAG,EAAE;QAErD6N,YAAY,CAAC7R,GAAG,CAACkT,YAAY,CAAC;QAE9B,IAAIA,YAAY,EAAE;YAChB,IAAIxO,UAAU,CAACuI,QAAQ,EAAE,EAAE;gBACzBzI,SAAS,CAAC/E,KAAK,EAAE;YACnB,CAAC,MAAM;gBACL+E,SAAS,CAACvD,MAAM,EAAE;gBAClBuD,SAAS,CAACtD,MAAM,CAAC,CAAC,CAAC;gBACnBsD,SAAS,CAACvD,MAAM,EAAE;YACpB;QACF;QAEA,IAAIkS,SAAS,EAAE;YACbF,aAAa,CAAChP,GAAG,CAAC+O,YAAY,CAAChP,GAAG,EAAE,CAAC;YACrCgP,YAAY,CAAC/O,GAAG,CAACI,MAAM,CAACtG,KAAK,CAAC;YAC9B6G,YAAY,CAACsD,IAAI,CAAC,QAAQ,CAAC;QAC7B;IACF;IAEA,SAAST,QAAQA,CAACjL,CAAS,EAAEwS,IAAa,EAAA;QACxC,MAAM3K,MAAM,GAAGM,YAAY,CAAC6C,UAAU,CAAChL,CAAC,EAAEwS,IAAI,CAAC;QAC/CvK,QAAQ,CAACJ,MAAM,CAAC;IAClB;IAEA,SAAStG,KAAKA,CAACvB,CAAS,EAAE0G,SAAiB,EAAA;QACzC,MAAMkQ,WAAW,GAAGJ,YAAY,CAAC9O,KAAK,EAAE,CAACD,GAAG,CAACzH,CAAC,CAAC;QAC/C,MAAM6H,MAAM,GAAGM,YAAY,CAAC+C,OAAO,CAAC0L,WAAW,CAACpP,GAAG,EAAE,EAAEd,SAAS,CAAC;QACjEuB,QAAQ,CAACJ,MAAM,CAAC;IAClB;IAEA,MAAMxE,IAAI,GAAiB;QACzB4H,QAAQ;QACR1J;KACD;IACD,OAAO8B,IAAI;AACb;SCzCgBwT,UAAUA,CACxBC,IAAiB,EACjB3I,MAAqB,EACrB0G,aAAiD,EACjD5M,QAAsB,EACtBC,UAA0B,EAC1B6O,UAA0B,EAC1B3O,YAA8B,EAC9B4O,UAAkC,EAAA;IAElC,MAAMC,oBAAoB,GAAG;QAAEpT,OAAO,EAAE,IAAI;QAAEqT,OAAO,EAAE;KAAM;IAC7D,IAAIC,gBAAgB,GAAG,CAAC;IAExB,SAASnS,IAAIA,CAAC6E,QAA2B,EAAA;QACvC,IAAI,CAACmN,UAAU,EAAE;QAEjB,SAASnI,eAAeA,CAACtN,KAAa,EAAA;YACpC,MAAM6V,OAAO,GAAG,IAAIC,IAAI,EAAE,CAACC,OAAO,EAAE;YACpC,MAAMlK,QAAQ,GAAGgK,OAAO,GAAGD,gBAAgB;YAE3C,IAAI/J,QAAQ,GAAG,EAAE,EAAE;YAEnBhF,YAAY,CAACsD,IAAI,CAAC,iBAAiB,CAAC;YACpCoL,IAAI,CAACS,UAAU,GAAG,CAAC;YAEnB,MAAMtC,KAAK,GAAGJ,aAAa,CAAC2C,SAAS,EAAEvC,KAAK,GAAKA,KAAK,CAACzK,QAAQ,CAACjJ,KAAK,CAAC,CAAC;YAEvE,IAAI,CAACjC,QAAQ,CAAC2V,KAAK,CAAC,EAAE;YAEtB/M,UAAU,CAACsD,WAAW,CAAC,CAAC,CAAC;YACzBvD,QAAQ,CAAC1G,KAAK,CAAC0T,KAAK,EAAE,CAAC,CAAC;YAExB7M,YAAY,CAACsD,IAAI,CAAC,YAAY,CAAC;QACjC;QAEAqL,UAAU,CAACvT,GAAG,CAACiU,QAAQ,EAAE,SAAS,EAAEC,gBAAgB,EAAE,KAAK,CAAC;QAE5DvJ,MAAM,CAAC7L,OAAO,CAAC,CAACqV,KAAK,EAAE1I,UAAU,KAAI;YACnC8H,UAAU,CAACvT,GAAG,CACZmU,KAAK,EACL,OAAO,GACNjV,GAAe,IAAI;gBAClB,IAAIjD,SAAS,CAACuX,UAAU,CAAC,IAAIA,UAAU,CAACnN,QAAQ,EAAEnH,GAAG,CAAC,EAAE;oBACtDmM,eAAe,CAACI,UAAU,CAAC;gBAC7B;aACD,EACDgI,oBAAoB,CACrB;QACH,CAAC,CAAC;IACJ;IAEA,SAASS,gBAAgBA,CAACE,KAAoB,EAAA;QAC5C,IAAIA,KAAK,CAACC,IAAI,KAAK,KAAK,EAAEV,gBAAgB,GAAG,IAAIE,IAAI,EAAE,CAACC,OAAO,EAAE;IACnE;IAEA,MAAMjU,IAAI,GAAmB;QAC3B2B;KACD;IACD,OAAO3B,IAAI;AACb;ACrEM,SAAUyU,QAAQA,CAACC,YAAoB,EAAA;IAC3C,IAAIC,KAAK,GAAGD,YAAY;IAExB,SAASvQ,GAAGA,GAAA;QACV,OAAOwQ,KAAK;IACd;IAEA,SAASvQ,GAAGA,CAACzH,CAAwB,EAAA;QACnCgY,KAAK,GAAGC,cAAc,CAACjY,CAAC,CAAC;IAC3B;IAEA,SAASwD,GAAGA,CAACxD,CAAwB,EAAA;QACnCgY,KAAK,IAAIC,cAAc,CAACjY,CAAC,CAAC;IAC5B;IAEA,SAASuR,QAAQA,CAACvR,CAAwB,EAAA;QACxCgY,KAAK,IAAIC,cAAc,CAACjY,CAAC,CAAC;IAC5B;IAEA,SAASiY,cAAcA,CAACjY,CAAwB,EAAA;QAC9C,OAAOV,QAAQ,CAACU,CAAC,CAAC,GAAGA,CAAC,GAAGA,CAAC,CAACwH,GAAG,EAAE;IAClC;IAEA,MAAMnE,IAAI,GAAiB;QACzBmE,GAAG;QACHC,GAAG;QACHjE,GAAG;QACH+N;KACD;IACD,OAAOlO,IAAI;AACb;AC9BgB,SAAA6U,SAASA,CACvBtS,IAAc,EACdsI,SAAsB,EAAA;IAEtB,MAAMiK,SAAS,GAAGvS,IAAI,CAACI,MAAM,KAAK,GAAG,GAAGoS,CAAC,GAAGC,CAAC;IAC7C,MAAMC,cAAc,GAAGpK,SAAS,CAACqK,KAAK;IACtC,IAAIC,cAAc,GAAkB,IAAI;IACxC,IAAItH,QAAQ,GAAG,KAAK;IAEpB,SAASkH,CAACA,CAACpY,CAAS,EAAA;QAClB,OAAO,CAAA,YAAA,EAAeA,CAAC,CAAa,WAAA,CAAA;IACtC;IAEA,SAASqY,CAACA,CAACrY,CAAS,EAAA;QAClB,OAAO,CAAA,gBAAA,EAAmBA,CAAC,CAAS,OAAA,CAAA;IACtC;IAEA,SAASyY,EAAEA,CAAC5Q,MAAc,EAAA;QACxB,IAAIqJ,QAAQ,EAAE;QAEd,MAAMwH,SAAS,GAAGhY,kBAAkB,CAACkF,IAAI,CAACc,SAAS,CAACmB,MAAM,CAAC,CAAC;QAC5D,IAAI6Q,SAAS,KAAKF,cAAc,EAAE;QAElCF,cAAc,CAACK,SAAS,GAAGR,SAAS,CAACO,SAAS,CAAC;QAC/CF,cAAc,GAAGE,SAAS;IAC5B;IAEA,SAASlH,YAAYA,CAACC,MAAe,EAAA;QACnCP,QAAQ,GAAG,CAACO,MAAM;IACpB;IAEA,SAASrN,KAAKA,GAAA;QACZ,IAAI8M,QAAQ,EAAE;QACdoH,cAAc,CAACK,SAAS,GAAG,EAAE;QAC7B,IAAI,CAACzK,SAAS,CAAC0K,YAAY,CAAC,OAAO,CAAC,EAAE1K,SAAS,CAAC2K,eAAe,CAAC,OAAO,CAAC;IAC1E;IAEA,MAAMxV,IAAI,GAAkB;QAC1Be,KAAK;QACLqU,EAAE;QACFjH;KACD;IACD,OAAOnO,IAAI;AACb;SC3BgByV,WAAWA,CACzBlT,IAAc,EACd7C,QAAgB,EAChB4O,WAAmB,EACnBjD,UAAoB,EACpBqK,kBAA4B,EAC5B3E,KAAe,EACfhB,WAAqB,EACrBrL,QAAsB,EACtBoG,MAAqB,EAAA;IAErB,MAAM6K,cAAc,GAAG,GAAG;IAC1B,MAAMC,QAAQ,GAAGpY,SAAS,CAACkY,kBAAkB,CAAC;IAC9C,MAAMG,SAAS,GAAGrY,SAAS,CAACkY,kBAAkB,CAAC,CAACI,OAAO,EAAE;IACzD,MAAMC,UAAU,GAAGC,WAAW,EAAE,CAAC9K,MAAM,CAAC+K,SAAS,EAAE,CAAC;IAEpD,SAASC,gBAAgBA,CAACC,OAAiB,EAAE7X,IAAY,EAAA;QACvD,OAAO6X,OAAO,CAACrX,MAAM,CAAC,CAACsT,CAAS,EAAE5T,CAAC,KAAI;YACrC,OAAO4T,CAAC,GAAGsD,kBAAkB,CAAClX,CAAC,CAAC;SACjC,EAAEF,IAAI,CAAC;IACV;IAEA,SAAS8X,WAAWA,CAACD,OAAiB,EAAEE,GAAW,EAAA;QACjD,OAAOF,OAAO,CAACrX,MAAM,CAAC,CAACsT,CAAW,EAAE5T,CAAC,KAAI;YACvC,MAAM8X,YAAY,GAAGJ,gBAAgB,CAAC9D,CAAC,EAAEiE,GAAG,CAAC;YAC7C,OAAOC,YAAY,GAAG,CAAC,GAAGlE,CAAC,CAAClH,MAAM,CAAC;gBAAC1M,CAAC;aAAC,CAAC,GAAG4T,CAAC;SAC5C,EAAE,EAAE,CAAC;IACR;IAEA,SAASmE,eAAeA,CAACjM,MAAc,EAAA;QACrC,OAAOyG,KAAK,CAACpT,GAAG,CAAC,CAACwR,IAAI,EAAEjR,KAAK,GAAA,CAAM;gBACjC0B,KAAK,EAAEuP,IAAI,GAAG9D,UAAU,CAACnN,KAAK,CAAC,GAAGyX,cAAc,GAAGrL,MAAM;gBACzDxK,GAAG,EAAEqP,IAAI,GAAGzP,QAAQ,GAAGiW,cAAc,GAAGrL;YACzC,CAAA,CAAC,CAAC;IACL;IAEA,SAASkM,cAAcA,CACrBL,OAAiB,EACjB7L,MAAc,EACdmM,SAAkB,EAAA;QAElB,MAAMC,WAAW,GAAGH,eAAe,CAACjM,MAAM,CAAC;QAE3C,OAAO6L,OAAO,CAACxY,GAAG,EAAEO,KAAK,IAAI;YAC3B,MAAMyY,OAAO,GAAGF,SAAS,GAAG,CAAC,GAAG,CAACnI,WAAW;YAC5C,MAAMsI,OAAO,GAAGH,SAAS,GAAGnI,WAAW,GAAG,CAAC;YAC3C,MAAMuI,SAAS,GAAGJ,SAAS,GAAG,KAAK,GAAG,OAAO;YAC7C,MAAMK,SAAS,GAAGJ,WAAW,CAACxY,KAAK,CAAC,CAAC2Y,SAAS,CAAC;YAE/C,OAAO;gBACL3Y,KAAK;gBACL4Y,SAAS;gBACTC,aAAa,EAAEtC,QAAQ,CAAC,CAAC,CAAC,CAAC;gBAC3BK,SAAS,EAAED,SAAS,CAACtS,IAAI,EAAEuI,MAAM,CAAC5M,KAAK,CAAC,CAAC;gBACzCsG,MAAM,EAAEA,IAAOE,QAAQ,CAACP,GAAG,EAAE,GAAG2S,SAAS,GAAGH,OAAO,GAAGC;aACvD;QACH,CAAC,CAAC;IACJ;IAEA,SAASZ,WAAWA,GAAA;QAClB,MAAMK,GAAG,GAAGtG,WAAW,CAAC,CAAC,CAAC;QAC1B,MAAMoG,OAAO,GAAGC,WAAW,CAACP,SAAS,EAAEQ,GAAG,CAAC;QAC3C,OAAOG,cAAc,CAACL,OAAO,EAAE7H,WAAW,EAAE,KAAK,CAAC;IACpD;IAEA,SAAS2H,SAASA,GAAA;QAChB,MAAMI,GAAG,GAAG3W,QAAQ,GAAGqQ,WAAW,CAAC,CAAC,CAAC,GAAG,CAAC;QACzC,MAAMoG,OAAO,GAAGC,WAAW,CAACR,QAAQ,EAAES,GAAG,CAAC;QAC1C,OAAOG,cAAc,CAACL,OAAO,EAAE,CAAC7H,WAAW,EAAE,IAAI,CAAC;IACpD;IAEA,SAAS0I,OAAOA,GAAA;QACd,OAAOjB,UAAU,CAACkB,KAAK,CAAC,CAAC,EAAE/Y,KAAAA,EAAO,KAAI;YACpC,MAAMgZ,YAAY,GAAGtB,QAAQ,CAAC5U,MAAM,EAAExC,CAAC,GAAKA,CAAC,KAAKN,KAAK,CAAC;YACxD,OAAOgY,gBAAgB,CAACgB,YAAY,EAAExX,QAAQ,CAAC,IAAI,GAAG;QACxD,CAAC,CAAC;IACJ;IAEA,SAASqE,IAAIA,GAAA;QACXgS,UAAU,CAAC9W,OAAO,EAAE6X,SAAS,IAAI;YAC/B,MAAM,EAAEtS,MAAM,EAAEsQ,SAAS,EAAEiC,aAAAA,EAAe,GAAGD,SAAS;YACtD,MAAMK,aAAa,GAAG3S,MAAM,EAAE;YAC9B,IAAI2S,aAAa,KAAKJ,aAAa,CAAC5S,GAAG,EAAE,EAAE;YAC3C2Q,SAAS,CAACM,EAAE,CAAC+B,aAAa,CAAC;YAC3BJ,aAAa,CAAC3S,GAAG,CAAC+S,aAAa,CAAC;QAClC,CAAC,CAAC;IACJ;IAEA,SAASpW,KAAKA,GAAA;QACZgV,UAAU,CAAC9W,OAAO,EAAE6X,SAAS,GAAKA,SAAS,CAAChC,SAAS,CAAC/T,KAAK,EAAE,CAAC;IAChE;IAEA,MAAMf,IAAI,GAAoB;QAC5BgX,OAAO;QACPjW,KAAK;QACLgD,IAAI;QACJgS;KACD;IACD,OAAO/V,IAAI;AACb;SC5GgBoX,aAAaA,CAC3BvM,SAAsB,EACtB9F,YAA8B,EAC9BsS,WAAoC,EAAA;IAEpC,IAAIC,gBAAkC;IACtC,IAAIhM,SAAS,GAAG,KAAK;IAErB,SAAS3J,IAAIA,CAAC6E,QAA2B,EAAA;QACvC,IAAI,CAAC6Q,WAAW,EAAE;QAElB,SAAS7L,eAAeA,CAAC+L,SAA2B,EAAA;YAClD,KAAK,MAAMC,QAAQ,IAAID,SAAS,CAAE;gBAChC,IAAIC,QAAQ,CAACnX,IAAI,KAAK,WAAW,EAAE;oBACjCmG,QAAQ,CAACyF,MAAM,EAAE;oBACjBlH,YAAY,CAACsD,IAAI,CAAC,eAAe,CAAC;oBAClC;gBACF;YACF;QACF;QAEAiP,gBAAgB,GAAG,IAAIG,gBAAgB,EAAEF,SAAS,IAAI;YACpD,IAAIjM,SAAS,EAAE;YACf,IAAIlP,SAAS,CAACib,WAAW,CAAC,IAAIA,WAAW,CAAC7Q,QAAQ,EAAE+Q,SAAS,CAAC,EAAE;gBAC9D/L,eAAe,CAAC+L,SAAS,CAAC;YAC5B;QACF,CAAC,CAAC;QAEFD,gBAAgB,CAACnL,OAAO,CAACtB,SAAS,EAAE;YAAE6M,SAAS,EAAE;QAAM,CAAA,CAAC;IAC1D;IAEA,SAAS5V,OAAOA,GAAA;QACd,IAAIwV,gBAAgB,EAAEA,gBAAgB,CAAClL,UAAU,EAAE;QACnDd,SAAS,GAAG,IAAI;IAClB;IAEA,MAAMtL,IAAI,GAAsB;QAC9B2B,IAAI;QACJG;KACD;IACD,OAAO9B,IAAI;AACb;AC1CM,SAAU2X,YAAYA,CAC1B9M,SAAsB,EACtBC,MAAqB,EACrB/F,YAA8B,EAC9B6S,SAAkC,EAAA;IAElC,MAAMC,oBAAoB,GAA6B,CAAA,CAAE;IACzD,IAAIC,WAAW,GAAoB,IAAI;IACvC,IAAIC,cAAc,GAAoB,IAAI;IAC1C,IAAIC,oBAA0C;IAC9C,IAAI1M,SAAS,GAAG,KAAK;IAErB,SAAS3J,IAAIA,GAAA;QACXqW,oBAAoB,GAAG,IAAIC,oBAAoB,EAC5CxM,OAAO,IAAI;YACV,IAAIH,SAAS,EAAE;YAEfG,OAAO,CAACxM,OAAO,EAAEyM,KAAK,IAAI;gBACxB,MAAMxN,KAAK,GAAG4M,MAAM,CAACe,OAAO,CAAcH,KAAK,CAAClH,MAAM,CAAC;gBACvDqT,oBAAoB,CAAC3Z,KAAK,CAAC,GAAGwN,KAAK;YACrC,CAAC,CAAC;YAEFoM,WAAW,GAAG,IAAI;YAClBC,cAAc,GAAG,IAAI;YACrBhT,YAAY,CAACsD,IAAI,CAAC,cAAc,CAAC;QACnC,CAAC,EACD;YACEoL,IAAI,EAAE5I,SAAS,CAACqN,aAAa;YAC7BN;QACD,CAAA,CACF;QAED9M,MAAM,CAAC7L,OAAO,EAAEqV,KAAK,GAAK0D,oBAAoB,CAAC7L,OAAO,CAACmI,KAAK,CAAC,CAAC;IAChE;IAEA,SAASxS,OAAOA,GAAA;QACd,IAAIkW,oBAAoB,EAAEA,oBAAoB,CAAC5L,UAAU,EAAE;QAC3Dd,SAAS,GAAG,IAAI;IAClB;IAEA,SAAS6M,gBAAgBA,CAACC,MAAe,EAAA;QACvC,OAAO1a,UAAU,CAACma,oBAAoB,CAAC,CAAC/Y,MAAM,CAC5C,CAACuZ,IAAc,EAAEzM,UAAU,KAAI;YAC7B,MAAM1N,KAAK,GAAGoa,QAAQ,CAAC1M,UAAU,CAAC;YAClC,MAAM,EAAE2M,cAAAA,EAAgB,GAAGV,oBAAoB,CAAC3Z,KAAK,CAAC;YACtD,MAAMsa,WAAW,GAAGJ,MAAM,IAAIG,cAAc;YAC5C,MAAME,cAAc,GAAG,CAACL,MAAM,IAAI,CAACG,cAAc;YAEjD,IAAIC,WAAW,IAAIC,cAAc,EAAEJ,IAAI,CAACvX,IAAI,CAAC5C,KAAK,CAAC;YACnD,OAAOma,IAAI;SACZ,EACD,EAAE,CACH;IACH;IAEA,SAASlU,GAAGA,CAACiU,MAAA,GAAkB,IAAI,EAAA;QACjC,IAAIA,MAAM,IAAIN,WAAW,EAAE,OAAOA,WAAW;QAC7C,IAAI,CAACM,MAAM,IAAIL,cAAc,EAAE,OAAOA,cAAc;QAEpD,MAAMxG,YAAY,GAAG4G,gBAAgB,CAACC,MAAM,CAAC;QAE7C,IAAIA,MAAM,EAAEN,WAAW,GAAGvG,YAAY;QACtC,IAAI,CAAC6G,MAAM,EAAEL,cAAc,GAAGxG,YAAY;QAE1C,OAAOA,YAAY;IACrB;IAEA,MAAMvR,IAAI,GAAqB;QAC7B2B,IAAI;QACJG,OAAO;QACPqC;KACD;IAED,OAAOnE,IAAI;AACb;AC9EgB,SAAA0Y,UAAUA,CACxBnW,IAAc,EACdkO,aAA2B,EAC3BC,UAA0B,EAC1B5F,MAAqB,EACrB6N,WAAoB,EACpBrZ,WAAuB,EAAA;IAEvB,MAAM,EAAE2D,WAAW,EAAEJ,SAAS,EAAEE,OAAAA,EAAS,GAAGR,IAAI;IAChD,MAAMqW,WAAW,GAAGlI,UAAU,CAAC,CAAC,CAAC,IAAIiI,WAAW;IAChD,MAAME,QAAQ,GAAGC,eAAe,EAAE;IAClC,MAAMC,MAAM,GAAGC,aAAa,EAAE;IAC9B,MAAM3N,UAAU,GAAGqF,UAAU,CAAC/S,GAAG,CAACsF,WAAW,CAAC;IAC9C,MAAMyS,kBAAkB,GAAGuD,eAAe,EAAE;IAE5C,SAASH,eAAeA,GAAA;QACtB,IAAI,CAACF,WAAW,EAAE,OAAO,CAAC;QAC1B,MAAMM,SAAS,GAAGxI,UAAU,CAAC,CAAC,CAAC;QAC/B,OAAOhU,OAAO,CAAC+T,aAAa,CAAC5N,SAAS,CAAC,GAAGqW,SAAS,CAACrW,SAAS,CAAC,CAAC;IACjE;IAEA,SAASmW,aAAaA,GAAA;QACpB,IAAI,CAACJ,WAAW,EAAE,OAAO,CAAC;QAC1B,MAAM1D,KAAK,GAAG5V,WAAW,CAAC6Z,gBAAgB,CAACtb,SAAS,CAACiN,MAAM,CAAC,CAAC;QAC7D,OAAO6E,UAAU,CAACuF,KAAK,CAACkE,gBAAgB,CAAC,CAAUrW,OAAAA,EAAAA,OAAO,CAAE,CAAA,CAAC,CAAC;IAChE;IAEA,SAASkW,eAAeA,GAAA;QACtB,OAAOvI,UAAU,CACd/S,GAAG,CAAC,CAACwT,IAAI,EAAEjT,KAAK,EAAEgT,KAAK,KAAI;YAC1B,MAAM1B,OAAO,GAAG,CAACtR,KAAK;YACtB,MAAMuR,MAAM,GAAGxR,gBAAgB,CAACiT,KAAK,EAAEhT,KAAK,CAAC;YAC7C,IAAIsR,OAAO,EAAE,OAAOnE,UAAU,CAACnN,KAAK,CAAC,GAAG2a,QAAQ;YAChD,IAAIpJ,MAAM,EAAE,OAAOpE,UAAU,CAACnN,KAAK,CAAC,GAAG6a,MAAM;YAC7C,OAAO7H,KAAK,CAAChT,KAAK,GAAG,CAAC,CAAC,CAAC2E,SAAS,CAAC,GAAGsO,IAAI,CAACtO,SAAS,CAAC;QACtD,CAAC,CAAC,CACDlF,GAAG,CAACjB,OAAO,CAAC;IACjB;IAEA,MAAMsD,IAAI,GAAmB;QAC3BqL,UAAU;QACVqK,kBAAkB;QAClBmD,QAAQ;QACRE;KACD;IACD,OAAO/Y,IAAI;AACb;SCzCgBqZ,cAAcA,CAC5B9W,IAAc,EACd7C,QAAgB,EAChBiR,cAAwC,EACxC5M,IAAa,EACb0M,aAA2B,EAC3BC,UAA0B,EAC1BmI,QAAgB,EAChBE,MAAc,EACdtK,cAAsB,EAAA;IAEtB,MAAM,EAAE5L,SAAS,EAAEE,OAAO,EAAEM,SAAAA,EAAW,GAAGd,IAAI;IAC9C,MAAM+W,aAAa,GAAGrd,QAAQ,CAAC0U,cAAc,CAAC;IAE9C,SAAS4I,QAAQA,CAAO9b,KAAa,EAAE+b,SAAiB,EAAA;QACtD,OAAOhc,SAAS,CAACC,KAAK,CAAC,CACpBuD,MAAM,EAAExC,CAAC,GAAKA,CAAC,GAAGgb,SAAS,KAAK,CAAC,CAAC,CAClC7b,GAAG,EAAEa,CAAC,GAAKf,KAAK,CAACoS,KAAK,CAACrR,CAAC,EAAEA,CAAC,GAAGgb,SAAS,CAAC,CAAC;IAC9C;IAEA,SAASC,MAAMA,CAAOhc,KAAa,EAAA;QACjC,IAAI,CAACA,KAAK,CAACO,MAAM,EAAE,OAAO,EAAE;QAE5B,OAAOR,SAAS,CAACC,KAAK,CAAC,CACpBqB,MAAM,CAAC,CAAC+S,MAAgB,EAAE6H,KAAK,EAAExb,KAAK,KAAI;YACzC,MAAMyb,KAAK,GAAG9b,SAAS,CAACgU,MAAM,CAAC,IAAI,CAAC;YACpC,MAAMrC,OAAO,GAAGmK,KAAK,KAAK,CAAC;YAC3B,MAAMlK,MAAM,GAAGiK,KAAK,KAAK5b,cAAc,CAACL,KAAK,CAAC;YAE9C,MAAMmc,KAAK,GAAGnJ,aAAa,CAAC5N,SAAS,CAAC,GAAG6N,UAAU,CAACiJ,KAAK,CAAC,CAAC9W,SAAS,CAAC;YACrE,MAAMgX,KAAK,GAAGpJ,aAAa,CAAC5N,SAAS,CAAC,GAAG6N,UAAU,CAACgJ,KAAK,CAAC,CAAC3W,OAAO,CAAC;YACnE,MAAM+W,IAAI,GAAG,CAAC/V,IAAI,IAAIyL,OAAO,GAAGnM,SAAS,CAACwV,QAAQ,CAAC,GAAG,CAAC;YACvD,MAAMkB,IAAI,GAAG,CAAChW,IAAI,IAAI0L,MAAM,GAAGpM,SAAS,CAAC0V,MAAM,CAAC,GAAG,CAAC;YACpD,MAAMiB,SAAS,GAAGtd,OAAO,CAACmd,KAAK,GAAGE,IAAI,GAAA,CAAIH,KAAK,GAAGE,IAAI,CAAC,CAAC;YAExD,IAAI5b,KAAK,IAAI8b,SAAS,GAAGta,QAAQ,GAAG+O,cAAc,EAAEoD,MAAM,CAAC/Q,IAAI,CAAC4Y,KAAK,CAAC;YACtE,IAAIjK,MAAM,EAAEoC,MAAM,CAAC/Q,IAAI,CAACrD,KAAK,CAACO,MAAM,CAAC;YACrC,OAAO6T,MAAM;QACf,CAAC,EAAE,EAAE,CAAC,CACLlU,GAAG,CAAC,CAACsc,WAAW,EAAE/b,KAAK,EAAE2T,MAAM,KAAI;YAClC,MAAMqI,YAAY,GAAGtd,IAAI,CAACmB,GAAG,CAAC8T,MAAM,CAAC3T,KAAK,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC;YACrD,OAAOT,KAAK,CAACoS,KAAK,CAACqK,YAAY,EAAED,WAAW,CAAC;QAC/C,CAAC,CAAC;IACN;IAEA,SAASrJ,WAAWA,CAAOnT,KAAa,EAAA;QACtC,OAAO6b,aAAa,GAAGC,QAAQ,CAAC9b,KAAK,EAAEkT,cAAc,CAAC,GAAG8I,MAAM,CAAChc,KAAK,CAAC;IACxE;IAEA,MAAMuC,IAAI,GAAuB;QAC/B4Q;KACD;IACD,OAAO5Q,IAAI;AACb;ACOgB,SAAAma,MAAMA,CACpB1G,IAAiB,EACjB5I,SAAsB,EACtBC,MAAqB,EACrB3J,aAAuB,EACvB7B,WAAuB,EACvBiB,OAAoB,EACpBwE,YAA8B,EAAA;IAE9B,UAAA;IACA,MAAM,EACJtF,KAAK,EACL8C,IAAI,EAAE6X,UAAU,EAChB/W,SAAS,EACTgX,UAAU,EACVtW,IAAI,EACJqJ,QAAQ,EACRnI,QAAQ,EACRC,aAAa,EACboV,eAAe,EACf3J,cAAc,EAAEC,WAAW,EAC3BzL,SAAS,EACTqJ,aAAa,EACbzD,WAAW,EACXsM,WAAW,EACXhS,SAAS,EACTsO,UAAAA,EACD,GAAGpT,OAAO;IAEX,eAAA;IACA,MAAMkO,cAAc,GAAG,CAAC;IACxB,MAAMzD,SAAS,GAAGf,SAAS,EAAE;IAC7B,MAAMwG,aAAa,GAAGzF,SAAS,CAACjL,OAAO,CAAC8K,SAAS,CAAC;IAClD,MAAM6F,UAAU,GAAG5F,MAAM,CAACnN,GAAG,CAACqN,SAAS,CAACjL,OAAO,CAAC;IAChD,MAAMwC,IAAI,GAAGD,IAAI,CAAC8X,UAAU,EAAE/W,SAAS,CAAC;IACxC,MAAM3D,QAAQ,GAAG6C,IAAI,CAACU,WAAW,CAACwN,aAAa,CAAC;IAChD,MAAMzL,aAAa,GAAG2F,aAAa,CAACjL,QAAQ,CAAC;IAC7C,MAAM8Q,SAAS,GAAGhR,SAAS,CAACC,KAAK,EAAEC,QAAQ,CAAC;IAC5C,MAAM4R,YAAY,GAAG,CAACvN,IAAI,IAAI,CAAC,CAACyK,aAAa;IAC7C,MAAMmK,WAAW,GAAG5U,IAAI,IAAI,CAAC,CAACyK,aAAa;IAC3C,MAAM,EAAEnD,UAAU,EAAEqK,kBAAkB,EAAEmD,QAAQ,EAAEE,MAAAA,EAAQ,GAAGL,UAAU,CACrEnW,IAAI,EACJkO,aAAa,EACbC,UAAU,EACV5F,MAAM,EACN6N,WAAW,EACXrZ,WAAW,CACZ;IACD,MAAMqR,cAAc,GAAG0I,cAAc,CACnC9W,IAAI,EACJ7C,QAAQ,EACRkR,WAAW,EACX7M,IAAI,EACJ0M,aAAa,EACbC,UAAU,EACVmI,QAAQ,EACRE,MAAM,EACNtK,cAAc,CACf;IACD,MAAM,EAAEsC,KAAK,EAAExC,YAAAA,EAAc,GAAGgC,WAAW,CACzChO,IAAI,EACJiO,SAAS,EACTC,aAAa,EACbC,UAAU,EACVC,cAAc,CACf;IACD,MAAMrC,WAAW,GAAG,CAACzQ,SAAS,CAACkT,KAAK,CAAC,GAAGlT,SAAS,CAAC6X,kBAAkB,CAAC;IACrE,MAAM,EAAE3G,cAAc,EAAEF,kBAAAA,EAAoB,GAAGR,aAAa,CAC1D3O,QAAQ,EACR4O,WAAW,EACXC,YAAY,EACZC,aAAa,EACbC,cAAc,CACf;IACD,MAAMsB,WAAW,GAAGuB,YAAY,GAAGvC,cAAc,GAAGR,YAAY;IAChE,MAAM,EAAEd,KAAAA,EAAO,GAAGqC,WAAW,CAACxB,WAAW,EAAEyB,WAAW,EAAEhM,IAAI,CAAC;IAE7D,UAAA;IACA,MAAM7F,KAAK,GAAG4F,OAAO,CAAChG,cAAc,CAACiS,WAAW,CAAC,EAAEsK,UAAU,EAAEtW,IAAI,CAAC;IACpE,MAAMqP,aAAa,GAAGlV,KAAK,CAACmG,KAAK,EAAE;IACnC,MAAMkN,YAAY,GAAG/T,SAAS,CAACsN,MAAM,CAAC;IAEtC,YAAA;IACA,MAAM1J,MAAM,GAAyBA,CAAC,EACpCmZ,WAAW,EACX1V,UAAU,EACV6J,YAAY,EACZnO,OAAO,EAAE,EAAEwD,IAAAA,EAAM,EAClB,KAAI;QACH,IAAI,CAACA,IAAI,EAAE2K,YAAY,CAAC/K,SAAS,CAAC4W,WAAW,CAACtS,WAAW,EAAE,CAAC;QAC5DpD,UAAU,CAACkI,IAAI,EAAE;KAClB;IAED,MAAM1L,MAAM,GAAyBA,CACnC,EACEwD,UAAU,EACViQ,SAAS,EACTpQ,QAAQ,EACR4H,cAAc,EACdC,gBAAgB,EAChBiO,YAAY,EACZC,WAAW,EACXF,WAAW,EACX5V,SAAS,EACTI,YAAY,EACZ2J,YAAY,EACZnO,OAAO,EAAE,EAAEwD,IAAAA,EAAM,EAClB,EACD5B,KAAK,KACH;QACF,MAAMuY,YAAY,GAAG7V,UAAU,CAACsI,OAAO,EAAE;QACzC,MAAMwN,YAAY,GAAG,CAACjM,YAAY,CAACZ,eAAe,EAAE;QACpD,MAAM8M,UAAU,GAAG7W,IAAI,GAAG2W,YAAY,GAAGA,YAAY,IAAIC,YAAY;QACrE,MAAME,iBAAiB,GAAGD,UAAU,IAAI,CAACL,WAAW,CAACtS,WAAW,EAAE;QAElE,IAAI4S,iBAAiB,EAAElW,SAAS,CAAC5C,IAAI,EAAE;QAEvC,MAAM+Y,oBAAoB,GACxBpW,QAAQ,CAACP,GAAG,EAAE,GAAGhC,KAAK,GAAGoK,gBAAgB,CAACpI,GAAG,EAAE,GAAA,CAAI,CAAC,GAAGhC,KAAK,CAAC;QAE/DmK,cAAc,CAAClI,GAAG,CAAC0W,oBAAoB,CAAC;QAExC,IAAI/W,IAAI,EAAE;YACRyW,YAAY,CAACzW,IAAI,CAACc,UAAU,CAACxB,SAAS,EAAE,CAAC;YACzCoX,WAAW,CAAC1W,IAAI,EAAE;QACpB;QAEA+Q,SAAS,CAACM,EAAE,CAAC9I,cAAc,CAACnI,GAAG,EAAE,CAAC;QAElC,IAAI0W,iBAAiB,EAAE9V,YAAY,CAACsD,IAAI,CAAC,QAAQ,CAAC;QAClD,IAAI,CAACuS,UAAU,EAAE7V,YAAY,CAACsD,IAAI,CAAC,QAAQ,CAAC;KAC7C;IAED,MAAM1D,SAAS,GAAGzD,UAAU,CAC1BC,aAAa,EACb7B,WAAW,EACX,IAAM8B,MAAM,CAAC2Z,MAAM,CAAC,GACnB5Y,KAAa,GAAKd,MAAM,CAAC0Z,MAAM,EAAE5Y,KAAK,CAAC,CACzC;IAED,SAAA;IACA,MAAMgH,QAAQ,GAAG,IAAI;IACrB,MAAM6R,aAAa,GAAGjL,WAAW,CAAC7R,KAAK,CAACiG,GAAG,EAAE,CAAC;IAC9C,MAAMO,QAAQ,GAAG+P,QAAQ,CAACuG,aAAa,CAAC;IACxC,MAAMzO,gBAAgB,GAAGkI,QAAQ,CAACuG,aAAa,CAAC;IAChD,MAAM1O,cAAc,GAAGmI,QAAQ,CAACuG,aAAa,CAAC;IAC9C,MAAMxW,MAAM,GAAGiQ,QAAQ,CAACuG,aAAa,CAAC;IACtC,MAAMnW,UAAU,GAAGwH,UAAU,CAC3B3H,QAAQ,EACR4H,cAAc,EACdC,gBAAgB,EAChB/H,MAAM,EACN4I,QAAQ,EACRjE,QAAQ,CACT;IACD,MAAMrE,YAAY,GAAGiN,YAAY,CAC/BhO,IAAI,EACJgM,WAAW,EACXzB,WAAW,EACXb,KAAK,EACLjJ,MAAM,CACP;IACD,MAAMI,QAAQ,GAAGsO,QAAQ,CACvBvO,SAAS,EACTzG,KAAK,EACLkV,aAAa,EACbvO,UAAU,EACVC,YAAY,EACZN,MAAM,EACNO,YAAY,CACb;IACD,MAAMkW,cAAc,GAAG3K,cAAc,CAAC7C,KAAK,CAAC;IAC5C,MAAMiG,UAAU,GAAGzT,UAAU,EAAE;IAC/B,MAAMib,YAAY,GAAGvD,YAAY,CAC/B9M,SAAS,EACTC,MAAM,EACN/F,YAAY,EACZuV,eAAe,CAChB;IACD,MAAM,EAAE9I,aAAAA,EAAe,GAAGH,aAAa,CACrCC,YAAY,EACZ9C,aAAa,EACbuB,WAAW,EACXlB,kBAAkB,EAClB8B,cAAc,EACdY,YAAY,CACb;IACD,MAAM4J,UAAU,GAAG3H,UAAU,CAC3BC,IAAI,EACJ3I,MAAM,EACN0G,aAAa,EACb5M,QAAQ,EACRC,UAAU,EACV6O,UAAU,EACV3O,YAAY,EACZ4O,UAAU,CACX;IAED,SAAA;IACA,MAAMoH,MAAM,GAAe;QACzB5Z,aAAa;QACb7B,WAAW;QACXyF,YAAY;QACZ0L,aAAa;QACbC,UAAU;QACV/L,SAAS;QACTpC,IAAI;QACJgY,WAAW,EAAEjW,WAAW,CACtB/B,IAAI,EACJkR,IAAI,EACJtS,aAAa,EACb7B,WAAW,EACXkF,MAAM,EACN6E,WAAW,CAAC9G,IAAI,EAAEjD,WAAW,CAAC,EAC9BoF,QAAQ,EACRC,SAAS,EACTC,QAAQ,EACRC,UAAU,EACVC,YAAY,EACZ5G,KAAK,EACL6G,YAAY,EACZC,aAAa,EACbC,QAAQ,EACRC,aAAa,EACbC,SAAS,EACTgE,QAAQ,EACR9D,SAAS,CACV;QACDqO,UAAU;QACV1O,aAAa;QACb9G,KAAK;QACLkV,aAAa;QACb3F,KAAK;QACL/I,QAAQ;QACR4H,cAAc;QACdC,gBAAgB;QAChBhM,OAAO;QACP6a,aAAa,EAAExQ,aAAa,CAC1BC,SAAS,EACT9F,YAAY,EACZzF,WAAW,EACXwL,MAAM,EACNvI,IAAI,EACJwI,WAAW,EACXC,SAAS,CACV;QACDnG,UAAU;QACV6J,YAAY,EAAElB,YAAY,CACxBC,KAAK,EACLnB,cAAc,EACd9H,MAAM,EACNK,UAAU,EACVG,aAAa,CACd;QACDwV,YAAY,EAAExK,YAAY,CAAC1B,WAAW,EAAEb,KAAK,EAAEnB,cAAc,EAAE;YAC7D5H,QAAQ;YACR4H,cAAc;YACdC,gBAAgB;YAChB/H,MAAM;SACP,CAAC;QACFyW,cAAc;QACdI,cAAc,EAAEtL,WAAW,CAACpS,GAAG,CAACsd,cAAc,CAAC9W,GAAG,CAAC;QACnD4L,WAAW;QACXjL,YAAY;QACZF,QAAQ;QACR6V,WAAW,EAAEhF,WAAW,CACtBlT,IAAI,EACJ7C,QAAQ,EACR4O,WAAW,EACXjD,UAAU,EACVqK,kBAAkB,EAClB3E,KAAK,EACLhB,WAAW,EACXzD,cAAc,EACdxB,MAAM,CACP;QACDqQ,UAAU;QACVG,aAAa,EAAElE,aAAa,CAACvM,SAAS,EAAE9F,YAAY,EAAEsS,WAAW,CAAC;QAClE6D,YAAY;QACZ3J,YAAY;QACZC,aAAa;QACbb,cAAc;QACdnM,MAAM;QACNsQ,SAAS,EAAED,SAAS,CAACtS,IAAI,EAAEsI,SAAS;KACrC;IAED,OAAOkQ,MAAM;AACf;SC5UgBQ,YAAYA,GAAA;IAC1B,IAAIrb,SAAS,GAAkB,CAAA,CAAE;IACjC,IAAIsb,GAAsB;IAE1B,SAAS7Z,IAAIA,CAAC6E,QAA2B,EAAA;QACvCgV,GAAG,GAAGhV,QAAQ;IAChB;IAEA,SAASiV,YAAYA,CAACpc,GAAmB,EAAA;QACvC,OAAOa,SAAS,CAACb,GAAG,CAAC,IAAI,EAAE;IAC7B;IAEA,SAASgJ,IAAIA,CAAChJ,GAAmB,EAAA;QAC/Boc,YAAY,CAACpc,GAAG,CAAC,CAACJ,OAAO,EAAEyc,CAAC,GAAKA,CAAC,CAACF,GAAG,EAAEnc,GAAG,CAAC,CAAC;QAC7C,OAAOW,IAAI;IACb;IAEA,SAAS2b,EAAEA,CAACtc,GAAmB,EAAEuc,EAAgB,EAAA;QAC/C1b,SAAS,CAACb,GAAG,CAAC,GAAGoc,YAAY,CAACpc,GAAG,CAAC,CAAC6L,MAAM,CAAC;YAAC0Q,EAAE;SAAC,CAAC;QAC/C,OAAO5b,IAAI;IACb;IAEA,SAAS6b,GAAGA,CAACxc,GAAmB,EAAEuc,EAAgB,EAAA;QAChD1b,SAAS,CAACb,GAAG,CAAC,GAAGoc,YAAY,CAACpc,GAAG,CAAC,CAAC2B,MAAM,EAAE0a,CAAC,GAAKA,CAAC,KAAKE,EAAE,CAAC;QAC1D,OAAO5b,IAAI;IACb;IAEA,SAASe,KAAKA,GAAA;QACZb,SAAS,GAAG,CAAA,CAAE;IAChB;IAEA,MAAMF,IAAI,GAAqB;QAC7B2B,IAAI;QACJ0G,IAAI;QACJwT,GAAG;QACHF,EAAE;QACF5a;KACD;IACD,OAAOf,IAAI;AACb;AC5BO,MAAM8b,cAAc,GAAgB;IACzCrc,KAAK,EAAE,QAAQ;IACf8C,IAAI,EAAE,GAAG;IACTsI,SAAS,EAAE,IAAI;IACfC,MAAM,EAAE,IAAI;IACZ0D,aAAa,EAAE,WAAW;IAC1BnL,SAAS,EAAE,KAAK;IAChBsN,cAAc,EAAE,CAAC;IACjB2J,eAAe,EAAE,CAAC;IAClByB,WAAW,EAAE,CAAA,CAAE;IACf9W,QAAQ,EAAE,KAAK;IACfC,aAAa,EAAE,EAAE;IACjBnB,IAAI,EAAE,KAAK;IACXoB,SAAS,EAAE,KAAK;IAChBiI,QAAQ,EAAE,EAAE;IACZiN,UAAU,EAAE,CAAC;IACbjM,MAAM,EAAE,IAAI;IACZ/I,SAAS,EAAE,IAAI;IACf0F,WAAW,EAAE,IAAI;IACjBsM,WAAW,EAAE,IAAI;IACjB1D,UAAU,EAAE;CACb;ACjDK,SAAUqI,cAAcA,CAAC1c,WAAuB,EAAA;IACpD,SAAS2c,YAAYA,CACnBC,QAAe,EACfC,QAAgB,EAAA;QAEhB,OAAcxd,gBAAgB,CAACud,QAAQ,EAAEC,QAAQ,IAAI,CAAA,CAAE,CAAC;IAC1D;IAEA,SAASC,cAAcA,CAA2B7b,OAAa,EAAA;QAC7D,MAAM6b,cAAc,GAAG7b,OAAO,CAACwb,WAAW,IAAI,CAAA,CAAE;QAChD,MAAMM,mBAAmB,GAAG3e,UAAU,CAAC0e,cAAc,CAAC,CACnDpb,MAAM,EAAEsb,KAAK,GAAKhd,WAAW,CAACid,UAAU,CAACD,KAAK,CAAC,CAACE,OAAO,CAAC,CACxD7e,GAAG,EAAE2e,KAAK,GAAKF,cAAc,CAACE,KAAK,CAAC,CAAC,CACrCxd,MAAM,CAAC,CAACsT,CAAC,EAAEqK,WAAW,GAAKR,YAAY,CAAC7J,CAAC,EAAEqK,WAAW,CAAC,EAAE,CAAA,CAAE,CAAC;QAE/D,OAAOR,YAAY,CAAC1b,OAAO,EAAE8b,mBAAmB,CAAC;IACnD;IAEA,SAASK,mBAAmBA,CAACC,WAA0B,EAAA;QACrD,OAAOA,WAAW,CACfhf,GAAG,EAAE4C,OAAO,GAAK7C,UAAU,CAAC6C,OAAO,CAACwb,WAAW,IAAI,CAAA,CAAE,CAAC,CAAC,CACvDjd,MAAM,CAAC,CAAC8d,GAAG,EAAEC,YAAY,GAAKD,GAAG,CAAC1R,MAAM,CAAC2R,YAAY,CAAC,EAAE,EAAE,CAAC,CAC3Dlf,GAAG,CAAC2B,WAAW,CAACid,UAAU,CAAC;IAChC;IAEA,MAAMvc,IAAI,GAAuB;QAC/Bic,YAAY;QACZG,cAAc;QACdM;KACD;IACD,OAAO1c,IAAI;AACb;ACjCM,SAAU8c,cAAcA,CAC5BC,cAAkC,EAAA;IAElC,IAAIC,aAAa,GAAsB,EAAE;IAEzC,SAASrb,IAAIA,CACX6E,QAA2B,EAC3ByW,OAA0B,EAAA;QAE1BD,aAAa,GAAGC,OAAO,CAACjc,MAAM,CAC5B,CAAC,EAAET,OAAAA,EAAS,GAAKwc,cAAc,CAACX,cAAc,CAAC7b,OAAO,CAAC,CAAC6N,MAAM,KAAK,KAAK,CACzE;QACD4O,aAAa,CAAC/d,OAAO,EAAEie,MAAM,GAAKA,MAAM,CAACvb,IAAI,CAAC6E,QAAQ,EAAEuW,cAAc,CAAC,CAAC;QAExE,OAAOE,OAAO,CAACne,MAAM,CACnB,CAACnB,GAAG,EAAEuf,MAAM,GAAK5gB,MAAM,CAAC6gB,MAAM,CAACxf,GAAG,EAAE;gBAAE,CAACuf,MAAM,CAACE,IAAI,CAAA,EAAGF;YAAQ,CAAA,CAAC,EAC9D,CAAA,CAAE,CACH;IACH;IAEA,SAASpb,OAAOA,GAAA;QACdkb,aAAa,GAAGA,aAAa,CAAChc,MAAM,EAAEkc,MAAM,GAAKA,MAAM,CAACpb,OAAO,EAAE,CAAC;IACpE;IAEA,MAAM9B,IAAI,GAAuB;QAC/B2B,IAAI;QACJG;KACD;IACD,OAAO9B,IAAI;AACb;ACRA,SAASqd,aAAaA,CACpB5J,IAAiB,EACjB6J,WAA8B,EAC9BC,WAA+B,EAAA;IAE/B,MAAMpc,aAAa,GAAGsS,IAAI,CAACtS,aAAa;IACxC,MAAM7B,WAAW,GAAe6B,aAAa,CAACqc,WAAW;IACzD,MAAMT,cAAc,GAAGf,cAAc,CAAC1c,WAAW,CAAC;IAClD,MAAMme,cAAc,GAAGX,cAAc,CAACC,cAAc,CAAC;IACrD,MAAMW,aAAa,GAAGzd,UAAU,EAAE;IAClC,MAAM8E,YAAY,GAAGwW,YAAY,EAAE;IACnC,MAAM,EAAEU,YAAY,EAAEG,cAAc,EAAEM,mBAAAA,EAAqB,GAAGK,cAAc;IAC5E,MAAM,EAAEpB,EAAE,EAAEE,GAAG,EAAExT,IAAAA,EAAM,GAAGtD,YAAY;IACtC,MAAMkH,MAAM,GAAG0R,UAAU;IAEzB,IAAIrS,SAAS,GAAG,KAAK;IACrB,IAAIyP,MAAkB;IACtB,IAAI6C,WAAW,GAAG3B,YAAY,CAACH,cAAc,EAAEuB,aAAa,CAACQ,aAAa,CAAC;IAC3E,IAAItd,OAAO,GAAG0b,YAAY,CAAC2B,WAAW,CAAC;IACvC,IAAIE,UAAU,GAAsB,EAAE;IACtC,IAAIC,UAA4B;IAEhC,IAAIlT,SAAsB;IAC1B,IAAIC,MAAqB;IAEzB,SAASkT,aAAaA,GAAA;QACpB,MAAM,EAAEnT,SAAS,EAAEoT,aAAa,EAAEnT,MAAM,EAAEoT,UAAAA,EAAY,GAAG3d,OAAO;QAEhE,MAAM4d,eAAe,GAAGhiB,QAAQ,CAAC8hB,aAAa,CAAC,GAC3CxK,IAAI,CAAC2K,aAAa,CAACH,aAAa,CAAC,GACjCA,aAAa;QACjBpT,SAAS,GAAiBsT,eAAe,IAAI1K,IAAI,CAAC4K,QAAQ,CAAC,CAAC,CAAE;QAE9D,MAAMC,YAAY,GAAGniB,QAAQ,CAAC+hB,UAAU,CAAC,GACrCrT,SAAS,CAAC0T,gBAAgB,CAACL,UAAU,CAAC,GACtCA,UAAU;QACdpT,MAAM,GAAkB,EAAE,CAAC+E,KAAK,CAACpT,IAAI,CAAC6hB,YAAY,IAAIzT,SAAS,CAACwT,QAAQ,CAAC;IAC3E;IAEA,SAASG,YAAYA,CAACje,OAAoB,EAAA;QACxC,MAAMwa,MAAM,GAAGZ,MAAM,CACnB1G,IAAI,EACJ5I,SAAS,EACTC,MAAM,EACN3J,aAAa,EACb7B,WAAW,EACXiB,OAAO,EACPwE,YAAY,CACb;QAED,IAAIxE,OAAO,CAACwD,IAAI,IAAI,CAACgX,MAAM,CAACN,WAAW,CAACzD,OAAO,EAAE,EAAE;YACjD,MAAMyH,kBAAkB,GAAGniB,MAAM,CAAC6gB,MAAM,CAAC,CAAA,CAAE,EAAE5c,OAAO,EAAE;gBAAEwD,IAAI,EAAE;YAAK,CAAE,CAAC;YACtE,OAAOya,YAAY,CAACC,kBAAkB,CAAC;QACzC;QACA,OAAO1D,MAAM;IACf;IAEA,SAAS2D,QAAQA,CACfC,WAA8B,EAC9BC,WAA+B,EAAA;QAE/B,IAAItT,SAAS,EAAE;QAEfsS,WAAW,GAAG3B,YAAY,CAAC2B,WAAW,EAAEe,WAAW,CAAC;QACpDpe,OAAO,GAAG6b,cAAc,CAACwB,WAAW,CAAC;QACrCE,UAAU,GAAGc,WAAW,IAAId,UAAU;QAEtCE,aAAa,EAAE;QAEfjD,MAAM,GAAGyD,YAAY,CAACje,OAAO,CAAC;QAE9Bmc,mBAAmB,CAAC;YAClBkB,WAAW,EACX;eAAGE,UAAU,CAACngB,GAAG,CAAC,CAAC,EAAE4C,OAAAA,EAAS,GAAKA,OAAO,CAAC;SAC5C,CAAC,CAACtB,OAAO,EAAE4f,KAAK,GAAKnB,aAAa,CAACvd,GAAG,CAAC0e,KAAK,EAAE,QAAQ,EAAElB,UAAU,CAAC,CAAC;QAErE,IAAI,CAACpd,OAAO,CAAC6N,MAAM,EAAE;QAErB2M,MAAM,CAACjG,SAAS,CAACM,EAAE,CAAC2F,MAAM,CAACrW,QAAQ,CAACP,GAAG,EAAE,CAAC;QAC1C4W,MAAM,CAACpW,SAAS,CAAChD,IAAI,EAAE;QACvBoZ,MAAM,CAACG,YAAY,CAACvZ,IAAI,EAAE;QAC1BoZ,MAAM,CAACI,UAAU,CAACxZ,IAAI,CAAC3B,IAAI,CAAC;QAC5B+a,MAAM,CAAChW,YAAY,CAACpD,IAAI,CAAC3B,IAAI,CAAC;QAC9B+a,MAAM,CAACK,aAAa,CAACzZ,IAAI,CAAC3B,IAAI,CAAC;QAC/B+a,MAAM,CAACO,aAAa,CAAC3Z,IAAI,CAAC3B,IAAI,CAAC;QAE/B,IAAI+a,MAAM,CAACxa,OAAO,CAACwD,IAAI,EAAEgX,MAAM,CAACN,WAAW,CAAC1W,IAAI,EAAE;QAClD,IAAI8G,SAAS,CAACiU,YAAY,IAAIhU,MAAM,CAAC9M,MAAM,EAAE+c,MAAM,CAACR,WAAW,CAAC5Y,IAAI,CAAC3B,IAAI,CAAC;QAE1E+d,UAAU,GAAGN,cAAc,CAAC9b,IAAI,CAAC3B,IAAI,EAAE8d,UAAU,CAAC;IACpD;IAEA,SAASH,UAAUA,CACjBgB,WAA8B,EAC9BC,WAA+B,EAAA;QAE/B,MAAMvE,UAAU,GAAG0E,kBAAkB,EAAE;QACvCC,UAAU,EAAE;QACZN,QAAQ,CAACzC,YAAY,CAAC;YAAE5B;QAAU,CAAE,EAAEsE,WAAW,CAAC,EAAEC,WAAW,CAAC;QAChE7Z,YAAY,CAACsD,IAAI,CAAC,QAAQ,CAAC;IAC7B;IAEA,SAAS2W,UAAUA,GAAA;QACjBjE,MAAM,CAACR,WAAW,CAACzY,OAAO,EAAE;QAC5BiZ,MAAM,CAACrH,UAAU,CAAC3S,KAAK,EAAE;QACzBga,MAAM,CAACjG,SAAS,CAAC/T,KAAK,EAAE;QACxBga,MAAM,CAACN,WAAW,CAAC1Z,KAAK,EAAE;QAC1Bga,MAAM,CAACK,aAAa,CAACtZ,OAAO,EAAE;QAC9BiZ,MAAM,CAACO,aAAa,CAACxZ,OAAO,EAAE;QAC9BiZ,MAAM,CAACG,YAAY,CAACpZ,OAAO,EAAE;QAC7BiZ,MAAM,CAACpW,SAAS,CAAC7C,OAAO,EAAE;QAC1B2b,cAAc,CAAC3b,OAAO,EAAE;QACxB4b,aAAa,CAAC3c,KAAK,EAAE;IACvB;IAEA,SAASe,OAAOA,GAAA;QACd,IAAIwJ,SAAS,EAAE;QACfA,SAAS,GAAG,IAAI;QAChBoS,aAAa,CAAC3c,KAAK,EAAE;QACrBie,UAAU,EAAE;QACZja,YAAY,CAACsD,IAAI,CAAC,SAAS,CAAC;QAC5BtD,YAAY,CAAChE,KAAK,EAAE;IACtB;IAEA,SAAS6D,QAAQA,CAAC1G,KAAa,EAAE+gB,IAAc,EAAE5b,SAAkB,EAAA;QACjE,IAAI,CAAC9C,OAAO,CAAC6N,MAAM,IAAI9C,SAAS,EAAE;QAClCyP,MAAM,CAAClW,UAAU,CACd0I,eAAe,EAAE,CACjBpF,WAAW,CAAC8W,IAAI,KAAK,IAAI,GAAG,CAAC,GAAG1e,OAAO,CAAC6M,QAAQ,CAAC;QACpD2N,MAAM,CAACnW,QAAQ,CAAC1G,KAAK,CAACA,KAAK,EAAEmF,SAAS,IAAI,CAAC,CAAC;IAC9C;IAEA,SAAS6b,UAAUA,CAACD,IAAc,EAAA;QAChC,MAAMxX,IAAI,GAAGsT,MAAM,CAAC7c,KAAK,CAACiC,GAAG,CAAC,CAAC,CAAC,CAACgE,GAAG,EAAE;QACtCS,QAAQ,CAAC6C,IAAI,EAAEwX,IAAI,EAAE,CAAC,CAAC,CAAC;IAC1B;IAEA,SAASE,UAAUA,CAACF,IAAc,EAAA;QAChC,MAAMG,IAAI,GAAGrE,MAAM,CAAC7c,KAAK,CAACiC,GAAG,CAAC,CAAC,CAAC,CAAC,CAACgE,GAAG,EAAE;QACvCS,QAAQ,CAACwa,IAAI,EAAEH,IAAI,EAAE,CAAC,CAAC;IACzB;IAEA,SAASI,aAAaA,GAAA;QACpB,MAAM5X,IAAI,GAAGsT,MAAM,CAAC7c,KAAK,CAACiC,GAAG,CAAC,CAAC,CAAC,CAACgE,GAAG,EAAE;QACtC,OAAOsD,IAAI,KAAKsX,kBAAkB,EAAE;IACtC;IAEA,SAASO,aAAaA,GAAA;QACpB,MAAMF,IAAI,GAAGrE,MAAM,CAAC7c,KAAK,CAACiC,GAAG,CAAC,CAAC,CAAC,CAAC,CAACgE,GAAG,EAAE;QACvC,OAAOib,IAAI,KAAKL,kBAAkB,EAAE;IACtC;IAEA,SAAS1D,cAAcA,GAAA;QACrB,OAAON,MAAM,CAACM,cAAc;IAC9B;IAEA,SAASJ,cAAcA,GAAA;QACrB,OAAOF,MAAM,CAACE,cAAc,CAAC9W,GAAG,CAAC4W,MAAM,CAACzO,cAAc,CAACnI,GAAG,EAAE,CAAC;IAC/D;IAEA,SAAS4a,kBAAkBA,GAAA;QACzB,OAAOhE,MAAM,CAAC7c,KAAK,CAACiG,GAAG,EAAE;IAC3B;IAEA,SAASob,kBAAkBA,GAAA;QACzB,OAAOxE,MAAM,CAAC3H,aAAa,CAACjP,GAAG,EAAE;IACnC;IAEA,SAAS+W,YAAYA,GAAA;QACnB,OAAOH,MAAM,CAACG,YAAY,CAAC/W,GAAG,EAAE;IAClC;IAEA,SAASqb,eAAeA,GAAA;QACtB,OAAOzE,MAAM,CAACG,YAAY,CAAC/W,GAAG,CAAC,KAAK,CAAC;IACvC;IAEA,SAAS8Y,OAAOA,GAAA;QACd,OAAOc,UAAU;IACnB;IAEA,SAAS0B,cAAcA,GAAA;QACrB,OAAO1E,MAAM;IACf;IAEA,SAASxW,QAAQA,GAAA;QACf,OAAOkP,IAAI;IACb;IAEA,SAASiM,aAAaA,GAAA;QACpB,OAAO7U,SAAS;IAClB;IAEA,SAAS8U,UAAUA,GAAA;QACjB,OAAO7U,MAAM;IACf;IAEA,MAAM9K,IAAI,GAAsB;QAC9Bqf,aAAa;QACbC,aAAa;QACbI,aAAa;QACbD,cAAc;QACd3d,OAAO;QACP+Z,GAAG;QACHF,EAAE;QACFtT,IAAI;QACJ4U,OAAO;QACPsC,kBAAkB;QAClBtT,MAAM;QACN1H,QAAQ;QACR2a,UAAU;QACVC,UAAU;QACVlE,cAAc;QACdI,cAAc;QACdzW,QAAQ;QACRma,kBAAkB;QAClBY,UAAU;QACVzE,YAAY;QACZsE;KACD;IAEDd,QAAQ,CAACpB,WAAW,EAAEC,WAAW,CAAC;IAClCqC,UAAU,CAAC,IAAM7a,YAAY,CAACsD,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC;IAC9C,OAAOrI,IAAI;AACb;AAMAqd,aAAa,CAACQ,aAAa,GAAGjX,SAAS", "ignoreList": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35], "debugId": null}}, {"offset": {"line": 1600, "column": 0}, "map": {"version": 3, "file": "embla-carousel-react.esm.js", "sources": ["file:///C:/Users/<USER>/Documents/Cubentweb/cubent/node_modules/.pnpm/embla-carousel-react%408.6.0_react%4019.1.0/node_modules/embla-carousel-react/src/components/useEmblaCarousel.ts"], "sourcesContent": ["import { useRef, useEffect, useState, useCallback } from 'react'\nimport {\n  areOptionsEqual,\n  arePluginsEqual,\n  canUseDOM\n} from 'embla-carousel-reactive-utils'\nimport EmblaCarousel, {\n  EmblaCarouselType,\n  EmblaOptionsType,\n  EmblaPluginType\n} from 'embla-carousel'\n\nexport type EmblaViewportRefType = <ViewportElement extends HTMLElement>(\n  instance: ViewportElement | null\n) => void\n\nexport type UseEmblaCarouselType = [\n  EmblaViewportRefType,\n  EmblaCarouselType | undefined\n]\n\nfunction useEmblaCarousel(\n  options: EmblaOptionsType = {},\n  plugins: EmblaPluginType[] = []\n): UseEmblaCarouselType {\n  const storedOptions = useRef(options)\n  const storedPlugins = useRef(plugins)\n  const [emblaApi, setEmblaApi] = useState<EmblaCarouselType>()\n  const [viewport, setViewport] = useState<HTMLElement>()\n\n  const reInit = useCallback(() => {\n    if (emblaApi) emblaApi.reInit(storedOptions.current, storedPlugins.current)\n  }, [emblaApi])\n\n  useEffect(() => {\n    if (areOptionsEqual(storedOptions.current, options)) return\n    storedOptions.current = options\n    reInit()\n  }, [options, reInit])\n\n  useEffect(() => {\n    if (arePluginsEqual(storedPlugins.current, plugins)) return\n    storedPlugins.current = plugins\n    reInit()\n  }, [plugins, reInit])\n\n  useEffect(() => {\n    if (canUseDOM() && viewport) {\n      EmblaCarousel.globalOptions = useEmblaCarousel.globalOptions\n      const newEmblaApi = EmblaCarousel(\n        viewport,\n        storedOptions.current,\n        storedPlugins.current\n      )\n      setEmblaApi(newEmblaApi)\n      return () => newEmblaApi.destroy()\n    } else {\n      setEmblaApi(undefined)\n    }\n  }, [viewport, setEmblaApi])\n\n  return [<EmblaViewportRefType>setViewport, emblaApi]\n}\n\ndeclare namespace useEmblaCarousel {\n  let globalOptions: EmblaOptionsType | undefined\n}\n\nuseEmblaCarousel.globalOptions = undefined\n\nexport default useEmblaCarousel\n"], "names": ["useEmblaCarousel", "options", "plugins", "storedOptions", "useRef", "storedPlugins", "emblaApi", "setEmblaApi", "useState", "viewport", "setViewport", "reInit", "useCallback", "current", "useEffect", "areOptionsEqual", "arePluginsEqual", "canUseDOM", "EmblaCarousel", "globalOptions", "newEmblaApi", "destroy", "undefined"], "mappings": ";;;;;;;;;AAqBA,SAASA,gBAAgBA,CACvBC,OAAA,GAA4B,CAAA,CAAE,EAC9BC,UAA6B,EAAE,EAAA;IAE/B,MAAMC,aAAa,4TAAGC,SAAAA,AAAM,EAACH,OAAO,CAAC;IACrC,MAAMI,aAAa,4TAAGD,SAAAA,AAAM,EAACF,OAAO,CAAC;IACrC,MAAM,CAACI,QAAQ,EAAEC,WAAW,CAAC,4TAAGC,WAAAA,AAAQ,EAAqB;IAC7D,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,4TAAGF,WAAAA,AAAQ,EAAe;IAEvD,MAAMG,MAAM,IAAGC,sUAAAA,AAAW,EAAC,MAAK;QAC9B,IAAIN,QAAQ,EAAEA,QAAQ,CAACK,MAAM,CAACR,aAAa,CAACU,OAAO,EAAER,aAAa,CAACQ,OAAO,CAAC;IAC7E,CAAC,EAAE;QAACP,QAAQ;KAAC,CAAC;KAEdQ,oUAAAA,AAAS,EAAC,MAAK;QACb,2UAAIC,kBAAAA,AAAe,EAACZ,aAAa,CAACU,OAAO,EAAEZ,OAAO,CAAC,EAAE;QACrDE,aAAa,CAACU,OAAO,GAAGZ,OAAO;QAC/BU,MAAM,EAAE;IACV,CAAC,EAAE;QAACV,OAAO;QAAEU,MAAM;KAAC,CAAC;KAErBG,oUAAAA,AAAS,EAAC,MAAK;QACb,2UAAIE,kBAAAA,AAAe,EAACX,aAAa,CAACQ,OAAO,EAAEX,OAAO,CAAC,EAAE;QACrDG,aAAa,CAACQ,OAAO,GAAGX,OAAO;QAC/BS,MAAM,EAAE;IACV,CAAC,EAAE;QAACT,OAAO;QAAES,MAAM;KAAC,CAAC;6TAErBG,YAAAA,AAAS,EAAC,MAAK;QACb,2UAAIG,YAAAA,AAAS,EAAE,KAAIR,QAAQ,EAAE;+OAC3BS,UAAa,CAACC,aAAa,GAAGnB,gBAAgB,CAACmB,aAAa;YAC5D,MAAMC,WAAW,0OAAGF,UAAAA,AAAa,EAC/BT,QAAQ,EACRN,aAAa,CAACU,OAAO,EACrBR,aAAa,CAACQ,OAAO,CACtB;YACDN,WAAW,CAACa,WAAW,CAAC;YACxB,OAAO,IAAMA,WAAW,CAACC,OAAO,EAAE;QACpC,CAAC,MAAM;YACLd,WAAW,CAACe,SAAS,CAAC;QACxB;IACF,CAAC,EAAE;QAACb,QAAQ;QAAEF,WAAW;KAAC,CAAC;IAE3B,OAAO;QAAuBG,WAAW;QAAEJ,QAAQ;KAAC;AACtD;AAMAN,gBAAgB,CAACmB,aAAa,GAAGG,SAAS", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1662, "column": 0}, "map": {"version": 3, "file": "arrow-left.js", "sources": ["file:///C:/Users/<USER>/Documents/Cubentweb/cubent/node_modules/.pnpm/lucide-react%400.511.0_react%4019.1.0/node_modules/lucide-react/src/icons/arrow-left.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'm12 19-7-7 7-7', key: '1l729n' }],\n  ['path', { d: 'M19 12H5', key: 'x3x0zl' }],\n];\n\n/**\n * @component @name ArrowLeft\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJtMTIgMTktNy03IDctNyIgLz4KICA8cGF0aCBkPSJNMTkgMTJINSIgLz4KPC9zdmc+Cg==) - https://lucide.dev/icons/arrow-left\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst ArrowLeft = createLucideIcon('arrow-left', __iconNode);\n\nexport default ArrowLeft;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAuB,CAAA,CAAA,CAAA;IAClC;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAkB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IAC/C;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAY,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;CAC3C;AAaM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,SAAA,CAAY,CAAA,4PAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAiB,CAAjB,CAAA,AAAiB,CAAjB,AAAiB,CAAjB,AAAiB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAc,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1708, "column": 0}, "map": {"version": 3, "file": "arrow-right.js", "sources": ["file:///C:/Users/<USER>/Documents/Cubentweb/cubent/node_modules/.pnpm/lucide-react%400.511.0_react%4019.1.0/node_modules/lucide-react/src/icons/arrow-right.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'M5 12h14', key: '1ays0h' }],\n  ['path', { d: 'm12 5 7 7-7 7', key: 'xquz4c' }],\n];\n\n/**\n * @component @name ArrowRight\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNNSAxMmgxNCIgLz4KICA8cGF0aCBkPSJtMTIgNSA3IDctNyA3IiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/arrow-right\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst ArrowRight = createLucideIcon('arrow-right', __iconNode);\n\nexport default ArrowRight;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAuB,CAAA,CAAA,CAAA;IAClC;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAY,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IACzC;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAiB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;CAChD;AAaM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,UAAA,CAAa,CAAA,4PAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAiB,CAAjB,CAAA,AAAiB,CAAjB,AAAiB,CAAjB,AAAiB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAe,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1754, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Cubentweb/cubent/node_modules/.pnpm/%40radix-ui%2Breact-collapsible_cecf39ac1375c4a4148ed47a207c8f40/node_modules/%40radix-ui/react-collapsible/src/collapsible.tsx"], "sourcesContent": ["import * as React from 'react';\nimport { composeEventHandlers } from '@radix-ui/primitive';\nimport { createContextScope } from '@radix-ui/react-context';\nimport { useControllableState } from '@radix-ui/react-use-controllable-state';\nimport { useLayoutEffect } from '@radix-ui/react-use-layout-effect';\nimport { useComposedRefs } from '@radix-ui/react-compose-refs';\nimport { Primitive } from '@radix-ui/react-primitive';\nimport { Presence } from '@radix-ui/react-presence';\nimport { useId } from '@radix-ui/react-id';\n\nimport type { Scope } from '@radix-ui/react-context';\n\n/* -------------------------------------------------------------------------------------------------\n * Collapsible\n * -----------------------------------------------------------------------------------------------*/\n\nconst COLLAPSIBLE_NAME = 'Collapsible';\n\ntype ScopedProps<P> = P & { __scopeCollapsible?: Scope };\nconst [createCollapsibleContext, createCollapsibleScope] = createContextScope(COLLAPSIBLE_NAME);\n\ntype CollapsibleContextValue = {\n  contentId: string;\n  disabled?: boolean;\n  open: boolean;\n  onOpenToggle(): void;\n};\n\nconst [CollapsibleProvider, useCollapsibleContext] =\n  createCollapsibleContext<CollapsibleContextValue>(COLLAPSIBLE_NAME);\n\ntype CollapsibleElement = React.ComponentRef<typeof Primitive.div>;\ntype PrimitiveDivProps = React.ComponentPropsWithoutRef<typeof Primitive.div>;\ninterface CollapsibleProps extends PrimitiveDivProps {\n  defaultOpen?: boolean;\n  open?: boolean;\n  disabled?: boolean;\n  onOpenChange?(open: boolean): void;\n}\n\nconst Collapsible = React.forwardRef<CollapsibleElement, CollapsibleProps>(\n  (props: ScopedProps<CollapsibleProps>, forwardedRef) => {\n    const {\n      __scopeCollapsible,\n      open: openProp,\n      defaultOpen,\n      disabled,\n      onOpenChange,\n      ...collapsibleProps\n    } = props;\n\n    const [open, setOpen] = useControllableState({\n      prop: openProp,\n      defaultProp: defaultOpen ?? false,\n      onChange: onOpenChange,\n      caller: COLLAPSIBLE_NAME,\n    });\n\n    return (\n      <CollapsibleProvider\n        scope={__scopeCollapsible}\n        disabled={disabled}\n        contentId={useId()}\n        open={open}\n        onOpenToggle={React.useCallback(() => setOpen((prevOpen) => !prevOpen), [setOpen])}\n      >\n        <Primitive.div\n          data-state={getState(open)}\n          data-disabled={disabled ? '' : undefined}\n          {...collapsibleProps}\n          ref={forwardedRef}\n        />\n      </CollapsibleProvider>\n    );\n  }\n);\n\nCollapsible.displayName = COLLAPSIBLE_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * CollapsibleTrigger\n * -----------------------------------------------------------------------------------------------*/\n\nconst TRIGGER_NAME = 'CollapsibleTrigger';\n\ntype CollapsibleTriggerElement = React.ComponentRef<typeof Primitive.button>;\ntype PrimitiveButtonProps = React.ComponentPropsWithoutRef<typeof Primitive.button>;\ninterface CollapsibleTriggerProps extends PrimitiveButtonProps {}\n\nconst CollapsibleTrigger = React.forwardRef<CollapsibleTriggerElement, CollapsibleTriggerProps>(\n  (props: ScopedProps<CollapsibleTriggerProps>, forwardedRef) => {\n    const { __scopeCollapsible, ...triggerProps } = props;\n    const context = useCollapsibleContext(TRIGGER_NAME, __scopeCollapsible);\n    return (\n      <Primitive.button\n        type=\"button\"\n        aria-controls={context.contentId}\n        aria-expanded={context.open || false}\n        data-state={getState(context.open)}\n        data-disabled={context.disabled ? '' : undefined}\n        disabled={context.disabled}\n        {...triggerProps}\n        ref={forwardedRef}\n        onClick={composeEventHandlers(props.onClick, context.onOpenToggle)}\n      />\n    );\n  }\n);\n\nCollapsibleTrigger.displayName = TRIGGER_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * CollapsibleContent\n * -----------------------------------------------------------------------------------------------*/\n\nconst CONTENT_NAME = 'CollapsibleContent';\n\ntype CollapsibleContentElement = CollapsibleContentImplElement;\ninterface CollapsibleContentProps extends Omit<CollapsibleContentImplProps, 'present'> {\n  /**\n   * Used to force mounting when more control is needed. Useful when\n   * controlling animation with React animation libraries.\n   */\n  forceMount?: true;\n}\n\nconst CollapsibleContent = React.forwardRef<CollapsibleContentElement, CollapsibleContentProps>(\n  (props: ScopedProps<CollapsibleContentProps>, forwardedRef) => {\n    const { forceMount, ...contentProps } = props;\n    const context = useCollapsibleContext(CONTENT_NAME, props.__scopeCollapsible);\n    return (\n      <Presence present={forceMount || context.open}>\n        {({ present }) => (\n          <CollapsibleContentImpl {...contentProps} ref={forwardedRef} present={present} />\n        )}\n      </Presence>\n    );\n  }\n);\n\nCollapsibleContent.displayName = CONTENT_NAME;\n\n/* -----------------------------------------------------------------------------------------------*/\n\ntype CollapsibleContentImplElement = React.ComponentRef<typeof Primitive.div>;\ninterface CollapsibleContentImplProps extends PrimitiveDivProps {\n  present: boolean;\n}\n\nconst CollapsibleContentImpl = React.forwardRef<\n  CollapsibleContentImplElement,\n  CollapsibleContentImplProps\n>((props: ScopedProps<CollapsibleContentImplProps>, forwardedRef) => {\n  const { __scopeCollapsible, present, children, ...contentProps } = props;\n  const context = useCollapsibleContext(CONTENT_NAME, __scopeCollapsible);\n  const [isPresent, setIsPresent] = React.useState(present);\n  const ref = React.useRef<CollapsibleContentImplElement>(null);\n  const composedRefs = useComposedRefs(forwardedRef, ref);\n  const heightRef = React.useRef<number | undefined>(0);\n  const height = heightRef.current;\n  const widthRef = React.useRef<number | undefined>(0);\n  const width = widthRef.current;\n  // when opening we want it to immediately open to retrieve dimensions\n  // when closing we delay `present` to retrieve dimensions before closing\n  const isOpen = context.open || isPresent;\n  const isMountAnimationPreventedRef = React.useRef(isOpen);\n  const originalStylesRef = React.useRef<Record<string, string>>(undefined);\n\n  React.useEffect(() => {\n    const rAF = requestAnimationFrame(() => (isMountAnimationPreventedRef.current = false));\n    return () => cancelAnimationFrame(rAF);\n  }, []);\n\n  useLayoutEffect(() => {\n    const node = ref.current;\n    if (node) {\n      originalStylesRef.current = originalStylesRef.current || {\n        transitionDuration: node.style.transitionDuration,\n        animationName: node.style.animationName,\n      };\n      // block any animations/transitions so the element renders at its full dimensions\n      node.style.transitionDuration = '0s';\n      node.style.animationName = 'none';\n\n      // get width and height from full dimensions\n      const rect = node.getBoundingClientRect();\n      heightRef.current = rect.height;\n      widthRef.current = rect.width;\n\n      // kick off any animations/transitions that were originally set up if it isn't the initial mount\n      if (!isMountAnimationPreventedRef.current) {\n        node.style.transitionDuration = originalStylesRef.current.transitionDuration!;\n        node.style.animationName = originalStylesRef.current.animationName!;\n      }\n\n      setIsPresent(present);\n    }\n    /**\n     * depends on `context.open` because it will change to `false`\n     * when a close is triggered but `present` will be `false` on\n     * animation end (so when close finishes). This allows us to\n     * retrieve the dimensions *before* closing.\n     */\n  }, [context.open, present]);\n\n  return (\n    <Primitive.div\n      data-state={getState(context.open)}\n      data-disabled={context.disabled ? '' : undefined}\n      id={context.contentId}\n      hidden={!isOpen}\n      {...contentProps}\n      ref={composedRefs}\n      style={{\n        [`--radix-collapsible-content-height` as any]: height ? `${height}px` : undefined,\n        [`--radix-collapsible-content-width` as any]: width ? `${width}px` : undefined,\n        ...props.style,\n      }}\n    >\n      {isOpen && children}\n    </Primitive.div>\n  );\n});\n\n/* -----------------------------------------------------------------------------------------------*/\n\nfunction getState(open?: boolean) {\n  return open ? 'open' : 'closed';\n}\n\nconst Root = Collapsible;\nconst Trigger = CollapsibleTrigger;\nconst Content = CollapsibleContent;\n\nexport {\n  createCollapsibleScope,\n  //\n  Collapsible,\n  CollapsibleTrigger,\n  CollapsibleContent,\n  //\n  Root,\n  Trigger,\n  Content,\n};\nexport type { CollapsibleProps, CollapsibleTriggerProps, CollapsibleContentProps };\n"], "names": [], "mappings": ";;;;;;;;;;AAAA,YAAY,WAAW;AACvB,SAAS,4BAA4B;AACrC,SAAS,0BAA0B;AACnC,SAAS,4BAA4B;AACrC,SAAS,uBAAuB;AAChC,SAAS,uBAAuB;AAChC,SAAS,iBAAiB;AAC1B,SAAS,gBAAgB;AACzB,SAAS,aAAa;AA0Dd;;;;;;;;;;;;AAlDR,IAAM,mBAAmB;AAGzB,IAAM,CAAC,0BAA0B,sBAAsB,CAAA,qRAAI,qBAAA,EAAmB,gBAAgB;AAS9F,IAAM,CAAC,qBAAqB,qBAAqB,CAAA,GAC/C,yBAAkD,gBAAgB;AAWpE,IAAM,uUAAoB,aAAA,EACxB,CAAC,OAAsC,iBAAiB;IACtD,MAAM,EACJ,kBAAA,EACA,MAAM,QAAA,EACN,WAAA,EACA,QAAA,EACA,YAAA,EACA,GAAG,kBACL,GAAI;IAEJ,MAAM,CAAC,MAAM,OAAO,CAAA,GAAI,2TAAA,EAAqB;QAC3C,MAAM;QACN,aAAa,eAAe;QAC5B,UAAU;QACV,QAAQ;IACV,CAAC;IAED,OACE,aAAA,GAAA,CAAA,GAAA,sUAAA,CAAA,MAAA,EAAC,qBAAA;QACC,OAAO;QACP;QACA,gTAAW,QAAA,CAAM;QACjB;QACA,uUAAoB,cAAA,EAAY,IAAM,QAAQ,CAAC,WAAa,CAAC,QAAQ,GAAG;YAAC,OAAO;SAAC;QAEjF,UAAA,aAAA,GAAA,CAAA,GAAA,sUAAA,CAAA,MAAA,+QAAC,YAAA,CAAU,GAAA,EAAV;YACC,cAAY,SAAS,IAAI;YACzB,iBAAe,WAAW,KAAK,KAAA;YAC9B,GAAG,gBAAA;YACJ,KAAK;QAAA;IACP;AAGN;AAGF,YAAY,WAAA,GAAc;AAM1B,IAAM,eAAe;AAMrB,IAAM,qBAA2B,sUAAA,EAC/B,CAAC,OAA6C,iBAAiB;IAC7D,MAAM,EAAE,kBAAA,EAAoB,GAAG,aAAa,CAAA,GAAI;IAChD,MAAM,UAAU,sBAAsB,cAAc,kBAAkB;IACtE,OACE,aAAA,GAAA,CAAA,GAAA,sUAAA,CAAA,MAAA,+QAAC,YAAA,CAAU,MAAA,EAAV;QACC,MAAK;QACL,iBAAe,QAAQ,SAAA;QACvB,iBAAe,QAAQ,IAAA,IAAQ;QAC/B,cAAY,SAAS,QAAQ,IAAI;QACjC,iBAAe,QAAQ,QAAA,GAAW,KAAK,KAAA;QACvC,UAAU,QAAQ,QAAA;QACjB,GAAG,YAAA;QACJ,KAAK;QACL,gPAAS,wBAAA,EAAqB,MAAM,OAAA,EAAS,QAAQ,YAAY;IAAA;AAGvE;AAGF,mBAAmB,WAAA,GAAc;AAMjC,IAAM,eAAe;AAWrB,IAAM,yBAA2B,kUAAA,EAC/B,CAAC,OAA6C,iBAAiB;IAC7D,MAAM,EAAE,UAAA,EAAY,GAAG,aAAa,CAAA,GAAI;IACxC,MAAM,UAAU,sBAAsB,cAAc,MAAM,kBAAkB;IAC5E,OACE,aAAA,GAAA,CAAA,GAAA,sUAAA,CAAA,MAAA,iRAAC,WAAA,EAAA;QAAS,SAAS,cAAc,QAAQ,IAAA;QACtC,UAAA,CAAC,EAAE,OAAA,CAAQ,CAAA,GACV,aAAA,GAAA,CAAA,GAAA,sUAAA,CAAA,MAAA,EAAC,wBAAA;gBAAwB,GAAG,YAAA;gBAAc,KAAK;gBAAc;YAAA,CAAkB;IAAA,CAEnF;AAEJ;AAGF,mBAAmB,WAAA,GAAc;AASjC,IAAM,kVAA+B,aAAA,EAGnC,CAAC,OAAiD,iBAAiB;IACnE,MAAM,EAAE,kBAAA,EAAoB,OAAA,EAAS,QAAA,EAAU,GAAG,aAAa,CAAA,GAAI;IACnE,MAAM,UAAU,sBAAsB,cAAc,kBAAkB;IACtE,MAAM,CAAC,WAAW,YAAY,CAAA,4TAAU,WAAA,EAAS,OAAO;IACxD,MAAM,OAAY,iUAAA,EAAsC,IAAI;IAC5D,MAAM,sSAAe,kBAAA,EAAgB,cAAc,GAAG;IACtD,MAAM,qUAAkB,SAAA,EAA2B,CAAC;IACpD,MAAM,SAAS,UAAU,OAAA;IACzB,MAAM,oUAAiB,SAAA,EAA2B,CAAC;IACnD,MAAM,QAAQ,SAAS,OAAA;IAGvB,MAAM,SAAS,QAAQ,IAAA,IAAQ;IAC/B,MAAM,wVAAqC,SAAA,EAAO,MAAM;IACxD,MAAM,wBAA0B,8TAAA,EAA+B,KAAA,CAAS;6TAElE,YAAA,EAAU,MAAM;QACpB,MAAM,MAAM,sBAAsB,IAAO,6BAA6B,OAAA,GAAU,KAAM;QACtF,OAAO,IAAM,qBAAqB,GAAG;IACvC,GAAG,CAAC,CAAC;IAEL,CAAA,GAAA,6RAAA,CAAA,kBAAA,EAAgB,MAAM;QACpB,MAAM,OAAO,IAAI,OAAA;QACjB,IAAI,MAAM;YACR,kBAAkB,OAAA,GAAU,kBAAkB,OAAA,IAAW;gBACvD,oBAAoB,KAAK,KAAA,CAAM,kBAAA;gBAC/B,eAAe,KAAK,KAAA,CAAM,aAAA;YAC5B;YAEA,KAAK,KAAA,CAAM,kBAAA,GAAqB;YAChC,KAAK,KAAA,CAAM,aAAA,GAAgB;YAG3B,MAAM,OAAO,KAAK,qBAAA,CAAsB;YACxC,UAAU,OAAA,GAAU,KAAK,MAAA;YACzB,SAAS,OAAA,GAAU,KAAK,KAAA;YAGxB,IAAI,CAAC,6BAA6B,OAAA,EAAS;gBACzC,KAAK,KAAA,CAAM,kBAAA,GAAqB,kBAAkB,OAAA,CAAQ,kBAAA;gBAC1D,KAAK,KAAA,CAAM,aAAA,GAAgB,kBAAkB,OAAA,CAAQ,aAAA;YACvD;YAEA,aAAa,OAAO;QACtB;IAOF,GAAG;QAAC,QAAQ,IAAA;QAAM,OAAO;KAAC;IAE1B,OACE,aAAA,GAAA,CAAA,GAAA,sUAAA,CAAA,MAAA,+QAAC,YAAA,CAAU,GAAA,EAAV;QACC,cAAY,SAAS,QAAQ,IAAI;QACjC,iBAAe,QAAQ,QAAA,GAAW,KAAK,KAAA;QACvC,IAAI,QAAQ,SAAA;QACZ,QAAQ,CAAC;QACR,GAAG,YAAA;QACJ,KAAK;QACL,OAAO;YACL,CAAC,CAAA,kCAAA,CAA2C,CAAA,EAAG,SAAS,GAAG,MAAM,CAAA,EAAA,CAAA,GAAO,KAAA;YACxE,CAAC,CAAA,iCAAA,CAA0C,CAAA,EAAG,QAAQ,GAAG,KAAK,CAAA,EAAA,CAAA,GAAO,KAAA;YACrE,GAAG,MAAM,KAAA;QACX;QAEC,UAAA,UAAU;IAAA;AAGjB,CAAC;AAID,SAAS,SAAS,IAAA,EAAgB;IAChC,OAAO,OAAO,SAAS;AACzB;AAEA,IAAM,OAAO;AACb,IAAM,UAAU;AAChB,IAAM,UAAU", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1912, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Cubentweb/cubent/node_modules/.pnpm/%40radix-ui%2Breact-accordion%401_51304fa0823e9b2a065feab26851e0ad/node_modules/%40radix-ui/react-accordion/src/accordion.tsx"], "sourcesContent": ["import React from 'react';\nimport { createContextScope } from '@radix-ui/react-context';\nimport { createCollection } from '@radix-ui/react-collection';\nimport { useComposedRefs } from '@radix-ui/react-compose-refs';\nimport { composeEventHandlers } from '@radix-ui/primitive';\nimport { useControllableState } from '@radix-ui/react-use-controllable-state';\nimport { Primitive } from '@radix-ui/react-primitive';\nimport * as CollapsiblePrimitive from '@radix-ui/react-collapsible';\nimport { createCollapsibleScope } from '@radix-ui/react-collapsible';\nimport { useId } from '@radix-ui/react-id';\n\nimport type { Scope } from '@radix-ui/react-context';\nimport { useDirection } from '@radix-ui/react-direction';\n\ntype Direction = 'ltr' | 'rtl';\n\n/* -------------------------------------------------------------------------------------------------\n * Accordion\n * -----------------------------------------------------------------------------------------------*/\n\nconst ACCORDION_NAME = 'Accordion';\nconst ACCORDION_KEYS = ['Home', 'End', 'ArrowDown', 'ArrowUp', 'ArrowLeft', 'ArrowRight'];\n\nconst [Collection, useCollection, createCollectionScope] =\n  createCollection<AccordionTriggerElement>(ACCORDION_NAME);\n\ntype ScopedProps<P> = P & { __scopeAccordion?: Scope };\nconst [createAccordionContext, createAccordionScope] = createContextScope(ACCORDION_NAME, [\n  createCollectionScope,\n  createCollapsibleScope,\n]);\nconst useCollapsibleScope = createCollapsibleScope();\n\ntype AccordionElement = AccordionImplMultipleElement | AccordionImplSingleElement;\ninterface AccordionSingleProps extends AccordionImplSingleProps {\n  type: 'single';\n}\ninterface AccordionMultipleProps extends AccordionImplMultipleProps {\n  type: 'multiple';\n}\n\nconst Accordion = React.forwardRef<AccordionElement, AccordionSingleProps | AccordionMultipleProps>(\n  (props: ScopedProps<AccordionSingleProps | AccordionMultipleProps>, forwardedRef) => {\n    const { type, ...accordionProps } = props;\n    const singleProps = accordionProps as AccordionImplSingleProps;\n    const multipleProps = accordionProps as AccordionImplMultipleProps;\n    return (\n      <Collection.Provider scope={props.__scopeAccordion}>\n        {type === 'multiple' ? (\n          <AccordionImplMultiple {...multipleProps} ref={forwardedRef} />\n        ) : (\n          <AccordionImplSingle {...singleProps} ref={forwardedRef} />\n        )}\n      </Collection.Provider>\n    );\n  }\n);\n\nAccordion.displayName = ACCORDION_NAME;\n\n/* -----------------------------------------------------------------------------------------------*/\n\ntype AccordionValueContextValue = {\n  value: string[];\n  onItemOpen(value: string): void;\n  onItemClose(value: string): void;\n};\n\nconst [AccordionValueProvider, useAccordionValueContext] =\n  createAccordionContext<AccordionValueContextValue>(ACCORDION_NAME);\n\nconst [AccordionCollapsibleProvider, useAccordionCollapsibleContext] = createAccordionContext(\n  ACCORDION_NAME,\n  { collapsible: false }\n);\n\ntype AccordionImplSingleElement = AccordionImplElement;\ninterface AccordionImplSingleProps extends AccordionImplProps {\n  /**\n   * The controlled stateful value of the accordion item whose content is expanded.\n   */\n  value?: string;\n  /**\n   * The value of the item whose content is expanded when the accordion is initially rendered. Use\n   * `defaultValue` if you do not need to control the state of an accordion.\n   */\n  defaultValue?: string;\n  /**\n   * The callback that fires when the state of the accordion changes.\n   */\n  onValueChange?(value: string): void;\n  /**\n   * Whether an accordion item can be collapsed after it has been opened.\n   * @default false\n   */\n  collapsible?: boolean;\n}\n\nconst AccordionImplSingle = React.forwardRef<AccordionImplSingleElement, AccordionImplSingleProps>(\n  (props: ScopedProps<AccordionImplSingleProps>, forwardedRef) => {\n    const {\n      value: valueProp,\n      defaultValue,\n      onValueChange = () => {},\n      collapsible = false,\n      ...accordionSingleProps\n    } = props;\n\n    const [value, setValue] = useControllableState({\n      prop: valueProp,\n      defaultProp: defaultValue ?? '',\n      onChange: onValueChange,\n      caller: ACCORDION_NAME,\n    });\n\n    return (\n      <AccordionValueProvider\n        scope={props.__scopeAccordion}\n        value={React.useMemo(() => (value ? [value] : []), [value])}\n        onItemOpen={setValue}\n        onItemClose={React.useCallback(() => collapsible && setValue(''), [collapsible, setValue])}\n      >\n        <AccordionCollapsibleProvider scope={props.__scopeAccordion} collapsible={collapsible}>\n          <AccordionImpl {...accordionSingleProps} ref={forwardedRef} />\n        </AccordionCollapsibleProvider>\n      </AccordionValueProvider>\n    );\n  }\n);\n\n/* -----------------------------------------------------------------------------------------------*/\n\ntype AccordionImplMultipleElement = AccordionImplElement;\ninterface AccordionImplMultipleProps extends AccordionImplProps {\n  /**\n   * The controlled stateful value of the accordion items whose contents are expanded.\n   */\n  value?: string[];\n  /**\n   * The value of the items whose contents are expanded when the accordion is initially rendered. Use\n   * `defaultValue` if you do not need to control the state of an accordion.\n   */\n  defaultValue?: string[];\n  /**\n   * The callback that fires when the state of the accordion changes.\n   */\n  onValueChange?(value: string[]): void;\n}\n\nconst AccordionImplMultiple = React.forwardRef<\n  AccordionImplMultipleElement,\n  AccordionImplMultipleProps\n>((props: ScopedProps<AccordionImplMultipleProps>, forwardedRef) => {\n  const {\n    value: valueProp,\n    defaultValue,\n    onValueChange = () => {},\n    ...accordionMultipleProps\n  } = props;\n\n  const [value, setValue] = useControllableState({\n    prop: valueProp,\n    defaultProp: defaultValue ?? [],\n    onChange: onValueChange,\n    caller: ACCORDION_NAME,\n  });\n\n  const handleItemOpen = React.useCallback(\n    (itemValue: string) => setValue((prevValue = []) => [...prevValue, itemValue]),\n    [setValue]\n  );\n\n  const handleItemClose = React.useCallback(\n    (itemValue: string) =>\n      setValue((prevValue = []) => prevValue.filter((value) => value !== itemValue)),\n    [setValue]\n  );\n\n  return (\n    <AccordionValueProvider\n      scope={props.__scopeAccordion}\n      value={value}\n      onItemOpen={handleItemOpen}\n      onItemClose={handleItemClose}\n    >\n      <AccordionCollapsibleProvider scope={props.__scopeAccordion} collapsible={true}>\n        <AccordionImpl {...accordionMultipleProps} ref={forwardedRef} />\n      </AccordionCollapsibleProvider>\n    </AccordionValueProvider>\n  );\n});\n\n/* -----------------------------------------------------------------------------------------------*/\n\ntype AccordionImplContextValue = {\n  disabled?: boolean;\n  direction: AccordionImplProps['dir'];\n  orientation: AccordionImplProps['orientation'];\n};\n\nconst [AccordionImplProvider, useAccordionContext] =\n  createAccordionContext<AccordionImplContextValue>(ACCORDION_NAME);\n\ntype AccordionImplElement = React.ComponentRef<typeof Primitive.div>;\ntype PrimitiveDivProps = React.ComponentPropsWithoutRef<typeof Primitive.div>;\ninterface AccordionImplProps extends PrimitiveDivProps {\n  /**\n   * Whether or not an accordion is disabled from user interaction.\n   *\n   * @defaultValue false\n   */\n  disabled?: boolean;\n  /**\n   * The layout in which the Accordion operates.\n   * @default vertical\n   */\n  orientation?: React.AriaAttributes['aria-orientation'];\n  /**\n   * The language read direction.\n   */\n  dir?: Direction;\n}\n\nconst AccordionImpl = React.forwardRef<AccordionImplElement, AccordionImplProps>(\n  (props: ScopedProps<AccordionImplProps>, forwardedRef) => {\n    const { __scopeAccordion, disabled, dir, orientation = 'vertical', ...accordionProps } = props;\n    const accordionRef = React.useRef<AccordionImplElement>(null);\n    const composedRefs = useComposedRefs(accordionRef, forwardedRef);\n    const getItems = useCollection(__scopeAccordion);\n    const direction = useDirection(dir);\n    const isDirectionLTR = direction === 'ltr';\n\n    const handleKeyDown = composeEventHandlers(props.onKeyDown, (event) => {\n      if (!ACCORDION_KEYS.includes(event.key)) return;\n      const target = event.target as HTMLElement;\n      const triggerCollection = getItems().filter((item) => !item.ref.current?.disabled);\n      const triggerIndex = triggerCollection.findIndex((item) => item.ref.current === target);\n      const triggerCount = triggerCollection.length;\n\n      if (triggerIndex === -1) return;\n\n      // Prevents page scroll while user is navigating\n      event.preventDefault();\n\n      let nextIndex = triggerIndex;\n      const homeIndex = 0;\n      const endIndex = triggerCount - 1;\n\n      const moveNext = () => {\n        nextIndex = triggerIndex + 1;\n        if (nextIndex > endIndex) {\n          nextIndex = homeIndex;\n        }\n      };\n\n      const movePrev = () => {\n        nextIndex = triggerIndex - 1;\n        if (nextIndex < homeIndex) {\n          nextIndex = endIndex;\n        }\n      };\n\n      switch (event.key) {\n        case 'Home':\n          nextIndex = homeIndex;\n          break;\n        case 'End':\n          nextIndex = endIndex;\n          break;\n        case 'ArrowRight':\n          if (orientation === 'horizontal') {\n            if (isDirectionLTR) {\n              moveNext();\n            } else {\n              movePrev();\n            }\n          }\n          break;\n        case 'ArrowDown':\n          if (orientation === 'vertical') {\n            moveNext();\n          }\n          break;\n        case 'ArrowLeft':\n          if (orientation === 'horizontal') {\n            if (isDirectionLTR) {\n              movePrev();\n            } else {\n              moveNext();\n            }\n          }\n          break;\n        case 'ArrowUp':\n          if (orientation === 'vertical') {\n            movePrev();\n          }\n          break;\n      }\n\n      const clampedIndex = nextIndex % triggerCount;\n      triggerCollection[clampedIndex]!.ref.current?.focus();\n    });\n\n    return (\n      <AccordionImplProvider\n        scope={__scopeAccordion}\n        disabled={disabled}\n        direction={dir}\n        orientation={orientation}\n      >\n        <Collection.Slot scope={__scopeAccordion}>\n          <Primitive.div\n            {...accordionProps}\n            data-orientation={orientation}\n            ref={composedRefs}\n            onKeyDown={disabled ? undefined : handleKeyDown}\n          />\n        </Collection.Slot>\n      </AccordionImplProvider>\n    );\n  }\n);\n\n/* -------------------------------------------------------------------------------------------------\n * AccordionItem\n * -----------------------------------------------------------------------------------------------*/\n\nconst ITEM_NAME = 'AccordionItem';\n\ntype AccordionItemContextValue = { open?: boolean; disabled?: boolean; triggerId: string };\nconst [AccordionItemProvider, useAccordionItemContext] =\n  createAccordionContext<AccordionItemContextValue>(ITEM_NAME);\n\ntype AccordionItemElement = React.ComponentRef<typeof CollapsiblePrimitive.Root>;\ntype CollapsibleProps = React.ComponentPropsWithoutRef<typeof CollapsiblePrimitive.Root>;\ninterface AccordionItemProps\n  extends Omit<CollapsibleProps, 'open' | 'defaultOpen' | 'onOpenChange'> {\n  /**\n   * Whether or not an accordion item is disabled from user interaction.\n   *\n   * @defaultValue false\n   */\n  disabled?: boolean;\n  /**\n   * A string value for the accordion item. All items within an accordion should use a unique value.\n   */\n  value: string;\n}\n\n/**\n * `AccordionItem` contains all of the parts of a collapsible section inside of an `Accordion`.\n */\nconst AccordionItem = React.forwardRef<AccordionItemElement, AccordionItemProps>(\n  (props: ScopedProps<AccordionItemProps>, forwardedRef) => {\n    const { __scopeAccordion, value, ...accordionItemProps } = props;\n    const accordionContext = useAccordionContext(ITEM_NAME, __scopeAccordion);\n    const valueContext = useAccordionValueContext(ITEM_NAME, __scopeAccordion);\n    const collapsibleScope = useCollapsibleScope(__scopeAccordion);\n    const triggerId = useId();\n    const open = (value && valueContext.value.includes(value)) || false;\n    const disabled = accordionContext.disabled || props.disabled;\n\n    return (\n      <AccordionItemProvider\n        scope={__scopeAccordion}\n        open={open}\n        disabled={disabled}\n        triggerId={triggerId}\n      >\n        <CollapsiblePrimitive.Root\n          data-orientation={accordionContext.orientation}\n          data-state={getState(open)}\n          {...collapsibleScope}\n          {...accordionItemProps}\n          ref={forwardedRef}\n          disabled={disabled}\n          open={open}\n          onOpenChange={(open) => {\n            if (open) {\n              valueContext.onItemOpen(value);\n            } else {\n              valueContext.onItemClose(value);\n            }\n          }}\n        />\n      </AccordionItemProvider>\n    );\n  }\n);\n\nAccordionItem.displayName = ITEM_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * AccordionHeader\n * -----------------------------------------------------------------------------------------------*/\n\nconst HEADER_NAME = 'AccordionHeader';\n\ntype AccordionHeaderElement = React.ComponentRef<typeof Primitive.h3>;\ntype PrimitiveHeading3Props = React.ComponentPropsWithoutRef<typeof Primitive.h3>;\ninterface AccordionHeaderProps extends PrimitiveHeading3Props {}\n\n/**\n * `AccordionHeader` contains the content for the parts of an `AccordionItem` that will be visible\n * whether or not its content is collapsed.\n */\nconst AccordionHeader = React.forwardRef<AccordionHeaderElement, AccordionHeaderProps>(\n  (props: ScopedProps<AccordionHeaderProps>, forwardedRef) => {\n    const { __scopeAccordion, ...headerProps } = props;\n    const accordionContext = useAccordionContext(ACCORDION_NAME, __scopeAccordion);\n    const itemContext = useAccordionItemContext(HEADER_NAME, __scopeAccordion);\n    return (\n      <Primitive.h3\n        data-orientation={accordionContext.orientation}\n        data-state={getState(itemContext.open)}\n        data-disabled={itemContext.disabled ? '' : undefined}\n        {...headerProps}\n        ref={forwardedRef}\n      />\n    );\n  }\n);\n\nAccordionHeader.displayName = HEADER_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * AccordionTrigger\n * -----------------------------------------------------------------------------------------------*/\n\nconst TRIGGER_NAME = 'AccordionTrigger';\n\ntype AccordionTriggerElement = React.ComponentRef<typeof CollapsiblePrimitive.Trigger>;\ntype CollapsibleTriggerProps = React.ComponentPropsWithoutRef<typeof CollapsiblePrimitive.Trigger>;\ninterface AccordionTriggerProps extends CollapsibleTriggerProps {}\n\n/**\n * `AccordionTrigger` is the trigger that toggles the collapsed state of an `AccordionItem`. It\n * should always be nested inside of an `AccordionHeader`.\n */\nconst AccordionTrigger = React.forwardRef<AccordionTriggerElement, AccordionTriggerProps>(\n  (props: ScopedProps<AccordionTriggerProps>, forwardedRef) => {\n    const { __scopeAccordion, ...triggerProps } = props;\n    const accordionContext = useAccordionContext(ACCORDION_NAME, __scopeAccordion);\n    const itemContext = useAccordionItemContext(TRIGGER_NAME, __scopeAccordion);\n    const collapsibleContext = useAccordionCollapsibleContext(TRIGGER_NAME, __scopeAccordion);\n    const collapsibleScope = useCollapsibleScope(__scopeAccordion);\n    return (\n      <Collection.ItemSlot scope={__scopeAccordion}>\n        <CollapsiblePrimitive.Trigger\n          aria-disabled={(itemContext.open && !collapsibleContext.collapsible) || undefined}\n          data-orientation={accordionContext.orientation}\n          id={itemContext.triggerId}\n          {...collapsibleScope}\n          {...triggerProps}\n          ref={forwardedRef}\n        />\n      </Collection.ItemSlot>\n    );\n  }\n);\n\nAccordionTrigger.displayName = TRIGGER_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * AccordionContent\n * -----------------------------------------------------------------------------------------------*/\n\nconst CONTENT_NAME = 'AccordionContent';\n\ntype AccordionContentElement = React.ComponentRef<typeof CollapsiblePrimitive.Content>;\ntype CollapsibleContentProps = React.ComponentPropsWithoutRef<typeof CollapsiblePrimitive.Content>;\ninterface AccordionContentProps extends CollapsibleContentProps {}\n\n/**\n * `AccordionContent` contains the collapsible content for an `AccordionItem`.\n */\nconst AccordionContent = React.forwardRef<AccordionContentElement, AccordionContentProps>(\n  (props: ScopedProps<AccordionContentProps>, forwardedRef) => {\n    const { __scopeAccordion, ...contentProps } = props;\n    const accordionContext = useAccordionContext(ACCORDION_NAME, __scopeAccordion);\n    const itemContext = useAccordionItemContext(CONTENT_NAME, __scopeAccordion);\n    const collapsibleScope = useCollapsibleScope(__scopeAccordion);\n    return (\n      <CollapsiblePrimitive.Content\n        role=\"region\"\n        aria-labelledby={itemContext.triggerId}\n        data-orientation={accordionContext.orientation}\n        {...collapsibleScope}\n        {...contentProps}\n        ref={forwardedRef}\n        style={{\n          ['--radix-accordion-content-height' as any]: 'var(--radix-collapsible-content-height)',\n          ['--radix-accordion-content-width' as any]: 'var(--radix-collapsible-content-width)',\n          ...props.style,\n        }}\n      />\n    );\n  }\n);\n\nAccordionContent.displayName = CONTENT_NAME;\n\n/* -----------------------------------------------------------------------------------------------*/\n\nfunction getState(open?: boolean) {\n  return open ? 'open' : 'closed';\n}\n\nconst Root = Accordion;\nconst Item = AccordionItem;\nconst Header = AccordionHeader;\nconst Trigger = AccordionTrigger;\nconst Content = AccordionContent;\n\nexport {\n  createAccordionScope,\n  //\n  Accordion,\n  AccordionItem,\n  AccordionHeader,\n  AccordionTrigger,\n  AccordionContent,\n  //\n  Root,\n  Item,\n  Header,\n  Trigger,\n  Content,\n};\nexport type {\n  AccordionSingleProps,\n  AccordionMultipleProps,\n  AccordionItemProps,\n  AccordionHeaderProps,\n  AccordionTriggerProps,\n  AccordionContentProps,\n};\n"], "names": ["value", "open", "Root", "<PERSON><PERSON>", "Content"], "mappings": ";;;;;;;;;;;;;;AAAA,OAAO,WAAW;AAClB,SAAS,0BAA0B;AACnC,SAAS,wBAAwB;AACjC,SAAS,uBAAuB;AAChC,SAAS,4BAA4B;AACrC,SAAS,4BAA4B;AACrC,SAAS,iBAAiB;AAC1B,YAAY,0BAA0B;AAEtC,SAAS,aAAa;AAGtB,SAAS,oBAAoB;AAqCnB;;;;;;;;;;;;;;AA7BV,IAAM,iBAAiB;AACvB,IAAM,iBAAiB;IAAC;IAAQ;IAAO;IAAa;IAAW;IAAa,YAAY;CAAA;AAExF,IAAM,CAAC,YAAY,eAAe,qBAAqB,CAAA,qRACrD,mBAAA,EAA0C,cAAc;AAG1D,IAAM,CAAC,wBAAwB,oBAAoB,CAAA,GAAI,uSAAA,EAAmB,gBAAgB;IACxF;gRACA,yBAAA;CACD;AACD,IAAM,sSAAsB,yBAAA,CAAuB;AAUnD,IAAM,gUAAY,WAAA,CAAM,UAAA,CACtB,CAAC,OAAmE,iBAAiB;IACnF,MAAM,EAAE,IAAA,EAAM,GAAG,eAAe,CAAA,GAAI;IACpC,MAAM,cAAc;IACpB,MAAM,gBAAgB;IACtB,OACE,aAAA,GAAA,CAAA,GAAA,sUAAA,CAAA,MAAA,EAAC,WAAW,QAAA,EAAX;QAAoB,OAAO,MAAM,gBAAA;QAC/B,UAAA,SAAS,aACR,aAAA,GAAA,CAAA,GAAA,sUAAA,CAAA,MAAA,EAAC,uBAAA;YAAuB,GAAG,aAAA;YAAe,KAAK;QAAA,CAAc,IAE7D,aAAA,GAAA,CAAA,GAAA,sUAAA,CAAA,MAAA,EAAC,qBAAA;YAAqB,GAAG,WAAA;YAAa,KAAK;QAAA,CAAc;IAAA,CAE7D;AAEJ;AAGF,UAAU,WAAA,GAAc;AAUxB,IAAM,CAAC,wBAAwB,wBAAwB,CAAA,GACrD,uBAAmD,cAAc;AAEnE,IAAM,CAAC,8BAA8B,8BAA8B,CAAA,GAAI,uBACrE,gBACA;IAAE,aAAa;AAAM;AAyBvB,IAAM,2UAAsB,UAAA,CAAM,UAAA,CAChC,CAAC,OAA8C,iBAAiB;IAC9D,MAAM,EACJ,OAAO,SAAA,EACP,YAAA,EACA,gBAAgB,KAAO,CAAD,AAAC,EACvB,cAAc,KAAA,EACd,GAAG,sBACL,GAAI;IAEJ,MAAM,CAAC,OAAO,QAAQ,CAAA,uSAAI,uBAAA,EAAqB;QAC7C,MAAM;QACN,aAAa,gBAAgB;QAC7B,UAAU;QACV,QAAQ;IACV,CAAC;IAED,OACE,aAAA,GAAA,CAAA,GAAA,sUAAA,CAAA,MAAA,EAAC,wBAAA;QACC,OAAO,MAAM,gBAAA;QACb,4TAAO,UAAA,CAAM,OAAA,CAAQ,IAAO,QAAQ;gBAAC,KAAK;aAAA,GAAI,CAAC,CAAA,EAAI;YAAC,KAAK;SAAC;QAC1D,YAAY;QACZ,kUAAa,UAAA,CAAM,WAAA,CAAY,IAAM,eAAe,SAAS,EAAE,GAAG;YAAC;YAAa,QAAQ;SAAC;QAEzF,UAAA,aAAA,GAAA,CAAA,GAAA,sUAAA,CAAA,MAAA,EAAC,8BAAA;YAA6B,OAAO,MAAM,gBAAA;YAAkB;YAC3D,UAAA,aAAA,GAAA,CAAA,GAAA,sUAAA,CAAA,MAAA,EAAC,eAAA;gBAAe,GAAG,oBAAA;gBAAsB,KAAK;YAAA,CAAc;QAAA,CAC9D;IAAA;AAGN;AAsBF,IAAM,6UAAwB,UAAA,CAAM,UAAA,CAGlC,CAAC,OAAgD,iBAAiB;IAClE,MAAM,EACJ,OAAO,SAAA,EACP,YAAA,EACA,gBAAgB,KAAO,CAAD,AAAC,EACvB,GAAG,wBACL,GAAI;IAEJ,MAAM,CAAC,OAAO,QAAQ,CAAA,uSAAI,uBAAA,EAAqB;QAC7C,MAAM;QACN,aAAa,gBAAgB,CAAC,CAAA;QAC9B,UAAU;QACV,QAAQ;IACV,CAAC;IAED,MAAM,sUAAiB,UAAA,CAAM,WAAA,CAC3B,CAAC,YAAsB,SAAS,CAAC,YAAY,CAAC,CAAA,GAAM,CAAC;mBAAG;gBAAW,SAAS;aAAC,GAC7E;QAAC,QAAQ;KAAA;IAGX,MAAM,uUAAkB,UAAA,CAAM,WAAA,CAC5B,CAAC,YACC,SAAS,CAAC,YAAY,CAAC,CAAA,GAAM,UAAU,MAAA,CAAO,CAACA,SAAUA,WAAU,SAAS,CAAC,GAC/E;QAAC,QAAQ;KAAA;IAGX,OACE,aAAA,GAAA,CAAA,GAAA,sUAAA,CAAA,MAAA,EAAC,wBAAA;QACC,OAAO,MAAM,gBAAA;QACb;QACA,YAAY;QACZ,aAAa;QAEb,UAAA,aAAA,GAAA,CAAA,GAAA,sUAAA,CAAA,MAAA,EAAC,8BAAA;YAA6B,OAAO,MAAM,gBAAA;YAAkB,aAAa;YACxE,UAAA,aAAA,GAAA,CAAA,GAAA,sUAAA,CAAA,MAAA,EAAC,eAAA;gBAAe,GAAG,sBAAA;gBAAwB,KAAK;YAAA,CAAc;QAAA,CAChE;IAAA;AAGN,CAAC;AAUD,IAAM,CAAC,uBAAuB,mBAAmB,CAAA,GAC/C,uBAAkD,cAAc;AAsBlE,IAAM,qUAAgB,UAAA,CAAM,UAAA,CAC1B,CAAC,OAAwC,iBAAiB;IACxD,MAAM,EAAE,gBAAA,EAAkB,QAAA,EAAU,GAAA,EAAK,cAAc,UAAA,EAAY,GAAG,eAAe,CAAA,GAAI;IACzF,MAAM,oUAAe,UAAA,CAAM,MAAA,CAA6B,IAAI;IAC5D,MAAM,eAAe,ySAAA,EAAgB,cAAc,YAAY;IAC/D,MAAM,WAAW,cAAc,gBAAgB;IAC/C,MAAM,gBAAY,4RAAA,EAAa,GAAG;IAClC,MAAM,iBAAiB,cAAc;IAErC,MAAM,wPAAgB,uBAAA,EAAqB,MAAM,SAAA,EAAW,CAAC,UAAU;QACrE,IAAI,CAAC,eAAe,QAAA,CAAS,MAAM,GAAG,EAAG,CAAA;QACzC,MAAM,SAAS,MAAM,MAAA;QACrB,MAAM,oBAAoB,SAAS,EAAE,MAAA,CAAO,CAAC,OAAS,CAAC,KAAK,GAAA,CAAI,OAAA,EAAS,QAAQ;QACjF,MAAM,eAAe,kBAAkB,SAAA,CAAU,CAAC,OAAS,KAAK,GAAA,CAAI,OAAA,KAAY,MAAM;QACtF,MAAM,eAAe,kBAAkB,MAAA;QAEvC,IAAI,iBAAiB,CAAA,EAAI,CAAA;QAGzB,MAAM,cAAA,CAAe;QAErB,IAAI,YAAY;QAChB,MAAM,YAAY;QAClB,MAAM,WAAW,eAAe;QAEhC,MAAM,WAAW,MAAM;YACrB,YAAY,eAAe;YAC3B,IAAI,YAAY,UAAU;gBACxB,YAAY;YACd;QACF;QAEA,MAAM,WAAW,MAAM;YACrB,YAAY,eAAe;YAC3B,IAAI,YAAY,WAAW;gBACzB,YAAY;YACd;QACF;QAEA,OAAQ,MAAM,GAAA,EAAK;YACjB,KAAK;gBACH,YAAY;gBACZ;YACF,KAAK;gBACH,YAAY;gBACZ;YACF,KAAK;gBACH,IAAI,gBAAgB,cAAc;oBAChC,IAAI,gBAAgB;wBAClB,SAAS;oBACX,OAAO;wBACL,SAAS;oBACX;gBACF;gBACA;YACF,KAAK;gBACH,IAAI,gBAAgB,YAAY;oBAC9B,SAAS;gBACX;gBACA;YACF,KAAK;gBACH,IAAI,gBAAgB,cAAc;oBAChC,IAAI,gBAAgB;wBAClB,SAAS;oBACX,OAAO;wBACL,SAAS;oBACX;gBACF;gBACA;YACF,KAAK;gBACH,IAAI,gBAAgB,YAAY;oBAC9B,SAAS;gBACX;gBACA;QACJ;QAEA,MAAM,eAAe,YAAY;QACjC,iBAAA,CAAkB,YAAY,CAAA,CAAG,GAAA,CAAI,OAAA,EAAS,MAAM;IACtD,CAAC;IAED,OACE,aAAA,GAAA,CAAA,GAAA,sUAAA,CAAA,MAAA,EAAC,uBAAA;QACC,OAAO;QACP;QACA,WAAW;QACX;QAEA,UAAA,aAAA,GAAA,CAAA,GAAA,sUAAA,CAAA,MAAA,EAAC,WAAW,IAAA,EAAX;YAAgB,OAAO;YACtB,UAAA,aAAA,GAAA,CAAA,GAAA,sUAAA,CAAA,MAAA,+QAAC,YAAA,CAAU,GAAA,EAAV;gBACE,GAAG,cAAA;gBACJ,oBAAkB;gBAClB,KAAK;gBACL,WAAW,WAAW,KAAA,IAAY;YAAA;QACpC,CACF;IAAA;AAGN;AAOF,IAAM,YAAY;AAGlB,IAAM,CAAC,uBAAuB,uBAAuB,CAAA,GACnD,uBAAkD,SAAS;AAqB7D,IAAM,qUAAgB,UAAA,CAAM,UAAA,CAC1B,CAAC,OAAwC,iBAAiB;IACxD,MAAM,EAAE,gBAAA,EAAkB,KAAA,EAAO,GAAG,mBAAmB,CAAA,GAAI;IAC3D,MAAM,mBAAmB,oBAAoB,WAAW,gBAAgB;IACxE,MAAM,eAAe,yBAAyB,WAAW,gBAAgB;IACzE,MAAM,mBAAmB,oBAAoB,gBAAgB;IAC7D,MAAM,iTAAY,QAAA,CAAM;IACxB,MAAM,OAAQ,SAAS,aAAa,KAAA,CAAM,QAAA,CAAS,KAAK,KAAM;IAC9D,MAAM,WAAW,iBAAiB,QAAA,IAAY,MAAM,QAAA;IAEpD,OACE,aAAA,GAAA,CAAA,GAAA,sUAAA,CAAA,MAAA,EAAC,uBAAA;QACC,OAAO;QACP;QACA;QACA;QAEA,UAAA,aAAA,GAAA,CAAA,GAAA,sUAAA,CAAA,MAAA,8QAAsB,OAAA,EAArB;YACC,oBAAkB,iBAAiB,WAAA;YACnC,cAAY,SAAS,IAAI;YACxB,GAAG,gBAAA;YACH,GAAG,kBAAA;YACJ,KAAK;YACL;YACA;YACA,cAAc,CAACC,UAAS;gBACtB,IAAIA,OAAM;oBACR,aAAa,UAAA,CAAW,KAAK;gBAC/B,OAAO;oBACL,aAAa,WAAA,CAAY,KAAK;gBAChC;YACF;QAAA;IACF;AAGN;AAGF,cAAc,WAAA,GAAc;AAM5B,IAAM,cAAc;AAUpB,IAAM,kBAAkB,+TAAA,CAAM,UAAA,CAC5B,CAAC,OAA0C,iBAAiB;IAC1D,MAAM,EAAE,gBAAA,EAAkB,GAAG,YAAY,CAAA,GAAI;IAC7C,MAAM,mBAAmB,oBAAoB,gBAAgB,gBAAgB;IAC7E,MAAM,cAAc,wBAAwB,aAAa,gBAAgB;IACzE,OACE,aAAA,GAAA,CAAA,GAAA,sUAAA,CAAA,MAAA,+QAAC,YAAA,CAAU,EAAA,EAAV;QACC,oBAAkB,iBAAiB,WAAA;QACnC,cAAY,SAAS,YAAY,IAAI;QACrC,iBAAe,YAAY,QAAA,GAAW,KAAK,KAAA;QAC1C,GAAG,WAAA;QACJ,KAAK;IAAA;AAGX;AAGF,gBAAgB,WAAA,GAAc;AAM9B,IAAM,eAAe;AAUrB,IAAM,wUAAmB,UAAA,CAAM,UAAA,CAC7B,CAAC,OAA2C,iBAAiB;IAC3D,MAAM,EAAE,gBAAA,EAAkB,GAAG,aAAa,CAAA,GAAI;IAC9C,MAAM,mBAAmB,oBAAoB,gBAAgB,gBAAgB;IAC7E,MAAM,cAAc,wBAAwB,cAAc,gBAAgB;IAC1E,MAAM,qBAAqB,+BAA+B,cAAc,gBAAgB;IACxF,MAAM,mBAAmB,oBAAoB,gBAAgB;IAC7D,OACE,aAAA,GAAA,CAAA,GAAA,sUAAA,CAAA,MAAA,EAAC,WAAW,QAAA,EAAX;QAAoB,OAAO;QAC1B,UAAA,aAAA,GAAA,CAAA,GAAA,sUAAA,CAAA,MAAA,8QAAsB,UAAA,EAArB;YACC,iBAAgB,YAAY,IAAA,IAAQ,CAAC,mBAAmB,WAAA,IAAgB,KAAA;YACxE,oBAAkB,iBAAiB,WAAA;YACnC,IAAI,YAAY,SAAA;YACf,GAAG,gBAAA;YACH,GAAG,YAAA;YACJ,KAAK;QAAA;IACP,CACF;AAEJ;AAGF,iBAAiB,WAAA,GAAc;AAM/B,IAAM,eAAe;AASrB,IAAM,wUAAmB,UAAA,CAAM,UAAA,CAC7B,CAAC,OAA2C,iBAAiB;IAC3D,MAAM,EAAE,gBAAA,EAAkB,GAAG,aAAa,CAAA,GAAI;IAC9C,MAAM,mBAAmB,oBAAoB,gBAAgB,gBAAgB;IAC7E,MAAM,cAAc,wBAAwB,cAAc,gBAAgB;IAC1E,MAAM,mBAAmB,oBAAoB,gBAAgB;IAC7D,OACE,aAAA,GAAA,CAAA,GAAA,sUAAA,CAAA,MAAA,8QAAsB,UAAA,EAArB;QACC,MAAK;QACL,mBAAiB,YAAY,SAAA;QAC7B,oBAAkB,iBAAiB,WAAA;QAClC,GAAG,gBAAA;QACH,GAAG,YAAA;QACJ,KAAK;QACL,OAAO;YACL,CAAC,kCAAyC,CAAA,EAAG;YAC7C,CAAC,iCAAwC,CAAA,EAAG;YAC5C,GAAG,MAAM,KAAA;QACX;IAAA;AAGN;AAGF,iBAAiB,WAAA,GAAc;AAI/B,SAAS,SAAS,IAAA,EAAgB;IAChC,OAAO,OAAO,SAAS;AACzB;AAEA,IAAMC,QAAO;AACb,IAAM,OAAO;AACb,IAAM,SAAS;AACf,IAAMC,WAAU;AAChB,IAAMC,WAAU", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2237, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Cubentweb/cubent/node_modules/.pnpm/use-sync-external-store%401.5.0_react%4019.1.0/node_modules/use-sync-external-store/cjs/use-sync-external-store-shim.development.js"], "sourcesContent": ["/**\n * @license React\n * use-sync-external-store-shim.development.js\n *\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\n\"use strict\";\n\"production\" !== process.env.NODE_ENV &&\n  (function () {\n    function is(x, y) {\n      return (x === y && (0 !== x || 1 / x === 1 / y)) || (x !== x && y !== y);\n    }\n    function useSyncExternalStore$2(subscribe, getSnapshot) {\n      didWarnOld18Alpha ||\n        void 0 === React.startTransition ||\n        ((didWarnOld18Alpha = !0),\n        console.error(\n          \"You are using an outdated, pre-release alpha of React 18 that does not support useSyncExternalStore. The use-sync-external-store shim will not work correctly. Upgrade to a newer pre-release.\"\n        ));\n      var value = getSnapshot();\n      if (!didWarnUncachedGetSnapshot) {\n        var cachedValue = getSnapshot();\n        objectIs(value, cachedValue) ||\n          (console.error(\n            \"The result of getSnapshot should be cached to avoid an infinite loop\"\n          ),\n          (didWarnUncachedGetSnapshot = !0));\n      }\n      cachedValue = useState({\n        inst: { value: value, getSnapshot: getSnapshot }\n      });\n      var inst = cachedValue[0].inst,\n        forceUpdate = cachedValue[1];\n      useLayoutEffect(\n        function () {\n          inst.value = value;\n          inst.getSnapshot = getSnapshot;\n          checkIfSnapshotChanged(inst) && forceUpdate({ inst: inst });\n        },\n        [subscribe, value, getSnapshot]\n      );\n      useEffect(\n        function () {\n          checkIfSnapshotChanged(inst) && forceUpdate({ inst: inst });\n          return subscribe(function () {\n            checkIfSnapshotChanged(inst) && forceUpdate({ inst: inst });\n          });\n        },\n        [subscribe]\n      );\n      useDebugValue(value);\n      return value;\n    }\n    function checkIfSnapshotChanged(inst) {\n      var latestGetSnapshot = inst.getSnapshot;\n      inst = inst.value;\n      try {\n        var nextValue = latestGetSnapshot();\n        return !objectIs(inst, nextValue);\n      } catch (error) {\n        return !0;\n      }\n    }\n    function useSyncExternalStore$1(subscribe, getSnapshot) {\n      return getSnapshot();\n    }\n    \"undefined\" !== typeof __REACT_DEVTOOLS_GLOBAL_HOOK__ &&\n      \"function\" ===\n        typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStart &&\n      __REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStart(Error());\n    var React = require(\"react\"),\n      objectIs = \"function\" === typeof Object.is ? Object.is : is,\n      useState = React.useState,\n      useEffect = React.useEffect,\n      useLayoutEffect = React.useLayoutEffect,\n      useDebugValue = React.useDebugValue,\n      didWarnOld18Alpha = !1,\n      didWarnUncachedGetSnapshot = !1,\n      shim =\n        \"undefined\" === typeof window ||\n        \"undefined\" === typeof window.document ||\n        \"undefined\" === typeof window.document.createElement\n          ? useSyncExternalStore$1\n          : useSyncExternalStore$2;\n    exports.useSyncExternalStore =\n      void 0 !== React.useSyncExternalStore ? React.useSyncExternalStore : shim;\n    \"undefined\" !== typeof __REACT_DEVTOOLS_GLOBAL_HOOK__ &&\n      \"function\" ===\n        typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStop &&\n      __REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStop(Error());\n  })();\n"], "names": [], "mappings": "AAAA;;;;;;;;CAQC,GAED;AACA,oEACE,AAAC;IACC,SAAS,GAAG,CAAC,EAAE,CAAC;QACd,OAAO,AAAC,MAAM,KAAK,CAAC,MAAM,KAAK,IAAI,MAAM,IAAI,CAAC,KAAO,MAAM,KAAK,MAAM;IACxE;IACA,SAAS,uBAAuB,SAAS,EAAE,WAAW;QACpD,qBACE,KAAK,MAAM,MAAM,eAAe,IAChC,CAAC,AAAC,oBAAoB,CAAC,GACvB,QAAQ,KAAK,CACX,iMACD;QACH,IAAI,QAAQ;QACZ,IAAI,CAAC,4BAA4B;YAC/B,IAAI,cAAc;YAClB,SAAS,OAAO,gBACd,CAAC,QAAQ,KAAK,CACZ,yEAED,6BAA6B,CAAC,CAAE;QACrC;QACA,cAAc,SAAS;YACrB,MAAM;gBAAE,OAAO;gBAAO,aAAa;YAAY;QACjD;QACA,IAAI,OAAO,WAAW,CAAC,EAAE,CAAC,IAAI,EAC5B,cAAc,WAAW,CAAC,EAAE;QAC9B,gBACE;YACE,KAAK,KAAK,GAAG;YACb,KAAK,WAAW,GAAG;YACnB,uBAAuB,SAAS,YAAY;gBAAE,MAAM;YAAK;QAC3D,GACA;YAAC;YAAW;YAAO;SAAY;QAEjC,UACE;YACE,uBAAuB,SAAS,YAAY;gBAAE,MAAM;YAAK;YACzD,OAAO,UAAU;gBACf,uBAAuB,SAAS,YAAY;oBAAE,MAAM;gBAAK;YAC3D;QACF,GACA;YAAC;SAAU;QAEb,cAAc;QACd,OAAO;IACT;IACA,SAAS,uBAAuB,IAAI;QAClC,IAAI,oBAAoB,KAAK,WAAW;QACxC,OAAO,KAAK,KAAK;QACjB,IAAI;YACF,IAAI,YAAY;YAChB,OAAO,CAAC,SAAS,MAAM;QACzB,EAAE,OAAO,OAAO;YACd,OAAO,CAAC;QACV;IACF;IACA,SAAS,uBAAuB,SAAS,EAAE,WAAW;QACpD,OAAO;IACT;IACA,gBAAgB,OAAO,kCACrB,eACE,OAAO,+BAA+B,2BAA2B,IACnE,+BAA+B,2BAA2B,CAAC;IAC7D,IAAI,gOACF,WAAW,eAAe,OAAO,OAAO,EAAE,GAAG,OAAO,EAAE,GAAG,IACzD,WAAW,MAAM,QAAQ,EACzB,YAAY,MAAM,SAAS,EAC3B,kBAAkB,MAAM,eAAe,EACvC,gBAAgB,MAAM,aAAa,EACnC,oBAAoB,CAAC,GACrB,6BAA6B,CAAC,GAC9B,OACE,gBAAgB,OAAO,UACvB,gBAAgB,OAAO,OAAO,QAAQ,IACtC,gBAAgB,OAAO,OAAO,QAAQ,CAAC,aAAa,GAChD,yBACA;IACR,QAAQ,oBAAoB,GAC1B,KAAK,MAAM,MAAM,oBAAoB,GAAG,MAAM,oBAAoB,GAAG;IACvE,gBAAgB,OAAO,kCACrB,eACE,OAAO,+BAA+B,0BAA0B,IAClE,+BAA+B,0BAA0B,CAAC;AAC9D", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2313, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Cubentweb/cubent/node_modules/.pnpm/use-sync-external-store%401.5.0_react%4019.1.0/node_modules/use-sync-external-store/shim/index.js"], "sourcesContent": ["'use strict';\n\nif (process.env.NODE_ENV === 'production') {\n  module.exports = require('../cjs/use-sync-external-store-shim.production.js');\n} else {\n  module.exports = require('../cjs/use-sync-external-store-shim.development.js');\n}\n"], "names": [], "mappings": "AAAA;AAEA,uCAA2C;;AAE3C,OAAO;IACL,OAAO,OAAO;AAChB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2325, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Cubentweb/cubent/node_modules/.pnpm/%40radix-ui%2Breact-use-is-hydr_82baae08d9497ff2d0670f703ce5ede6/node_modules/%40radix-ui/react-use-is-hydrated/src/use-is-hydrated.tsx"], "sourcesContent": ["import { useSyncExternalStore } from 'use-sync-external-store/shim';\n\n/**\n * Determines whether or not the component tree has been hydrated.\n */\nexport function useIsHydrated() {\n  return useSyncExternalStore(\n    subscribe,\n    () => true,\n    () => false\n  );\n}\n\nfunction subscribe() {\n  return () => {};\n}\n"], "names": [], "mappings": ";;;;AAAA,SAAS,4BAA4B;;AAK9B,SAAS,gBAAgB;IAC9B,gRAAO,uBAAA,EACL,WACA,IAAM,MACN,IAAM;AAEV;AAEA,SAAS,YAAY;IACnB,OAAO,KAAO,CAAD;AACf", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2345, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Cubentweb/cubent/node_modules/.pnpm/%40radix-ui%2Breact-avatar%401.1._2102b0ade537f1e2a172941acff2e7b1/node_modules/%40radix-ui/react-avatar/src/avatar.tsx"], "sourcesContent": ["import * as React from 'react';\nimport { createContextScope } from '@radix-ui/react-context';\nimport { useCallbackRef } from '@radix-ui/react-use-callback-ref';\nimport { useLayoutEffect } from '@radix-ui/react-use-layout-effect';\nimport { Primitive } from '@radix-ui/react-primitive';\nimport { useIsHydrated } from '@radix-ui/react-use-is-hydrated';\n\nimport type { Scope } from '@radix-ui/react-context';\n\n/* -------------------------------------------------------------------------------------------------\n * Avatar\n * -----------------------------------------------------------------------------------------------*/\n\nconst AVATAR_NAME = 'Avatar';\n\ntype ScopedProps<P> = P & { __scopeAvatar?: Scope };\nconst [createAvatarContext, createAvatarScope] = createContextScope(AVATAR_NAME);\n\ntype ImageLoadingStatus = 'idle' | 'loading' | 'loaded' | 'error';\n\ntype AvatarContextValue = {\n  imageLoadingStatus: ImageLoadingStatus;\n  onImageLoadingStatusChange(status: ImageLoadingStatus): void;\n};\n\nconst [AvatarProvider, useAvatarContext] = createAvatarContext<AvatarContextValue>(AVATAR_NAME);\n\ntype AvatarElement = React.ComponentRef<typeof Primitive.span>;\ntype PrimitiveSpanProps = React.ComponentPropsWithoutRef<typeof Primitive.span>;\ninterface AvatarProps extends PrimitiveSpanProps {}\n\nconst Avatar = React.forwardRef<AvatarElement, AvatarProps>(\n  (props: ScopedProps<AvatarProps>, forwardedRef) => {\n    const { __scopeAvatar, ...avatarProps } = props;\n    const [imageLoadingStatus, setImageLoadingStatus] = React.useState<ImageLoadingStatus>('idle');\n    return (\n      <AvatarProvider\n        scope={__scopeAvatar}\n        imageLoadingStatus={imageLoadingStatus}\n        onImageLoadingStatusChange={setImageLoadingStatus}\n      >\n        <Primitive.span {...avatarProps} ref={forwardedRef} />\n      </AvatarProvider>\n    );\n  }\n);\n\nAvatar.displayName = AVATAR_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * AvatarImage\n * -----------------------------------------------------------------------------------------------*/\n\nconst IMAGE_NAME = 'AvatarImage';\n\ntype AvatarImageElement = React.ComponentRef<typeof Primitive.img>;\ntype PrimitiveImageProps = React.ComponentPropsWithoutRef<typeof Primitive.img>;\ninterface AvatarImageProps extends PrimitiveImageProps {\n  onLoadingStatusChange?: (status: ImageLoadingStatus) => void;\n}\n\nconst AvatarImage = React.forwardRef<AvatarImageElement, AvatarImageProps>(\n  (props: ScopedProps<AvatarImageProps>, forwardedRef) => {\n    const { __scopeAvatar, src, onLoadingStatusChange = () => {}, ...imageProps } = props;\n    const context = useAvatarContext(IMAGE_NAME, __scopeAvatar);\n    const imageLoadingStatus = useImageLoadingStatus(src, imageProps);\n    const handleLoadingStatusChange = useCallbackRef((status: ImageLoadingStatus) => {\n      onLoadingStatusChange(status);\n      context.onImageLoadingStatusChange(status);\n    });\n\n    useLayoutEffect(() => {\n      if (imageLoadingStatus !== 'idle') {\n        handleLoadingStatusChange(imageLoadingStatus);\n      }\n    }, [imageLoadingStatus, handleLoadingStatusChange]);\n\n    return imageLoadingStatus === 'loaded' ? (\n      <Primitive.img {...imageProps} ref={forwardedRef} src={src} />\n    ) : null;\n  }\n);\n\nAvatarImage.displayName = IMAGE_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * AvatarFallback\n * -----------------------------------------------------------------------------------------------*/\n\nconst FALLBACK_NAME = 'AvatarFallback';\n\ntype AvatarFallbackElement = React.ComponentRef<typeof Primitive.span>;\ninterface AvatarFallbackProps extends PrimitiveSpanProps {\n  delayMs?: number;\n}\n\nconst AvatarFallback = React.forwardRef<AvatarFallbackElement, AvatarFallbackProps>(\n  (props: ScopedProps<AvatarFallbackProps>, forwardedRef) => {\n    const { __scopeAvatar, delayMs, ...fallbackProps } = props;\n    const context = useAvatarContext(FALLBACK_NAME, __scopeAvatar);\n    const [canRender, setCanRender] = React.useState(delayMs === undefined);\n\n    React.useEffect(() => {\n      if (delayMs !== undefined) {\n        const timerId = window.setTimeout(() => setCanRender(true), delayMs);\n        return () => window.clearTimeout(timerId);\n      }\n    }, [delayMs]);\n\n    return canRender && context.imageLoadingStatus !== 'loaded' ? (\n      <Primitive.span {...fallbackProps} ref={forwardedRef} />\n    ) : null;\n  }\n);\n\nAvatarFallback.displayName = FALLBACK_NAME;\n\n/* -----------------------------------------------------------------------------------------------*/\n\nfunction resolveLoadingStatus(image: HTMLImageElement | null, src?: string): ImageLoadingStatus {\n  if (!image) {\n    return 'idle';\n  }\n  if (!src) {\n    return 'error';\n  }\n  if (image.src !== src) {\n    image.src = src;\n  }\n  return image.complete && image.naturalWidth > 0 ? 'loaded' : 'loading';\n}\n\nfunction useImageLoadingStatus(\n  src: string | undefined,\n  { referrerPolicy, crossOrigin }: AvatarImageProps\n) {\n  const isHydrated = useIsHydrated();\n  const imageRef = React.useRef<HTMLImageElement | null>(null);\n  const image = (() => {\n    if (!isHydrated) return null;\n    if (!imageRef.current) {\n      imageRef.current = new window.Image();\n    }\n    return imageRef.current;\n  })();\n\n  const [loadingStatus, setLoadingStatus] = React.useState<ImageLoadingStatus>(() =>\n    resolveLoadingStatus(image, src)\n  );\n\n  useLayoutEffect(() => {\n    setLoadingStatus(resolveLoadingStatus(image, src));\n  }, [image, src]);\n\n  useLayoutEffect(() => {\n    const updateStatus = (status: ImageLoadingStatus) => () => {\n      setLoadingStatus(status);\n    };\n\n    if (!image) return;\n\n    const handleLoad = updateStatus('loaded');\n    const handleError = updateStatus('error');\n    image.addEventListener('load', handleLoad);\n    image.addEventListener('error', handleError);\n    if (referrerPolicy) {\n      image.referrerPolicy = referrerPolicy;\n    }\n    if (typeof crossOrigin === 'string') {\n      image.crossOrigin = crossOrigin;\n    }\n\n    return () => {\n      image.removeEventListener('load', handleLoad);\n      image.removeEventListener('error', handleError);\n    };\n  }, [image, crossOrigin, referrerPolicy]);\n\n  return loadingStatus;\n}\n\nconst Root = Avatar;\nconst Image = AvatarImage;\nconst Fallback = AvatarFallback;\n\nexport {\n  createAvatarScope,\n  //\n  Avatar,\n  AvatarImage,\n  AvatarFallback,\n  //\n  Root,\n  Image,\n  Fallback,\n};\nexport type { AvatarProps, AvatarImageProps, AvatarFallbackProps };\n"], "names": [], "mappings": ";;;;;;;;;;AAAA,YAAY,WAAW;AACvB,SAAS,0BAA0B;AACnC,SAAS,sBAAsB;AAC/B,SAAS,uBAAuB;AAChC,SAAS,iBAAiB;AAC1B,SAAS,qBAAqB;AAoCtB;;;;;;;;;AA5BR,IAAM,cAAc;AAGpB,IAAM,CAAC,qBAAqB,iBAAiB,CAAA,qRAAI,qBAAA,EAAmB,WAAW;AAS/E,IAAM,CAAC,gBAAgB,gBAAgB,CAAA,GAAI,oBAAwC,WAAW;AAM9F,IAAM,kUAAe,aAAA,EACnB,CAAC,OAAiC,iBAAiB;IACjD,MAAM,EAAE,aAAA,EAAe,GAAG,YAAY,CAAA,GAAI;IAC1C,MAAM,CAAC,oBAAoB,qBAAqB,CAAA,4TAAU,WAAA,EAA6B,MAAM;IAC7F,OACE,aAAA,GAAA,CAAA,GAAA,sUAAA,CAAA,MAAA,EAAC,gBAAA;QACC,OAAO;QACP;QACA,4BAA4B;QAE5B,UAAA,aAAA,GAAA,CAAA,GAAA,sUAAA,CAAA,MAAA,+QAAC,YAAA,CAAU,IAAA,EAAV;YAAgB,GAAG,WAAA;YAAa,KAAK;QAAA,CAAc;IAAA;AAG1D;AAGF,OAAO,WAAA,GAAc;AAMrB,IAAM,aAAa;AAQnB,IAAM,sUAAoB,cAAA,EACxB,CAAC,OAAsC,iBAAiB;IACtD,MAAM,EAAE,aAAA,EAAe,GAAA,EAAK,wBAAwB,KAAO,CAAD,AAAC,EAAG,GAAG,WAAW,CAAA,GAAI;IAChF,MAAM,UAAU,iBAAiB,YAAY,aAAa;IAC1D,MAAM,qBAAqB,sBAAsB,KAAK,UAAU;IAChE,MAAM,0TAA4B,iBAAA,EAAe,CAAC,WAA+B;QAC/E,sBAAsB,MAAM;QAC5B,QAAQ,0BAAA,CAA2B,MAAM;IAC3C,CAAC;IAED,CAAA,GAAA,6RAAA,CAAA,kBAAA,EAAgB,MAAM;QACpB,IAAI,uBAAuB,QAAQ;YACjC,0BAA0B,kBAAkB;QAC9C;IACF,GAAG;QAAC;QAAoB,yBAAyB;KAAC;IAElD,OAAO,uBAAuB,WAC5B,aAAA,GAAA,CAAA,GAAA,sUAAA,CAAA,MAAA,+QAAC,YAAA,CAAU,GAAA,EAAV;QAAe,GAAG,UAAA;QAAY,KAAK;QAAc;IAAA,CAAU,IAC1D;AACN;AAGF,YAAY,WAAA,GAAc;AAM1B,IAAM,gBAAgB;AAOtB,IAAM,0UAAuB,aAAA,EAC3B,CAAC,OAAyC,iBAAiB;IACzD,MAAM,EAAE,aAAA,EAAe,OAAA,EAAS,GAAG,cAAc,CAAA,GAAI;IACrD,MAAM,UAAU,iBAAiB,eAAe,aAAa;IAC7D,MAAM,CAAC,WAAW,YAAY,CAAA,4TAAU,WAAA,EAAS,YAAY,KAAA,CAAS;QAEhE,iUAAA,EAAU,MAAM;QACpB,IAAI,YAAY,KAAA,GAAW;YACzB,MAAM,UAAU,OAAO,UAAA,CAAW,IAAM,aAAa,IAAI,GAAG,OAAO;YACnE,OAAO,IAAM,OAAO,YAAA,CAAa,OAAO;QAC1C;IACF,GAAG;QAAC,OAAO;KAAC;IAEZ,OAAO,aAAa,QAAQ,kBAAA,KAAuB,WACjD,aAAA,GAAA,CAAA,GAAA,sUAAA,CAAA,MAAA,EAAC,yRAAA,CAAU,IAAA,EAAV;QAAgB,GAAG,aAAA;QAAe,KAAK;IAAA,CAAc,IACpD;AACN;AAGF,eAAe,WAAA,GAAc;AAI7B,SAAS,qBAAqB,KAAA,EAAgC,GAAA,EAAkC;IAC9F,IAAI,CAAC,OAAO;QACV,OAAO;IACT;IACA,IAAI,CAAC,KAAK;QACR,OAAO;IACT;IACA,IAAI,MAAM,GAAA,KAAQ,KAAK;QACrB,MAAM,GAAA,GAAM;IACd;IACA,OAAO,MAAM,QAAA,IAAY,MAAM,YAAA,GAAe,IAAI,WAAW;AAC/D;AAEA,SAAS,sBACP,GAAA,EACA,EAAE,cAAA,EAAgB,WAAA,CAAY,CAAA,EAC9B;IACA,MAAM,cAAa,+SAAA,CAAc;IACjC,MAAM,oUAAiB,SAAA,EAAgC,IAAI;IAC3D,MAAM,QAAA,CAAS,MAAM;QACnB,IAAI,CAAC,WAAY,CAAA,OAAO;QACxB,IAAI,CAAC,SAAS,OAAA,EAAS;YACrB,SAAS,OAAA,GAAU,IAAI,OAAO,KAAA,CAAM;QACtC;QACA,OAAO,SAAS,OAAA;IAClB,CAAA,EAAG;IAEH,MAAM,CAAC,eAAe,gBAAgB,CAAA,4TAAU,WAAA,EAA6B,IAC3E,qBAAqB,OAAO,GAAG;IAGjC,CAAA,GAAA,6RAAA,CAAA,kBAAA,EAAgB,MAAM;QACpB,iBAAiB,qBAAqB,OAAO,GAAG,CAAC;IACnD,GAAG;QAAC;QAAO,GAAG;KAAC;IAEf,CAAA,GAAA,6RAAA,CAAA,kBAAA,EAAgB,MAAM;QACpB,MAAM,eAAe,CAAC,SAA+B,MAAM;gBACzD,iBAAiB,MAAM;YACzB;QAEA,IAAI,CAAC,MAAO,CAAA;QAEZ,MAAM,aAAa,aAAa,QAAQ;QACxC,MAAM,cAAc,aAAa,OAAO;QACxC,MAAM,gBAAA,CAAiB,QAAQ,UAAU;QACzC,MAAM,gBAAA,CAAiB,SAAS,WAAW;QAC3C,IAAI,gBAAgB;YAClB,MAAM,cAAA,GAAiB;QACzB;QACA,IAAI,OAAO,gBAAgB,UAAU;YACnC,MAAM,WAAA,GAAc;QACtB;QAEA,OAAO,MAAM;YACX,MAAM,mBAAA,CAAoB,QAAQ,UAAU;YAC5C,MAAM,mBAAA,CAAoB,SAAS,WAAW;QAChD;IACF,GAAG;QAAC;QAAO;QAAa,cAAc;KAAC;IAEvC,OAAO;AACT;AAEA,IAAM,OAAO;AACb,IAAM,QAAQ;AACd,IAAM,WAAW", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2496, "column": 0}, "map": {"version": 3, "file": "user.js", "sources": ["file:///C:/Users/<USER>/Documents/Cubentweb/cubent/node_modules/.pnpm/lucide-react%400.511.0_react%4019.1.0/node_modules/lucide-react/src/icons/user.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2', key: '975kel' }],\n  ['circle', { cx: '12', cy: '7', r: '4', key: '17ys0d' }],\n];\n\n/**\n * @component @name User\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTkgMjF2LTJhNCA0IDAgMCAwLTQtNEg5YTQgNCAwIDAgMC00IDR2MiIgLz4KICA8Y2lyY2xlIGN4PSIxMiIgY3k9IjciIHI9IjQiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/user\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst User = createLucideIcon('user', __iconNode);\n\nexport default User;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAuB,CAAA,CAAA,CAAA;IAClC;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAA6C,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IAC1E;QAAC,QAAU,CAAA;QAAA,CAAA;YAAE,EAAI,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAM,CAAI,CAAA,CAAA,CAAA,GAAA,CAAK;YAAA,CAAG,EAAA,CAAA,CAAA,CAAA,CAAA;YAAK,GAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA,CAAU;KAAA;CACzD;AAaM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,IAAA,CAAO,CAAA,4PAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAiB,CAAjB,CAAA,AAAiB,CAAjB,AAAiB,CAAjB,AAAiB,CAAA,CAAA,CAAA,CAAA,EAAQ,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "ignoreList": [0], "debugId": null}}]}