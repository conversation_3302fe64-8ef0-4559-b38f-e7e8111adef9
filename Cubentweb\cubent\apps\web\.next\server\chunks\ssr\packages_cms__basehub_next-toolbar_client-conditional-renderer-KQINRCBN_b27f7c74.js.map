{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Cubentweb/cubent/packages/cms/.basehub/next-toolbar/client-conditional-renderer-KQINRCBN.js/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const ClientConditionalRenderer = registerClientReference(\n    function() { throw new Error(\"Attempted to call ClientConditionalRenderer() from the server but ClientConditional<PERSON>ender<PERSON> is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/packages/cms/.basehub/next-toolbar/client-conditional-renderer-KQINRCBN.js <module evaluation>\",\n    \"ClientConditionalRenderer\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,4BAA4B,CAAA,GAAA,oWAAA,CAAA,0BAAuB,AAAD,EAC3D;IAAa,MAAM,IAAI,MAAM;AAAkQ,GAC/R,4GACA", "debugId": null}}, {"offset": {"line": 21, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Cubentweb/cubent/packages/cms/.basehub/next-toolbar/client-conditional-renderer-KQINRCBN.js/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const ClientConditionalRenderer = registerClientReference(\n    function() { throw new Error(\"Attempted to call ClientConditionalRenderer() from the server but ClientConditional<PERSON>ender<PERSON> is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/packages/cms/.basehub/next-toolbar/client-conditional-renderer-KQINRCBN.js\",\n    \"ClientConditionalRenderer\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,4BAA4B,CAAA,GAAA,oWAAA,CAAA,0BAAuB,AAAD,EAC3D;IAAa,MAAM,IAAI,MAAM;AAAkQ,GAC/R,wFACA", "debugId": null}}, {"offset": {"line": 35, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}]}